{"name": "Routing Management", "trans": ["路由管理"], "methods": [{"name": "Navigator Basic Usage", "trans": ["Navigator基本用法"], "usage": {"syntax": "Navigator.push(context, MaterialPageRoute(builder: ...)), Navigator.pop(context)", "description": "Navigator用于页面跳转和返回，push方法跳转新页面，pop方法返回上一页。", "parameters": [{"name": "context", "description": "上下文对象"}, {"name": "route", "description": "页面路由对象"}], "returnValue": "无返回值，页面跳转或返回。", "examples": [{"code": "// Navigator基本用法示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      home: FirstPage(),\n    );\n  }\n}\n\nclass FirstPage extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('第一页')),\n      body: Center(\n        child: ElevatedButton(\n          child: Text('跳转到第二页'),\n          onPressed: () {\n            Navigator.push(context, MaterialPageRoute(builder: (_) => SecondPage()));\n          },\n        ),\n      ),\n    );\n  }\n}\n\nclass SecondPage extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('第二页')),\n      body: Center(\n        child: ElevatedButton(\n          child: Text('返回'),\n          onPressed: () {\n            Navigator.pop(context);\n          },\n        ),\n      ),\n    );\n  }\n}", "explanation": "演示了Navigator的push和pop方法实现页面跳转和返回。"}]}}, {"name": "Named Routes", "trans": ["命名路由"], "usage": {"syntax": "MaterialApp(routes: {...}, initialRoute: ...), Navigator.pushNamed(context, '/route')", "description": "命名路由通过字符串标识页面，便于统一管理和跳转。", "parameters": [{"name": "routes", "description": "路由表，定义路由名与页面的映射关系"}, {"name": "initialRoute", "description": "应用启动时的初始路由"}], "returnValue": "无返回值，页面跳转。", "examples": [{"code": "// 命名路由示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      initialRoute: '/',\n      routes: {\n        '/': (context) => HomePage(),\n        '/about': (context) => AboutPage(),\n      },\n    );\n  }\n}\n\nclass HomePage extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('首页')),\n      body: Center(\n        child: ElevatedButton(\n          child: Text('跳转到关于页'),\n          onPressed: () {\n            Navigator.pushNamed(context, '/about');\n          },\n        ),\n      ),\n    );\n  }\n}\n\nclass AboutPage extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('关于页')),\n      body: Center(child: Text('这是关于页面')),\n    );\n  }\n}", "explanation": "演示了如何通过命名路由进行页面跳转。"}]}}, {"name": "Route Parameters Passing", "trans": ["路由参数传递"], "usage": {"syntax": "Navigator.push(context, MaterialPageRoute(builder: (context) => Page(param: value)))", "description": "通过构造函数参数或settings.arguments传递数据到新页面。", "parameters": [{"name": "参数", "description": "通过构造函数或arguments传递到目标页面的数据"}], "returnValue": "无返回值，目标页面可接收参数。", "examples": [{"code": "// 路由参数传递示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      home: FirstPage(),\n    );\n  }\n}\n\nclass FirstPage extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('第一页')),\n      body: Center(\n        child: ElevatedButton(\n          child: Text('传递参数到第二页'),\n          onPressed: () {\n            Navigator.push(context, MaterialPageRoute(builder: (_) => SecondPage(msg: '你好，第二页')));\n          },\n        ),\n      ),\n    );\n  }\n}\n\nclass SecondPage extends StatelessWidget {\n  final String msg;\n  SecondPage({required this.msg});\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('第二页')),\n      body: Center(child: Text(msg)),\n    );\n  }\n}", "explanation": "演示了通过构造函数传递参数到新页面。"}]}}, {"name": "Route Return Value", "trans": ["路由返回值"], "usage": {"syntax": "Navigator.push返回Future，pop时可传递返回值。", "description": "通过await Navigator.push获取新页面返回的数据，pop时传递返回值。", "parameters": [{"name": "pop返回值", "description": "Navigator.pop(context, value)返回的数据"}], "returnValue": "Future，返回新页面pop时传递的数据。", "examples": [{"code": "// 路由返回值示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      home: FirstPage(),\n    );\n  }\n}\n\nclass FirstPage extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('第一页')),\n      body: Center(\n        child: ElevatedButton(\n          child: Text('跳转并等待返回'),\n          onPressed: () async {\n            final result = await Navigator.push(context, MaterialPageRoute(builder: (_) => SecondPage()));\n            ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('返回值: \\${result}')));\n          },\n        ),\n      ),\n    );\n  }\n}\n\nclass SecondPage extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('第二页')),\n      body: Center(\n        child: ElevatedButton(\n          child: Text('返回数据'),\n          onPressed: () {\n            Navigator.pop(context, '这是返回值');\n          },\n        ),\n      ),\n    );\n  }\n}", "explanation": "演示了如何通过pop返回数据，并在前一个页面获取。"}]}}, {"name": "Route Guard and Middleware", "trans": ["路由守卫与中间件"], "usage": {"syntax": "onGenerateRoute、拦截器等实现路由守卫。", "description": "可通过onGenerateRoute实现路由拦截、鉴权等守卫逻辑。", "parameters": [{"name": "onGenerateRoute", "description": "全局路由生成回调，可自定义拦截逻辑"}], "returnValue": "无返回值，拦截或放行路由。", "examples": [{"code": "// 路由守卫与中间件示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nbool isLogin = false; // 假设的登录状态\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      initialRoute: '/',\n      onGenerateRoute: (settings) {\n        if (settings.name == '/protected' && !isLogin) {\n          // 未登录拦截，跳转到登录页\n          return MaterialPageRoute(builder: (_) => LoginPage());\n        }\n        if (settings.name == '/protected') {\n          return MaterialPageRoute(builder: (_) => ProtectedPage());\n        }\n        return MaterialPageRoute(builder: (_) => HomePage());\n      },\n    );\n  }\n}\n\nclass HomePage extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('首页')),\n      body: Center(\n        child: ElevatedButton(\n          child: Text('进入受保护页面'),\n          onPressed: () {\n            Navigator.pushNamed(context, '/protected');\n          },\n        ),\n      ),\n    );\n  }\n}\n\nclass LoginPage extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('登录页')),\n      body: Center(child: Text('请先登录')),\n    );\n  }\n}\n\nclass ProtectedPage extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('受保护页面')),\n      body: Center(child: Text('已登录，访问成功')),\n    );\n  }\n}", "explanation": "演示了通过onGenerateRoute实现路由守卫和拦截。"}]}}, {"name": "Assignment", "trans": ["作业：实现页面跳转、参数传递和返回值功能。"], "usage": {"syntax": "无", "description": "请实现两个页面，完成页面跳转、参数传递和返回值功能。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 作业参考实现\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      home: FirstPage(),\n    );\n  }\n}\n\nclass FirstPage extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('第一页')),\n      body: Center(\n        child: ElevatedButton(\n          child: Text('跳转到第二页'),\n          onPressed: () async {\n            final result = await Navigator.push(context, MaterialPageRoute(builder: (_) => SecondPage(msg: 'Hello')));\n            ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('返回值: \\${result}')));\n          },\n        ),\n      ),\n    );\n  }\n}\n\nclass SecondPage extends StatelessWidget {\n  final String msg;\n  SecondPage({required this.msg});\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('第二页')),\n      body: Center(\n        child: Column(\n          mainAxisAlignment: MainAxisAlignment.center,\n          children: [\n            Text('收到参数: \\${msg}'),\n            ElevatedButton(\n              child: Text('返回数据'),\n              onPressed: () {\n                Navigator.pop(context, '这是返回值');\n              },\n            ),\n          ],\n        ),\n      ),\n    );\n  }\n}", "explanation": "作业要求实现页面跳转、参数传递和返回值功能。"}]}}]}