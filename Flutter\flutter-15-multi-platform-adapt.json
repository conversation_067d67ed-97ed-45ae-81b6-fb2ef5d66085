{"name": "Multi-platform Adaptation", "trans": ["多端适配"], "methods": [{"name": "Responsive Layout", "trans": ["响应式布局"], "usage": {"syntax": "LayoutBuilder、MediaQuery、Flexible、Expanded等", "description": "通过LayoutBuilder、MediaQuery等组件获取屏幕尺寸，结合弹性布局实现自适应多端的响应式界面。", "parameters": [{"name": "context", "description": "BuildContext，用于获取屏幕信息"}, {"name": "constraints", "description": "LayoutBuilder的约束参数"}], "returnValue": "根据屏幕尺寸自适应的Widget。", "examples": [{"code": "// 响应式布局示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(home: ResponsiveDemo());\n  }\n}\n\nclass ResponsiveDemo extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('响应式布局')),\n      body: LayoutBuilder(\n        builder: (context, constraints) {\n          if (constraints.maxWidth < 600) {\n            // 小屏手机布局\n            return Center(child: Text('手机界面'));\n          } else {\n            // 大屏平板/桌面布局\n            return Center(child: Text('平板/桌面界面'));\n          }\n        },\n      ),\n    );\n  }\n}\n", "explanation": "通过LayoutBuilder根据屏幕宽度自适应不同布局。"}]}}, {"name": "Internationalization and Localization", "trans": ["国际化与本地化"], "usage": {"syntax": "MaterialApp(localizationsDelegates: ..., supportedLocales: [...])", "description": "通过配置localizationsDelegates和supportedLocales实现多语言支持，结合intl包进行文本国际化。", "parameters": [{"name": "localizationsDelegates", "description": "本地化代理列表"}, {"name": "supportedLocales", "description": "支持的语言列表"}], "returnValue": "根据系统语言自动切换的多语言界面。", "examples": [{"code": "// 国际化与本地化示例（简化）\nimport 'package:flutter/material.dart';\nimport 'package:flutter_localizations/flutter_localizations.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      localizationsDelegates: [\n        GlobalMaterialLocalizations.delegate,\n        GlobalWidgetsLocalizations.delegate,\n        GlobalCupertinoLocalizations.delegate,\n      ],\n      supportedLocales: [\n        const Locale('en', ''),\n        const Locale('zh', ''),\n      ],\n      home: I18nDemo(),\n    );\n  }\n}\n\nclass I18nDemo extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('国际化示例')),\n      body: Center(child: Text(Localizations.localeOf(context).languageCode == 'zh' ? '你好' : 'Hello')),\n    );\n  }\n}\n", "explanation": "通过localizationsDelegates和supportedLocales实现中英文切换。"}]}}, {"name": "Theme and Style Customization", "trans": ["主题与样式自定义"], "usage": {"syntax": "ThemeData、Theme.of(context)、自定义颜色字体等", "description": "通过ThemeData全局配置主题，或Theme.of(context)动态切换主题，实现多端统一风格和个性化样式。", "parameters": [{"name": "ThemeData", "description": "主题配置对象"}, {"name": "primaryColor", "description": "主色调"}, {"name": "textTheme", "description": "文本样式"}], "returnValue": "应用主题和样式的Widget。", "examples": [{"code": "// 主题与样式自定义示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      theme: ThemeData(\n        primarySwatch: Colors.blue,\n        textTheme: TextTheme(bodyText2: TextStyle(fontSize: 20, color: Colors.red)),\n      ),\n      home: ThemeDemo(),\n    );\n  }\n}\n\nclass ThemeDemo extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('主题与样式')),\n      body: Center(child: Text('自定义样式', style: Theme.of(context).textTheme.bodyText2)),\n    );\n  }\n}\n", "explanation": "通过ThemeData和Theme.of(context)实现全局和局部样式自定义。"}]}}, {"name": "Assignment", "trans": ["作业：实现一个多端适配的Flutter页面。"], "usage": {"syntax": "无", "description": "请实现一个页面，要求根据屏幕宽度自适应布局，支持中英文切换，并自定义主题样式。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 作业参考实现（简化版）\nimport 'package:flutter/material.dart';\nimport 'package:flutter_localizations/flutter_localizations.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      theme: ThemeData(primarySwatch: Colors.green),\n      localizationsDelegates: [\n        GlobalMaterialLocalizations.delegate,\n        GlobalWidgetsLocalizations.delegate,\n        GlobalCupertinoLocalizations.delegate,\n      ],\n      supportedLocales: [\n        const Locale('en', ''),\n        const Locale('zh', ''),\n      ],\n      home: MultiAdaptDemo(),\n    );\n  }\n}\n\nclass MultiAdaptDemo extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('多端适配作业')),\n      body: LayoutBuilder(\n        builder: (context, constraints) {\n          String text = Localizations.localeOf(context).languageCode == 'zh' ? '你好' : 'Hello';\n          if (constraints.maxWidth < 600) {\n            return Center(child: Text(text, style: Theme.of(context).textTheme.bodyText2));\n          } else {\n            return Row(\n              mainAxisAlignment: MainAxisAlignment.center,\n              children: [\n                Text(text, style: Theme.of(context).textTheme.bodyText2),\n                SizedBox(width: 40),\n                Icon(Icons.devices, size: 40),\n              ],\n            );\n          }\n        },\n      ),\n    );\n  }\n}\n", "explanation": "作业要求实现响应式布局、国际化和主题自定义。"}]}}]}