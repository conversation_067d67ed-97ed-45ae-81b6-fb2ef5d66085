{"name": "MyBatis Integration", "trans": ["MyBatis集成"], "methods": [{"name": "MyBatis Dependency and Configuration", "trans": ["MyBatis依赖与配置"], "usage": {"syntax": "pom.xml中添加mybatis-spring-boot-starter依赖，配置数据源和mybatis参数", "description": "Spring Boot通过mybatis-spring-boot-starter集成MyBatis，需配置数据源和mybatis相关参数。", "parameters": [{"name": "mybatis-spring-boot-starter", "description": "MyBatis核心依赖"}, {"name": "spring.datasource.*", "description": "数据库连接参数"}, {"name": "mybatis.mapper-locations", "description": "Mapper XML文件路径"}], "returnValue": "自动配置的MyBatis环境", "examples": [{"code": "<!-- pom.xml -->\n<dependency>\n  <groupId>org.mybatis.spring.boot</groupId>\n  <artifactId>mybatis-spring-boot-starter</artifactId>\n  <version>2.3.1</version>\n</dependency>\n\n# application.properties\nspring.datasource.url=*****************************************************************************************************************************************************", "explanation": "引入依赖并配置参数即可使用MyBatis。"}]}}, {"name": "Mapper Interface and XML", "trans": ["Mapper接口与XML"], "usage": {"syntax": "定义Mapper接口，编写对应的XML映射文件", "description": "Mapper接口声明数据库操作方法，XML文件编写SQL语句并与接口方法绑定。", "parameters": [{"name": "@Mapper", "description": "标记Mapper接口"}, {"name": "XML映射文件", "description": "定义SQL语句和结果映射"}], "returnValue": "自动实现的数据库操作方法", "examples": [{"code": "@Mapper\npublic interface UserMapper {\n    User selectById(Long id);\n}\n\n<!-- resources/mapper/UserMapper.xml -->\n<mapper namespace=\"com.example.demo.mapper.UserMapper\">\n  <select id=\"selectById\" resultType=\"com.example.demo.entity.User\">\n    SELECT * FROM user WHERE id = #{id}\n  </select>\n</mapper>", "explanation": "接口与XML配合实现SQL与Java方法的绑定。"}]}}, {"name": "Dynamic SQL", "trans": ["动态SQL"], "usage": {"syntax": "<if>、<choose>等标签实现动态SQL拼接", "description": "MyBatis支持在XML中用<if>、<choose>等标签根据参数动态生成SQL，提升灵活性。", "parameters": [{"name": "<if>", "description": "条件判断"}, {"name": "<choose>", "description": "多分支选择"}], "returnValue": "动态生成的SQL语句", "examples": [{"code": "<select id=\"findUser\" resultType=\"User\">\n  SELECT * FROM user\n  <where>\n    <if test=\"name != null\">AND name = #{name}</if>\n    <if test=\"age != null\">AND age = #{age}</if>\n  </where>\n</select>", "explanation": "<if>标签可根据参数动态拼接SQL条件。"}]}}, {"name": "Pagination Plugin", "trans": ["分页插件"], "usage": {"syntax": "集成PageHelper等分页插件，拦截SQL实现自动分页", "description": "MyBatis可集成PageHelper等插件，自动拦截SQL实现物理分页，简化分页开发。", "parameters": [{"name": "Page<PERSON><PERSON><PERSON>", "description": "常用MyBatis分页插件"}, {"name": "startPage", "description": "设置分页参数的方法"}], "returnValue": "分页后的查询结果", "examples": [{"code": "PageHelper.startPage(1, 10);\nList<User> users = userMapper.findAll();\nPageInfo<User> pageInfo = new PageInfo<>(users);", "explanation": "PageHelper可自动处理分页参数和结果。"}]}}, {"name": "Multi-table Join Query", "trans": ["多表关联查询"], "usage": {"syntax": "XML中编写多表JOIN SQL，映射结果到对象", "description": "MyBatis支持在XML中编写多表JOIN查询，并通过resultMap映射结果到Java对象。", "parameters": [{"name": "JOIN SQL", "description": "多表关联的SQL语句"}, {"name": "resultMap", "description": "自定义结果映射"}], "returnValue": "多表关联后的对象集合", "examples": [{"code": "<select id=\"findUserWithDept\" resultMap=\"userDeptMap\">\n  SELECT u.*, d.name as deptName FROM user u\n  LEFT JOIN dept d ON u.dept_id = d.id\n</select>\n<resultMap id=\"userDeptMap\" type=\"User\">\n  <result property=\"deptName\" column=\"deptName\"/>\n</resultMap>", "explanation": "resultMap可将多表结果映射到Java对象属性。"}]}}, {"name": "MyBatis Integration Assignment", "trans": ["MyBatis集成练习"], "usage": {"syntax": "# MyBatis集成练习", "description": "完成以下练习，巩固Spring Boot MyBatis集成相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 配置MyBatis依赖和参数。\n2. 定义Mapper接口和XML。\n3. 实现动态SQL和分页。\n4. 实现多表关联查询。", "explanation": "通过这些练习掌握Spring Boot MyBatis集成的核心用法。"}]}}]}