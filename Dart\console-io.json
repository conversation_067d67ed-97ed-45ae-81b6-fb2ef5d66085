{"name": "Console Input/Output", "trans": ["控制台输入输出"], "methods": [{"name": "Print Output", "trans": ["print输出"], "usage": {"syntax": "print(object);\nprint('文本内容');\nprint('$变量名');\nprint('${表达式}');\n\n// 其他输出方法\nstdout.write('内容');\nstdout.writeln('内容');\nstderr.writeln('错误信息');", "description": "print()是Dart中最常用的输出函数，用于在控制台显示信息。它可以输出任何类型的对象，会自动调用对象的toString()方法将其转换为字符串。print()函数会在输出内容后自动添加换行符。除了print()，还可以使用stdout.write()进行不换行输出，stdout.writeln()进行换行输出，stderr.writeln()输出错误信息到标准错误流。字符串插值是Dart的强大特性，使用$变量名或${表达式}可以在字符串中嵌入变量值或表达式结果。", "parameters": [{"name": "object", "description": "要输出的对象，可以是任何类型，会自动转换为字符串"}], "returnValue": "void，无返回值", "examples": [{"code": "import 'dart:io';\n\nvoid main() {\n  // 基本输出\n  print('Hello, Dart!');\n  \n  // 输出变量\n  String name = 'Alice';\n  int age = 25;\n  print('姓名: $name');\n  print('年龄: $age岁');\n  \n  // 字符串插值与表达式\n  double price = 19.99;\n  int quantity = 3;\n  print('单价: \\$${price.toStringAsFixed(2)}');\n  print('总价: \\$${(price * quantity).toStringAsFixed(2)}');\n  \n  // 输出不同数据类型\n  List<String> fruits = ['苹果', '香蕉', '橙子'];\n  Map<String, int> scores = {'数学': 95, '英语': 88, '物理': 92};\n  print('水果列表: $fruits');\n  print('成绩单: $scores');\n  \n  // 多行字符串输出\n  String multiLine = '''\n这是一个多行字符串\n可以包含多行内容\n非常适合输出格式化信息\n''';\n  print(multiLine);\n  \n  // 使用stdout进行精确控制\n  stdout.write('请输入您的姓名: '); // 不换行\n  \n  // 输出到标准错误流\n  stderr.writeln('这是一条错误信息');\n  \n  // 格式化输出\n  String formatted = '用户: $name, 年龄: $age, 状态: ${age >= 18 ? \"成年\" : \"未成年\"}';\n  print(formatted);\n  \n  // 输出特殊字符\n  print('制表符:\\t内容');\n  print('换行符:\\n内容');\n  print('反斜杠: \\\\\\\\');\n  print('引号: \\\"双引号\\\" 和 \\'单引号\\'');\n  \n  // 输出Unicode字符\n  print('心形: \\u2665');\n  print('笑脸: \\u263A');\n  \n  // 条件输出\n  bool isDebug = true;\n  if (isDebug) {\n    print('[DEBUG] 调试信息: 程序运行正常');\n  }\n  \n  // 循环输出\n  print('\\n倒计时:');\n  for (int i = 5; i >= 1; i--) {\n    stdout.write('$i... ');\n  }\n  print('\\n发射!');\n}", "explanation": "这个示例展示了Dart中各种输出方式的使用。包括基本的print输出、变量和表达式的字符串插值、不同数据类型的输出、多行字符串、使用stdout进行精确控制、输出到错误流、格式化输出、特殊字符和Unicode字符的输出，以及在条件和循环中的输出应用。"}]}}, {"name": "Standard Input", "trans": ["stdin输入"], "usage": {"syntax": "import 'dart:io';\n\n// 读取一行输入\nString? input = stdin.readLineSync();\n\n// 读取一行输入并指定编码\nString? input = stdin.readLineSync(encoding: utf8);\n\n// 异步读取输入\nStream<String> lines = stdin.transform(utf8.decoder).transform(LineSplitter());\n\n// 读取单个字符（需要特殊处理）\nstdin.echoMode = false;\nstdin.lineMode = false;\nint charCode = stdin.readByteSync();\nstdin.echoMode = true;\nstdin.lineMode = true;", "description": "stdin（标准输入）用于从控制台读取用户输入。readLineSync()是最常用的同步输入方法，它会阻塞程序执行直到用户输入一行文本并按回车键。该方法返回String?类型，如果输入流结束则返回null。可以指定编码格式，默认使用系统编码。对于异步输入处理，可以使用stdin的流特性。读取单个字符需要设置echoMode和lineMode为false。输入的数据通常需要进行验证和类型转换，因为用户可能输入无效数据。", "parameters": [{"name": "encoding", "description": "可选参数，指定输入的字符编码，默认为系统编码"}], "returnValue": "String? - 用户输入的字符串，如果输入流结束则返回null", "examples": [{"code": "import 'dart:io';\nimport 'dart:convert';\n\nvoid main() {\n  // 基本输入示例\n  print('请输入您的姓名:');\n  String? name = stdin.readLineSync();\n  \n  if (name != null && name.isNotEmpty) {\n    print('您好, $name!');\n  } else {\n    print('您没有输入姓名。');\n  }\n  \n  // 数字输入与验证\n  print('\\n请输入您的年龄:');\n  String? ageInput = stdin.readLineSync();\n  \n  try {\n    int age = int.parse(ageInput ?? '0');\n    if (age > 0 && age < 150) {\n      print('您今年$age岁，${age >= 18 ? \"已成年\" : \"未成年\"}');\n    } else {\n      print('请输入有效的年龄（1-149）');\n    }\n  } catch (e) {\n    print('年龄格式不正确，请输入数字');\n  }\n  \n  // 浮点数输入\n  print('\\n请输入您的身高（米）:');\n  String? heightInput = stdin.readLineSync();\n  \n  try {\n    double height = double.parse(heightInput ?? '0');\n    if (height > 0.5 && height < 3.0) {\n      print('您的身高是${height.toStringAsFixed(2)}米');\n    } else {\n      print('请输入有效的身高');\n    }\n  } catch (e) {\n    print('身高格式不正确');\n  }\n  \n  // 选择菜单输入\n  print('\\n请选择您喜欢的编程语言:');\n  print('1. Dart');\n  print('2. Python');\n  print('3. JavaScript');\n  print('4. Java');\n  print('请输入选项编号:');\n  \n  String? choice = stdin.readLineSync();\n  \n  switch (choice) {\n    case '1':\n      print('很好的选择！Dart是一门现代化的语言。');\n      break;\n    case '2':\n      print('Python简洁易学，很受欢迎！');\n      break;\n    case '3':\n      print('JavaScript是Web开发的核心语言！');\n      break;\n    case '4':\n      print('Java是企业级开发的首选！');\n      break;\n    default:\n      print('无效的选择，请输入1-4之间的数字。');\n  }\n  \n  // 确认输入（Y/N）\n  print('\\n是否继续？(y/n):');\n  String? confirm = stdin.readLineSync()?.toLowerCase();\n  \n  if (confirm == 'y' || confirm == 'yes') {\n    print('继续执行...');\n  } else {\n    print('程序结束。');\n    return;\n  }\n  \n  // 密码输入（隐藏显示）\n  print('\\n请输入密码（输入时不显示）:');\n  stdin.echoMode = false; // 关闭回显\n  String? password = stdin.readLineSync();\n  stdin.echoMode = true;  // 恢复回显\n  \n  if (password != null && password.length >= 6) {\n    print('密码设置成功！');\n  } else {\n    print('密码长度至少6位！');\n  }\n  \n  // 多行输入\n  print('\\n请输入您的自我介绍（输入空行结束）:');\n  List<String> introduction = [];\n  \n  while (true) {\n    String? line = stdin.readLineSync();\n    if (line == null || line.isEmpty) {\n      break;\n    }\n    introduction.add(line);\n  }\n  \n  if (introduction.isNotEmpty) {\n    print('\\n您的自我介绍:');\n    for (int i = 0; i < introduction.length; i++) {\n      print('${i + 1}. ${introduction[i]}');\n    }\n  }\n}", "explanation": "这个示例展示了Dart中各种输入处理方式。包括基本的字符串输入、数字输入与验证、浮点数输入、选择菜单处理、确认输入、密码输入（隐藏显示）以及多行输入处理。示例还展示了输入验证、异常处理和不同数据类型的转换方法。"}]}}, {"name": "Formatted Output", "trans": ["格式化输出"], "usage": {"syntax": "// 使用字符串插值格式化\nprint('${变量.toStringAsFixed(2)}');\nprint('${变量.toString().padLeft(10)}');\n\n// 使用sprintf风格格式化（需要sprintf包）\n// sprintf('%d %s', [数字, 字符串]);\n\n// 手动格式化\nString.fromCharCodes([字符码]);\n'字符串'.padLeft(宽度, '填充字符');\n'字符串'.padRight(宽度, '填充字符');", "description": "Dart提供了多种格式化输出的方法。最常用的是字符串插值结合各种toString方法，如toStringAsFixed()用于控制小数位数，padLeft()和padRight()用于对齐。虽然Dart没有内置printf风格的格式化函数，但可以通过第三方包如sprintf实现。对于复杂的格式化需求，可以使用NumberFormat、DateFormat等专门的格式化类。表格输出可以通过计算列宽和使用填充字符实现对齐效果。", "parameters": [], "returnValue": "void，无返回值", "examples": [{"code": "import 'dart:io';\nimport 'dart:math';\n\nvoid main() {\n  // 数字格式化\n  double pi = 3.14159265359;\n  print('π的值:');\n  print('  默认: $pi');\n  print('  2位小数: ${pi.toStringAsFixed(2)}');\n  print('  4位小数: ${pi.toStringAsFixed(4)}');\n  print('  科学计数法: ${pi.toStringAsExponential(2)}');\n  \n  // 整数格式化\n  int number = 42;\n  print('\\n整数格式化:');\n  print('  十进制: $number');\n  print('  十六进制: 0x${number.toRadixString(16).toUpperCase()}');\n  print('  二进制: 0b${number.toRadixString(2)}');\n  print('  八进制: 0o${number.toRadixString(8)}');\n  \n  // 字符串对齐和填充\n  List<String> names = ['Alice', 'Bob', 'Charlie', '<PERSON>'];\n  List<int> scores = [95, 87, 92, 78];\n  \n  print('\\n成绩表（左对齐）:');\n  print('姓名'.padRight(10) + '分数'.padLeft(6));\n  print('-' * 16);\n  \n  for (int i = 0; i < names.length; i++) {\n    String name = names[i].padRight(10);\n    String score = scores[i].toString().padLeft(6);\n    print('$name$score');\n  }\n  \n  // 表格输出（右对齐数字）\n  print('\\n销售报表:');\n  List<Map<String, dynamic>> sales = [\n    {'product': '笔记本电脑', 'quantity': 15, 'price': 5999.99},\n    {'product': '鼠标', 'quantity': 120, 'price': 29.99},\n    {'product': '键盘', 'quantity': 85, 'price': 199.50},\n    {'product': '显示器', 'quantity': 32, 'price': 1299.00},\n  ];\n  \n  // 表头\n  String header = '产品'.padRight(12) + \n                  '数量'.padLeft(8) + \n                  '单价'.padLeft(12) + \n                  '总价'.padLeft(12);\n  print(header);\n  print('=' * 44);\n  \n  double totalAmount = 0;\n  \n  for (var item in sales) {\n    String product = item['product'].toString().padRight(12);\n    String quantity = item['quantity'].toString().padLeft(8);\n    String price = '¥${item['price'].toStringAsFixed(2)}'.padLeft(12);\n    double amount = item['quantity'] * item['price'];\n    String total = '¥${amount.toStringAsFixed(2)}'.padLeft(12);\n    \n    print('$product$quantity$price$total');\n    totalAmount += amount;\n  }\n  \n  print('-' * 44);\n  String totalLine = '总计'.padRight(32) + \n                     '¥${totalAmount.toStringAsFixed(2)}'.padLeft(12);\n  print(totalLine);\n  \n  // 进度条显示\n  print('\\n下载进度:');\n  for (int progress = 0; progress <= 100; progress += 10) {\n    int barLength = 30;\n    int filled = (progress * barLength / 100).round();\n    String bar = '█' * filled + '░' * (barLength - filled);\n    String percentage = '${progress.toString().padLeft(3)}%';\n    stdout.write('\\r[$bar] $percentage');\n    \n    // 模拟下载延迟\n    sleep(Duration(milliseconds: 200));\n  }\n  print('\\n下载完成!');\n  \n  // 货币格式化\n  List<double> amounts = [1234.56, 9876543.21, 0.99, 1000000];\n  print('\\n货币格式化:');\n  \n  for (double amount in amounts) {\n    // 简单的千位分隔符\n    String formatted = formatCurrency(amount);\n    print('¥$formatted');\n  }\n  \n  // 时间格式化\n  DateTime now = DateTime.now();\n  print('\\n时间格式化:');\n  print('完整时间: $now');\n  print('日期: ${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}');\n  print('时间: ${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}');\n  \n  // 百分比格式化\n  List<double> percentages = [0.1234, 0.5678, 0.9999, 1.0];\n  print('\\n百分比格式化:');\n  \n  for (double pct in percentages) {\n    String formatted = '${(pct * 100).toStringAsFixed(2)}%';\n    print('$pct => $formatted');\n  }\n}\n\n// 货币格式化辅助函数\nString formatCurrency(double amount) {\n  String str = amount.toStringAsFixed(2);\n  List<String> parts = str.split('.');\n  String integerPart = parts[0];\n  String decimalPart = parts[1];\n  \n  // 添加千位分隔符\n  String formatted = '';\n  for (int i = 0; i < integerPart.length; i++) {\n    if (i > 0 && (integerPart.length - i) % 3 == 0) {\n      formatted += ',';\n    }\n    formatted += integerPart[i];\n  }\n  \n  return '$formatted.$decimalPart';\n}", "explanation": "这个示例展示了Dart中各种格式化输出技术。包括数字的小数位控制、进制转换、字符串的对齐和填充、表格输出、进度条显示、货币格式化、时间格式化和百分比格式化。通过这些技术可以创建美观、易读的控制台输出。"}]}}, {"name": "Interactive Console Applications", "trans": ["交互式控制台应用"], "usage": {"syntax": "// 基本交互循环\nwhile (true) {\n  stdout.write('> ');\n  String? input = stdin.readLineSync();\n  if (input == 'exit') break;\n  // 处理输入\n}\n\n// 菜单驱动程序\nvoid showMenu() {\n  print('1. 选项1');\n  print('2. 选项2');\n  print('0. 退出');\n}\n\n// 输入验证循环\nwhile (true) {\n  String? input = stdin.readLineSync();\n  if (isValid(input)) break;\n  print('输入无效，请重试:');\n}", "description": "交互式控制台应用是学习编程的重要实践方式。这类应用通常包含菜单系统、用户输入处理、数据验证和循环交互。设计良好的交互式应用应该有清晰的菜单、友好的提示信息、完善的错误处理和优雅的退出机制。常见的模式包括主循环、菜单驱动、命令解析和状态管理。", "parameters": [], "returnValue": "void，无返回值", "examples": [{"code": "import 'dart:io';\nimport 'dart:math';\n\nvoid main() {\n  print('=== 简易计算器 ===');\n  print('支持基本运算：+、-、*、/');\n  print('输入 \"help\" 查看帮助，输入 \"exit\" 退出');\n  \n  while (true) {\n    stdout.write('\\n计算器> ');\n    String? input = stdin.readLineSync();\n    \n    if (input == null || input.trim().isEmpty) {\n      continue;\n    }\n    \n    input = input.trim().toLowerCase();\n    \n    if (input == 'exit' || input == 'quit') {\n      print('再见！');\n      break;\n    }\n    \n    if (input == 'help') {\n      showHelp();\n      continue;\n    }\n    \n    if (input == 'clear') {\n      // 清屏（在支持的终端中）\n      print('\\x1B[2J\\x1B[0;0H');\n      continue;\n    }\n    \n    // 解析和计算表达式\n    try {\n      double result = evaluateExpression(input);\n      print('结果: $result');\n    } catch (e) {\n      print('错误: $e');\n      print('输入 \"help\" 查看使用说明');\n    }\n  }\n}\n\nvoid showHelp() {\n  print('\\n=== 使用说明 ===');\n  print('支持的运算:');\n  print('  加法: 5 + 3');\n  print('  减法: 10 - 4');\n  print('  乘法: 6 * 7');\n  print('  除法: 15 / 3');\n  print('\\n特殊命令:');\n  print('  help  - 显示此帮助');\n  print('  clear - 清屏');\n  print('  exit  - 退出程序');\n  print('\\n示例: 2.5 * 4');\n}\n\ndouble evaluateExpression(String expression) {\n  // 移除空格\n  expression = expression.replaceAll(' ', '');\n  \n  // 查找运算符\n  String operator = '';\n  int operatorIndex = -1;\n  \n  for (int i = 1; i < expression.length; i++) {\n    String char = expression[i];\n    if (['+', '-', '*', '/'].contains(char)) {\n      operator = char;\n      operatorIndex = i;\n      break;\n    }\n  }\n  \n  if (operatorIndex == -1) {\n    throw Exception('未找到有效的运算符');\n  }\n  \n  // 提取操作数\n  String leftStr = expression.substring(0, operatorIndex);\n  String rightStr = expression.substring(operatorIndex + 1);\n  \n  if (leftStr.isEmpty || rightStr.isEmpty) {\n    throw Exception('操作数不能为空');\n  }\n  \n  double left = double.parse(leftStr);\n  double right = double.parse(rightStr);\n  \n  // 执行运算\n  switch (operator) {\n    case '+':\n      return left + right;\n    case '-':\n      return left - right;\n    case '*':\n      return left * right;\n    case '/':\n      if (right == 0) {\n        throw Exception('除数不能为零');\n      }\n      return left / right;\n    default:\n      throw Exception('不支持的运算符: $operator');\n  }\n}", "explanation": "这个示例实现了一个交互式计算器应用，展示了控制台应用的典型结构：主循环、命令解析、错误处理和帮助系统。用户可以输入数学表达式进行计算，也可以使用特殊命令如help、clear和exit。"}]}}, {"name": "Assignment", "trans": ["实践作业"], "usage": {"syntax": "// 控制台输入输出实践", "description": "完成以下任务，练习Dart的控制台输入输出：1) 创建一个个人信息收集程序，要求用户输入姓名、年龄、邮箱等信息，并进行格式验证；2) 实现一个简单的菜单系统，包含至少3个功能选项；3) 创建一个数字猜测游戏，程序随机生成1-100的数字，用户猜测并给出提示；4) 实现一个简单的文本编辑器，支持添加、查看、删除文本行；5) 挑战：创建一个学生成绩管理系统，支持添加学生、录入成绩、查看统计信息等功能。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 参考实现：学生成绩管理系统\n\nimport 'dart:io';\nimport 'dart:math';\n\nclass Student {\n  String name;\n  int age;\n  Map<String, double> scores;\n  \n  Student(this.name, this.age) : scores = {};\n  \n  void addScore(String subject, double score) {\n    scores[subject] = score;\n  }\n  \n  double getAverage() {\n    if (scores.isEmpty) return 0.0;\n    double total = scores.values.reduce((a, b) => a + b);\n    return total / scores.length;\n  }\n  \n  @override\n  String toString() {\n    return '姓名: $name, 年龄: $age, 平均分: ${getAverage().toStringAsFixed(2)}';\n  }\n}\n\nclass StudentManager {\n  List<Student> students = [];\n  \n  void addStudent() {\n    print('\\n=== 添加学生 ===');\n    \n    stdout.write('请输入学生姓名: ');\n    String? name = stdin.readLineSync();\n    if (name == null || name.trim().isEmpty) {\n      print('姓名不能为空！');\n      return;\n    }\n    \n    stdout.write('请输入学生年龄: ');\n    String? ageStr = stdin.readLineSync();\n    \n    try {\n      int age = int.parse(ageStr ?? '0');\n      if (age <= 0 || age > 100) {\n        print('请输入有效的年龄（1-100）！');\n        return;\n      }\n      \n      students.add(Student(name.trim(), age));\n      print('学生 $name 添加成功！');\n    } catch (e) {\n      print('年龄格式不正确！');\n    }\n  }\n  \n  void addScore() {\n    if (students.isEmpty) {\n      print('\\n还没有学生记录，请先添加学生！');\n      return;\n    }\n    \n    print('\\n=== 录入成绩 ===');\n    listStudents();\n    \n    stdout.write('请选择学生编号: ');\n    String? indexStr = stdin.readLineSync();\n    \n    try {\n      int index = int.parse(indexStr ?? '0') - 1;\n      if (index < 0 || index >= students.length) {\n        print('无效的学生编号！');\n        return;\n      }\n      \n      Student student = students[index];\n      \n      stdout.write('请输入科目名称: ');\n      String? subject = stdin.readLineSync();\n      if (subject == null || subject.trim().isEmpty) {\n        print('科目名称不能为空！');\n        return;\n      }\n      \n      stdout.write('请输入成绩（0-100）: ');\n      String? scoreStr = stdin.readLineSync();\n      \n      double score = double.parse(scoreStr ?? '0');\n      if (score < 0 || score > 100) {\n        print('成绩必须在0-100之间！');\n        return;\n      }\n      \n      student.addScore(subject.trim(), score);\n      print('成绩录入成功！');\n    } catch (e) {\n      print('输入格式不正确！');\n    }\n  }\n  \n  void listStudents() {\n    if (students.isEmpty) {\n      print('\\n暂无学生记录。');\n      return;\n    }\n    \n    print('\\n=== 学生列表 ===');\n    for (int i = 0; i < students.length; i++) {\n      print('${i + 1}. ${students[i]}');\n    }\n  }\n  \n  void showStatistics() {\n    if (students.isEmpty) {\n      print('\\n暂无学生记录。');\n      return;\n    }\n    \n    print('\\n=== 统计信息 ===');\n    print('学生总数: ${students.length}');\n    \n    // 计算全班平均分\n    List<double> allAverages = students\n        .map((s) => s.getAverage())\n        .where((avg) => avg > 0)\n        .toList();\n    \n    if (allAverages.isNotEmpty) {\n      double classAverage = allAverages.reduce((a, b) => a + b) / allAverages.length;\n      print('全班平均分: ${classAverage.toStringAsFixed(2)}');\n      \n      double highest = allAverages.reduce((a, b) => a > b ? a : b);\n      double lowest = allAverages.reduce((a, b) => a < b ? a : b);\n      \n      print('最高平均分: ${highest.toStringAsFixed(2)}');\n      print('最低平均分: ${lowest.toStringAsFixed(2)}');\n    }\n    \n    // 按平均分排序显示\n    List<Student> sortedStudents = List.from(students);\n    sortedStudents.sort((a, b) => b.getAverage().compareTo(a.getAverage()));\n    \n    print('\\n排名（按平均分）:');\n    for (int i = 0; i < sortedStudents.length; i++) {\n      Student s = sortedStudents[i];\n      if (s.getAverage() > 0) {\n        print('${i + 1}. ${s.name} - ${s.getAverage().toStringAsFixed(2)}分');\n      }\n    }\n  }\n}\n\nvoid main() {\n  StudentManager manager = StudentManager();\n  \n  print('=== 学生成绩管理系统 ===');\n  \n  while (true) {\n    print('\\n请选择操作:');\n    print('1. 添加学生');\n    print('2. 录入成绩');\n    print('3. 查看学生列表');\n    print('4. 统计信息');\n    print('0. 退出系统');\n    \n    stdout.write('\\n请输入选项: ');\n    String? choice = stdin.readLineSync();\n    \n    switch (choice) {\n      case '1':\n        manager.addStudent();\n        break;\n      case '2':\n        manager.addScore();\n        break;\n      case '3':\n        manager.listStudents();\n        break;\n      case '4':\n        manager.showStatistics();\n        break;\n      case '0':\n        print('\\n感谢使用学生成绩管理系统！');\n        return;\n      default:\n        print('\\n无效的选项，请重新选择！');\n    }\n  }\n}", "explanation": "这个参考实现展示了一个完整的学生成绩管理系统，包含了控制台输入输出的各种技术：菜单系统、数据验证、格式化输出、错误处理等。系统支持添加学生、录入成绩、查看列表和统计分析等功能，是一个很好的综合练习项目。"}]}}]}