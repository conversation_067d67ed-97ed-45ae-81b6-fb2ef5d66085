{"name": "Locks", "trans": ["锁机制"], "methods": [{"name": "Row-level and Table-level Locks", "trans": ["行级锁与表级锁"], "usage": {"syntax": "-- 行级锁（InnoDB引擎）\nSELECT ... FOR UPDATE;\nSELECT ... LOCK IN SHARE MODE;\n\n-- 表级锁\nLOCK TABLES 表名 READ|WRITE;\nUNLOCK TABLES;", "description": "MySQL支持多种锁定级别，主要有表级锁和行级锁。InnoDB存储引擎支持行级锁，可以实现更高的并发性；MyISAM存储引擎只支持表级锁，并发性较低但开销小。", "parameters": [{"name": "表名", "description": "要锁定的表名"}, {"name": "READ|WRITE", "description": "锁定模式，READ为共享锁，WRITE为排他锁"}], "returnValue": "无返回值", "examples": [{"code": "-- 行级锁示例（InnoDB引擎）\n\n-- 排他行锁：锁定满足条件的行进行更新\nSTART TRANSACTION;\nSELECT * FROM accounts WHERE account_id = 1001 FOR UPDATE;\n-- 此时其他事务无法修改account_id为1001的行，直到本事务提交或回滚\nUPDATE accounts SET balance = balance - 1000 WHERE account_id = 1001;\nCOMMIT;\n\n-- 共享行锁：锁定行但允许其他事务读取\nSTART TRANSACTION;\nSELECT * FROM accounts WHERE account_id = 1002 LOCK IN SHARE MODE;\n-- 此时其他事务可以读取但不能修改account_id为1002的行\nCOMMIT;\n\n-- 表级锁示例（适用于所有存储引擎）\n\n-- 读锁定（共享锁）\nLOCK TABLES accounts READ;\n-- 此时只能读取accounts表，不能写入\nSELECT * FROM accounts;\n-- 尝试更新会报错\n-- UPDATE accounts SET balance = balance + 100 WHERE account_id = 1001; -- 会报错\nUNLOCK TABLES;\n\n-- 写锁定（排他锁）\nLOCK TABLES accounts WRITE;\n-- 此时可以读取和写入accounts表\nSELECT * FROM accounts;\nUPDATE accounts SET balance = balance + 100 WHERE account_id = 1001;\n-- 其他会话无法读取或写入accounts表\nUNLOCK TABLES;", "explanation": "这个例子展示了MySQL中的行级锁和表级锁。行级锁通过FOR UPDATE或LOCK IN SHARE MODE在事务中实现，只锁定满足条件的行；表级锁通过LOCK TABLES和UNLOCK TABLES语句实现，锁定整个表。行级锁提供更高的并发性，而表级锁实现简单但并发性较低。"}]}}, {"name": "Shared and Exclusive Locks", "trans": ["共享锁与排他锁"], "usage": {"syntax": "-- 共享锁（读锁）\nSELECT ... LOCK IN SHARE MODE;\n-- 或\nSELECT ... FOR SHARE; -- MySQL 8.0+\n\n-- 排他锁（写锁）\nSELECT ... FOR UPDATE;", "description": "MySQL支持两种基本的锁类型：共享锁（读锁）和排他锁（写锁）。共享锁允许多个事务同时读取数据但阻止写入；排他锁阻止其他事务读取或写入被锁定的数据。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "-- 共享锁（读锁）示例\nSTART TRANSACTION;\n-- 对id=100的行加共享锁\nSELECT * FROM products WHERE id = 100 LOCK IN SHARE MODE;\n-- 或使用MySQL 8.0+的新语法\n-- SELECT * FROM products WHERE id = 100 FOR SHARE;\n\n-- 此时其他事务可以读取id=100的行\n-- 其他事务也可以对该行加共享锁\n-- 但其他事务不能对该行加排他锁或修改该行，直到本事务提交或回滚\nCOMMIT;\n\n-- 排他锁（写锁）示例\nSTART TRANSACTION;\n-- 对id=200的行加排他锁\nSELECT * FROM products WHERE id = 200 FOR UPDATE;\n\n-- 此时其他事务不能读取（使用共享锁或排他锁）或修改id=200的行\n-- 可以执行修改操作\nUPDATE products SET stock = stock - 1 WHERE id = 200;\nCOMMIT;\n\n-- 锁定多行示例\nSTART TRANSACTION;\n-- 锁定category='electronics'的所有行\nSELECT * FROM products WHERE category = 'electronics' FOR UPDATE;\n\n-- 此时可以安全地更新这些行，不用担心其他事务同时修改\nUPDATE products SET price = price * 1.1 WHERE category = 'electronics';\nCOMMIT;", "explanation": "这个例子展示了共享锁和排他锁的使用。共享锁(LOCK IN SHARE MODE/FOR SHARE)允许其他事务读取但不能修改被锁定的行；排他锁(FOR UPDATE)阻止其他事务读取或修改被锁定的行。正确使用这些锁可以在保证数据一致性的同时提高并发性能。"}]}}, {"name": "Deadlocks and Resolution", "trans": ["死锁与解决"], "usage": {"syntax": "-- 查看最后一次死锁信息\nSHOW ENGINE INNODB STATUS;", "description": "死锁是指两个或多个事务互相持有对方需要的锁，导致所有事务都无法继续执行的情况。MySQL的InnoDB引擎能够自动检测死锁并回滚其中一个事务，但了解死锁的原因和预防方法仍然很重要。", "parameters": [], "returnValue": "返回InnoDB引擎状态信息，包括死锁信息", "examples": [{"code": "-- 死锁示例\n-- 会话1\nSTART TRANSACTION;\nUPDATE accounts SET balance = balance - 100 WHERE account_id = 1001;\n-- 此时会话1持有account_id=1001的行锁\n\n-- 会话2\nSTART TRANSACTION;\nUPDATE accounts SET balance = balance - 200 WHERE account_id = 1002;\n-- 此时会话2持有account_id=1002的行锁\n\n-- 会话1继续\nUPDATE accounts SET balance = balance + 100 WHERE account_id = 1002;\n-- 会话1等待会话2释放account_id=1002的行锁\n\n-- 会话2继续\nUPDATE accounts SET balance = balance + 200 WHERE account_id = 1001;\n-- 会话2等待会话1释放account_id=1001的行锁\n-- 此时形成死锁，MySQL会检测到并回滚其中一个事务\n\n-- 查看死锁信息\nSHOW ENGINE INNODB STATUS;\n\n-- 预防死锁的方法\n\n-- 1. 按固定顺序访问表和行\n-- 正确的做法：总是先操作account_id小的账户\nSTART TRANSACTION;\n-- 假设要在账户1001和1002之间转账\nIF 1001 < 1002 THEN\n  UPDATE accounts SET balance = balance - 100 WHERE account_id = 1001;\n  UPDATE accounts SET balance = balance + 100 WHERE account_id = 1002;\nELSE\n  UPDATE accounts SET balance = balance - 100 WHERE account_id = 1002;\n  UPDATE accounts SET balance = balance + 100 WHERE account_id = 1001;\nEND IF;\nCOMMIT;\n\n-- 2. 减小事务范围和持续时间\n-- 不好的做法：事务太长\nSTART TRANSACTION;\nSELECT * FROM large_table WHERE some_column = 'some_value';\n-- 执行一些耗时的业务逻辑\n-- ...\nUPDATE accounts SET balance = balance - 100 WHERE account_id = 1001;\nCOMMIT;\n\n-- 好的做法：缩短事务持续时间\n-- 先执行查询和业务逻辑\nSELECT * FROM large_table WHERE some_column = 'some_value';\n-- 执行业务逻辑\n-- ...\n-- 只在需要更新时才开启事务\nSTART TRANSACTION;\nUPDATE accounts SET balance = balance - 100 WHERE account_id = 1001;\nCOMMIT;\n\n-- 3. 使用锁超时和重试机制\nSET innodb_lock_wait_timeout = 50; -- 设置锁等待超时时间（秒）\n\n-- 实现重试逻辑\nDELIMITER //\nCREATE PROCEDURE transfer_with_retry(IN from_id INT, IN to_id INT, IN amount DECIMAL(10,2), IN max_retries INT)\nBEGIN\n  DECLARE retry_count INT DEFAULT 0;\n  DECLARE success BOOLEAN DEFAULT FALSE;\n  \n  retry_loop: WHILE retry_count < max_retries AND NOT success DO\n    BEGIN\n      -- 声明异常处理\n      DECLARE EXIT HANDLER FOR SQLEXCEPTION\n      BEGIN\n        -- 回滚事务\n        ROLLBACK;\n        SET retry_count = retry_count + 1;\n        -- 等待一小段时间再重试\n        DO SLEEP(1);\n      END;\n      \n      START TRANSACTION;\n      UPDATE accounts SET balance = balance - amount WHERE account_id = from_id;\n      UPDATE accounts SET balance = balance + amount WHERE account_id = to_id;\n      COMMIT;\n      \n      SET success = TRUE;\n    END;\n  END WHILE;\n  \n  IF NOT success THEN\n    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '转账失败，达到最大重试次数';\n  END IF;\nEND //\nDELIMITER ;", "explanation": "这个例子展示了死锁的形成过程和三种预防死锁的方法：按固定顺序访问资源、减小事务范围和持续时间、使用锁超时和重试机制。当死锁发生时，MySQL会自动检测并回滚其中一个事务，可以通过SHOW ENGINE INNODB STATUS查看详细信息。"}]}}, {"name": "Lock Types and Monitoring", "trans": ["锁类型与监控"], "usage": {"syntax": "-- 查看当前锁等待情况\nSELECT * FROM performance_schema.data_lock_waits;\nSELECT * FROM performance_schema.data_locks;\n\n-- 查看锁等待超时设置\nSHOW VARIABLES LIKE 'innodb_lock_wait_timeout';", "description": "MySQL除了基本的共享锁和排他锁外，还有意向锁、自增锁等多种锁类型。了解这些锁类型和如何监控锁状态对于优化并发性能和排查锁相关问题非常重要。", "parameters": [], "returnValue": "返回锁相关的信息", "examples": [{"code": "-- 启用performance_schema监控锁\nUPDATE performance_schema.setup_instruments\nSET ENABLED = 'YES', TIMED = 'YES'\nWHERE NAME LIKE 'wait/lock/metadata/%';\n\n-- 查看当前的锁\nSELECT * FROM performance_schema.data_locks;\n\n-- 查看当前的锁等待\nSELECT * FROM performance_schema.data_lock_waits;\n\n-- 查看锁等待超时设置\nSHOW VARIABLES LIKE 'innodb_lock_wait_timeout';\n\n-- 设置锁等待超时（单位：秒）\nSET GLOBAL innodb_lock_wait_timeout = 50;\nSET SESSION innodb_lock_wait_timeout = 30;\n\n-- 查看事务隔离级别（影响锁行为）\nSELECT @@transaction_isolation;\n\n-- 不同类型的锁示例\n\n-- 1. 意向锁：当事务需要获取表级锁时，会先获取意向锁\n-- 意向锁不需要显式设置，InnoDB会自动管理\n\n-- 2. 记录锁：锁定索引记录\nSTART TRANSACTION;\nSELECT * FROM accounts WHERE account_id = 1001 FOR UPDATE;\n-- 此时会在account_id=1001的行上加记录锁\nCOMMIT;\n\n-- 3. 间隙锁：锁定索引记录之间的间隙\n-- 在REPEATABLE READ隔离级别下，防止幻读\nSTART TRANSACTION;\nSELECT * FROM accounts WHERE account_id BETWEEN 1000 AND 2000 FOR UPDATE;\n-- 此时会锁定account_id在1000到2000之间的所有行，以及可能的间隙\nCOMMIT;\n\n-- 4. Next-Key锁：记录锁和间隙锁的组合\n-- 在REPEATABLE READ隔离级别下自动使用\n\n-- 5. 自增锁：用于自增列\n-- 不需要显式设置，插入操作会自动获取\nINSERT INTO accounts (account_name, balance) VALUES ('新用户', 1000);\n-- 如果account_id是自增主键，会自动获取自增锁", "explanation": "这个例子展示了如何监控MySQL中的锁状态和各种锁类型。通过performance_schema可以查看当前的锁和锁等待情况；通过设置innodb_lock_wait_timeout可以控制锁等待超时时间；了解不同类型的锁（记录锁、间隙锁、Next-Key锁等）有助于优化并发性能和避免锁相关问题。"}]}}, {"name": "Locks Assignment", "trans": ["锁机制练习"], "usage": {"syntax": "# 锁机制练习", "description": "完成以下练习，巩固MySQL锁机制的基本概念和操作。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n假设有一个简单的库存管理系统，包含以下表结构：\n\n```sql\n-- 产品表\nCREATE TABLE products (\n  product_id INT PRIMARY KEY,\n  product_name VARCHAR(100),\n  category VARCHAR(50),\n  price DECIMAL(10,2),\n  stock INT,\n  last_updated DATETIME\n);\n\n-- 订单表\nCREATE TABLE orders (\n  order_id INT PRIMARY KEY AUTO_INCREMENT,\n  customer_id INT,\n  order_date DATETIME,\n  status VARCHAR(20)\n);\n\n-- 订单项目表\nCREATE TABLE order_items (\n  item_id INT PRIMARY KEY AUTO_INCREMENT,\n  order_id INT,\n  product_id INT,\n  quantity INT,\n  price DECIMAL(10,2),\n  FOREIGN KEY (order_id) REFERENCES orders(order_id),\n  FOREIGN KEY (product_id) REFERENCES products(product_id)\n);\n```\n\n请完成以下任务：\n\n1. 插入测试数据：\n```sql\nINSERT INTO products (product_id, product_name, category, price, stock, last_updated)\nVALUES \n(101, '笔记本电脑', '电子产品', 5999.00, 10, NOW()),\n(102, '智能手机', '电子产品', 2999.00, 20, NOW()),\n(103, '办公椅', '家具', 599.00, 15, NOW());\n```\n\n2. 编写一个使用行级锁的事务，实现以下功能：\n   - 检查产品ID为101的库存\n   - 如果库存大于等于2，则减少2个库存\n   - 创建一个新订单和订单项目，记录这次购买\n   - 确保整个过程中，其他事务无法修改该产品的库存\n\n3. 使用共享锁和排他锁，实现以下场景：\n   - 事务A开始，对所有电子产品类别的产品加共享锁\n   - 事务B开始，尝试更新产品ID为101的价格\n   - 解释事务B是否会被阻塞，以及原因\n\n4. 设计一个可能导致死锁的场景，并提出至少两种解决方案。\n\n5. 使用性能模式(Performance Schema)查询当前系统中的锁情况，并解释结果。", "explanation": "通过这个练习，你将学习如何在实际应用中使用MySQL的行级锁和表级锁、共享锁和排他锁，以及如何识别和解决死锁问题。这些技能对于开发高并发的数据库应用至关重要。"}]}}]}