{"name": "Setup Function", "trans": ["setup函数"], "methods": [{"name": "Setup Parameters", "trans": ["setup参数"], "usage": {"syntax": "setup(props, context)", "description": "setup函数接收两个参数：props（父组件传递的属性，响应式只读），context（包含attrs、slots、emit等）。", "parameters": [{"name": "props", "description": "父组件传递的属性对象"}, {"name": "context", "description": "上下文对象，含attrs、slots、emit"}], "returnValue": "无返回值，参数用于setup内部逻辑", "examples": [{"code": "import { defineComponent } from 'vue'\nexport default defineComponent({\n  props: ['msg'],\n  setup(props, context) {\n    console.log(props.msg)\n    console.log(context.attrs)\n  }\n})", "explanation": "setup通过props和context获取父组件传递的数据和上下文。"}]}}, {"name": "Setup Return Value", "trans": ["返回值"], "usage": {"syntax": "setup() { return { a, b, fn } }", "description": "setup返回的对象会暴露给模板使用，包括响应式数据、方法、计算属性等。", "parameters": [{"name": "返回对象", "description": "包含要暴露给模板的变量和方法"}], "returnValue": "返回对象的属性可在模板中直接使用", "examples": [{"code": "import { ref } from 'vue'\nexport default {\n  setup() {\n    const count = ref(0)\n    const inc = () => count.value++\n    return { count, inc }\n  }\n}\n// 模板中\n<button @click=\"inc\">{{ count }}</button>", "explanation": "setup返回的count和inc可直接在模板中使用。"}]}}, {"name": "Lifecycle Hooks in Setup", "trans": ["生命周期钩子"], "usage": {"syntax": "import { onMounted, onUpdated } from 'vue'\nonMounted(fn)\nonUpdated(fn)", "description": "在setup中通过onMounted、onUpdated等API注册生命周期钩子，替代选项式API中的生命周期方法。", "parameters": [{"name": "onMounted/onUpdated等", "description": "生命周期钩子函数"}], "returnValue": "无返回值，钩子在对应阶段自动执行", "examples": [{"code": "import { onMounted } from 'vue'\nsetup() {\n  onMounted(() => {\n    console.log('组件已挂载')\n  })\n}", "explanation": "onMounted在组件挂载后执行回调。"}]}}, {"name": "Composition API vs Options API", "trans": ["组合式API与选项式API对比"], "usage": {"syntax": "// 组合式API\nsetup() { ... }\n// 选项式API\ndata() { ... }\nmethods: { ... }\nmounted() { ... }", "description": "组合式API通过setup集中管理逻辑，便于复用和组织复杂功能。选项式API按data、methods、computed等分散声明，适合简单组件。", "parameters": [{"name": "setup/data/methods等", "description": "两种API的声明方式"}], "returnValue": "无返回值，便于理解两种API的差异和适用场景", "examples": [{"code": "// 组合式API\nimport { ref } from 'vue'\nsetup() {\n  const count = ref(0)\n  return { count }\n}\n// 选项式API\ndata() { return { count: 0 } }", "explanation": "组合式API逻辑集中，选项式API结构分散。"}]}}, {"name": "Setup Function Assignment", "trans": ["setup函数练习"], "usage": {"syntax": "# setup函数练习", "description": "完成以下练习，巩固setup函数相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 在setup中接收props并打印。\n2. 返回响应式数据和方法供模板使用。\n3. 在setup中注册onMounted钩子。\n4. 对比组合式API和选项式API的写法。", "explanation": "通过这些练习掌握setup参数、返回值、生命周期和API对比。"}]}}]}