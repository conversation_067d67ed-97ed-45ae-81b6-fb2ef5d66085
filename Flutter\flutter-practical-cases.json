{"name": "Practical Cases", "trans": ["实战案例"], "methods": [{"name": "Counter App", "trans": ["计数器应用"], "usage": {"syntax": "StatefulWidget、setState", "description": "最基础的Flutter入门案例，通过按钮点击实现计数自增，掌握StatefulWidget和setState的用法。", "parameters": [{"name": "StatefulWidget", "description": "有状态组件，管理计数状态"}, {"name": "setState", "description": "更新状态并刷新UI"}], "returnValue": "无返回值", "examples": [{"code": "// 计数器应用示例\nimport 'package:flutter/material.dart';\n\nclass CounterApp extends StatefulWidget {\n  @override\n  _CounterAppState createState() => _CounterAppState();\n}\n\nclass _CounterAppState extends State<CounterApp> {\n  int _count = 0;\n\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('计数器')),\n      body: Center(\n        child: Text('当前计数：$_count', style: TextStyle(fontSize: 24)),\n      ),\n      floatingActionButton: FloatingActionButton(\n        onPressed: () {\n          setState(() {\n            _count++;\n          });\n        },\n        child: Icon(Icons.add),\n      ),\n    );\n  }\n}", "explanation": "演示如何用StatefulWidget和setState实现计数器自增功能。"}]}}, {"name": "Todo List App", "trans": ["待办事项App"], "usage": {"syntax": "ListView、TextField、setState", "description": "通过输入框添加待办事项，使用ListView展示，掌握列表渲染和输入处理。", "parameters": [{"name": "ListView", "description": "渲染待办事项列表"}, {"name": "TextField", "description": "输入新事项"}, {"name": "setState", "description": "添加/删除事项时刷新UI"}], "returnValue": "无返回值", "examples": [{"code": "// 待办事项App示例\nimport 'package:flutter/material.dart';\n\nclass TodoApp extends StatefulWidget {\n  @override\n  _TodoAppState createState() => _TodoAppState();\n}\n\nclass _TodoAppState extends State<TodoApp> {\n  final List<String> _todos = [];\n  final TextEditingController _controller = TextEditingController();\n\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('待办事项')),\n      body: Column(\n        children: [\n          Padding(\n            padding: EdgeInsets.all(8),\n            child: Row(\n              children: [\n                Expanded(\n                  child: TextField(\n                    controller: _controller,\n                    decoration: InputDecoration(hintText: '输入新事项'),\n                  ),\n                ),\n                IconButton(\n                  icon: Icon(Icons.add),\n                  onPressed: () {\n                    if (_controller.text.isNotEmpty) {\n                      setState(() {\n                        _todos.add(_controller.text);\n                        _controller.clear();\n                      });\n                    }\n                  },\n                )\n              ],\n            ),\n          ),\n          Expanded(\n            child: ListView.builder(\n              itemCount: _todos.length,\n              itemBuilder: (context, index) => ListTile(\n                title: Text(_todos[index]),\n                trailing: Icon<PERSON>utton(\n                  icon: Icon(Icons.delete),\n                  onPressed: () {\n                    setState(() {\n                      _todos.removeAt(index);\n                    });\n                  },\n                ),\n              ),\n            ),\n          )\n        ],\n      ),\n    );\n  }\n}", "explanation": "演示如何用TextField输入、ListView渲染和setState管理待办事项列表。"}]}}, {"name": "Weather Query App", "trans": ["天气查询App"], "usage": {"syntax": "http、FutureBuilder、json解析", "description": "通过网络请求获取天气数据，展示异步数据加载和json解析。", "parameters": [{"name": "http", "description": "发起天气API请求"}, {"name": "FutureBuilder", "description": "异步加载数据"}, {"name": "jsonDecode", "description": "解析API返回的json"}], "returnValue": "无返回值", "examples": [{"code": "// 天气查询App示例（需引入http包）\nimport 'package:flutter/material.dart';\nimport 'package:http/http.dart' as http;\nimport 'dart:convert';\n\nclass WeatherApp extends StatefulWidget {\n  @override\n  _WeatherAppState createState() => _WeatherAppState();\n}\n\nclass _WeatherAppState extends State<WeatherApp> {\n  Future<String> fetchWeather() async {\n    final response = await http.get(Uri.parse('https://wttr.in/?format=3'));\n    if (response.statusCode == 200) {\n      return response.body;\n    } else {\n      throw Exception('获取天气失败');\n    }\n  }\n\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('天气查询')),\n      body: Center(\n        child: FutureBuilder<String>(\n          future: fetchWeather(),\n          builder: (context, snapshot) {\n            if (snapshot.connectionState == ConnectionState.waiting) {\n              return CircularProgressIndicator();\n            } else if (snapshot.hasError) {\n              return Text('错误: \\${snapshot.error}');\n            } else {\n              return Text('当前天气: \\${snapshot.data}');\n            }\n          },\n        ),\n      ),\n    );\n  }\n}", "explanation": "演示如何用http请求和FutureBuilder异步加载天气数据。"}]}}, {"name": "Chat Room App", "trans": ["聊天室App"], "usage": {"syntax": "ListView、TextField、WebSocket（或本地模拟）", "description": "实现简单的聊天室界面，支持消息输入和展示，掌握消息列表和输入框的结合。", "parameters": [{"name": "ListView", "description": "展示消息列表"}, {"name": "TextField", "description": "输入消息"}, {"name": "WebSocket/本地", "description": "消息发送与接收"}], "returnValue": "无返回值", "examples": [{"code": "// 聊天室App本地模拟示例\nimport 'package:flutter/material.dart';\n\nclass ChatRoomApp extends StatefulWidget {\n  @override\n  _ChatRoomAppState createState() => _ChatRoomAppState();\n}\n\nclass _ChatRoomAppState extends State<ChatRoomApp> {\n  final List<String> _messages = [];\n  final TextEditingController _controller = TextEditingController();\n\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('聊天室')),\n      body: Column(\n        children: [\n          Expanded(\n            child: ListView.builder(\n              itemCount: _messages.length,\n              itemBuilder: (context, index) => ListTile(\n                title: Text(_messages[index]),\n              ),\n            ),\n          ),\n          Padding(\n            padding: EdgeInsets.all(8),\n            child: Row(\n              children: [\n                Expanded(\n                  child: TextField(\n                    controller: _controller,\n                    decoration: InputDecoration(hintText: '输入消息'),\n                  ),\n                ),\n                IconButton(\n                  icon: Icon(Icons.send),\n                  onPressed: () {\n                    if (_controller.text.isNotEmpty) {\n                      setState(() {\n                        _messages.add(_controller.text);\n                        _controller.clear();\n                      });\n                    }\n                  },\n                )\n              ],\n            ),\n          )\n        ],\n      ),\n    );\n  }\n}", "explanation": "演示如何用ListView和TextField实现本地聊天室消息输入与展示。"}]}}, {"name": "E-commerce Product List and Detail", "trans": ["电商商品列表与详情"], "usage": {"syntax": "ListView、Navigator、路由跳转", "description": "实现商品列表和详情页跳转，掌握多页面路由与数据传递。", "parameters": [{"name": "ListView", "description": "展示商品列表"}, {"name": "Navigator", "description": "页面跳转"}, {"name": "路由参数", "description": "传递商品详情数据"}], "returnValue": "无返回值", "examples": [{"code": "// 电商商品列表与详情示例\nimport 'package:flutter/material.dart';\n\nclass Product {\n  final String name;\n  final String desc;\n  Product(this.name, this.desc);\n}\n\nclass ProductListPage extends StatelessWidget {\n  final List<Product> products = [\n    Product('手机', '高性能智能手机'),\n    Product('耳机', '降噪蓝牙耳机'),\n    Product('手表', '智能手表'),\n  ];\n\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('商品列表')),\n      body: ListView.builder(\n        itemCount: products.length,\n        itemBuilder: (context, index) => ListTile(\n          title: Text(products[index].name),\n          subtitle: Text(products[index].desc),\n          onTap: () {\n            Navigator.push(\n              context,\n              MaterialPageRoute(\n                builder: (_) => ProductDetailPage(product: products[index]),\n              ),\n            );\n          },\n        ),\n      ),\n    );\n  }\n}\n\nclass ProductDetailPage extends StatelessWidget {\n  final Product product;\n  ProductDetailPage({required this.product});\n\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text(product.name)),\n      body: Padding(\n        padding: EdgeInsets.all(16),\n        child: Text(product.desc, style: TextStyle(fontSize: 20)),\n      ),\n    );\n  }\n}", "explanation": "演示如何用ListView展示商品列表，点击跳转到详情页并传递数据。"}]}}, {"name": "Assignment", "trans": ["作业：实战案例练习"], "usage": {"syntax": "无", "description": "任选一个案例，独立实现并扩展功能（如添加删除、网络请求、页面跳转等），提交代码和效果截图。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 作业参考实现\n// 任选计数器、待办事项、天气、聊天室、电商案例，独立实现并扩展功能。\n// 比如：待办事项支持编辑、天气App支持城市切换、电商App支持购物车等。\n// 提交你的代码和运行效果截图。", "explanation": "通过动手实现和扩展实战案例，提升Flutter项目开发能力。"}]}}, {"name": "Custom Widget Development", "trans": ["自定义组件开发"], "usage": {"syntax": "class MyWidget extends StatelessWidget/StatefulWidget", "description": "学习如何创建自定义Flutter组件，掌握组件封装、复用与参数传递。", "parameters": [{"name": "StatelessWidget", "description": "无状态组件，适合静态展示"}, {"name": "StatefulWidget", "description": "有状态组件，适合需要交互和状态管理的场景"}, {"name": "props", "description": "自定义组件的参数"}], "returnValue": "无返回值", "examples": [{"code": "// 自定义按钮组件示例\nimport 'package:flutter/material.dart';\n\nclass MyButton extends StatelessWidget {\n  final String text;\n  final VoidCallback onPressed;\n  MyButton({required this.text, required this.onPressed});\n\n  @override\n  Widget build(BuildContext context) {\n    return ElevatedButton(\n      onPressed: onPressed,\n      child: Text(text),\n    );\n  }\n}\n\n// 使用自定义组件\nMyButton(text: '点击我', onPressed: () { print('按钮被点击'); });", "explanation": "演示如何自定义一个带参数的按钮组件，并在页面中复用。"}]}}, {"name": "Multi-page Routing", "trans": ["多页面路由实现"], "usage": {"syntax": "Navigator.push/MaterialPageRoute", "description": "掌握Flutter多页面跳转与路由管理，支持页面间数据传递。", "parameters": [{"name": "Navigator", "description": "页面跳转和返回"}, {"name": "MaterialPageRoute", "description": "定义跳转目标页面"}, {"name": "路由参数", "description": "页面间传递的数据"}], "returnValue": "无返回值", "examples": [{"code": "// 跳转到新页面并传递参数\nNavigator.push(\n  context,\n  MaterialPageRoute(builder: (_) => DetailPage(data: 'hello')),\n);\n\n// 目标页面接收参数\nclass DetailPage extends StatelessWidget {\n  final String data;\n  DetailPage({required this.data});\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(body: Center(child: Text(data)));\n  }\n}", "explanation": "演示如何通过Navigator和MaterialPageRoute实现页面跳转和参数传递。"}]}}, {"name": "Network & Local Storage Integration", "trans": ["集成网络请求与本地存储"], "usage": {"syntax": "http、shared_preferences", "description": "结合网络请求与本地存储，实现数据的获取与持久化。", "parameters": [{"name": "http", "description": "发起网络请求"}, {"name": "shared_preferences", "description": "本地存储简单数据"}], "returnValue": "无返回值", "examples": [{"code": "// 获取数据并保存到本地\nimport 'package:http/http.dart' as http;\nimport 'package:shared_preferences/shared_preferences.dart';\n\nFuture<void> fetchAndSave() async {\n  final res = await http.get(Uri.parse('https://api.example.com/data'));\n  if (res.statusCode == 200) {\n    final prefs = await SharedPreferences.getInstance();\n    await prefs.setString('data', res.body);\n  }\n}", "explanation": "演示如何用http获取数据并用shared_preferences保存到本地。"}]}}, {"name": "Animation & Interaction Practice", "trans": ["动画与交互综合练习"], "usage": {"syntax": "AnimatedContainer、GestureDetector", "description": "通过动画组件和手势交互，提升界面动效与用户体验。", "parameters": [{"name": "AnimatedContainer", "description": "内置动画容器"}, {"name": "GestureDetector", "description": "手势识别与交互"}], "returnValue": "无返回值", "examples": [{"code": "// 点击切换颜色的动画容器\nAnimatedContainer(\n  duration: Duration(milliseconds: 500),\n  color: isRed ? Colors.red : Colors.blue,\n  width: 100, height: 100,\n  child: GestureDetector(\n    onTap: () { setState(() { isRed = !isRed; }); },\n  ),\n)", "explanation": "演示如何结合AnimatedContainer和GestureDetector实现交互动画。"}]}}, {"name": "Performance Optimization Practice", "trans": ["性能优化实战"], "usage": {"syntax": "const、ListView.builder、shouldRebuild", "description": "掌握Flutter常用性能优化技巧，如使用const、懒加载列表、减少重建。", "parameters": [{"name": "const", "description": "常量化组件，减少重建"}, {"name": "ListView.builder", "description": "高效渲染长列表"}, {"name": "shouldRebuild", "description": "自定义组件重建条件"}], "returnValue": "无返回值", "examples": [{"code": "// 使用const优化组件\nconst Text('静态文本');\n\n// 高效渲染长列表\nListView.builder(itemCount: 1000, itemBuilder: (c, i) => Text('item: \\${i}'));", "explanation": "演示如何用const和ListView.builder优化性能。"}]}}, {"name": "Unit & Integration Test Practice", "trans": ["单元与集成测试实践"], "usage": {"syntax": "test、flutter_test", "description": "学习如何为Flutter项目编写单元测试和集成测试，保障代码质量。", "parameters": [{"name": "test", "description": "单元测试框架"}, {"name": "flutter_test", "description": "Flutter集成测试工具"}], "returnValue": "无返回值", "examples": [{"code": "// 单元测试示例\nimport 'package:test/test.dart';\nvoid main() {\n  test('加法测试', () {\n    expect(1 + 1, 2);\n  });\n}", "explanation": "演示如何用test包编写简单的单元测试。"}]}}, {"name": "Assignment", "trans": ["作业：练习与项目实践"], "usage": {"syntax": "无", "description": "任选一个练习点，独立实现并扩展功能（如自定义组件、动画交互、性能优化等），提交代码和效果截图。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 作业参考实现\n// 任选自定义组件、路由、网络存储、动画、性能优化、测试等，独立实现并扩展功能。\n// 比如：自定义一个可复用的卡片组件，或实现一个带动画的交互页面。\n// 提交你的代码和运行效果截图。", "explanation": "通过动手实现和扩展练习项目，提升Flutter综合开发能力。"}]}}]}