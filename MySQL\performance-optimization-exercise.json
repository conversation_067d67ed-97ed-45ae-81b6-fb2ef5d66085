{"name": "Performance Optimization Exercise", "trans": ["性能优化练习"], "methods": [{"name": "Student Table Indexing", "trans": ["学生表索引优化"], "usage": {"syntax": "-- 创建索引\nCREATE INDEX 索引名 ON students(列名);\n\n-- 分析查询\nEXPLAIN SELECT * FROM students WHERE 条件;", "description": "通过为学生表的学号和姓名建立合适的索引，并使用EXPLAIN分析查询执行计划，了解索引对查询性能的影响。", "parameters": [{"name": "索引名", "description": "要创建的索引名称"}, {"name": "列名", "description": "要建立索引的列名"}, {"name": "条件", "description": "查询的WHERE条件"}], "returnValue": "EXPLAIN返回查询执行计划的详细信息", "examples": [{"code": "-- 创建学生表\nCREATE TABLE students (\n  id INT PRIMARY KEY AUTO_INCREMENT,\n  student_id VARCHAR(20),  -- 学号\n  name VARCHAR(50),        -- 姓名\n  gender CHAR(1),          -- 性别\n  age INT,                 -- 年龄\n  class_id INT,            -- 班级ID\n  enrollment_date DATE,    -- 入学日期\n  email VARCHAR(100)       -- 邮箱\n);\n\n-- 插入测试数据\nINSERT INTO students (student_id, name, gender, age, class_id, enrollment_date, email)\nVALUES \n('2023001', '张三', 'M', 20, 1, '2023-09-01', 'zhang<PERSON>@example.com'),\n('2023002', '李四', 'M', 21, 1, '2023-09-01', '<EMAIL>'),\n('2023003', '王五', 'M', 19, 2, '2023-09-01', '<EMAIL>'),\n('2023004', '赵六', 'F', 20, 2, '2023-09-01', 'z<PERSON><PERSON><PERSON>@example.com'),\n('2023005', '钱七', 'F', 22, 3, '2023-09-01', '<EMAIL>');\n\n-- 不使用索引的查询分析\nEXPLAIN SELECT * FROM students WHERE student_id = '2023003';\n\n-- 为学号创建索引\nCREATE INDEX idx_student_id ON students(student_id);\n\n-- 使用索引后的查询分析\nEXPLAIN SELECT * FROM students WHERE student_id = '2023003';\n\n-- 为姓名创建索引\nCREATE INDEX idx_student_name ON students(name);\n\n-- 分析按姓名查询的执行计划\nEXPLAIN SELECT * FROM students WHERE name = '王五';\n\n-- 创建复合索引\nCREATE INDEX idx_class_age ON students(class_id, age);\n\n-- 分析使用复合索引的查询\nEXPLAIN SELECT * FROM students WHERE class_id = 1 AND age > 20;\nEXPLAIN SELECT * FROM students WHERE class_id = 1;\nEXPLAIN SELECT * FROM students WHERE age > 20; -- 注意：这个查询可能不会使用复合索引", "explanation": "这个例子展示了如何为学生表的学号和姓名字段创建索引，并使用EXPLAIN分析查询执行计划。通过比较索引前后的执行计划，可以清楚地看到索引对查询性能的影响。同时也演示了复合索引的使用和最左前缀原则。"}]}}, {"name": "Query Optimization Analysis", "trans": ["查询优化分析"], "usage": {"syntax": "-- 优化前\nEXPLAIN SELECT ... FROM ... WHERE ...;\n\n-- 优化后\nEXPLAIN SELECT ... FROM ... WHERE ...;", "description": "通过对比优化前后的查询执行计划，分析不同查询写法和索引策略对性能的影响，学习如何优化SQL查询。", "parameters": [], "returnValue": "EXPLAIN返回查询执行计划的详细信息", "examples": [{"code": "-- 创建测试表\nCREATE TABLE orders (\n  id INT PRIMARY KEY AUTO_INCREMENT,\n  order_no VARCHAR(20),\n  customer_id INT,\n  order_date DATE,\n  status VARCHAR(20),\n  total_amount DECIMAL(10,2)\n);\n\nCREATE TABLE order_items (\n  id INT PRIMARY KEY AUTO_INCREMENT,\n  order_id INT,\n  product_id INT,\n  quantity INT,\n  price DECIMAL(10,2)\n);\n\n-- 创建索引\nCREATE INDEX idx_order_customer ON orders(customer_id);\nCREATE INDEX idx_order_date ON orders(order_date);\nCREATE INDEX idx_orderitems_order ON order_items(order_id);\n\n-- 案例1：优化前（使用函数导致索引失效）\nEXPLAIN SELECT * FROM orders WHERE YEAR(order_date) = 2023;\n\n-- 案例1：优化后（改写条件使索引生效）\nEXPLAIN SELECT * FROM orders WHERE order_date BETWEEN '2023-01-01' AND '2023-12-31';\n\n-- 案例2：优化前（使用OR可能导致索引失效）\nEXPLAIN SELECT * FROM orders WHERE customer_id = 100 OR order_date = '2023-05-01';\n\n-- 案例2：优化后（使用UNION ALL）\nEXPLAIN SELECT * FROM orders WHERE customer_id = 100\nUNION ALL\nSELECT * FROM orders WHERE order_date = '2023-05-01' AND customer_id != 100;\n\n-- 案例3：优化前（不必要的JOIN）\nEXPLAIN SELECT o.* \nFROM orders o \nJOIN order_items oi ON o.id = oi.order_id\nWHERE o.customer_id = 100;\n\n-- 案例3：优化后（使用EXISTS）\nEXPLAIN SELECT * FROM orders o \nWHERE o.customer_id = 100\nAND EXISTS (SELECT 1 FROM order_items oi WHERE oi.order_id = o.id);", "explanation": "这个例子展示了几种常见的查询优化技巧：避免在索引列上使用函数、使用UNION ALL替代OR条件、使用EXISTS替代不必要的JOIN。通过EXPLAIN分析优化前后的执行计划，可以清楚地看到这些优化技巧的效果。"}]}}, {"name": "Comprehensive Performance Exercise", "trans": ["综合性能优化练习"], "usage": {"syntax": "# 综合性能优化练习", "description": "通过一个综合案例，实践索引设计、查询优化、表结构优化等多种性能优化技术。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n假设你是一个学校管理系统的数据库管理员，系统中有以下表结构：\n\n```sql\n-- 学生表\nCREATE TABLE students (\n  id INT PRIMARY KEY AUTO_INCREMENT,\n  student_id VARCHAR(20),  -- 学号\n  name VARCHAR(50),        -- 姓名\n  gender CHAR(1),          -- 性别\n  birth_date DATE,         -- 出生日期\n  class_id INT,            -- 班级ID\n  enrollment_date DATE,    -- 入学日期\n  address TEXT,            -- 家庭住址\n  phone VARCHAR(20),       -- 联系电话\n  email VARCHAR(100),      -- 邮箱\n  status VARCHAR(10)       -- 状态：在读、休学、毕业等\n);\n\n-- 课程表\nCREATE TABLE courses (\n  id INT PRIMARY KEY AUTO_INCREMENT,\n  course_code VARCHAR(20), -- 课程代码\n  course_name VARCHAR(100),-- 课程名称\n  credit DECIMAL(3,1),     -- 学分\n  department VARCHAR(50),  -- 所属院系\n  description TEXT         -- 课程描述\n);\n\n-- 成绩表\nCREATE TABLE scores (\n  id INT PRIMARY KEY AUTO_INCREMENT,\n  student_id INT,          -- 学生ID\n  course_id INT,           -- 课程ID\n  semester VARCHAR(20),    -- 学期\n  score DECIMAL(5,2),      -- 分数\n  exam_date DATE           -- 考试日期\n);\n```\n\n请完成以下任务：\n\n1. 为学生表的学号(student_id)和姓名(name)字段建立合适的索引，并使用EXPLAIN分析以下查询：\n   ```sql\n   SELECT * FROM students WHERE student_id = '2023001';\n   SELECT * FROM students WHERE name = '张三';\n   ```\n\n2. 为成绩表设计合适的索引，优化以下查询：\n   ```sql\n   SELECT s.student_id, s.name, c.course_name, sc.score\n   FROM students s\n   JOIN scores sc ON s.id = sc.student_id\n   JOIN courses c ON sc.course_id = c.id\n   WHERE s.class_id = 1 AND sc.semester = '2023-1';\n   ```\n\n3. 分析并优化以下查询：\n   ```sql\n   SELECT * FROM students \n   WHERE YEAR(enrollment_date) = 2023 AND status = '在读';\n   ```\n\n4. 设计一个垂直分区方案，将学生表拆分为基本信息表和详细信息表，以提高常用查询的性能。\n\n5. 使用EXPLAIN分析所有优化后的查询，并解释索引的使用情况。", "explanation": "这个综合练习涵盖了索引设计、查询优化和表结构优化等多个方面，通过实际操作帮助掌握MySQL性能优化的核心技术。"}]}}]}