{"name": "Functions", "trans": ["函数定义与调用"], "methods": [{"name": "Function Structure", "trans": ["函数的基本结构"], "usage": {"syntax": "// 基本函数结构\n返回类型 函数名(参数列表) {\n  // 函数体\n  return 返回值;\n}\n\n// 例如:\nvoid sayHello() {\n  print('Hello');\n}\n\nint add(int a, int b) {\n  return a + b;\n}\n\n// 省略返回类型（自动推断）\nadd(int a, int b) {\n  return a + b;\n}\n\n// main函数（程序入口）\nvoid main() {\n  // 代码\n}", "description": "函数是Dart中可重用代码的基本单位，用于封装特定功能的代码块，使其可以在多处调用。Dart中的函数由返回类型、函数名、参数列表和函数体组成。返回类型可以是任何数据类型（包括void表示无返回值），也可以省略让Dart自动推断。函数名应遵循小驼峰命名法，并且通常使用动词或动词短语。参数列表定义了函数接受的输入，可以是必需的、可选的或命名的。函数体包含在花括号中，包括实际执行的代码，以及可选的return语句（用于指定返回值）。所有Dart程序都需要一个main函数作为执行入口点。函数是一等公民，这意味着它们可以赋值给变量、作为参数传递给其他函数，或作为函数的返回值。", "parameters": [], "returnValue": "任何有效的Dart类型（包括void表示无返回值）", "examples": [{"code": "void main() {\n  // 调用无参数函数\n  sayHello();\n  \n  // 调用带参数的函数\n  int sum = add(5, 3);\n  print('5 + 3 = $sum');\n  \n  // 使用函数返回值\n  String message = createGreeting('Alice');\n  print(message);\n  \n  // 函数链式调用\n  print(processText('  Hello World  '));\n  \n  // 调用多参数函数\n  introduceYourself('Bob', 30, 'New York');\n  \n  // 调用有返回值但不使用返回值的函数\n  calculateSum(10, 20); // 结果被忽略\n  \n  // 调用参数类型不同的函数\n  printTypes(10, 'hello', true, 3.14);\n  \n  // 函数作为一等公民示例\n  Function mathOperation = add;\n  print('使用函数变量: ${mathOperation(7, 3)}');\n  \n  // 自执行函数\n  (String name) {\n    print('自执行函数: 你好, $name!');\n  }('David');\n}\n\n// 无参数、无返回值的函数\nvoid sayHello() {\n  print('Hello, World!');\n}\n\n// 带两个参数和返回值的函数\nint add(int a, int b) {\n  return a + b;\n}\n\n// 带字符串参数和返回值的函数\nString createGreeting(String name) {\n  return 'Hello, $name!';\n}\n\n// 使用多个返回语句的函数\nString getCategory(int score) {\n  if (score >= 90) return 'A';\n  if (score >= 80) return 'B';\n  if (score >= 70) return 'C';\n  if (score >= 60) return 'D';\n  return 'F';\n}\n\n// 链式处理函数\nString processText(String text) {\n  return text.trim().toUpperCase().replaceAll('WORLD', 'DART');\n}\n\n// 多参数函数\nvoid introduceYourself(String name, int age, String city) {\n  print('我叫$name，今年$age岁，来自$city。');\n}\n\n// 有返回值但可能不使用的函数\nint calculateSum(int a, int b) {\n  int result = a + b;\n  print('计算结果: $result');\n  return result;\n}\n\n// 接受不同类型参数的函数\nvoid printTypes(int number, String text, bool flag, double decimal) {\n  print('整数: $number');\n  print('字符串: $text');\n  print('布尔值: $flag');\n  print('浮点数: $decimal');\n}\n\n// 嵌套函数示例\nString formatName(String firstName, String lastName) {\n  // 嵌套函数定义\n  String capitalize(String s) {\n    return s[0].toUpperCase() + s.substring(1).toLowerCase();\n  }\n  \n  // 使用嵌套函数\n  return '${capitalize(firstName)} ${capitalize(lastName)}';\n}\n\n// 返回void的函数可以不使用return语句\nvoid printInfo(String info) {\n  print('信息: $info');\n  // 函数结束时隐式返回\n}\n\n// 带有局部变量的函数\nint calculateAverage(List<int> numbers) {\n  int sum = 0; // 局部变量\n  \n  for (int number in numbers) {\n    sum += number;\n  }\n  \n  return sum ~/ numbers.length;\n}\n\n// 递归函数\nint factorial(int n) {\n  if (n <= 1) return 1;\n  return n * factorial(n - 1);\n}", "explanation": "这个示例全面展示了Dart中函数的基本结构和多种类型。首先展示了无参数函数、带参数函数以及有返回值函数的定义和调用方式。示例包括了多种函数使用场景，如函数链式调用、多参数函数、使用或忽略函数返回值等。此外，还展示了函数作为一等公民的概念，即函数可以像变量一样被赋值和传递。示例还包括了嵌套函数（在函数内部定义的函数）、自执行函数、不同类型参数的函数，以及带有局部变量的函数。最后展示了递归函数的基本用法。这些示例说明了函数如何用于组织和复用代码，以及Dart函数系统的灵活性。"}]}}, {"name": "Parameters and Return Values", "trans": ["参数与返回值"], "usage": {"syntax": "// 必需参数\n返回类型 函数名(参数类型1 参数名1, 参数类型2 参数名2) {\n  // 函数体\n  return 返回值;\n}\n\n// 无参数函数\n返回类型 函数名() {\n  // 函数体\n  return 返回值;\n}\n\n// 多种类型参数\n返回类型 函数名(int a, String b, bool c) {\n  // 函数体\n  return 返回值;\n}\n\n// 类型推断的返回值\n函数名(参数列表) {\n  return 值; // 返回类型将被推断\n}\n\n// 动态类型参数\nvoid 函数名(dynamic param) {\n  // 可以接受任何类型\n}\n\n// 无返回值（void）\nvoid 函数名(参数列表) {\n  // 无需return语句\n}", "description": "参数和返回值是定义函数输入和输出的关键部分。参数定义了函数接受什么样的输入，每个参数都有类型和名称，允许在函数体内使用。Dart中的参数默认是必需的，调用函数时必须提供。函数可以有任意数量的参数，甚至可以没有参数（空括号）。参数类型可以是任何Dart类型，包括基本类型、集合、对象或函数。使用dynamic类型的参数可以接受任何类型的值，但会失去类型安全性。返回值定义了函数执行后产生的结果，由return语句指定，其类型应与函数声明的返回类型兼容。如果函数不返回值，应使用void返回类型。在不指定返回类型时，Dart会根据return语句推断返回类型。如果函数声明了非void返回类型但没有return语句，默认返回null（对于可空类型）或抛出错误（对于非空类型）。最后，函数可以有多个return语句，执行到哪个就返回哪个结果。", "parameters": [], "returnValue": "由函数声明的返回类型决定", "examples": [{"code": "void main() {\n  // 使用无参数函数\n  printCurrentTime();\n  \n  // 使用带单个参数的函数\n  greet('Alice');\n  \n  // 使用带多个参数的函数\n  int sum = addNumbers(5, 3);\n  print('5 + 3 = $sum');\n  \n  // 传递不同类型的参数\n  printDetails('Bob', 30, 'Developer');\n  \n  // 使用动态类型参数\n  printValue(10);\n  printValue('Hello');\n  printValue(true);\n  printValue([1, 2, 3]);\n  \n  // 传递空值（对于可空类型）\n  String? nullableValue;\n  processNullable(nullableValue);\n  \n  // 使用带返回值的函数\n  double circleArea = calculateCircleArea(5.0);\n  print('圆的面积: $circleArea');\n  \n  // 使用多种返回值类型的函数\n  print('数字转换为字符串: ${convertToString(42)}');\n  print('布尔值转换为字符串: ${convertToString(true)}');\n  print('列表转换为字符串: ${convertToString([1, 2, 3])}');\n  \n  // 使用void函数\n  logMessage('这是一条日志消息');\n  \n  // 函数中的提前返回\n  print('检查正数: ${checkPositive(5)}');\n  print('检查正数: ${checkPositive(-3)}');\n  \n  // 使用带有多个return语句的函数\n  print('10分: ${getGrade(10)}');\n  print('85分: ${getGrade(85)}');\n  print('95分: ${getGrade(95)}');\n  \n  // 忽略返回值\n  generateRandomNumber(); // 返回值被忽略\n  \n  // 使用推断类型的返回值\n  var result = multiply(4, 5);\n  print('4 * 5 = $result (${result.runtimeType})');\n  \n  // 使用函数链式调用\n  int value = doubleAndAdd(5, 3);\n  print('(5 * 2) + 3 = $value');\n}\n\n// 无参数函数\nvoid printCurrentTime() {\n  print('当前时间: ${DateTime.now()}');\n}\n\n// 单个参数函数\nvoid greet(String name) {\n  print('你好, $name!');\n}\n\n// 两个参数函数带返回值\nint addNumbers(int a, int b) {\n  return a + b;\n}\n\n// 多个不同类型参数\nvoid printDetails(String name, int age, String occupation) {\n  print('$name, $age岁, 职业: $occupation');\n}\n\n// 动态类型参数\nvoid printValue(dynamic value) {\n  print('值: $value (类型: ${value.runtimeType})');\n}\n\n// 可空类型参数\nvoid processNullable(String? text) {\n  if (text == null) {\n    print('处理空值');\n  } else {\n    print('处理文本: $text');\n  }\n}\n\n// 带返回值的函数\ndouble calculateCircleArea(double radius) {\n  return 3.14159 * radius * radius;\n}\n\n// 使用dynamic返回类型\ndynamic convertToString(dynamic value) {\n  return value.toString();\n}\n\n// void返回类型\nvoid logMessage(String message) {\n  print('[日志] $message');\n  // 无需return语句\n}\n\n// 提前返回\nbool checkPositive(int number) {\n  if (number < 0) {\n    return false; // 提前返回\n  }\n  \n  // 其他逻辑...\n  return true;\n}\n\n// 多个return语句\nString getGrade(int score) {\n  if (score >= 90) return 'A';\n  if (score >= 80) return 'B';\n  if (score >= 70) return 'C';\n  if (score >= 60) return 'D';\n  return 'F';\n}\n\n// 推断返回类型\nmultiply(int a, int b) {\n  return a * b; // 返回类型被推断为int\n}\n\n// 生成随机数的函数，返回值可能被忽略\nint generateRandomNumber() {\n  final random = DateTime.now().millisecondsSinceEpoch % 100;\n  print('生成的随机数: $random');\n  return random;\n}\n\n// 函数链式调用\nint doubleAndAdd(int a, int b) {\n  return multiplyByTwo(a) + b;\n}\n\nint multiplyByTwo(int number) {\n  return number * 2;\n}\n\n// 具有副作用的函数\nint incrementCounter() {\n  static int counter = 0;\n  counter++;\n  print('计数器: $counter');\n  return counter;\n}", "explanation": "这个示例全面展示了Dart中函数参数和返回值的多种用法。首先展示了无参数函数、单参数函数和多参数函数的定义和调用方式。然后演示了如何处理不同类型的参数，包括基本类型、可空类型和动态类型。示例还包括了返回值的各种情况，如基本类型返回值、动态类型返回值、void函数（无返回值）以及类型推断的返回值。此外，示例展示了函数中提前返回和多返回语句的用法，以及如何忽略函数返回值或在函数链式调用中使用返回值。最后，示例包含了一个具有副作用的函数（incrementCounter），它不仅返回值，还修改了静态变量。这些示例说明了Dart中函数参数和返回值的灵活性和多样性，以及如何根据不同需求使用它们。"}]}}, {"name": "Optional Parameters and Default Values", "trans": ["可选参数与默认值"], "usage": {"syntax": "// 可选位置参数（使用方括号）\n返回类型 函数名(参数类型1 参数名1, [参数类型2? 参数名2, 参数类型3? 参数名3]) {\n  // 函数体\n}\n\n// 带默认值的可选位置参数\n返回类型 函数名(参数类型1 参数名1, [参数类型2 参数名2 = 默认值2, 参数类型3 参数名3 = 默认值3]) {\n  // 函数体\n}\n\n// 可选命名参数（使用花括号）\n返回类型 函数名(参数类型1 参数名1, {参数类型2? 参数名2, 参数类型3? 参数名3}) {\n  // 函数体\n}\n\n// 带默认值的可选命名参数\n返回类型 函数名(参数类型1 参数名1, {参数类型2 参数名2 = 默认值2, 参数类型3 参数名3 = 默认值3}) {\n  // 函数体\n}\n\n// 必需的命名参数（使用required关键字）\n返回类型 函数名({required 参数类型1 参数名1, required 参数类型2 参数名2, 参数类型3? 参数名3}) {\n  // 函数体\n}\n\n// 混合使用位置参数和命名参数\n返回类型 函数名(参数类型1 参数名1, 参数类型2 参数名2, {参数类型3? 参数名3, 参数类型4? 参数名4}) {\n  // 函数体\n}", "description": "Dart提供了灵活的参数定义方式，允许设置可选参数和默认值，使函数调用更加灵活。可选参数有两种类型：位置可选参数和命名可选参数。位置可选参数使用方括号[]包围，按照定义的顺序提供，如果省略则使用默认值或null。命名可选参数使用花括号{}包围，调用时需要指定参数名，顺序不重要。命名参数默认是可选的，但可以使用required关键字将其标记为必需。为可选参数提供默认值可以避免null值并提高代码的健壮性，默认值必须是编译时常量。在函数体内，可以使用空安全操作符（??）为未提供的可选参数设置默认值。位置参数和命名参数可以混合使用，但所有命名参数必须在位置参数之后声明。命名参数提高了代码的可读性，特别是当函数有多个参数时，而位置参数则提供了更简洁的语法。", "parameters": [], "returnValue": "由函数声明的返回类型决定", "examples": [{"code": "void main() {\n  // 使用可选位置参数\n  printInfo('Alice');\n  printInfo('Bob', 30);\n  printInfo('Charlie', 25, 'Developer');\n  \n  // 使用带默认值的可选位置参数\n  greetWithDefault('David');\n  greetWithDefault('Eve', '晚上好');\n  greetWithDefault('<PERSON>', '早上好', 3);\n  \n  // 使用可选命名参数\n  createUser('Alice', age: 25);\n  createUser('Bob', age: 30, role: 'Admin');\n  createUser('Charlie', role: 'User'); // 省略age参数\n  \n  // 使用带默认值的可选命名参数\n  configureSettings(fontSize: 14);\n  configureSettings(theme: 'dark');\n  configureSettings(fontSize: 16, theme: 'light', animated: true);\n  \n  // 使用必需的命名参数\n  registerUser(username: 'dave123', email: '<EMAIL>');\n  \n  // 混合使用位置参数和命名参数\n  drawShape('circle', 5, color: 'red');\n  drawShape('rectangle', 10, width: 5, color: 'blue');\n  \n  // 忽略可选参数的不同组合\n  printDetails('Product1');\n  printDetails('Product2', price: 9.99);\n  printDetails('Product3', quantity: 5);\n  printDetails('Product4', price: 19.99, quantity: 2);\n  \n  // 使用null安全和可选参数\n  processInput('Hello');\n  processInput('World', transform: toUpperCase);\n  processInput('Dart', prefix: 'Language: ');\n  processInput('Flutter', prefix: 'Framework: ', transform: toUpperCase);\n}\n\n// 使用可选位置参数\nvoid printInfo(String name, [int? age, String? occupation]) {\n  print('姓名: $name');\n  \n  if (age != null) {\n    print('年龄: $age');\n  }\n  \n  if (occupation != null) {\n    print('职业: $occupation');\n  }\n}\n\n// 带默认值的可选位置参数\nvoid greetWithDefault(String name, [String greeting = '你好', int times = 1]) {\n  for (int i = 0; i < times; i++) {\n    print('$greeting, $name!');\n  }\n}\n\n// 使用可选命名参数\nvoid createUser(String name, {int? age, String? role}) {\n  print('创建用户:');\n  print('- 姓名: $name');\n  print('- 年龄: ${age ?? \"未指定\"}');\n  print('- 角色: ${role ?? \"普通用户\"}');\n}\n\n// 带默认值的可选命名参数\nvoid configureSettings({int fontSize = 12, String theme = 'default', bool animated = false}) {\n  print('应用设置:');\n  print('- 字体大小: $fontSize');\n  print('- 主题: $theme');\n  print('- 动画: ${animated ? \"启用\" : \"禁用\"}');\n}\n\n// 使用必需的命名参数\nvoid registerUser({required String username, required String email, int? age}) {\n  print('注册用户:');\n  print('- 用户名: $username');\n  print('- 电子邮件: $email');\n  \n  if (age != null) {\n    print('- 年龄: $age');\n  }\n}\n\n// 混合使用位置参数和命名参数\nvoid drawShape(String shape, double height, {double? width, String color = 'black'}) {\n  print('绘制$color色$shape:');\n  print('- 高度: $height');\n  \n  if (width != null) {\n    print('- 宽度: $width');\n  }\n}\n\n// 忽略不同组合的可选参数\nvoid printDetails(String productName, {double? price, int? quantity}) {\n  print('产品: $productName');\n  \n  if (price != null) {\n    print('- 价格: \\$${price.toStringAsFixed(2)}');\n  }\n  \n  if (quantity != null) {\n    print('- 数量: $quantity');\n  }\n  \n  if (price != null && quantity != null) {\n    print('- 总价: \\$${(price * quantity).toStringAsFixed(2)}');\n  }\n}\n\n// 字符串转换函数，用于下面的示例\nString toUpperCase(String text) {\n  return text.toUpperCase();\n}\n\n// 使用函数类型的可选参数\nvoid processInput(String input, {String prefix = '', String Function(String)? transform}) {\n  String result = input;\n  \n  if (transform != null) {\n    result = transform(result);\n  }\n  \n  print('$prefix$result');\n}\n\n// 较复杂的示例：构建查询参数\nString buildQueryString(String baseUrl, {Map<String, String>? parameters, bool encode = true}) {\n  if (parameters == null || parameters.isEmpty) {\n    return baseUrl;\n  }\n  \n  List<String> queryParts = [];\n  \n  parameters.forEach((key, value) {\n    String queryPart = '$key=$value';\n    if (encode) {\n      queryPart = Uri.encodeComponent(key) + '=' + Uri.encodeComponent(value);\n    }\n    queryParts.add(queryPart);\n  });\n  \n  return '$baseUrl?${queryParts.join('&')}';\n}", "explanation": "这个示例全面展示了Dart中可选参数和默认值的多种用法。首先展示了可选位置参数的定义方式（使用方括号）和调用方式，包括不提供可选参数、提供部分可选参数和提供所有可选参数的情况。然后演示了带默认值的可选位置参数，默认值在未提供参数时会被使用。接着展示了可选命名参数的定义方式（使用花括号）和调用方式，调用时需要指定参数名。示例还包括了带默认值的可选命名参数，以及使用required关键字标记必需的命名参数。此外，示例展示了如何混合使用位置参数和命名参数，以及如何处理不同组合的可选参数。最后，展示了将函数作为可选参数传递的用法，以及一个构建查询字符串的较复杂示例。这些示例说明了Dart函数参数系统的灵活性，以及如何使用可选参数和默认值使函数调用更加灵活和易读。"}]}}, {"name": "Named Parameters and Positional Parameters", "trans": ["命名参数与位置参数"], "usage": {"syntax": "// 位置参数（按顺序传递）\n返回类型 函数名(参数类型1 参数名1, 参数类型2 参数名2) {\n  // 函数体\n}\n\n// 调用位置参数函数\n函数名(值1, 值2);\n\n// 命名参数（按名称传递）\n返回类型 函数名({参数类型1? 参数名1, 参数类型2? 参数名2}) {\n  // 函数体\n}\n\n// 调用命名参数函数\n函数名(参数名1: 值1, 参数名2: 值2);\n\n// 必需的命名参数\n返回类型 函数名({required 参数类型1 参数名1, required 参数类型2 参数名2}) {\n  // 函数体\n}\n\n// 混合使用位置参数和命名参数\n返回类型 函数名(位置参数1, 位置参数2, {命名参数1, 命名参数2}) {\n  // 函数体\n}", "description": "Dart提供了两种主要的参数传递方式：位置参数和命名参数，它们有不同的特点和适用场景。位置参数是最基本的参数形式，在函数定义时直接列出，调用时按照定义的顺序传递值。位置参数适用于参数少且顺序明确的情况，例如`add(a, b)`。命名参数在定义时用花括号{}包围，调用时通过`参数名: 值`的形式传递，顺序不重要。命名参数默认是可选的，可以用required关键字标记为必需。命名参数的主要优势是提高了代码的可读性，特别是当函数有多个参数时，能清晰表明每个参数的用途，如`createUser(name: 'Alice', age: 30)`。位置参数和命名参数可以混合使用，但位置参数必须在命名参数之前。在选择参数类型时，如果参数数量少且顺序明确，位置参数更简洁；如果参数较多或希望提高调用代码的可读性，命名参数更适合。从Dart 2.9开始，函数调用时可以使用命名参数语法为位置参数命名，如`add(a: 5, b: 3)`，这提高了代码的可读性，但参数顺序仍需遵循位置要求。", "parameters": [], "returnValue": "由函数声明的返回类型决定", "examples": [{"code": "void main() {\n  // 使用位置参数\n  int sum = add(5, 3);\n  print('5 + 3 = $sum');\n  \n  // 调用时可以为位置参数提供名称（但仍按位置传递）\n  sum = add(a: 10, b: 7);\n  print('10 + 7 = $sum');\n  \n  // 使用命名参数\n  createUser(name: 'Alice', age: 30, role: 'Admin');\n  \n  // 命名参数可以以任意顺序传递\n  createUser(role: 'User', name: 'Bob', age: 25);\n  \n  // 可选的命名参数可以省略\n  createUser(name: 'Charlie', age: 35); // 省略role参数\n  \n  // 必需的命名参数不能省略\n  registerUser( username: 'dave123',email: '<EMAIL>');\n  \n  // 混合使用位置参数和命名参数\n  displayInfo('Product XYZ', 19.99, category: 'Electronics', inStock: true);\n  \n  // 省略可选的命名参数\n  displayInfo('Book ABC', 9.99);\n  \n  // 对比位置参数和命名参数的可读性\n  // 位置参数：不清楚每个参数的含义\n  configureWithPositional(12, 'dark', true, 'en-US');\n  \n  // 命名参数：清晰表明每个参数的用途\n  configureWithNamed(\n    fontSize: 12,\n    theme: 'dark',\n    animated: true,\n    language: 'en-US'\n  );\n  \n  // 使用命名参数处理复杂对象配置\n  final button = createButton(\n    text: '点击我',\n    width: 200,\n    height: 50,\n    backgroundColor: 'blue',\n    textColor: 'white',\n    onPressed: () {\n      print('按钮被点击了！');\n    }\n  );\n  \n  // 模拟使用按钮\n  simulateButtonClick(button);\n}\n\n// 使用位置参数的函数\nint add(int a, int b) {\n  return a + b;\n}\n\n// 使用命名参数的函数（所有参数都是可选的）\nvoid createUser({String? name, int? age, String? role}) {\n  print('创建用户:');\n  print('- 姓名: ${name ?? \"未指定\"}');\n  print('- 年龄: ${age ?? \"未指定\"}');\n  print('- 角色: ${role ?? \"普通用户\"}');\n}\n\n// 使用必需的命名参数\nvoid registerUser({required String username, required String email, int? age}) {\n  print('注册用户:');\n  print('- 用户名: $username');\n  print('- 电子邮件: $email');\n  \n  if (age != null) {\n    print('- 年龄: $age');\n  }\n}\n\n// 混合使用位置参数和命名参数\nvoid displayInfo(String name, double price, {String? category, bool inStock = true}) {\n  print('产品信息:');\n  print('- 名称: $name');\n  print('- 价格: \\$${price.toStringAsFixed(2)}');\n  \n  if (category != null) {\n    print('- 类别: $category');\n  }\n  \n  print('- 库存: ${inStock ? \"有货\" : \"缺货\"}');\n}\n\n// 使用位置参数的配置函数（可读性较差）\nvoid configureWithPositional(int fontSize, String theme, bool animated, String language) {\n  print('配置（位置参数）:');\n  print('- 字体大小: $fontSize');\n  print('- 主题: $theme');\n  print('- 动画: ${animated ? \"启用\" : \"禁用\"}');\n  print('- 语言: $language');\n}\n\n// 使用命名参数的配置函数（可读性更好）\nvoid configureWithNamed({\n  required int fontSize,\n  required String theme,\n  required bool animated,\n  required String language\n}) {\n  print('配置（命名参数）:');\n  print('- 字体大小: $fontSize');\n  print('- 主题: $theme');\n  print('- 动画: ${animated ? \"启用\" : \"禁用\"}');\n  print('- 语言: $language');\n}\n\n// 使用命名参数处理复杂对象配置\nMap<String, dynamic> createButton({\n  required String text,\n  required double width,\n  required double height,\n  required String backgroundColor,\n  required String textColor,\n  required Function onPressed\n}) {\n  print('创建按钮:');\n  print('- 文本: $text');\n  print('- 尺寸: ${width}x${height}');\n  print('- 背景色: $backgroundColor');\n  print('- 文本颜色: $textColor');\n  \n  // 返回一个模拟的按钮对象\n  return {\n    'text': text,\n    'width': width,'height': height,'backgroundColor': backgroundColor,'textColor': textColor,'onPressed': onPressed};\n}\n\n// 模拟按钮点击\nvoid simulateButtonClick(Map<String, dynamic> button) {\n  print('\\n模拟点击按钮 \"${button[\"text\"]}\"');\n  Function onPressed = button['onPressed'];\n  onPressed();\n}\n\n// 函数返回类型为函数，使用命名参数\nFunction createGreeter({required String greeting, String punctuation = '!'}) {\n  // 返回一个函数，该函数接受一个名字参数\n  return (String name) {\n    return '$greeting, $name$punctuation';\n  };\n}\n\n// 使用命名参数的高阶函数\nvoid processItems<T>(List<T> items, {required T Function(T item) processor}) {\n  for (var item in items) {\n    var result = processor(item);\n    print('处理 $item -> $result');\n  }\n}", "explanation": "这个示例全面比较了Dart中位置参数和命名参数的用法和特点。首先展示了使用位置参数的函数定义和调用方式，参数按照定义的顺序传递。然后演示了使用命名参数的函数，调用时通过参数名指定值，可以以任意顺序传递。示例还包括了必需的命名参数（使用required关键字标记）以及混合使用位置参数和命名参数的情况。特别对比了使用位置参数和命名参数实现相同功能的两个配置函数，说明了命名参数在提高代码可读性方面的优势，尤其是当函数有多个参数时。最后，示例展示了使用命名参数处理复杂对象配置的场景，以及在高阶函数中使用命名参数的方法。这些示例说明了如何根据不同的需求选择合适的参数类型，以及如何利用命名参数提高代码的清晰度和可维护性。"}]}}, {"name": "Anonymous Functions and Arrow Functions", "trans": ["匿名函数与箭头函数"], "usage": {"syntax": "// 匿名函数\nvar 变量名 = (参数列表) {\n  // 函数体\n  return 返回值;\n};\n\n// 调用匿名函数\n变量名(参数);\n\n// 直接声明并调用匿名函数\n((参数列表) {\n  // 函数体\n})(实际参数);\n\n// 箭头函数（单表达式函数的简写）\nvar 变量名 = (参数列表) => 表达式;\n\n// 在集合方法中使用匿名函数\n列表.forEach((元素) {\n  // 对元素的操作\n});\n\n// 在集合方法中使用箭头函数\n列表.map((元素) => 转换表达式).toList();", "description": "匿名函数和箭头函数是Dart中创建函数的简洁方式，特别适用于将函数作为参数传递或简短的函数定义。匿名函数（也称为lambda或闭包）是没有名称的函数，可以赋值给变量或直接作为参数传递给其他函数。匿名函数的语法与普通函数类似，但没有函数名，由参数列表、函数体和可选的返回值组成。箭头函数（=>）是单表达式函数的简写形式，表达式的结果会被自动返回，无需显式的return语句。箭头函数特别适用于简单的转换或计算操作，如在map、where、forEach等高阶函数中使用。匿名函数和箭头函数都可以访问其定义作用域内的变量（闭包特性），这使它们在回调、事件处理和函数式编程中非常有用。与命名函数相比，匿名函数的主要区别是它们没有名称，不能直接递归调用自身，通常用于不需要重复使用的简短逻辑。从Dart 2.12开始，箭头函数也支持空安全特性，可以与可空类型一起使用。", "parameters": [], "returnValue": "由函数体或箭头表达式确定", "examples": [{"code": "void main() {\n  // 1. 基本匿名函数\n  var greet = (String name) {\n    return 'Hello, $name!';\n  };\n  \n  // 调用匿名函数\n  print(greet('Alice')); // 输出: Hello, Alice!\n  \n  // 2. 箭头函数（单表达式函数）\n  var multiply = (int a, int b) => a * b;\n  print('3 * 4 = ${multiply(3, 4)}'); // 输出: 3 * 4 = 12\n  \n  // 3. 在集合操作中使用匿名函数\n  List<int> numbers = [1, 2, 3, 4, 5];\n  \n  // 使用匿名函数过滤偶数\n  var evenNumbers = numbers.where((number) {\n    return number % 2 == 0;\n  }).toList();\n  print('偶数: $evenNumbers'); // 输出: 偶数: [2, 4]\n  \n  // 使用箭头函数简化集合操作\n  var doubled = numbers.map((n) => n * 2).toList();\n  print('加倍后: $doubled'); // 输出: 加倍后: [2, 4, 6, 8, 10]\n  \n  // 4. 函数作为参数\n  void processNumbers(List<int> nums, bool Function(int) filter) {\n    for (var n in nums) {\n      if (filter(n)) {\n        print('通过过滤: $n');\n      }\n    }\n  }\n  \n  // 传递匿名函数作为参数\n  processNumbers(numbers, (n) => n > 3);\n  \n  // 5. 自执行匿名函数\n  var result = ((int x, int y) {\n    var sum = x + y;\n    return sum * sum;\n  })(2, 3);\n  print('自执行函数结果: $result'); // 输出: 自执行函数结果: 25\n  \n  // 6. 闭包特性：访问外部变量\n  String prefix = '用户';\n  var userFormatter = (String name) {\n    return '$prefix: $name'; // 访问外部变量prefix\n  };\n  print(userFormatter('Bob')); // 输出: 用户: Bob\n  \n  // 修改外部变量后再次调用\n  prefix = '客户';\n  print(userFormatter('Charlie')); // 输出: 客户: Charlie\n  \n  // 7. 在forEach中使用匿名函数\n  var fruits = ['apple', 'banana', 'orange'];\n  fruits.forEach((fruit) {\n    print('我喜欢吃 $fruit');\n  });\n  \n  // 8. 创建计数器闭包\n  var counter = createCounter();\n  print('计数: ${counter()}'); // 输出: 计数: 1\n  print('计数: ${counter()}'); // 输出: 计数: 2\n  print('计数: ${counter()}'); // 输出: 计数: 3\n  \n  // 9. 使用箭头函数进行排序\n  var names = ['Zack', 'Alice', 'Bob', 'Charlie'];\n  names.sort((a, b) => a.compareTo(b));\n  print('排序后的名字: $names'); // 输出: 排序后的名字: [Alice, Bob, Charlie, Zack]\n  \n  // 10. 函数类型和匿名函数\n  // 声明接受两个int并返回int的函数类型变量\n  int Function(int, int) operation;\n  \n  // 根据条件分配不同的匿名函数\n  bool isAdding = true;\n  if (isAdding) {\n    operation = (a, b) => a + b;\n  } else {\n    operation = (a, b) => a - b;\n  }\n  \n  print('操作结果: ${operation(5, 3)}'); // 输出: 操作结果: 8\n  \n  // 11. 在异步操作中使用匿名函数\n  // 模拟异步操作\n  Future<void> fetchData(String url, void Function(String) onSuccess) async {\n    // 模拟网络延迟\n    await Future.delayed(Duration(milliseconds: 100));\n    // 模拟获取的数据\n    String data = '来自 $url 的数据';\n    onSuccess(data);\n  }\n  \n  // 使用匿名函数作为回调\n  fetchData('example.com/api', (data) {\n    print('获取到数据: $data');\n  });\n  \n  // 12. 链式使用箭头函数\n  var prices = [99.5, 49.9, 19.95, 29.99];\n  var formattedPrices = prices\n      .where((price) => price < 50) // 过滤低于50的价格\n      .map((price) => '\\$${price.toStringAsFixed(2)}') // 格式化为货币\n      .toList();\n  print('格式化后的价格: $formattedPrices');\n}\n\n// 创建一个闭包计数器函数\nFunction createCounter() {\n  int count = 0;\n  \n  // 返回一个匿名函数，每次调用时count加1\n  return () {\n    count++;\n    return count;\n  };\n}\n\n// 使用箭头函数的高阶函数\nList<T> filterList<T>(List<T> list, bool Function(T) predicate) =>\n    list.where(predicate).toList();\n\n// 返回函数的函数（函数工厂）\nFunction makeAdder(int addBy) => (int i) => i + addBy;\n\n// 在类中使用箭头函数\nclass Calculator {\n  // 使用箭头函数定义方法\n  int add(int a, int b) => a + b;\n  int subtract(int a, int b) => a - b;\n  int multiply(int a, int b) => a * b;\n  double divide(int a, int b) => b != 0 ? a / b : throw Exception('除数不能为零');\n}", "explanation": "这个示例全面展示了Dart中匿名函数和箭头函数的使用方式。首先介绍了基本的匿名函数定义和调用，然后展示了箭头函数作为单表达式函数的简写形式。示例中包含了在集合操作中使用匿名函数和箭头函数的常见场景，如在where、map、forEach等方法中作为参数传递。特别展示了匿名函数的闭包特性，即能够访问和记住其定义环境中的变量，这在createCounter函数中尤为明显。此外，示例还展示了自执行匿名函数、函数作为参数传递、函数类型变量、在异步操作中使用匿名函数作为回调，以及链式使用箭头函数等高级用法。最后，展示了在类中使用箭头函数定义方法，以及如何创建返回函数的函数（高阶函数）。这些示例说明了匿名函数和箭头函数在提高代码简洁性和灵活性方面的优势，特别是在函数式编程风格中的应用。"}]}}, {"name": "Functions as Objects", "trans": ["函数作为对象"], "usage": {"syntax": "// 将函数赋值给变量\nFunction 变量名 = 函数名;\n\n// 函数类型声明\n返回类型 Function(参数类型1, 参数类型2) 变量名;\n\n// 函数作为参数\nvoid 高阶函数(参数类型 参数名, 函数类型 回调函数) {\n  // 使用回调函数\n  回调函数(参数);\n}\n\n// 函数作为返回值\n函数类型 函数工厂() {\n  return (参数) {\n    // 函数体\n    return 返回值;\n  };\n}", "description": "Dart中的函数是一等公民（first-class objects），这意味着函数可以像其他任何对象一样被操作和传递。函数可以赋值给变量，作为参数传递给其他函数，或作为函数的返回值。这种特性使得函数式编程在Dart中成为可能，允许开发者创建更加灵活和可组合的代码。函数类型可以显式声明，例如`int Function(String, bool)`表示一个接受String和bool参数并返回int的函数。接受函数作为参数的函数称为高阶函数（higher-order functions），常见于回调、事件处理、集合操作（如map、where、forEach）等场景。返回函数的函数通常用于创建函数工厂或闭包，允许动态生成具有特定行为的函数。通过函数作为对象的特性，可以实现函数组合、部分应用（partial application）、柯里化（currying）等函数式编程技术，提高代码的抽象级别和复用性。", "parameters": [], "returnValue": "取决于函数的定义", "examples": [{"code": "void main() {\n  // 1. 将函数赋值给变量\n  Function greet = sayHello;\n  greet(); // 输出: Hello!\n  \n  // 2. 使用具体的函数类型\n  int Function(int, int) operation = add;\n  print('5 + 3 = ${operation(5, 3)}'); // 输出: 5 + 3 = 8\n  \n  // 改变引用的函数\n  operation = multiply;\n  print('5 * 3 = ${operation(5, 3)}'); // 输出: 5 * 3 = 15\n  \n  // 3. 函数作为参数（高阶函数）\n  processNumbers([1, 2, 3, 4, 5], doubleValue);\n  processNumbers([1, 2, 3, 4, 5], squareValue);\n  \n  // 使用匿名函数作为参数\n  processNumbers([1, 2, 3, 4, 5], (int n) {\n    return n * 10;\n  });\n  \n  // 使用箭头函数简化\n  processNumbers([1, 2, 3, 4, 5], (n) => n - 1);\n  \n  // 4. 函数作为返回值\n  var addFive = createAdder(5);\n  var addTen = createAdder(10);\n  \n  print('8 + 5 = ${addFive(8)}'); // 输出: 8 + 5 = 13\n  print('8 + 10 = ${addTen(8)}'); // 输出: 8 + 10 = 18\n  \n  // 5. 函数组合\n  Function<T, R, S>(R Function(T) f, S Function(R) g) compose =>\n      (T x) => g(f(x));\n  \n  var doubleIt = (int x) => x * 2;\n  var addThree = (int x) => x + 3;\n  \n  var doubleThenAddThree = compose(doubleIt, addThree);\n  var addThreeThenDouble = compose(addThree, doubleIt);\n  \n  print('对5先加倍再加3: ${doubleThenAddThree(5)}'); // 输出: 对5先加倍再加3: 13\n  print('对5先加3再加倍: ${addThreeThenDouble(5)}'); // 输出: 对5先加3再加倍: 16\n  \n  // 6. 函数作为类成员和参数\n  var calculator = Calculator();\n  \n  // 使用不同的操作函数\n  print('10 和 5 的和: ${calculator.calculate(10, 5, add)}');\n  print('10 和 5 的积: ${calculator.calculate(10, 5, multiply)}');\n  print('10 和 5 的差: ${calculator.calculate(10, 5, (a, b) => a - b)}');\n  \n  // 7. 使用函数类型的回调\n  fetchData('https://example.com/api', (data) {\n    print('获取到数据: $data');\n  }, (error) {\n    print('错误: $error');\n  });\n  \n  // 8. 使用typedef简化函数类型声明\n  BinaryOperation subtraction = (a, b) => a - b;\n  print('15 - 7 = ${subtraction(15, 7)}'); // 输出: 15 - 7 = 8\n  \n  // 9. 部分应用和柯里化\n  var multiply2 = multiply.curry(2);\n  print('2 * 8 = ${multiply2(8)}'); // 输出: 2 * 8 = 16\n  \n  // 10. 函数式编程示例: 处理列表\n  var numbers = [1, 2, 3, 4, 5];\n  \n  // 使用map转换\n  var doubled = numbers.map(doubleValue).toList();\n  print('加倍后: $doubled'); // 输出: 加倍后: [2, 4, 6, 8, 10]\n  \n  // 使用where过滤\n  var evenNumbers = numbers.where((n) => n % 2 == 0).toList();\n  print('偶数: $evenNumbers'); // 输出: 偶数: [2, 4]\n  \n  // 使用reduce聚合\n  var sum = numbers.reduce((value, element) => value + element);\n  print('总和: $sum'); // 输出: 总和: 15\n  \n  // 链式调用\n  var result = numbers\n      .map((n) => n * 2)\n      .where((n) => n > 5)\n      .fold(0, (prev, curr) => prev + curr);\n  print('大于5的双倍值之和: $result'); // 输出: 大于5的双倍值之和: 18\n}\n\n// 基本函数\nvoid sayHello() {\n  print('Hello!');\n}\n\n// 简单算术函数\nint add(int a, int b) {\n  return a + b;\n}\n\nint multiply(int a, int b) {\n  return a * b;\n}\n\n// 用于转换的函数\nint doubleValue(int n) {\n  return n * 2;\n}\n\nint squareValue(int n) {\n  return n * n;\n}\n\n// 高阶函数：接受函数作为参数\nvoid processNumbers(List<int> numbers, int Function(int) processor) {\n  var results = [];\n  for (var number in numbers) {\n    results.add(processor(number));\n  }\n  print('处理结果: $results');\n}\n\n// 返回函数的函数\nint Function(int) createAdder(int addBy) {\n  // 返回一个函数，该函数会将参数加上addBy\n  return (int x) {\n    return x + addBy;\n  };\n}\n\n// 使用函数的类\nclass Calculator {\n  int calculate(int a, int b, int Function(int, int) operation) {\n    return operation(a, b);\n  }\n}\n\n// 模拟异步操作的函数，使用回调\nvoid fetchData(\n    String url,void Function(String) onSuccess,void Function(String) onError) {\n  // 模拟网络请求\n  try {\n    // 假设成功获取数据\n    var data = '来自 $url 的模拟数据';\n    onSuccess(data);\n  } catch (e) {\n    onError(e.toString());\n  }\n}\n\n// 使用typedef定义函数类型\ntypedef BinaryOperation = int Function(int a, int b);\n\n// 扩展Function类以支持柯里化\nextension FunctionExtension<T, U> on U Function(T) {\n  // 返回一个已经应用了第一个参数的新函数\n  U Function(T) curry(T x) {\n    return (T y) => this(x);\n  }\n}\n\n// 柯里化的扩展，用于两个参数的函数\nextension BinaryFunctionExtension<T, U, V> on V Function(T, U) {\n  // 部分应用第一个参数\n  V Function(U) curry(T x) {\n    return (U y) => this(x, y);\n  }\n}", "explanation": "这个示例全面展示了Dart中函数作为一等公民的各种用法。首先展示了如何将函数赋值给变量，包括使用通用的Function类型和更具体的函数类型定义。然后演示了高阶函数的概念，即函数可以接受其他函数作为参数，这在processNumbers函数中得到了展示，它能够使用不同的处理函数对数字列表进行操作。示例还展示了返回函数的函数createAdder，它根据传入的值创建一个新的加法函数。进一步，示例展示了更高级的概念，如函数组合、部分应用和柯里化，这些是函数式编程中的重要技术。通过Calculator类，示例展示了如何在类中使用函数作为参数。最后，示例通过集合操作(map、where、reduce、fold)展示了函数式编程在实际应用中的强大之处，允许通过链式调用进行复杂的数据转换和处理。通过使用typedef和扩展，示例还展示了如何简化函数类型声明和增强函数的功能。这些功能共同使Dart成为一种支持多种编程范式的灵活语言。"}]}}, {"name": "Function Assignments", "trans": ["函数作业"], "usage": {"syntax": "// 编写一个函数\n// 函数描述和要求\n返回类型 函数名(参数列表) {\n  // 实现函数逻辑\n  return 返回值;\n}", "description": "以下是一系列函数相关的练习题，旨在帮助你巩固Dart函数的知识和用法。这些练习涵盖了基本函数定义、参数传递、返回值、匿名函数、箭头函数以及函数作为对象等概念。每个练习都有明确的要求和预期输出，建议按顺序完成，循序渐进地掌握Dart函数的各个方面。完成这些练习将帮助你更好地理解函数在Dart中的重要性和灵活性，为进一步学习面向对象编程和异步编程打下基础。", "parameters": [], "returnValue": "无", "examples": [{"code": "void main() {\n  // 练习1: 编写一个计算两个数字平均值的函数\n  // 要求: 接受两个double参数，返回它们的平均值\n  \n  // 练习2: 创建一个判断数字是否为质数的函数\n  // 要求: 接受一个整数参数，如果是质数返回true，否则返回false\n  \n  // 练习3: 编写一个计算阶乘的函数\n  // 要求: 使用递归方式实现，接受一个非负整数n，返回n!\n  \n  // 练习4: 创建一个返回数字列表中最大值的函数\n  // 要求: 使用可选参数，允许指定是否忽略负数\n  \n  // 练习5: 实现一个格式化个人信息的函数\n  // 要求: 使用命名参数接收姓名(必需)、年龄和职业(可选)，返回格式化的字符串\n  \n  // 练习6: 创建一个函数，对列表中的每个元素应用指定操作\n  // 要求: 函数接受一个整数列表和一个函数参数，返回处理后的新列表\n  \n  // 练习7: 实现一个通用的过滤函数\n  // 要求: 接受任意类型的列表和一个判断函数，返回符合条件的元素列表\n  \n  // 练习8: 编写一个创建计数器的函数\n  // 要求: 返回一个函数，每次调用该函数时返回递增的计数值\n  \n  // 练习9: 实现一个函数组合器\n  // 要求: 接受两个函数f和g，返回一个新函数h，使得h(x) = g(f(x))\n  \n  // 练习10: 创建一个多功能计算器\n  // 要求: 设计一个Calculator类，包含多种操作方法，并能通过函数参数自定义操作\n}\n\n// 练习1参考实现\ndouble calculateAverage(double a, double b) {\n  return (a + b) / 2;\n}\n\n// 练习2参考实现\nbool isPrime(int number) {\n  if (number <= 1) return false;\n  if (number <= 3) return true;\n  if (number % 2 == 0 || number % 3 == 0) return false;\n  \n  int i = 5;\n  while (i * i <= number) {\n    if (number % i == 0 || number % (i + 2) == 0) return false;\n    i += 6;\n  }\n  return true;\n}\n\n// 练习3参考实现\nint factorial(int n) {\n  if (n <= 1) return 1;\n  return n * factorial(n - 1);\n}\n\n// 练习4参考实现\nint findMax(List<int> numbers, {bool ignoreNegative = false}) {\n  if (numbers.isEmpty) throw ArgumentError('列表不能为空');\n  \n  List<int> validNumbers = ignoreNegative\n      ? numbers.where((n) => n >= 0).toList()\n      : numbers;\n  \n  if (validNumbers.isEmpty) return 0; // 如果忽略负数后列表为空\n  \n  int max = validNumbers[0];\n  for (int number in validNumbers) {\n    if (number > max) max = number;\n  }\n  return max;\n}\n\n// 练习5参考实现\nString formatPersonInfo({required String name, int? age, String? occupation}) {\n  String info = '姓名: $name';\n  \n  if (age != null) {\n    info += ', 年龄: $age岁';\n  }\n  \n  if (occupation != null) {\n    info += ', 职业: $occupation';\n  }\n  \n  return info;\n}\n\n// 练习6参考实现\nList<int> applyToEach(List<int> list, int Function(int) operation) {\n  return list.map(operation).toList();\n}\n\n// 练习7参考实现\nList<T> filter<T>(List<T> items, bool Function(T) test) {\n  List<T> result = [];\n  for (var item in items) {\n    if (test(item)) {\n      result.add(item);\n    }\n  }\n  return result;\n}\n\n// 练习8参考实现\nFunction createCounter() {\n  int count = 0;\n  return () {\n    count++;\n    return count;\n  };\n}\n\n// 练习9参考实现\nR Function(T) compose<T, U, R>(U Function(T) f, R Function(U) g) {\n  return (T x) => g(f(x));\n}\n\n// 练习10参考实现\nclass Calculator {\n  int add(int a, int b) => a + b;\n  int subtract(int a, int b) => a - b;\n  int multiply(int a, int b) => a * b;\n  double divide(int a, int b) => b != 0 ? a / b : throw Exception('除数不能为零');\n  \n  dynamic calculate(int a, int b, Function operation) {\n    return operation(a, b);\n  }\n  \n  List<int> processAll(List<int> numbers, int Function(int) processor) {\n    return numbers.map(processor).toList();\n  }\n}", "explanation": "这个示例提供了10个函数相关的练习题，涵盖了Dart函数的各个方面。练习1-3专注于基本函数定义，包括简单计算、条件判断和递归实现。练习4-5涉及可选参数和命名参数的使用，展示了Dart参数系统的灵活性。练习6-7引入了函数作为参数的概念，演示了高阶函数的基本应用。练习8展示了闭包的概念，通过返回一个能记住状态的函数。练习9演示了函数组合的概念，这是函数式编程的重要技术。最后，练习10综合运用前面的知识，实现一个多功能计算器类，包含方法和使用函数作为参数的能力。每个练习都提供了参考实现，学习者可以先尝试自己解决，然后参考实现进行对比学习。这些练习由简单到复杂，帮助学习者循序渐进地掌握Dart函数的核心概念和实际应用技巧。"}]}}]}