{"name": "OAuth2 and JWT", "trans": ["OAuth2与JWT"], "methods": [{"name": "OAuth2 Authorization Modes", "trans": ["OAuth2授权模式"], "usage": {"syntax": "配置spring-security-oauth2依赖，选择授权码、密码、简化、客户端模式", "description": "Spring Boot支持OAuth2多种授权模式，常用有授权码、密码、简化、客户端模式，适合不同场景。", "parameters": [{"name": "spring-security-oauth2", "description": "OAuth2核心依赖"}, {"name": "授权模式", "description": "authorization_code、password、implicit、client_credentials"}], "returnValue": "获取到的Access Token或授权码", "examples": [{"code": "# application.properties\nspring.security.oauth2.client.registration.myclient.client-id=clientid\nspring.security.oauth2.client.registration.myclient.client-secret=secret\nspring.security.oauth2.client.registration.myclient.authorization-grant-type=authorization_code\nspring.security.oauth2.client.registration.myclient.redirect-uri=http://localhost:8080/login/oauth2/code/myclient", "explanation": "配置不同授权模式，前后端可获取Token。"}]}}, {"name": "JWT Principle and Integration", "trans": ["JWT原理与集成"], "usage": {"syntax": "引入jjwt或spring-security-oauth2-jose依赖，配置JWT参数", "description": "JWT是一种无状态令牌，常用于分布式认证。Spring Boot可集成jjwt或spring-security-oauth2-jose实现JWT签发与校验。", "parameters": [{"name": "jjwt", "description": "常用JWT实现库"}, {"name": "JWT签名密钥", "description": "用于签发和校验Token"}], "returnValue": "签发和校验的JWT Token字符串", "examples": [{"code": "// 签发Token\nString token = Jwts.builder().setSubject(\"user\").signWith(SignatureAlgorithm.HS256, \"secret\").compact();\n// 校验Token\nClaims claims = Jwts.parser().setSigningKey(\"secret\").parseClaimsJws(token).getBody();", "explanation": "JWT可实现无状态认证，适合微服务和前后端分离。"}]}}, {"name": "Single Sign-On (SSO)", "trans": ["单点登录（SSO）"], "usage": {"syntax": "集成Spring Security OAuth2 Client实现SSO", "description": "通过OAuth2 Client集成第三方认证（如GitHub、企业微信等）实现单点登录，提升用户体验。", "parameters": [{"name": "OAuth2 Client", "description": "第三方登录集成"}, {"name": "redirect-uri", "description": "认证成功回调地址"}], "returnValue": "统一认证后的用户信息", "examples": [{"code": "# application.properties\nspring.security.oauth2.client.registration.github.client-id=xxx\nspring.security.oauth2.client.registration.github.client-secret=yyy\nspring.security.oauth2.client.registration.github.redirect-uri=http://localhost:8080/login/oauth2/code/github", "explanation": "配置OAuth2 Client即可实现第三方SSO。"}]}}, {"name": "Token Refresh and Invalidation", "trans": ["Token刷新与失效"], "usage": {"syntax": "实现Refresh Token机制和Token失效策略", "description": "可通过OAuth2的Refresh Token机制实现Token续期，通过黑名单等方式实现Token失效，保障安全。", "parameters": [{"name": "Refresh <PERSON>", "description": "用于获取新Access Token"}, {"name": "Token黑名单", "description": "实现Token主动失效"}], "returnValue": "可续期和可失效的Token机制", "examples": [{"code": "// 刷新Token\nPOST /oauth/token?grant_type=refresh_token&refresh_token=xxx\n// 失效Token\n将Token加入黑名单，拦截校验时拒绝访问。", "explanation": "Refresh Token提升用户体验，黑名单机制提升安全性。"}]}}, {"name": "OAuth2 and JWT Assignment", "trans": ["OAuth2与JWT练习"], "usage": {"syntax": "# OAuth2与JWT练习", "description": "完成以下练习，巩固Spring Boot OAuth2与JWT相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 配置OAuth2依赖和授权模式。\n2. 集成JWT实现Token签发与校验。\n3. 实现第三方SSO登录。\n4. 实现Token刷新与失效机制。", "explanation": "通过这些练习掌握Spring Boot OAuth2与JWT的核心用法。"}]}}]}