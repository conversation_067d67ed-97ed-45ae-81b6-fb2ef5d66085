{"name": "Integration Testing", "trans": ["集成测试"], "methods": [{"name": "Component Integration", "trans": ["组件集成"], "usage": {"syntax": "import { render, screen } from '@testing-library/react';\nrender(<App />);\nexpect(screen.getByText('内容')).toBeInTheDocument();", "description": "组件集成测试关注多个组件组合后的整体行为，验证它们之间的交互和数据流。", "parameters": [{"name": "App", "description": "包含多个子组件的根组件。"}], "returnValue": "无返回值，断言通过即测试通过。", "examples": [{"code": "import { render, screen } from '@testing-library/react';\nfunction Header() { return <div>头部</div>; }\nfunction Main() { return <div>内容</div>; }\nfunction App() { return (<><Header /><Main /></>); }\ntest('集成渲染', () => {\n  render(<App />);\n  expect(screen.getByText('头部')).toBeInTheDocument();\n  expect(screen.getByText('内容')).toBeInTheDocument();\n});", "explanation": "测试多个组件组合后能否正确渲染。"}]}}, {"name": "Routing Testing", "trans": ["路由测试"], "usage": {"syntax": "import { render, screen } from '@testing-library/react';\nimport { MemoryRouter } from 'react-router-dom';\nrender(<MemoryRouter initialEntries={['/about']}><App /></MemoryRouter>);\nexpect(screen.getByText('关于')).toBeInTheDocument();", "description": "路由测试用于验证不同路由下组件的渲染和跳转行为，常用MemoryRouter模拟路由环境。", "parameters": [{"name": "App", "description": "包含路由的根组件。"}, {"name": "initialEntries", "description": "初始路由路径数组。"}], "returnValue": "无返回值，断言通过即测试通过。", "examples": [{"code": "import { render, screen } from '@testing-library/react';\nimport { MemoryRouter, Route, Routes } from 'react-router-dom';\nfunction About() { return <div>关于</div>; }\nfunction App() {\n  return (<Routes><Route path='/about' element={<About />} /></Routes>);\n}\ntest('路由渲染', () => {\n  render(<MemoryRouter initialEntries={['/about']}><App /></MemoryRouter>);\n  expect(screen.getByText('关于')).toBeInTheDocument();\n});", "explanation": "测试指定路由下组件是否正确渲染。"}]}}, {"name": "State Management Testing", "trans": ["状态管理测试"], "usage": {"syntax": "import { render, screen } from '@testing-library/react';\nimport { Provider } from 'react-redux';\nrender(<Provider store={store}><App /></Provider>);\nexpect(screen.getByText('内容')).toBeInTheDocument();", "description": "状态管理测试关注Redux、Context等全局状态在组件间的流转和同步。", "parameters": [{"name": "App", "description": "使用全局状态的根组件。"}, {"name": "store", "description": "Redux或其他状态管理实例。"}], "returnValue": "无返回值，断言通过即测试通过。", "examples": [{"code": "import { render, screen } from '@testing-library/react';\nimport { Provider } from 'react-redux';\nimport { createStore } from 'redux';\nfunction reducer(state={value:1}) { return state; }\nconst store = createStore(reducer);\nfunction App() {\n  return <div>{store.getState().value}</div>;\n}\ntest('状态管理', () => {\n  render(<Provider store={store}><App /></Provider>);\n  expect(screen.getByText('1')).toBeInTheDocument();\n});", "explanation": "测试全局状态能否正确传递和渲染。"}]}}, {"name": "API Interaction Testing", "trans": ["API交互测试"], "usage": {"syntax": "jest.mock('api模块');\nimport { fetchData } from 'api模块';\nfetchData.mockResolvedValue(数据);\n// 组件内调用fetchData后断言渲染结果", "description": "API交互测试通过mock接口请求，验证组件在不同数据和状态下的表现。", "parameters": [{"name": "api模块", "description": "需要mock的API模块。"}, {"name": "fetchData", "description": "被mock的API函数。"}], "returnValue": "无返回值，断言通过即测试通过。", "examples": [{"code": "jest.mock('./api');\nimport { fetchData } from './api';\nfetchData.mockResolvedValue({ name: '<PERSON>' });\nfunction User() {\n  const [user, setUser] = useState();\n  useEffect(() => { fetchData().then(setUser); }, []);\n  if (!user) return null;\n  return <div>{user.name}</div>;\n}\ntest('API交互', async () => {\n  render(<User />);\n  expect(await screen.findByText('Tom')).toBeInTheDocument();\n});", "explanation": "通过mock API测试组件异步渲染。"}]}}, {"name": "User Flow Testing", "trans": ["用户流程测试"], "usage": {"syntax": "import { render, screen } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nrender(<App />);\n// 多步交互模拟和断言", "description": "用户流程测试关注用户在页面上的完整操作流程，模拟多步交互和状态变化。", "parameters": [{"name": "App", "description": "包含完整流程的根组件。"}], "returnValue": "无返回值，断言通过即测试通过。", "examples": [{"code": "import { render, screen } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nfunction Login() {\n  const [user, setUser] = useState('');\n  return (\n    <div>\n      <input value={user} onChange={e => setUser(e.target.value)} />\n      <button disabled={!user}>登录</button>\n    </div>\n  );\n}\ntest('用户流程', () => {\n  render(<Login />);\n  userEvent.type(screen.getByRole('textbox'), 'Tom');\n  expect(screen.getByRole('button')).not.toBeDisabled();\n});", "explanation": "模拟用户输入和按钮状态变化的完整流程。"}]}}, {"name": "Test Coverage", "trans": ["测试覆盖率"], "usage": {"syntax": "// 运行覆盖率\nnpm test -- --coverage\n// 查看coverage目录下的报告", "description": "测试覆盖率用于衡量测试用例对代码的覆盖程度，Jest可自动生成覆盖率报告。", "parameters": [], "returnValue": "无返回值，报告生成在coverage目录。", "examples": [{"code": "// 运行\nnpm test -- --coverage\n// 查看coverage/lcov-report/index.html", "explanation": "生成并查看测试覆盖率报告。"}]}}, {"name": "作业：集成测试实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 编写组件集成、路由、状态、API、用户流程测试\n// 2. 生成并分析测试覆盖率", "description": "通过实践组件集成、路由、状态、API、用户流程测试和覆盖率分析，掌握React集成测试方法。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 编写集成、路由、状态、API、流程测试\n// 2. 生成覆盖率报告", "explanation": "作业提示，学生需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nimport { render, screen } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nfunction App() {\n  const [user, setUser] = useState('');\n  return (\n    <div>\n      <input value={user} onChange={e => setUser(e.target.value)} />\n      <button disabled={!user}>登录</button>\n    </div>\n  );\n}\ntest('集成流程', () => {\n  render(<App />);\n  userEvent.type(screen.getByRole('textbox'), 'Tom');\n  expect(screen.getByRole('button')).not.toBeDisabled();\n});", "explanation": "集成测试的正确实现示例，覆盖用户流程。"}]}}]}