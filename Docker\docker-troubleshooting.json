{"name": "Troubleshooting", "trans": ["常见问题排查"], "methods": [{"name": "Container <PERSON><PERSON> Failure", "trans": ["容器无法启动"], "usage": {"syntax": "docker run ...\ndocker ps -a\ndocker logs <容器名>", "description": "当容器无法启动时，需通过docker ps -a查看状态，docker logs查看详细错误日志，定位问题原因。", "parameters": [{"name": "docker run", "description": "启动容器命令。"}, {"name": "docker ps -a", "description": "查看所有容器及状态。"}, {"name": "docker logs", "description": "查看容器启动日志。"}], "returnValue": "无返回值，通过日志定位启动失败原因。", "examples": [{"code": "docker run --name test busybox\ndocker ps -a\ndocker logs test", "explanation": "通过日志排查容器启动失败的原因。"}]}}, {"name": "Image Pull Failure", "trans": ["镜像拉取失败"], "usage": {"syntax": "docker pull <镜像名>\ndocker info\ndocker login", "description": "镜像拉取失败常见原因包括网络问题、仓库认证失败、镜像名错误等。可通过docker info检查配置，docker login认证，或更换镜像源。", "parameters": [{"name": "docker pull", "description": "拉取镜像命令。"}, {"name": "docker info", "description": "查看Docker配置信息。"}, {"name": "docker login", "description": "登录镜像仓库。"}], "returnValue": "无返回值，排查并解决拉取失败问题。", "examples": [{"code": "docker pull busybox\ndocker info\ndocker login", "explanation": "通过配置检查和登录解决镜像拉取失败。"}]}}, {"name": "Network Connectivity Issues", "trans": ["网络不通"], "usage": {"syntax": "docker network ls\ndocker network inspect <网络名>\ndocker exec <容器> ping <目标>\niptables -L", "description": "容器间或容器与外部网络不通时，可检查网络配置、网络模式、容器互通性及主机防火墙设置。", "parameters": [{"name": "docker network ls", "description": "列出所有网络。"}, {"name": "docker network inspect", "description": "查看网络详细信息。"}, {"name": "docker exec ... ping", "description": "容器内测试连通性。"}, {"name": "iptables -L", "description": "主机防火墙规则检查。"}], "returnValue": "无返回值，定位并解决网络不通问题。", "examples": [{"code": "docker network ls\ndocker network inspect bridge\ndocker exec myapp ping www.baidu.com\niptables -L", "explanation": "逐步排查容器网络不通的原因。"}]}}, {"name": "Volume Permission Issues", "trans": ["数据卷权限问题"], "usage": {"syntax": "docker volume ls\ndocker inspect <容器名>\nls -l <挂载目录>\nchmod/chown", "description": "数据卷权限问题常见于主机与容器用户不一致。需检查挂载目录权限，必要时调整属主或权限。", "parameters": [{"name": "docker volume ls", "description": "列出所有数据卷。"}, {"name": "docker inspect", "description": "查看挂载信息。"}, {"name": "ls -l", "description": "查看主机目录权限。"}, {"name": "chmod/chown", "description": "修改目录权限或属主。"}], "returnValue": "无返回值，解决数据卷权限导致的读写异常。", "examples": [{"code": "docker volume ls\ndocker inspect myapp\nls -l /data\nsudo chown -R 1000:1000 /data", "explanation": "通过调整主机目录属主解决容器权限问题。"}]}}, {"name": "Logs & Monitoring", "trans": ["日志与监控"], "usage": {"syntax": "docker logs <容器名>\ndocker stats\ndocker events", "description": "通过日志、资源监控和事件流，实时掌握容器运行状态，辅助问题定位和性能分析。", "parameters": [{"name": "docker logs", "description": "查看容器日志。"}, {"name": "docker stats", "description": "实时监控容器资源。"}, {"name": "docker events", "description": "查看容器事件流。"}], "returnValue": "无返回值，辅助排查和监控容器运行。", "examples": [{"code": "docker logs myapp\ndocker stats\ndocker events", "explanation": "通过日志和监控命令排查和分析问题。"}]}}, {"name": "Assignment: 问题排查实践", "trans": ["作业：问题排查实践"], "usage": {"syntax": "请完成以下任务：\n1. 模拟容器启动失败并排查原因\n2. 解决一次镜像拉取失败\n3. 排查并修复容器网络不通\n4. 处理一次数据卷权限异常\n5. 使用日志和监控工具定位问题", "description": "通过实际操作掌握常见Docker问题的排查与解决方法。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 1. 容器启动失败排查\ndocker run ...\ndocker logs ...\n# 2. 镜像拉取失败排查\ndocker pull ...\ndocker info\n# 3. 网络不通排查\ndocker network inspect ...\ndocker exec ... ping ...\n# 4. 数据卷权限修复\nls -l ...\nsudo chown ...\n# 5. 日志与监控\ndocker logs ...\ndocker stats", "explanation": "依次完成常见问题的模拟、排查与修复。"}]}}]}