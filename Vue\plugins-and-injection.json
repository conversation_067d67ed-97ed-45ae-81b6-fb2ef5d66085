{"name": "Plugins and Injection", "trans": ["依赖注入与插件"], "methods": [{"name": "Plugin Development", "trans": ["插件开发"], "usage": {"syntax": "const MyPlugin = {\n  install(app, options) {\n    // 安装逻辑\n  }\n}\napp.use(MyPlugin, options)", "description": "插件是扩展Vue功能的主要方式，需实现install方法，参数为app实例和可选配置。通过app.use注册。", "parameters": [{"name": "install(app, options)", "description": "插件安装方法，app为Vue应用实例，options为用户配置"}], "returnValue": "无返回值，通过app.use注册插件", "examples": [{"code": "const MyPlugin = {\n  install(app, options) {\n    app.config.globalProperties.$hello = () => 'hello'\n  }\n}\napp.use(MyPlugin)", "explanation": "开发一个简单插件，向全局添加$hello方法。"}]}}, {"name": "Global Properties and Methods", "trans": ["全局属性与方法"], "usage": {"syntax": "app.config.globalProperties.$name = value", "description": "通过app.config.globalProperties可为所有组件添加全局属性和方法，组件内通过this.$name访问。", "parameters": [{"name": "$name", "description": "全局属性或方法名"}, {"name": "value", "description": "属性值或方法体"}], "returnValue": "所有组件可访问的全局属性或方法", "examples": [{"code": "app.config.globalProperties.$api = () => fetch('/api')\n// 组件内\nthis.$api()", "explanation": "为全局添加$api方法，所有组件可用。"}]}}, {"name": "Global Component Registration", "trans": ["全局组件注册"], "usage": {"syntax": "app.component('MyComp', MyComp)", "description": "通过app.component可全局注册组件，所有子组件可直接使用，无需单独引入。", "parameters": [{"name": "name", "description": "全局组件名"}, {"name": "MyComp", "description": "组件对象"}], "returnValue": "全局注册的组件可在任意模板中使用", "examples": [{"code": "import MyButton from './MyButton.vue'\napp.component('MyButton', MyButton)\n// 任何组件模板中\n<MyButton />", "explanation": "全局注册MyButton组件，所有页面可用。"}]}}, {"name": "Plugin Lifecycle", "trans": ["插件生命周期"], "usage": {"syntax": "// 插件的install只会调用一次\napp.use(plugin)", "description": "插件的install方法在app.use时只会调用一次，适合做全局初始化、资源挂载等。", "parameters": [{"name": "install", "description": "插件安装钩子"}], "returnValue": "插件全局初始化，无返回值", "examples": [{"code": "const MyPlugin = {\n  install(app) {\n    // 只初始化一次\n  }\n}\napp.use(MyPlugin)", "explanation": "install只会被调用一次，适合全局初始化。"}]}}, {"name": "Plugins and Injection Assignment", "trans": ["依赖注入与插件练习"], "usage": {"syntax": "# 依赖注入与插件练习", "description": "完成以下练习，巩固插件开发与全局注入相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 编写一个插件，向全局添加$log方法。\n2. 全局注册一个自定义组件。\n3. 在插件install中做一次性初始化。\n4. 通过全局属性在组件中访问方法。", "explanation": "通过这些练习掌握插件开发与全局注入。"}]}}]}