{"name": "Triggers", "trans": ["触发器"], "methods": [{"name": "Create Trigger", "trans": ["创建触发器"], "usage": {"syntax": "CREATE TRIGGER trigger_name BEFORE|AFTER INSERT|UPDATE|DELETE ON 表名 FOR EACH ROW BEGIN\n  -- 触发器逻辑\nEND;", "description": "用于在指定表的插入、更新或删除操作前后自动执行一段SQL逻辑。常用于数据自动校验、日志记录等。", "parameters": [{"name": "trigger_name", "description": "触发器名称"}, {"name": "BEFORE|AFTER", "description": "触发时机，操作前或后"}, {"name": "INSERT|UPDATE|DELETE", "description": "触发类型"}, {"name": "表名", "description": "绑定的目标表"}], "returnValue": "无返回值", "examples": [{"code": "-- 在student表插入前自动记录日志\nCREATE TRIGGER log_before_insert_student BEFORE INSERT ON student\nFOR EACH ROW BEGIN\n  INSERT INTO student_log(action, action_time) VALUES ('insert', NOW());\nEND;", "explanation": "本例创建了一个在student表插入前自动写入日志表的触发器。"}]}}, {"name": "Drop Trigger", "trans": ["删除触发器"], "usage": {"syntax": "DROP TRIGGER [IF EXISTS] trigger_name;", "description": "用于删除已存在的触发器，释放数据库对象。", "parameters": [{"name": "trigger_name", "description": "要删除的触发器名称"}], "returnValue": "无返回值", "examples": [{"code": "-- 删除log_before_insert_student触发器\nDROP TRIGGER IF EXISTS log_before_insert_student;", "explanation": "本例演示了如何删除一个已存在的触发器。"}]}}, {"name": "Trigger Use Cases", "trans": ["触发器应用场景"], "usage": {"syntax": "-- 常见应用场景说明", "description": "触发器常用于：1. 自动维护审计日志；2. 数据变更自动校验；3. 级联更新或删除；4. 自动生成统计数据等。", "parameters": [], "returnValue": "无返回值，仅为概念说明", "examples": [{"code": "-- 审计日志：记录所有对student表的更新\nCREATE TRIGGER log_update_student AFTER UPDATE ON student\nFOR EACH ROW BEGIN\n  INSERT INTO student_log(action, action_time, old_name, new_name)\n  VALUES ('update', NOW(), OLD.name, NEW.name);\nEND;", "explanation": "本例展示了如何通过触发器自动记录student表的更新日志。"}]}}, {"name": "Triggers Assignment", "trans": ["触发器练习"], "usage": {"syntax": "# 触发器练习", "description": "完成以下练习，巩固MySQL触发器的基本用法和实际应用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 创建一个触发器，在student表插入新记录时，自动将操作写入student_log表。\n2. 创建一个触发器，在student表删除记录时，自动备份被删除的数据到student_backup表。\n3. 删除上述触发器，并验证效果。", "explanation": "通过这些练习，你将掌握MySQL触发器的创建、删除和常见应用场景。"}]}}]}