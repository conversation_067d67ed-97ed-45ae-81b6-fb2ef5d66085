{"name": "Best Practices", "trans": ["代码与架构最佳实践"], "methods": [{"name": "Layered Architecture Design", "trans": ["分层架构设计"], "usage": {"syntax": "controller -> service -> repository 分层", "description": "采用控制器、服务、数据访问分层架构，提升代码可维护性、可测试性和解耦性。", "parameters": [{"name": "Controller", "description": "负责接收请求和返回响应"}, {"name": "Service", "description": "封装业务逻辑"}, {"name": "Repository", "description": "数据访问层，操作数据库"}], "returnValue": "结构清晰、职责分明的项目架构", "examples": [{"code": "// Controller层\n@RestController\npublic class UserController {\n    @Autowired\n    private UserService userService;\n    @GetMapping(\"/users/{id}\")\n    public User getUser(@PathVariable Long id) {\n        return userService.getUser(id);\n    }\n}\n// Service层\n@Service\npublic class UserService {\n    @Autowired\n    private UserRepository userRepository;\n    public User getUser(Long id) {\n        return userRepository.findById(id).orElse(null);\n    }\n}", "explanation": "展示了典型的三层架构分层方式。"}]}}, {"name": "Domain Driven Design (DDD)", "trans": ["领域驱动设计（DDD）"], "usage": {"syntax": "实体、值对象、聚合根、领域服务、仓储接口", "description": "通过领域驱动设计，将业务复杂性归纳到领域模型中，提升系统可扩展性和业务一致性。", "parameters": [{"name": "实体(Entity)", "description": "有唯一标识的业务对象"}, {"name": "值对象(Value Object)", "description": "无唯一标识、不可变对象"}, {"name": "聚合根(Aggregate Root)", "description": "聚合内唯一对外访问入口"}, {"name": "领域服务(Domain Service)", "description": "跨实体的业务逻辑"}, {"name": "仓储接口(Repository)", "description": "持久化抽象"}], "returnValue": "高内聚、低耦合的领域模型", "examples": [{"code": "// 实体\npublic class Order {\n    private Long id;\n    private List<OrderItem> items;\n}\n// 值对象\npublic class Address {\n    private String city;\n    private String street;\n}\n// 仓储接口\npublic interface OrderRepository {\n    Order findById(Long id);\n}", "explanation": "展示了DDD的基本要素。"}]}}, {"name": "Unified Exception and Log Handling", "trans": ["统一异常与日志处理"], "usage": {"syntax": "@ControllerAdvice + 日志框架统一处理", "description": "通过@ControllerAdvice统一处理全局异常，结合日志框架记录异常和关键操作，提升系统健壮性和可追溯性。", "parameters": [{"name": "@ControllerAdvice", "description": "全局异常处理注解"}, {"name": "日志框架", "description": "Logback/Log4j2等"}], "returnValue": "统一的异常响应和完整的日志记录", "examples": [{"code": "@ControllerAdvice\npublic class GlobalExceptionHandler {\n    @ExceptionHandler(Exception.class)\n    public ResponseEntity<String> handle(Exception ex) {\n        // 记录日志\n        log.error(\"系统异常\", ex);\n        return ResponseEntity.status(500).body(\"服务器异常\");\n    }\n}", "explanation": "通过全局异常处理和日志记录提升系统健壮性。"}]}}, {"name": "Config and Code Separation", "trans": ["配置与代码分离"], "usage": {"syntax": "application.yml、@Value、@ConfigurationProperties", "description": "通过配置文件、@Value和@ConfigurationProperties注解实现配置与代码解耦，便于环境切换和敏感信息管理。", "parameters": [{"name": "application.yml", "description": "集中管理配置信息"}, {"name": "@Value", "description": "注入单个配置值"}, {"name": "@ConfigurationProperties", "description": "批量注入配置"}], "returnValue": "灵活可维护的配置管理方案", "examples": [{"code": "# application.yml\napp:\n  name: demo\n  timeout: 30\n// 配置类\n@Component\n@ConfigurationProperties(prefix = \"app\")\npublic class AppConfig {\n    private String name;\n    private int timeout;\n}", "explanation": "通过配置文件和注解实现配置与代码分离。"}]}}, {"name": "Dependency Injection and Decoupling", "trans": ["依赖注入与解耦"], "usage": {"syntax": "@Autowired、构造器注入、接口编程", "description": "通过依赖注入和接口编程，降低模块间耦合度，提升可测试性和扩展性。推荐优先使用构造器注入。", "parameters": [{"name": "@Autowired", "description": "自动注入依赖对象"}, {"name": "构造器注入", "description": "推荐的依赖注入方式"}, {"name": "接口编程", "description": "面向接口解耦"}], "returnValue": "低耦合、高可测的系统结构", "examples": [{"code": "// 构造器注入\n@Service\npublic class OrderService {\n    private final OrderRepository orderRepository;\n    public OrderService(OrderRepository orderRepository) {\n        this.orderRepository = orderRepository;\n    }\n}", "explanation": "推荐使用构造器注入实现解耦。"}]}}, {"name": "Common Security Vulnerability Protection", "trans": ["常见安全漏洞防护"], "usage": {"syntax": "输入校验、XSS/CSRF防护、依赖升级", "description": "通过输入校验、XSS/CSRF防护、依赖升级等手段，防止常见安全漏洞，保障系统安全。", "parameters": [{"name": "输入校验", "description": "防止SQL注入、XSS等"}, {"name": "CSRF防护", "description": "防止跨站请求伪造"}, {"name": "依赖升级", "description": "修复已知安全漏洞"}], "returnValue": "更安全的系统架构", "examples": [{"code": "// 输入校验\n@PostMapping(\"/login\")\npublic void login(@Valid @RequestBody LoginDTO dto) { }\n// CSRF防护\nhttp.csrf().enable();", "explanation": "通过输入校验和CSRF防护提升安全性。"}]}}, {"name": "Version Upgrade and Compatibility", "trans": ["版本升级与兼容性"], "usage": {"syntax": "依赖版本管理、兼容性测试、灰度发布", "description": "通过依赖版本管理、兼容性测试、灰度发布等手段，平滑升级系统并保障兼容性。", "parameters": [{"name": "依赖版本管理", "description": "统一管理依赖版本"}, {"name": "兼容性测试", "description": "升级前后功能验证"}, {"name": "灰度发布", "description": "逐步切换新旧版本"}], "returnValue": "平滑升级且兼容的系统", "examples": [{"code": "# Maven依赖管理\n<dependencyManagement>\n  <dependencies>\n    <dependency>\n      <groupId>org.springframework.boot</groupId>\n      <artifactId>spring-boot-dependencies</artifactId>\n      <version>2.7.0</version>\n      <type>pom</type>\n      <scope>import</scope>\n    </dependency>\n  </dependencies>\n</dependencyManagement>", "explanation": "通过依赖管理和兼容性测试实现平滑升级。"}]}}, {"name": "Best Practices Assignment", "trans": ["最佳实践练习"], "usage": {"syntax": "# 最佳实践练习", "description": "完成以下练习，巩固Spring Boot代码与架构最佳实践相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 按分层架构重构一个小型项目。\n2. 设计一个简单的领域模型并实现仓储接口。\n3. 实现全局异常处理和日志记录。\n4. 使用@ConfigurationProperties实现配置与代码分离。\n5. 优先使用构造器注入。\n6. 增加输入校验和CSRF防护。\n7. 进行一次依赖升级并验证兼容性。", "explanation": "通过这些练习掌握Spring Boot最佳实践。"}]}}]}