{"name": "Setup Development Environment", "trans": ["开发环境搭建"], "methods": [{"name": "Install Flutter SDK", "trans": ["Flutter SDK安装"], "usage": {"syntax": "下载并解压Flutter SDK，配置环境变量。", "description": "从Flutter官网下载对应操作系统的SDK，解压到本地目录，并将flutter/bin加入系统环境变量PATH。", "parameters": [{"name": "下载地址", "description": "https://flutter.dev/docs/get-started/install"}, {"name": "环境变量", "description": "将flutter/bin目录添加到PATH"}], "returnValue": "无返回值", "examples": [{"code": "# Windows下安装Flutter SDK示例\n# 1. 下载Flutter SDK压缩包\n# 2. 解压到D:\\dev\\flutter\n# 3. 配置环境变量：将D:\\dev\\flutter\\bin添加到PATH\n# 4. 验证安装\nflutter --version  # 查看Flutter版本\n", "explanation": "本示例演示了在Windows下安装Flutter SDK的基本步骤。"}]}}, {"name": "IDE Configuration", "trans": ["Android Studio/VS Code配置"], "usage": {"syntax": "安装Flutter插件，配置开发环境。", "description": "在Android Studio或VS Code中安装Flutter和Dart插件，配置模拟器或真机调试环境。", "parameters": [{"name": "IDE", "description": "Android Studio或VS Code"}, {"name": "插件", "description": "Flutter插件、Dart插件"}], "returnValue": "无返回值", "examples": [{"code": "// VS Code配置Flutter开发环境\n// 1. 安装Flutter插件和Dart插件\n// 2. 按Ctrl+Shift+P，输入Flutter: New Project创建新项目\n// 3. 配置Android/iOS模拟器或连接真机\n", "explanation": "本示例说明了在VS Code中配置Flutter开发环境的主要步骤。"}]}}, {"name": "Emulator and Device Debugging", "trans": ["模拟器与真机调试"], "usage": {"syntax": "使用模拟器或真机进行Flutter应用调试。", "description": "可通过Android Studio自带的AVD Manager创建模拟器，或连接手机开启开发者模式进行真机调试。", "parameters": [{"name": "模拟器", "description": "Android/iOS模拟器"}, {"name": "真机", "description": "连接物理设备，开启USB调试"}], "returnValue": "无返回值", "examples": [{"code": "// 启动模拟器或连接真机调试\n// 1. Android Studio中启动AVD模拟器\n// 2. 连接手机并开启开发者模式和USB调试\n// 3. 终端输入flutter devices查看可用设备\n// 4. 运行应用：flutter run\n", "explanation": "本示例展示了如何启动模拟器或连接真机进行Flutter应用调试。"}]}}, {"name": "First Flutter App", "trans": ["第一个Flutter应用"], "usage": {"syntax": "flutter create my_app", "description": "使用命令行创建Flutter项目，运行并查看效果。", "parameters": [{"name": "项目名", "description": "my_app等自定义名称"}], "returnValue": "无返回值", "examples": [{"code": "# 创建并运行第一个Flutter应用\nflutter create my_app  # 创建项目\ncd my_app\nflutter run           # 运行项目\n// main.dart默认代码如下：\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      home: Scaffold(\n        appBar: AppBar(title: Text('Hello Flutter')),\n        body: Center(child: Text('欢迎使用Flutter!')),\n      ),\n    );\n  }\n}", "explanation": "本示例演示了如何创建并运行第一个Flutter应用。"}]}}, {"name": "Project Structure and Run", "trans": ["项目结构与运行"], "usage": {"syntax": "Flutter项目结构说明及运行流程。", "description": "Flutter项目包含lib、android、ios等目录，主入口为lib/main.dart。通过flutter run命令编译并运行应用。", "parameters": [{"name": "lib/main.dart", "description": "应用主入口"}, {"name": "android/ios/web", "description": "各平台相关目录"}], "returnValue": "无返回值", "examples": [{"code": "// Flutter项目结构示例\nmy_app/\n  lib/\n    main.dart    // 应用主入口\n  android/       // Android平台相关\n  ios/           // iOS平台相关\n  pubspec.yaml   // 配置依赖和资源\n// 运行项目\nflutter run\n", "explanation": "本示例说明了Flutter项目的基本结构和运行方式。"}]}}, {"name": "Assignment", "trans": ["作业：完成环境搭建并运行你的第一个Flutter应用。"], "usage": {"syntax": "无", "description": "请完成Flutter SDK安装、IDE配置、创建并运行第一个Flutter应用，并截图运行效果。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 作业参考实现\n// 1. 安装Flutter SDK并配置环境变量\n// 2. 配置IDE插件\n// 3. 创建并运行第一个Flutter应用，页面显示自定义文本\n// 4. 截图运行效果作为作业提交\n", "explanation": "作业要求学员完成环境搭建并成功运行Flutter应用。"}]}}]}