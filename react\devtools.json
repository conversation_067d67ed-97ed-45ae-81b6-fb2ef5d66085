{"name": "React DevTools", "trans": ["React DevTools"], "methods": [{"name": "Component Inspection", "trans": ["组件检查"], "usage": {"syntax": "// 打开React DevTools，选中组件树中的组件进行检查", "description": "通过DevTools可查看组件树结构、层级关系和嵌套，定位UI问题。", "parameters": [], "returnValue": "无返回值，界面展示组件结构。", "examples": [{"code": "// 1. 打开DevTools，切换到Components面板\n// 2. 点击组件节点，查看其结构和props", "explanation": "检查组件树和层级关系。"}]}}, {"name": "Props and State Viewing", "trans": ["Props和State查看"], "usage": {"syntax": "// 在Components面板中查看props和state的当前值", "description": "可实时查看和编辑组件的props和state，调试数据流和状态变化。", "parameters": [], "returnValue": "无返回值，界面展示props和state。", "examples": [{"code": "// 1. 选中组件\n// 2. 在右侧面板查看props和state的值", "explanation": "调试组件数据和状态。"}]}}, {"name": "Performance Profiling", "trans": ["性能分析"], "usage": {"syntax": "// 切换到Profiler面板，点击Record录制渲染性能", "description": "通过Profiler分析组件渲染耗时、重渲染原因和性能瓶颈。", "parameters": [], "returnValue": "无返回值，界面展示性能分析结果。", "examples": [{"code": "// 1. Profiler面板点击Record\n// 2. 操作页面后停止录制，分析渲染耗时", "explanation": "定位性能瓶颈和优化点。"}]}}, {"name": "Hooks Debugging", "trans": ["调试Hooks"], "usage": {"syntax": "// 在组件面板查看Hooks的当前值和调用顺序", "description": "可查看useState、useEffect等Hooks的当前值，调试自定义Hook逻辑。", "parameters": [], "returnValue": "无返回值，界面展示Hooks信息。", "examples": [{"code": "// 1. 选中组件\n// 2. 查看Hooks列表和每个Hook的值", "explanation": "调试Hooks状态和副作用。"}]}}, {"name": "Component Filtering", "trans": ["组件过滤"], "usage": {"syntax": "// 在Components面板顶部输入组件名进行过滤", "description": "可通过名称过滤组件，快速定位目标组件，提升调试效率。", "parameters": [], "returnValue": "无返回值，界面展示过滤结果。", "examples": [{"code": "// 在过滤框输入组件名，列表只显示匹配的组件", "explanation": "快速定位目标组件。"}]}}, {"name": "Time Travel Debugging", "trans": ["时间旅行调试"], "usage": {"syntax": "// 使用Redux DevTools等支持时间旅行的工具", "description": "通过时间旅行功能回溯和重放状态变化，调试复杂交互。", "parameters": [], "returnValue": "无返回值，界面展示状态历史。", "examples": [{"code": "// 在Redux DevTools中拖动时间轴，回放状态变化", "explanation": "调试状态流转和还原。"}]}}, {"name": "Network Throttling", "trans": ["网络环境模拟"], "usage": {"syntax": "// 在DevTools Network面板设置网络速度", "description": "可模拟慢网速、离线等环境，测试应用在不同网络下的表现。", "parameters": [], "returnValue": "无返回值，界面模拟网络环境。", "examples": [{"code": "// Network面板选择Slow 3G等模式，刷新页面观察加载表现", "explanation": "测试应用在不同网络下的体验。"}]}}, {"name": "作业：DevTools实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 检查组件树、props、state和Hooks\n// 2. 分析性能和调试网络环境\n// 3. 实践组件过滤和时间旅行调试", "description": "通过实践组件检查、数据查看、性能分析、Hooks调试、组件过滤、时间旅行和网络模拟，掌握DevTools调试技巧。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 检查组件树和数据\n// 2. 用Profiler分析性能\n// 3. 模拟网络和时间旅行调试", "explanation": "作业提示，学生需结合本节内容完成实现。"}, {"code": "// 正确实现示例\n// 1. 打开DevTools，检查组件和props\n// 2. Profiler分析渲染性能\n// 3. Network面板模拟慢网速", "explanation": "DevTools调试的正确实现示例。"}]}}]}