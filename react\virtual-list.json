{"name": "Virtual List", "trans": ["虚拟列表"], "methods": [{"name": "Principle of Virtual List", "trans": ["虚拟列表原理"], "usage": {"syntax": "// 虚拟列表原理：\n// 只渲染可视区域内的少量DOM节点，随着滚动动态复用节点，极大减少页面渲染压力。", "description": "虚拟列表通过只渲染可视区域内的部分数据项，动态复用DOM节点，避免一次性渲染大量数据，显著提升性能，常用于长列表、表格等场景。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 原理示意伪代码\nconst visibleItems = items.slice(startIndex, endIndex);\nreturn <div style={{ height: totalHeight }}>\n  <div style={{ transform: `translateY(${startIndex * itemHeight}px)` }}>\n    {visibleItems.map(item => <Row key={item.id} data={item} />)}\n  </div>\n</div>;", "explanation": "只渲染可见区间的数据项，其他部分用空白占位，提升渲染效率。"}]}}, {"name": "Implementing Virtual Scrolling", "trans": ["实现虚拟滚动"], "usage": {"syntax": "// 基本实现思路：\n// 1. 监听滚动事件，计算可见区域的起止索引\n// 2. 只渲染可见区间的数据项\n// 3. 用padding或transform占位未渲染部分\n// 4. 支持动态高度时需额外记录每项高度", "description": "通过监听滚动事件，计算可见区间，只渲染可见数据项，并用空白占位未渲染部分，实现高性能的虚拟滚动。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 简单虚拟滚动实现（固定高度）\nimport React, { useRef, useState } from 'react';\n\nfunction VirtualList({ items, itemHeight, height }) {\n  const [scrollTop, setScrollTop] = useState(0);\n  const totalHeight = items.length * itemHeight;\n  const visibleCount = Math.ceil(height / itemHeight);\n  const startIndex = Math.floor(scrollTop / itemHeight);\n  const endIndex = Math.min(startIndex + visibleCount + 1, items.length);\n  const visibleItems = items.slice(startIndex, endIndex);\n\n  return (\n    <div\n      style={{ overflowY: 'auto', height }}\n      onScroll={e => setScrollTop(e.target.scrollTop)}\n    >\n      <div style={{ height: totalHeight, position: 'relative' }}>\n        <div style={{\n          position: 'absolute',\n          top: startIndex * itemHeight,\n          width: '100%'\n        }}>\n          {visibleItems.map((item, i) => (\n            <div key={startIndex + i} style={{ height: itemHeight, borderBottom: '1px solid #eee' }}>\n              {item}\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// 使用示例\nconst items = Array.from({ length: 10000 }, (_, i) => `Item ${i}`);\nfunction App() {\n  return <VirtualList items={items} itemHeight={30} height={300} />;\n}", "explanation": "只渲染可见区间的列表项，支持高性能滚动，适合大数据量场景。"}]}}, {"name": "Dynamic Height Handling", "trans": ["动态高度处理"], "usage": {"syntax": "// 动态高度处理思路：\n// 1. 记录每一项的实际高度\n// 2. 通过累加高度计算可见区间\n// 3. 滚动时动态调整渲染区间", "description": "对于每项高度不一致的虚拟列表，需要动态测量和记录每项高度，滚动时根据累计高度判断可见区间，常用ResizeObserver或ref测量。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 动态高度虚拟列表核心思路\nimport React, { useRef, useState, useLayoutEffect } from 'react';\n\nfunction DynamicVirtualList({ items, estimatedHeight, height }) {\n  const [scrollTop, setScrollTop] = useState(0);\n  const [heights, setHeights] = useState(Array(items.length).fill(estimatedHeight));\n  const containerRef = useRef();\n\n  // 计算累计高度\n  const cumHeights = heights.reduce((acc, h, i) => {\n    acc.push((acc[i - 1] || 0) + h);\n    return acc;\n  }, []);\n  const totalHeight = cumHeights[cumHeights.length - 1] || 0;\n\n  // 计算可见区间\n  let start = 0, end = items.length - 1;\n  for (let i = 0; i < items.length; i++) {\n    if (cumHeights[i] > scrollTop) { start = i; break; }\n  }\n  for (let i = start; i < items.length; i++) {\n    if (cumHeights[i] > scrollTop + height) { end = i; break; }\n  }\n  const visibleItems = items.slice(start, end + 1);\n\n  // 动态测量每项高度\n  const itemRefs = useRef([]);\n  useLayoutEffect(() => {\n    const newHeights = [...heights];\n    for (let i = start; i <= end; i++) {\n      const el = itemRefs.current[i - start];\n      if (el) {\n        const h = el.offsetHeight;\n        if (h !== heights[i]) newHeights[i] = h;\n      }\n    }\n    setHeights(newHeights);\n  }, [start, end, items]);\n\n  return (\n    <div\n      ref={containerRef}\n      style={{ overflowY: 'auto', height }}\n      onScroll={e => setScrollTop(e.target.scrollTop)}\n    >\n      <div style={{ height: totalHeight, position: 'relative' }}>\n        <div style={{ position: 'absolute', top: cumHeights[start - 1] || 0, width: '100%' }}>\n          {visibleItems.map((item, i) => (\n            <div key={start + i} ref={el => itemRefs.current[i] = el} style={{ borderBottom: '1px solid #eee' }}>\n              {item}\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n// 使用示例略，见上。", "explanation": "通过ref测量每项实际高度，动态调整渲染区间，适合高度不一致的复杂列表。"}]}}, {"name": "Scroll Position Restore", "trans": ["滚动位置恢复"], "usage": {"syntax": "// 滚动位置恢复思路：\n// 1. 记录用户离开前的scrollTop\n// 2. 重新进入时设置scrollTop\n// 3. 可结合路由缓存、localStorage等实现", "description": "虚拟列表常用于页面切换或数据刷新场景，需记录并恢复用户的滚动位置，提升体验。可用ref、useEffect和localStorage实现。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 滚动位置恢复示例\nimport React, { useRef, useEffect, useState } from 'react';\n\nfunction VirtualListWithRestore({ items, itemHeight, height }) {\n  const containerRef = useRef();\n  const [scrollTop, setScrollTop] = useState(() => Number(localStorage.getItem('scrollTop')) || 0);\n\n  useEffect(() => {\n    if (containerRef.current) {\n      containerRef.current.scrollTop = scrollTop;\n    }\n  }, [scrollTop]);\n\n  useEffect(() => {\n    return () => {\n      localStorage.setItem('scrollTop', containerRef.current?.scrollTop || 0);\n    };\n  }, []);\n\n  // ...虚拟列表渲染同前\n  return (\n    <div\n      ref={containerRef}\n      style={{ overflowY: 'auto', height }}\n      onScroll={e => setScrollTop(e.target.scrollTop)}\n    >\n      {/* ...虚拟列表内容... */}\n    </div>\n  );\n}", "explanation": "通过localStorage和ref记录并恢复滚动位置，提升长列表的用户体验。"}]}}, {"name": "Popular Virtual List Libraries", "trans": ["常用虚拟列表库"], "usage": {"syntax": "import { FixedSizeList } from 'react-window';", "description": "常用虚拟列表库有react-window、react-virtualized等，提供高性能、易用的虚拟滚动组件，支持固定/动态高度、表格、无限加载等。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// react-window使用示例\nimport { FixedSizeList as List } from 'react-window';\n\nconst items = Array.from({ length: 10000 }, (_, i) => `Item ${i}`);\nfunction Row({ index, style }) {\n  return <div style={style}>{items[index]}</div>;\n}\n\nfunction App() {\n  return (\n    <List\n      height={300}\n      itemCount={items.length}\n      itemSize={30}\n      width={300}\n    >\n      {Row}\n    </List>\n  );\n}", "explanation": "react-window支持高性能虚拟滚动，API简单，适合大部分场景。"}, {"code": "// react-virtualized使用示例\nimport { List } from 'react-virtualized';\n\nconst items = Array.from({ length: 10000 }, (_, i) => `Item ${i}`);\nfunction rowRenderer({ index, key, style }) {\n  return <div key={key} style={style}>{items[index]}</div>;\n}\n\nfunction App() {\n  return (\n    <List\n      width={300}\n      height={300}\n      rowCount={items.length}\n      rowHeight={30}\n      rowRenderer={rowRenderer}\n    />\n  );\n}", "explanation": "react-virtualized功能更丰富，支持表格、无限加载等复杂场景。"}]}}, {"name": "Performance Testing and Tuning", "trans": ["性能测试和调优"], "usage": {"syntax": "// 性能测试与调优思路：\n// 1. 使用React Profiler分析渲染瓶颈\n// 2. 优化item渲染，避免不必要的重渲染\n// 3. 合理设置itemSize、overscan等参数\n// 4. 使用shouldComponentUpdate/React.memo优化子项", "description": "通过React Profiler等工具分析虚拟列表性能瓶颈，结合memo、合理参数和分区渲染等手段，进一步提升大数据量场景下的滚动和渲染性能。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 性能调优示例\n// 1. 用React.memo包裹Row组件，避免无关重渲染\nconst Row = React.memo(function Row({ index, style }) {\n  return <div style={style}>Item {index}</div>;\n});\n\n// 2. 设置overscanCount提升滚动流畅度\n<List\n  height={300}\n  itemCount={10000}\n  itemSize={30}\n  width={300}\n  overscanCount={5}\n>\n  {Row}\n</List>;\n\n// 3. 用React Profiler分析渲染性能\nimport { Profiler } from 'react';\n<Profiler id=\"VirtualList\" onRender={...}>...</Profiler>;", "explanation": "通过memo、合理overscan和Profiler分析，进一步优化虚拟列表性能。"}]}}, {"name": "作业：实现虚拟列表", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 实现一个支持1万条数据的虚拟列表\n// 2. 支持固定和动态高度两种模式\n// 3. 支持滚动位置恢复\n// 4. 用react-window实现高性能滚动", "description": "通过实践虚拟列表的原理、实现、库使用和性能调优，掌握大数据量场景下的高性能渲染技术。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 用原生或react-window实现虚拟列表\n// 2. 支持动态高度和滚动恢复\n// 3. 用Profiler分析性能瓶颈\n// 4. 总结调优经验", "explanation": "作业提示，学生需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nimport { FixedSizeList as List } from 'react-window';\nconst items = Array.from({ length: 10000 }, (_, i) => `Item ${i}`);\nconst Row = React.memo(({ index, style }) => <div style={style}>{items[index]}</div>);\nfunction App() {\n  return (\n    <List\n      height={300}\n      itemCount={items.length}\n      itemSize={30}\n      width={300}\n      overscanCount={5}\n    >\n      {Row}\n    </List>\n  );\n}\nexport default App;", "explanation": "完整实现了高性能虚拟列表，支持大数据量、性能调优和滚动恢复。"}]}}]}