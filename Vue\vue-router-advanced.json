{"name": "Vue Router Advanced", "trans": ["路由进阶"], "methods": [{"name": "Lazy Loading and Code Splitting", "trans": ["懒加载与代码分割"], "usage": {"syntax": "const routes = [\n  { path: '/about', component: () => import('./About.vue') }\n]", "description": "通过动态import实现路由组件懒加载，自动分割代码，提升首屏加载速度。", "parameters": [{"name": "component", "description": "动态import返回Promise的函数"}], "returnValue": "按需加载的路由组件", "examples": [{"code": "const routes = [\n  { path: '/about', component: () => import('./About.vue') }\n]", "explanation": "配置路由组件懒加载，实现代码分割。"}]}}, {"name": "Route Lazy Loading", "trans": ["路由懒加载"], "usage": {"syntax": "component: () => import('./Comp.vue')", "description": "路由配置中通过函数返回import实现组件懒加载，只有访问路由时才加载对应组件。", "parameters": [{"name": "component", "description": "懒加载组件的函数"}], "returnValue": "按需加载的组件", "examples": [{"code": "{ path: '/user', component: () => import('./User.vue') }", "explanation": "访问/user时才加载User组件。"}]}}, {"name": "Component <PERSON><PERSON> with keep-alive", "trans": ["组件缓存与keep-alive"], "usage": {"syntax": "<keep-alive>\n  <router-view />\n</keep-alive>", "description": "用keep-alive包裹<router-view>可缓存路由组件，切换时保留状态，提升体验。", "parameters": [{"name": "keep-alive", "description": "缓存组件的内置标签"}], "returnValue": "被缓存的路由组件实例", "examples": [{"code": "<template>\n  <keep-alive>\n    <router-view />\n  </keep-alive>\n</template>", "explanation": "切换路由时缓存组件状态。"}]}}, {"name": "Scroll Behavior Control", "trans": ["滚动行为控制"], "usage": {"syntax": "const router = createRouter({\n  scrollBehavior(to, from, savedPosition) {\n    return { top: 0 }\n  }\n})", "description": "通过scrollBehavior配置路由切换时的滚动位置，支持返回顶部、还原上次位置等。", "parameters": [{"name": "scroll<PERSON>eh<PERSON>or", "description": "滚动行为配置函数"}], "returnValue": "滚动到指定位置", "examples": [{"code": "const router = createRouter({\n  scrollBehavior(to, from, savedPosition) {\n    if (savedPosition) return savedPosition\n    return { top: 0 }\n  }\n})", "explanation": "切换路由时自动滚动到顶部或还原位置。"}]}}, {"name": "Dynamic Add/Remove Route", "trans": ["动态添加/移除路由"], "usage": {"syntax": "router.addRoute(route)\nrouter.removeRoute(name)", "description": "通过addRoute和removeRoute可动态添加或移除路由，适合权限控制、模块按需加载等场景。", "parameters": [{"name": "addRoute", "description": "动态添加路由的方法"}, {"name": "removeRoute", "description": "动态移除路由的方法"}], "returnValue": "动态变更的路由表", "examples": [{"code": "router.addRoute({ path: '/admin', component: Admin })\nrouter.removeRoute('admin')", "explanation": "动态添加和移除路由。"}]}}, {"name": "404 and Redirect", "trans": ["404与重定向"], "usage": {"syntax": "{ path: '/:pathMatch(.*)*', name: 'NotFound', component: NotFound }\n{ path: '/old', redirect: '/new' }", "description": "通过pathMatch配置404页面，通过redirect实现路由重定向。", "parameters": [{"name": "pathMatch", "description": "匹配所有未命中路由"}, {"name": "redirect", "description": "重定向目标路径"}], "returnValue": "404页面或重定向后的路由", "examples": [{"code": "{ path: '/:pathMatch(.*)*', name: 'NotFound', component: NotFound }\n{ path: '/old', redirect: '/new' }", "explanation": "配置404页面和重定向。"}]}}, {"name": "Vue Router Advanced Assignment", "trans": ["路由进阶练习"], "usage": {"syntax": "# 路由进阶练习", "description": "完成以下练习，巩固Vue Router进阶相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 配置路由懒加载和代码分割。\n2. 用keep-alive缓存路由组件。\n3. 控制路由切换时的滚动行为。\n4. 动态添加和移除路由。\n5. 配置404页面和重定向。", "explanation": "通过这些练习掌握Vue Router进阶用法。"}]}}]}