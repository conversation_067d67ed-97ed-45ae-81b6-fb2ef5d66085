{"name": "Redux Basics", "trans": ["Redux基础"], "methods": [{"name": "Store Concept", "trans": ["Store概念"], "usage": {"syntax": "import { createStore } from 'redux';\nconst store = createStore(reducer);", "description": "Store是Redux的状态容器，保存应用的所有状态，只能通过dispatch action改变。", "parameters": [{"name": "reducer", "description": "用于处理状态变更的函数"}], "returnValue": "Redux store对象，包含getState、dispatch、subscribe等方法", "examples": [{"code": "import { createStore } from 'redux';\n// 定义reducer\nfunction reducer(state = { count: 0 }, action) {\n  switch (action.type) {\n    case 'increment':\n      return { count: state.count + 1 };\n    default:\n      return state;\n  }\n}\n// 创建store\nconst store = createStore(reducer);\n// 获取当前状态\nconsole.log(store.getState());\n// 派发action\nstore.dispatch({ type: 'increment' });\n// 订阅状态变化\nstore.subscribe(() => {\n  console.log('状态变化:', store.getState());\n});", "explanation": "通过createStore创建store，管理全局状态。"}]}}, {"name": "Action Definition", "trans": ["Action定义"], "usage": {"syntax": "const action = { type: 'actionType', payload: data };\nstore.dispatch(action);", "description": "Action是描述状态变化的普通对象，必须包含type字段，payload用于传递额外数据。", "parameters": [{"name": "type", "description": "操作类型标识符"}, {"name": "payload", "description": "附加数据"}], "returnValue": "无返回值（通过reducer处理）", "examples": [{"code": "// 定义action\nconst addAction = { type: 'add', payload: 1 };\n// 派发action\nstore.dispatch(addAction);", "explanation": "action对象描述一次状态变更。"}]}}, {"name": "Reducer Writing", "trans": ["Reducer编写"], "usage": {"syntax": "function reducer(state, action) { ... }", "description": "Reducer是纯函数，接收旧状态和action，返回新状态。必须保持不可变性和纯净性。", "parameters": [{"name": "state", "description": "当前状态"}, {"name": "action", "description": "描述操作的对象"}], "returnValue": "新的状态对象", "examples": [{"code": "function reducer(state = { count: 0 }, action) {\n  switch (action.type) {\n    case 'increment':\n      return { count: state.count + 1 };\n    case 'decrement':\n      return { count: state.count - 1 };\n    default:\n      return state;\n  }\n}", "explanation": "Reducer根据action类型返回新状态，保持不可变性。"}]}}, {"name": "Middleware Usage", "trans": ["中间件使用"], "usage": {"syntax": "import { applyMiddleware, createStore } from 'redux';\nconst store = createStore(reducer, applyMiddleware(middleware));", "description": "中间件用于扩展dispatch功能，如日志、异步、权限等。常用redux-thunk、redux-logger等。", "parameters": [{"name": "middleware", "description": "中间件函数"}], "returnValue": "增强后的store对象", "examples": [{"code": "import { createStore, applyMiddleware } from 'redux';\nimport logger from 'redux-logger';\n// 创建带日志中间件的store\nconst store = createStore(reducer, applyMiddleware(logger));", "explanation": "通过applyMiddleware增强store，支持日志等功能。"}]}}, {"name": "Connecting React Components", "trans": ["连接React组件"], "usage": {"syntax": "import { Provider, useSelector, useDispatch } from 'react-redux';", "description": "通过react-redux的Provider、useSelector、useDispatch将Redux store与React组件连接，实现状态读取和派发。", "parameters": [{"name": "Provider", "description": "顶层包裹组件，注入store"}, {"name": "useSelector", "description": "读取store中的状态"}, {"name": "useDispatch", "description": "获取dispatch函数"}], "returnValue": "组件可访问的全局状态和dispatch", "examples": [{"code": "import React from 'react';\nimport { Provider, useSelector, useDispatch } from 'react-redux';\nimport { createStore } from 'redux';\n// 定义reducer\nfunction reducer(state = { count: 0 }, action) {\n  switch (action.type) {\n    case 'increment':\n      return { count: state.count + 1 };\n    default:\n      return state;\n  }\n}\n// 创建store\nconst store = createStore(reducer);\n// 计数器组件\nfunction Counter() {\n  // 读取全局状态\n  const count = useSelector(state => state.count);\n  // 获取dispatch\n  const dispatch = useDispatch();\n  return (\n    <div>\n      <span>全局计数: {count}</span>\n      <button onClick={() => dispatch({ type: 'increment' })}>加一</button>\n    </div>\n  );\n}\n// 用法\nfunction App() {\n  return (\n    <Provider store={store}>\n      <Counter />\n    </Provider>\n  );\n}", "explanation": "通过Provider、useSelector、useDispatch连接Redux和React。"}]}}, {"name": "Async Handling", "trans": ["异步处理"], "usage": {"syntax": "// 使用redux-thunk处理异步action", "description": "Redux本身只支持同步，需借助中间件（如redux-thunk）实现异步action。", "parameters": [], "returnValue": "异步操作后的状态更新", "examples": [{"code": "import { createStore, applyMiddleware } from 'redux';\nimport thunk from 'redux-thunk';\n// 异步action creator\nfunction fetchData() {\n  return dispatch => {\n    fetch('/api/data').then(res => res.json()).then(data => {\n      dispatch({ type: 'setData', payload: data });\n    });\n  };\n}\n// reducer\nfunction reducer(state = { data: null }, action) {\n  switch (action.type) {\n    case 'setData':\n      return { ...state, data: action.payload };\n    default:\n      return state;\n  }\n}\n// 创建store\nconst store = createStore(reducer, applyMiddleware(thunk));", "explanation": "通过redux-thunk实现异步action。"}]}}, {"name": "Redux DevTools", "trans": ["Redux开发工具"], "usage": {"syntax": "// 安装Redux DevTools扩展，配合composeEnhancers使用", "description": "Redux DevTools用于可视化调试状态变化，需在创建store时配置composeEnhancers。", "parameters": [], "returnValue": "可调试的Redux store", "examples": [{"code": "import { createStore, compose } from 'redux';\n// 配置Redux DevTools\nconst composeEnhancers = window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ || compose;\nconst store = createStore(reducer, composeEnhancers());", "explanation": "通过composeEnhancers集成Redux DevTools。"}]}}, {"name": "作业：实现一个Redux全局计数器", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 用Redux实现全局计数器。\n// 2. 支持加一、减一。\n// 3. 代码结构清晰，注释完整。", "description": "请实现上述Redux计数器，提交时请附上完整代码和注释。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 示例代码略，请学生自行实现\n// 提示：用createStore、Provider、useSelector、useDispatch实现。", "explanation": "作业要求学生掌握Redux全流程。"}, {"code": "import React from 'react';\nimport { createStore } from 'redux';\nimport { Provider, useSelector, useDispatch } from 'react-redux';\n// 1. 定义reducer\nfunction reducer(state = { count: 0 }, action) {\n  switch (action.type) {\n    case 'increment':\n      return { count: state.count + 1 };\n    case 'decrement':\n      return { count: state.count - 1 };\n    default:\n      return state;\n  }\n}\n// 2. 创建store\nconst store = createStore(reducer);\n// 3. 计数器组件\nfunction Counter() {\n  // 读取全局状态\n  const count = useSelector(state => state.count);\n  // 获取dispatch\n  const dispatch = useDispatch();\n  return (\n    <div>\n      <span>全局计数: {count}</span>\n      <button onClick={() => dispatch({ type: 'increment' })}>加一</button>\n      <button onClick={() => dispatch({ type: 'decrement' })}>减一</button>\n    </div>\n  );\n}\n// 4. 用法\nfunction App() {\n  return (\n    <Provider store={store}>\n      <Counter />\n    </Provider>\n  );\n}", "explanation": "完整实现Redux全局计数器，支持加一减一。"}]}}]}