{"topics": [{"id": "basics-syntax", "name": "基础语法", "file": "Python/basics-syntax.json", "description": "详细介绍Python解释器与运行、注释与缩进、标识符与关键字、代码块与语句、输入输出、交互式编程等基础语法知识，帮助入门Python。"}, {"id": "data-types", "name": "数据类型", "file": "Python/data-types.json", "description": "系统讲解Python的数字类型、字符串、布尔值、列表、元组、集合、字典、类型转换及可变与不可变类型，帮助理解和应用数据类型。"}, {"id": "operators-and-expressions", "name": "运算符与表达式", "file": "Python/operators-and-expressions.json", "description": "系统讲解Python的算术、赋值、比较、逻辑、位、成员、身份等运算符及表达式，帮助掌握表达式计算和条件判断。"}, {"id": "control-flow", "name": "流程控制", "file": "Python/control-flow.json", "description": "系统讲解if/else、for、while、break/continue、pass、列表推导式、条件表达式等流程控制语句，帮助掌握程序流程。"}, {"id": "functions", "name": "函数基础", "file": "Python/functions.json", "description": "系统讲解函数定义与调用、参数类型、返回值、作用域、global与nonlocal、lambda、文档字符串、函数对象等，帮助掌握Python函数基础。"}, {"id": "higher-order-functions", "name": "高阶函数", "file": "Python/higher-order-functions.json", "description": "系统讲解map、filter、reduce、sorted、闭包、装饰器、偏函数、递归等高阶函数及其应用，帮助提升函数式编程能力。"}, {"id": "classes-and-objects", "name": "类与对象", "file": "Python/classes-and-objects.json", "description": "系统讲解类的定义、实例与属性、方法与self、构造方法、类属性与实例属性、类方法与静态方法、魔术方法等，帮助掌握Python面向对象基础。"}, {"id": "inheritance-and-polymorphism", "name": "继承与多态", "file": "Python/inheritance-and-polymorphism.json", "description": "系统讲解单继承与多继承、方法重写、super、鸭子类型、抽象类与接口、MRO等，帮助掌握Python面向对象的继承与多态机制。"}, {"id": "modules", "name": "模块基础", "file": "Python/modules.json", "description": "系统讲解模块的创建与导入、import与from...import、__name__与主程序判断、模块搜索路径、标准库与第三方库等，帮助掌握Python模块化开发。"}, {"id": "package-management", "name": "包管理", "file": "Python/package-management.json", "description": "系统讲解包的结构与__init__.py、相对与绝对导入、虚拟环境venv、pip工具、requirements.txt、包的发布与安装等，帮助掌握Python包管理与依赖管理。"}, {"id": "file-io", "name": "文件操作", "file": "Python/file-io.json", "description": "系统讲解文件的打开与关闭、文本与二进制读写、with语句、文件指针与seek、文件编码等，帮助掌握Python文件操作。"}, {"id": "input-output", "name": "输入输出", "file": "Python/input-output.json", "description": "系统讲解input函数、print函数、格式化输出、标准输入输出与重定向等，帮助掌握Python输入输出操作。"}, {"id": "exceptions", "name": "异常基础", "file": "Python/exceptions.json", "description": "系统讲解try...except结构、else与finally、多异常捕获、raise、自定义异常类、异常链与traceback等，帮助掌握Python异常处理机制。"}, {"id": "built-in-modules", "name": "内置模块", "file": "Python/built-in-modules.json", "description": "系统讲解sys、os、math、random、datetime、time、collections、itertools、functools、re、json、pickle、logging、argparse等常用内置模块，帮助掌握标准库的高效使用。"}, {"id": "network-and-concurrency", "name": "网络与并发", "file": "Python/network-and-concurrency.json", "description": "系统讲解requests、urllib、socket、threading、multiprocessing、asyncio、queue等网络通信与并发编程基础，帮助掌握Python网络与并发开发。"}, {"id": "iterators-and-generators", "name": "迭代器与生成器", "file": "Python/iterators-and-generators.json", "description": "系统讲解迭代器协议、生成器函数与yield、生成器表达式、itertools高级用法等，帮助掌握Python高效数据迭代与生成。"}, {"id": "decorators-and-context-managers", "name": "装饰器与上下文管理器", "file": "Python/decorators-and-context-managers.json", "description": "系统讲解函数装饰器、类装饰器、contextlib模块、自定义上下文管理器等，帮助掌握Python高级函数与资源管理。"}, {"id": "metaprogramming", "name": "元编程", "file": "Python/metaprogramming.json", "description": "系统讲解动态属性与方法、getattr/setattr、type与元类、动态导入与反射等，帮助掌握Python元编程与高级动态特性。"}, {"id": "unit-testing", "name": "单元测试", "file": "Python/unit-testing.json", "description": "系统讲解unittest、pytest、断言、fixture、Mock、patch、覆盖率报告等，帮助掌握Python单元测试与测试驱动开发。"}, {"id": "debugging", "name": "调试技巧", "file": "Python/debugging.json", "description": "系统讲解print与断点调试、logging调试、pdb交互调试、IDE调试工具、性能分析与profile等，帮助掌握Python调试与性能优化。"}, {"id": "project-structure-and-style", "name": "项目结构与规范", "file": "Python/project-structure-and-style.json", "description": "系统讲解代码组织结构、命名规范与PEP8、文档与注释、flake8风格检查、black/isort自动格式化等，帮助掌握Python项目工程化与规范化开发。"}, {"id": "dependency-management-and-build", "name": "依赖管理与构建", "file": "Python/dependency-management-and-build.json", "description": "系统讲解requirements.txt、pipenv、poetry、虚拟环境、构建与打包、PyPI发布等，帮助掌握Python依赖管理与项目发布。"}, {"id": "ci-cd-and-deployment", "name": "持续集成与部署", "file": "Python/ci-cd-and-deployment.json", "description": "系统讲解Git与版本管理、自动化测试、CI/CD流程、Docker容器化、云部署与服务器运维等，帮助掌握Python项目的持续集成与自动化部署。"}, {"id": "modern-python-ecosystem", "name": "现代Python生态", "file": "Python/modern-python-ecosystem.json", "description": "系统讲解NumPy、Pandas、Matplotlib、Django、Flask、FastAPI、scikit-learn、TensorFlow、PyTorch、Scrapy、Selenium等现代Python生态工具与应用。"}, {"id": "practical-projects", "name": "综合实战", "file": "Python/practical-projects.json", "description": "包含命令行工具、数据分析、Web应用、爬虫、自动化脚本等典型Python实战项目，帮助提升综合开发能力。"}]}