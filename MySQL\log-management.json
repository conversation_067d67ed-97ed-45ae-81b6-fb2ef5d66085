{"name": "Log Management", "trans": ["日志管理"], "methods": [{"name": "<PERSON><PERSON><PERSON>", "trans": ["错误日志"], "usage": {"syntax": "-- 查看错误日志文件路径\nSHOW VARIABLES LIKE 'log_error';", "description": "错误日志记录MySQL服务器启动、关闭、运行过程中的错误和警告信息，是排查数据库故障的重要依据。", "parameters": [{"name": "log_error", "description": "错误日志文件路径变量"}], "returnValue": "无返回值（查看日志内容需到操作系统层面）", "examples": [{"code": "-- 查看错误日志文件路径\nSHOW VARIABLES LIKE 'log_error';\n\n-- 在操作系统中查看日志内容（以Linux为例）\ncat /var/log/mysql/error.log", "explanation": "本例展示了如何获取MySQL错误日志文件路径，并在系统中查看日志内容。"}]}}, {"name": "General Query Log", "trans": ["查询日志"], "usage": {"syntax": "-- 启用查询日志\nSET GLOBAL general_log = 'ON';\n-- 查看查询日志文件路径\nSHOW VARIABLES LIKE 'general_log_file';", "description": "查询日志记录所有客户端发给MySQL服务器的SQL语句，适合调试和审计，但会影响性能，生产环境建议谨慎开启。", "parameters": [{"name": "general_log", "description": "查询日志开关"}, {"name": "general_log_file", "description": "查询日志文件路径变量"}], "returnValue": "无返回值（查看日志内容需到操作系统层面）", "examples": [{"code": "-- 启用查询日志\nSET GLOBAL general_log = 'ON';\n-- 查看查询日志文件路径\nSHOW VARIABLES LIKE 'general_log_file';\n\n-- 在操作系统中查看日志内容（以Linux为例）\ncat /var/log/mysql/mysql.log", "explanation": "本例展示了如何开启查询日志、获取日志文件路径并查看日志内容。"}]}}, {"name": "Slow Query Log", "trans": ["慢查询日志"], "usage": {"syntax": "-- 启用慢查询日志\nSET GLOBAL slow_query_log = 'ON';\n-- 设置慢查询阈值（秒）\nSET GLOBAL long_query_time = 2;\n-- 查看慢查询日志文件路径\nSHOW VARIABLES LIKE 'slow_query_log_file';", "description": "慢查询日志记录所有执行时间超过指定阈值的SQL语句，是优化数据库性能的重要工具。", "parameters": [{"name": "slow_query_log", "description": "慢查询日志开关"}, {"name": "long_query_time", "description": "慢查询阈值（秒）"}, {"name": "slow_query_log_file", "description": "慢查询日志文件路径变量"}], "returnValue": "无返回值（查看日志内容需到操作系统层面）", "examples": [{"code": "-- 启用慢查询日志\nSET GLOBAL slow_query_log = 'ON';\n-- 设置慢查询阈值为2秒\nSET GLOBAL long_query_time = 2;\n-- 查看慢查询日志文件路径\nSHOW VARIABLES LIKE 'slow_query_log_file';\n\n-- 在操作系统中查看日志内容（以Linux为例）\ncat /var/log/mysql/mysql-slow.log", "explanation": "本例展示了如何开启慢查询日志、设置阈值并查看日志内容。"}]}}, {"name": "Log Management Assignment", "trans": ["日志管理练习"], "usage": {"syntax": "# 日志管理练习", "description": "完成以下练习，巩固MySQL日志管理的基本技能。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 查看MySQL错误日志、查询日志和慢查询日志的文件路径。\n2. 启用查询日志和慢查询日志，执行几条SQL语句后，查看日志内容。\n3. 总结三类日志的作用和适用场景。", "explanation": "通过这些练习，你将掌握MySQL日志管理的常用操作和分析方法。"}]}}]}