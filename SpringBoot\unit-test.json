{"name": "Unit Testing", "trans": ["单元测试"], "methods": [{"name": "JUnit5 Integration", "trans": ["JUnit5集成"], "usage": {"syntax": "@SpringBootTest\n@Test\nvoid testMethod() { ... }", "description": "JUnit5是Spring Boot默认的测试框架，支持注解驱动的单元测试和集成测试。@SpringBootTest可加载完整Spring环境。", "parameters": [{"name": "@SpringBootTest", "description": "加载Spring Boot应用上下文"}, {"name": "@Test", "description": "标记测试方法"}], "returnValue": "无返回值", "examples": [{"code": "@SpringBootTest\npublic class UserServiceTest {\n    @Test\n    void testAddUser() {\n        // 测试逻辑\n        Assertions.assertEquals(1, 1);\n    }\n}", "explanation": "演示了JUnit5和@SpringBootTest的基本用法。"}]}}, {"name": "MockMvc Web Test", "trans": ["MockMvc测试Web接口"], "usage": {"syntax": "@AutoConfigureMockMvc\n@Autowired\nprivate MockMvc mockMvc;\nmockMvc.perform(MockMvcRequestBuilders.get(\"/api\")).andExpect(...);", "description": "MockMvc可用于不启动服务器的Web接口测试，支持请求模拟和断言响应。", "parameters": [{"name": "@AutoConfigureMockMvc", "description": "自动配置MockMvc"}, {"name": "MockMvc", "description": "模拟HTTP请求的核心对象"}], "returnValue": "无返回值", "examples": [{"code": "@SpringBootTest\n@AutoConfigureMockMvc\npublic class UserControllerTest {\n    @Autowired\n    private MockMvc mockMvc;\n    @Test\n    void testGetUser() throws Exception {\n        mockMvc.perform(MockMvcRequestBuilders.get(\"/users/1\"))\n            .andExpect(status().isOk())\n            .andExpect(jsonPath(\"$.id\").value(1));\n    }\n}", "explanation": "演示了MockMvc测试Web接口的用法。"}]}}, {"name": "Mo<PERSON>to and @MockBean", "trans": ["Mockito/MockBean用法"], "usage": {"syntax": "@MockBean\nwhen(mock.method()).thenReturn(value);", "description": "Mockito是常用的Mock框架，@MockBean可注入Mock对象到Spring容器，便于隔离依赖进行单元测试。", "parameters": [{"name": "@MockBean", "description": "注入Mock对象到Spring容器"}, {"name": "when/thenReturn", "description": "定义Mock行为"}], "returnValue": "无返回值", "examples": [{"code": "@SpringBootTest\npublic class OrderServiceTest {\n    @MockBean\n    private UserService userService;\n    @Test\n    void testOrder() {\n        Mockito.when(userService.getUser(1L)).thenReturn(new User(1L, \"张三\"));\n        // 测试逻辑\n    }\n}", "explanation": "演示了@MockBean和Mockito的基本用法。"}]}}, {"name": "Database Test (@DataJpaTest)", "trans": ["数据库测试（@DataJpaTest）"], "usage": {"syntax": "@DataJpaTest\n@Test\nvoid testRepository() { ... }", "description": "@DataJpaTest用于只加载JPA相关组件，适合Repository层的数据库单元测试，默认使用内存数据库。", "parameters": [{"name": "@DataJpaTest", "description": "只加载JPA相关Bean"}, {"name": "@Test", "description": "标记测试方法"}], "returnValue": "无返回值", "examples": [{"code": "@DataJpaTest\npublic class UserRepositoryTest {\n    @Autowired\n    private UserRepository userRepository;\n    @Test\n    void testSave() {\n        User user = new User(null, \"李四\");\n        userRepository.save(user);\n        Assertions.assertNotNull(user.getId());\n    }\n}", "explanation": "演示了@DataJpaTest进行数据库单元测试的方法。"}]}}, {"name": "Parameterized Test", "trans": ["参数化测试"], "usage": {"syntax": "@ParameterizedTest\n@ValueSource(ints = {1,2,3})\nvoid test(int value) { ... }", "description": "参数化测试可用一组数据多次运行同一测试方法，提升测试覆盖率。", "parameters": [{"name": "@ParameterizedTest", "description": "参数化测试注解"}, {"name": "@ValueSource", "description": "提供测试数据源"}], "returnValue": "无返回值", "examples": [{"code": "@ParameterizedTest\n@ValueSource(ints = {1, 2, 3})\nvoid testAdd(int value) {\n    Assertions.assertTrue(value > 0);\n}", "explanation": "演示了参数化测试的用法。"}]}}, {"name": "Unit Test Assignment", "trans": ["单元测试练习"], "usage": {"syntax": "# 单元测试练习", "description": "完成以下练习，巩固Spring Boot单元测试相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 使用JUnit5和@SpringBootTest编写一个服务类的单元测试。\n2. 使用MockMvc测试一个Web接口。\n3. 使用@MockBean和Mockito隔离依赖进行测试。\n4. 使用@DataJpaTest测试Repository层。\n5. 编写一个参数化测试方法。", "explanation": "通过这些练习掌握Spring Boot单元测试的核心用法。"}]}}]}