{"name": "Integration Test", "trans": ["集成测试"], "methods": [{"name": "Using integration_test Package", "trans": ["integration_test包"], "usage": {"syntax": "import 'package:integration_test/integration_test.dart';\nIntegrationTestWidgetsFlutterBinding.ensureInitialized();\ntestWidgets('描述', (WidgetTester tester) async { ... });", "description": "integration_test包是Flutter官方的集成测试框架，用于在真实设备上进行端到端的自动化测试。", "parameters": [{"name": "IntegrationTestWidgetsFlutterBinding", "description": "集成测试绑定，初始化测试环境"}, {"name": "testWidgets", "description": "测试用例函数，包含描述和测试逻辑"}], "returnValue": "无返回值，测试通过或失败。", "examples": [{"code": "// integration_test包用法示例\nimport 'package:flutter/material.dart';\nimport 'package:flutter_test/flutter_test.dart';\nimport 'package:integration_test/integration_test.dart';\n\nimport 'package:my_app/main.dart' as app;\n\nvoid main() {\n  IntegrationTestWidgetsFlutterBinding.ensureInitialized();\n  testWidgets('计数器测试', (WidgetTester tester) async {\n    app.main();\n    await tester.pumpAndSettle();\n    expect(find.text('0'), findsOneWidget);\n    await tester.tap(find.byIcon(Icons.add));\n    await tester.pumpAndSettle();\n    expect(find.text('1'), findsOneWidget);\n  });\n}\n", "explanation": "演示如何用integration_test包编写简单的集成测试，运行实际应用并测试计数器功能。"}]}}, {"name": "Automated UI Test", "trans": ["自动化UI测试"], "usage": {"syntax": "tester.pumpAndSettle、tester.tap、tester.drag、tester.enterText等", "description": "通过集成测试可以自动化测试整个应用的UI交互流程，包括页面跳转、表单填写、列表滚动等。", "parameters": [{"name": "pumpAndSettle", "description": "等待所有动画和异步操作完成"}, {"name": "tap/drag/enterText", "description": "模拟各种用户交互"}, {"name": "expect", "description": "断言UI状态"}], "returnValue": "无返回值，测试通过或失败。", "examples": [{"code": "// 自动化UI测试示例\nimport 'package:flutter/material.dart';\nimport 'package:flutter_test/flutter_test.dart';\nimport 'package:integration_test/integration_test.dart';\n\nimport 'package:my_app/main.dart' as app;\n\nvoid main() {\n  IntegrationTestWidgetsFlutterBinding.ensureInitialized();\n  testWidgets('登录流程测试', (WidgetTester tester) async {\n    app.main();\n    await tester.pumpAndSettle();\n    // 点击登录按钮\n    await tester.tap(find.text('登录'));\n    await tester.pumpAndSettle();\n    // 输入用户名密码\n    await tester.enterText(find.byKey(Key('username')), 'testuser');\n    await tester.enterText(find.byKey(Key('password')), '123456');\n    // 点击提交\n    await tester.tap(find.text('提交'));\n    await tester.pumpAndSettle();\n    // 验证登录成功\n    expect(find.text('欢迎, testuser'), findsOneWidget);\n  });\n}\n", "explanation": "演示如何通过集成测试自动化测试登录流程。"}]}}, {"name": "Assignment", "trans": ["作业：编写一个完整的集成测试用例。"], "usage": {"syntax": "无", "description": "请为一个简单的待办事项应用编写集成测试，要求测试添加、完成、删除待办事项的完整流程。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 作业参考实现\nimport 'package:flutter/material.dart';\nimport 'package:flutter_test/flutter_test.dart';\nimport 'package:integration_test/integration_test.dart';\n\nimport 'package:todo_app/main.dart' as app;\n\nvoid main() {\n  IntegrationTestWidgetsFlutterBinding.ensureInitialized();\n  testWidgets('待办事项应用测试', (WidgetTester tester) async {\n    app.main();\n    await tester.pumpAndSettle();\n    // 添加待办事项\n    await tester.tap(find.byIcon(Icons.add));\n    await tester.pumpAndSettle();\n    await tester.enterText(find.byType(TextField), '学习Flutter');\n    await tester.tap(find.text('保存'));\n    await tester.pumpAndSettle();\n    // 验证添加成功\n    expect(find.text('学习Flutter'), findsOneWidget);\n    // 标记为已完成\n    await tester.tap(find.byType(Checkbox).first);\n    await tester.pumpAndSettle();\n    // 验证已完成状态\n    final Checkbox checkbox = tester.widget(find.byType(Checkbox).first);\n    expect(checkbox.value, true);\n    // 删除待办事项\n    await tester.tap(find.byIcon(Icons.delete).first);\n    await tester.pumpAndSettle();\n    // 验证删除成功\n    expect(find.text('学习Flutter'), findsNothing);\n  });\n}\n", "explanation": "作业要求测试待办事项应用的添加、完成、删除功能。"}]}}]}