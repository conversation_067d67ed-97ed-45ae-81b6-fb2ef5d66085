{"name": "Exception Handling", "trans": ["异常处理"], "methods": [{"name": "Try/Catch/Finally", "trans": ["try/catch/finally"], "usage": {"syntax": "try {\n  // 可能抛出异常的代码\n} on 特定异常类型 {\n  // 处理特定类型的异常\n} on 另一种异常类型 catch(e) {\n  // 处理另一种类型的异常，并可以访问异常对象\n} catch (e, s) {\n  // 处理任何其他类型的异常\n  // e是异常对象，s是堆栈跟踪\n} finally {\n  // 无论是否发生异常，总是执行的代码\n}", "description": "Dart中的异常处理机制允许程序在运行时检测和响应错误情况，而不会导致程序崩溃。try/catch/finally是Dart中处理异常的主要语法结构。\n\ntry块包含可能引发异常的代码。当try块中的代码抛出异常时，执行流会立即跳转到相应的catch块。如果没有匹配的catch块，异常会向上传递。\n\nDart提供了两种捕获异常的方式：\n- 使用on关键字捕获特定类型的异常\n- 使用catch关键字捕获异常对象\n\n这两种方式可以结合使用，也可以单独使用。通过on关键字，可以根据异常类型执行不同的处理逻辑；通过catch关键字，可以访问异常对象和堆栈跟踪信息。\n\nfinally块包含无论是否发生异常都会执行的代码，通常用于释放资源、清理操作或完成必要的最终步骤。finally块在以下情况后执行：\n- try块正常完成\n- try块因为异常而退出，并且该异常被某个catch块处理\n- try块因为异常而退出，但没有匹配的catch块（在这种情况下，finally块执行后异常会继续传递）\n\n即使在try或catch块中使用了return、break或continue语句，finally块也会在函数返回或循环控制流改变之前执行。", "parameters": [], "returnValue": "无特定返回值", "examples": [{"code": "void main() {\n  print('开始异常处理示例');\n  \n  // 基本的try/catch示例\n  print('\\n1. 基本的try/catch:');\n  try {\n    int result = 10 ~/ 0; // 整数除以零会抛出异常\n    print('  结果: $result'); // 这行不会执行\n  } catch (e) {\n    print('  捕获到异常: $e');\n  }\n  \n  // 使用on捕获特定类型的异常\n  print('\\n2. 使用on捕获特定异常类型:');\n  try {\n    int result = 10 ~/ 0; // IntegerDivisionByZeroException\n    print('  结果: $result');\n  } on IntegerDivisionByZeroException {\n    print('  捕获到除零异常');\n  } on FormatException {\n    print('  捕获到格式异常');\n  } catch (e) {\n    print('  捕获到其他异常: $e');\n  }\n  \n  // 结合on和catch\n  print('\\n3. 结合on和catch:');\n  try {\n    int result = int.parse('abc'); // FormatException\n    print('  结果: $result');\n  } on FormatException catch (e) {\n    print('  捕获到格式异常: $e');\n    print('  异常消息: ${e.message}');\n    print('  源数据: ${e.source}');\n  }\n  \n  // 捕获异常和堆栈跟踪\n  print('\\n4. 捕获异常和堆栈跟踪:');\n  try {\n    throw Exception('这是一个故意抛出的异常');\n  } catch (e, stackTrace) {\n    print('  捕获到异常: $e');\n    print('  堆栈跟踪:');\n    print('  ${stackTrace.toString().split('\\n').take(3).join('\\n  ')}...'); // 只显示前三行\n  }\n  \n  // 使用finally块\n  print('\\n5. 使用finally块:');\n  try {\n    print('  尝试一些可能会失败的操作');\n    // 模拟成功的操作\n  } catch (e) {\n    print('  操作失败: $e');\n  } finally {\n    print('  无论成功还是失败，这里的代码都会执行');\n  }\n  \n  // finally块与return的交互\n  print('\\n6. finally块与return的交互:');\n  String result = processFile('test.txt');\n  print('  处理结果: $result');\n  \n  // 嵌套的try/catch\n  print('\\n7. 嵌套的try/catch:');\n  try {\n    print('  外部try块开始');\n    try {\n      print('  内部try块开始');\n      throw Exception('内部异常');\n      print('  内部try块结束'); // 不会执行\n    } catch (e) {\n      print('  内部catch块捕获异常: $e');\n      throw Exception('重新抛出新异常'); // 抛出新异常\n    } finally {\n      print('  内部finally块执行');\n    }\n    print('  外部try块结束'); // 不会执行\n  } catch (e) {\n    print('  外部catch块捕获异常: $e');\n  } finally {\n    print('  外部finally块执行');\n  }\n  \n  // 使用rethrow重新抛出当前异常\n  print('\\n8. 使用rethrow重新抛出当前异常:');\n  try {\n    try {\n      int.parse('非数字'); // 抛出FormatException\n    } catch (e) {\n      print('  内部处理: 记录异常 $e');\n      rethrow; // 重新抛出同一个异常，保留原始堆栈跟踪\n    }\n  } catch (e) {\n    print('  外部处理: 最终捕获异常 $e');\n  }\n  \n  print('\\n异常处理示例结束');\n}\n\n// 演示finally与return的交互\nString processFile(String filename) {\n  print('  开始处理文件: $filename');\n  try {\n    // 模拟文件处理\n    print('  读取文件内容');\n    if (filename.contains('.')) {\n      return '成功读取文件';\n    }\n    throw Exception('无效的文件名');\n  } catch (e) {\n    print('  处理文件时出错: $e');\n    return '处理出错';\n  } finally {\n    // 即使有return语句，finally块也会执行\n    print('  关闭文件资源（总是执行）');\n  }\n}", "explanation": "这个示例全面展示了Dart中的try/catch/finally异常处理机制。首先，演示了基本的try/catch用法，捕获整数除以零的异常。然后，展示了如何使用on关键字捕获特定类型的异常，如IntegerDivisionByZeroException。接着，演示了如何结合使用on和catch来捕获特定类型的异常并访问异常对象的详细信息。示例还展示了如何捕获异常和堆栈跟踪信息，这对于调试非常有用。finally块的使用也得到了演示，展示了无论try块是否抛出异常，finally块中的代码都会执行。示例中的processFile函数说明了finally块与return语句的交互关系，即使函数通过return语句返回，finally块中的代码也会在函数真正返回之前执行。最后，示例演示了嵌套的try/catch/finally结构和使用rethrow关键字重新抛出当前异常的方法。"}, {"code": "import 'dart:io';\n\nvoid main() {\n  print('异常处理实践示例');\n  \n  // 1. 文件操作中的异常处理\n  print('\\n1. 文件操作中的异常处理:');\n  safeReadFile('existing.txt');\n  safeReadFile('nonexistent.txt');\n  \n  // 2. 数据解析中的异常处理\n  print('\\n2. 数据解析中的异常处理:');\n  parseJsonData('{\"name\": \"<PERSON>\", \"age\": 30}');\n  parseJsonData('{name: <PERSON>}'); // 无效的JSON\n  parseJsonData('不是JSON格式');\n  \n  // 3. 资源管理模式\n  print('\\n3. 资源管理模式:');\n  processResourceWithCleanup();\n  \n  // 4. 多重异常处理策略\n  print('\\n4. 多重异常处理策略:');\n  handleMultipleExceptions(1); // 除零错误\n  handleMultipleExceptions(2); // 格式错误\n  handleMultipleExceptions(3); // 自定义错误\n  handleMultipleExceptions(4); // 未分类错误\n  \n  print('\\n异常处理实践示例完成');\n}\n\n// 安全读取文件的示例\nvoid safeReadFile(String filename) {\n  print('  尝试读取文件: $filename');\n  try {\n    // 模拟文件操作\n    if (filename == 'existing.txt') {\n      print('  文件内容: 这是文件中的文本内容');\n    } else {\n      throw FileSystemException('文件不存在', filename);\n    }\n  } on FileSystemException catch (e) {\n    print('  文件系统错误: ${e.message}, ${e.path}');\n    print('  使用默认内容代替');\n  } catch (e) {\n    print('  读取文件时发生未知错误: $e');\n  } finally {\n    print('  文件操作完成');\n  }\n}\n\n// JSON解析示例\nvoid parseJsonData(String jsonStr) {\n  print('  尝试解析JSON: $jsonStr');\n  try {\n    // 模拟JSON解析\n    if (jsonStr.startsWith('{') && jsonStr.endsWith('}')) {\n      if (jsonStr.contains('\"name\"') && jsonStr.contains('\"age\"')) {\n        print('  解析成功，提取数据: 名称=John, 年龄=30');\n      } else {\n        throw FormatException('JSON格式不正确', jsonStr);\n      }\n    } else {\n      throw FormatException('不是有效的JSON字符串', jsonStr);\n    }\n  } on FormatException catch (e) {\n    print('  格式错误: ${e.message}');\n    print('  问题数据: ${e.source}');\n    print('  使用默认值代替');\n  } catch (e) {\n    print('  解析过程中发生未知错误: $e');\n  }\n}\n\n// 资源管理模式示例\nvoid processResourceWithCleanup() {\n  print('  开始处理资源');\n  Resource resource = Resource('数据库连接');\n  \n  try {\n    print('  使用资源进行操作');\n    // 模拟操作过程中出现问题\n    if (DateTime.now().millisecond % 2 == 0) {\n      throw Exception('处理过程中出现随机错误');\n    }\n    print('  操作成功完成');\n  } catch (e) {\n    print('  处理资源时出错: $e');\n  } finally {\n    // 无论操作是否成功，确保资源被清理\n    resource.close();\n    print('  资源已关闭');\n  }\n}\n\n// 模拟资源类\nclass Resource {\n  final String name;\n  bool isOpen = true;\n  \n  Resource(this.name) {\n    print('  资源\"$name\"已打开');\n  }\n  \n  void close() {\n    if (isOpen) {\n      print('  正在关闭资源\"$name\"');\n      isOpen = false;\n    } else {\n      print('  资源\"$name\"已经关闭');\n    }\n  }\n}\n\n// 多重异常处理策略示例\nvoid handleMultipleExceptions(int scenario) {\n  print('  场景 $scenario:');\n  try {\n    switch (scenario) {\n      case 1:\n        // 触发除零错误\n        print('  尝试除以零');\n        int result = 10 ~/ 0;\n        print('  结果: $result'); // 不会执行\n        break;\n      case 2:\n        // 触发格式错误\n        print('  尝试解析非数字');\n        int.parse('非数字');\n        break;\n      case 3:\n        // 触发自定义错误\n        print('  触发业务逻辑错误');\n        throw BusinessException('无效的操作', code: 'INVALID_OP');\n        break;\n      default:\n        // 触发一般错误\n        print('  触发一般错误');\n        throw Exception('未知错误');\n    }\n  } on IntegerDivisionByZeroException {\n    print('  处理策略: 除零错误 - 提供默认值0');\n  } on FormatException catch (e) {\n    print('  处理策略: 格式错误 - ${e.message}，使用默认值');\n  } on BusinessException catch (e) {\n    print('  处理策略: 业务错误 - ${e.message}，错误代码: ${e.code}');\n    print('  通知用户并记录日志');\n  } catch (e) {\n    print('  处理策略: 通用错误处理 - $e');\n    print('  记录错误并向管理员报告');\n  } finally {\n    print('  完成当前场景处理');\n  }\n}\n\n// 自定义业务异常\nclass BusinessException implements Exception {\n  final String message;\n  final String code;\n  \n  BusinessException(this.message, {required this.code});\n  \n  @override\n  String toString() => 'BusinessException: $message (Code: $code)';\n}", "explanation": "这个示例展示了Dart异常处理在实际应用场景中的使用。首先演示了文件操作中的异常处理，通过捕获FileSystemException来处理文件不存在的情况。然后展示了数据解析中的异常处理，特别是处理JSON解析时可能出现的FormatException。接下来演示了资源管理模式，这是try/finally的一个常见用例，确保无论操作是否成功，资源（如数据库连接、文件句柄等）都能被正确关闭。最后，展示了多重异常处理策略，根据不同类型的异常采取不同的处理方法，包括处理内置异常（如IntegerDivisionByZeroException和FormatException）以及自定义的业务异常（BusinessException）。这个示例展示了如何在实际应用中构建健壮的错误处理机制，提高应用的可靠性和用户体验。"}, {"code": "void main() {\n  print('异常处理练习：');\n  \n  // 1. 编写一个函数，尝试将字符串转换为整数，处理可能的FormatException\n  // 如果转换成功，返回该整数；如果失败，返回默认值0\n  \n  // 测试数据\n  List<String> testInputs = ['123', '456abc', '', 'dart'];\n  \n  // 在这里实现您的解决方案\n  for (var input in testInputs) {\n    int result = parseIntSafely(input);\n    print('将\"$input\"转换为整数: $result');\n  }\n}\n\n// 安全的整数解析函数\nint parseIntSafely(String str) {\n  try {\n    return int.parse(str);\n  } on FormatException {\n    // 当输入无法解析为整数时返回默认值\n    return 0;\n  }\n}", "explanation": "这是一个简单的异常处理练习，要求学生编写一个函数，安全地将字符串转换为整数。函数需要捕获可能出现的FormatException（当字符串不能被解析为整数时抛出），并在发生异常时返回默认值0。这个练习帮助学生理解如何使用try/on结构来处理特定类型的异常，以及如何构建更健壮的函数，使其在输入无效时不会崩溃。"}]}}, {"name": "Throw and Custom Exceptions", "trans": ["抛出异常与自定义异常"], "usage": {"syntax": "// 抛出异常\nthrow Exception('异常信息');\nthrow ArgumentError('无效参数');\nthrow FormatException('格式错误', 原始数据);\nthrow '这是一个字符串异常'; // 任何对象都可以作为异常抛出\n\n// 自定义异常类\nclass 自定义异常类 implements Exception {\n  // 属性\n  // 构造函数\n  // 方法\n}", "description": "在Dart中，异常不仅可以被捕获，还可以被主动抛出。当检测到程序无法正常执行或者满足特定条件时，开发者可以使用throw语句抛出异常，中断正常的程序流程。\n\n抛出异常的主要目的是：\n- 表明程序遇到了无法处理的情况\n- 将错误处理的责任转移给调用者\n- 提供详细的错误信息，以便调试和日志记录\n\nDart允许抛出任何对象作为异常，包括内置异常类、自定义异常类，甚至基本数据类型。但推荐使用实现了Exception或Error接口的对象，以保持代码的一致性和可读性。\n\nDart提供了多种内置异常类：\n- Exception：通用异常的基类，通常表示程序可以处理的异常情况\n- Error：表示程序错误，通常是不应该尝试捕获的严重问题\n- ArgumentError：表示函数参数错误\n- FormatException：表示格式错误，如解析无效的数字字符串\n- StateError：表示对象状态错误，如在迭代器已经结束后继续调用next()\n- RangeError：表示索引超出范围\n\n自定义异常类可以提供更多特定领域的信息和行为，使异常处理更加精确和有意义。通常，自定义异常类需要实现Exception接口，并可以根据需要定义属性、构造函数和方法。自定义异常可以包含更多上下文信息，如错误代码、错误发生时间、受影响的对象等。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "void main() {\n  print('开始抛出异常与自定义异常示例');\n  \n  // 1. 抛出内置异常\n  print('\\n1. 抛出内置异常:');\n  try {\n    validateAge(-5); // 传递无效年龄\n  } catch (e) {\n    print('  捕获到异常: $e');\n  }\n  \n  try {\n    validateEmail('invalid-email'); // 传递无效邮箱\n  } catch (e) {\n    print('  捕获到异常: $e');\n  }\n  \n  // 2. 抛出任意对象作为异常\n  print('\\n2. 抛出任意对象作为异常:');\n  try {\n    // 抛出字符串作为异常\n    if (DateTime.now().second % 2 == 0) {\n      throw '当前秒数是偶数';\n    } else {\n      throw 404; // 抛出整数作为异常\n    }\n  } catch (e) {\n    print('  捕获到非标准异常: $e (类型: ${e.runtimeType})');\n  }\n  \n  // 3. 自定义异常类\n  print('\\n3. 自定义异常类:');\n  try {\n    checkPermission('admin_area', 'user');\n  } catch (e) {\n    if (e is AuthorizationException) {\n      print('  认证错误: ${e.message}');\n      print('  用户: ${e.user}, 需要权限: ${e.requiredPermission}');\n      print('  时间戳: ${e.timestamp}');\n      \n      // 使用自定义方法\n      print('  建议操作: ${e.getSuggestedAction()}');\n    } else {\n      print('  其他错误: $e');\n    }\n  }\n  \n  // 4. 异常层次结构\n  print('\\n4. 异常层次结构:');\n  try {\n    processPayment(150, 'expired_card');\n  } catch (e) {\n    if (e is PaymentException) {\n      print('  支付错误: ${e.message}');\n      if (e is InsufficientFundsException) {\n        print('  余额不足。当前余额: ${e.currentBalance}, 需要: ${e.requiredAmount}');\n        print('  差额: ${e.requiredAmount - e.currentBalance}');\n      } else if (e is InvalidPaymentMethodException) {\n        print('  支付方式无效。原因: ${e.reason}');\n      }\n    } else {\n      print('  其他错误: $e');\n    }\n  }\n  \n  // 5. 抛出带堆栈跟踪的异常\n  print('\\n5. 捕获异常的堆栈跟踪:');\n  try {\n    deepFunction(3); // 触发嵌套函数中的异常\n  } catch (e, stackTrace) {\n    print('  异常: $e');\n    print('  堆栈跟踪:');\n    print('  ${stackTrace.toString().split('\\n').take(5).join('\\n  ')}...'); // 显示前5行\n  }\n  \n  print('\\n抛出异常与自定义异常示例结束');\n}\n\n// 验证年龄，抛出ArgumentError\nvoid validateAge(int age) {\n  print('  验证年龄: $age');\n  if (age < 0) {\n    throw ArgumentError('年龄不能为负数');\n  }\n  if (age > 120) {\n    throw ArgumentError.value(age, 'age', '年龄不能超过120岁');\n  }\n  print('  年龄有效');\n}\n\n// 验证邮箱，抛出FormatException\nvoid validateEmail(String email) {\n  print('  验证邮箱: $email');\n  if (!email.contains('@')) {\n    throw FormatException('邮箱格式无效，必须包含@符号', email);\n  }\n  print('  邮箱格式有效');\n}\n\n// 自定义认证异常类\nclass AuthorizationException implements Exception {\n  final String message;\n  final String user;\n  final String requiredPermission;\n  final DateTime timestamp;\n  \n  AuthorizationException(this.message, this.user, this.requiredPermission)\n      : timestamp = DateTime.now();\n  \n  // 自定义方法\n  String getSuggestedAction() {\n    return '请联系管理员获取 $requiredPermission 权限';\n  }\n  \n  @override\n  String toString() => 'AuthorizationException: $message';\n}\n\n// 检查权限，抛出AuthorizationException\nvoid checkPermission(String area, String userRole) {\n  print('  检查权限: 用户=$userRole, 区域=$area');\n  if (area == 'admin_area' && userRole != 'admin') {\n    throw AuthorizationException(\n      '没有访问管理区域的权限',\n      userRole,\n      'admin'\n    );\n  }\n  print('  权限检查通过');\n}\n\n// 异常层次结构示例\n\n// 基础支付异常\nclass PaymentException implements Exception {\n  final String message;\n  \n  PaymentException(this.message);\n  \n  @override\n  String toString() => 'PaymentException: $message';\n}\n\n// 余额不足异常\nclass InsufficientFundsException extends PaymentException {\n  final double currentBalance;\n  final double requiredAmount;\n  \n  InsufficientFundsException(this.currentBalance, this.requiredAmount)\n      : super('余额不足');\n  \n  @override\n  String toString() => 'InsufficientFundsException: $message ';\n}\n\n// 支付方式无效异常\nclass InvalidPaymentMethodException extends PaymentException {\n  final String reason;\n  \n  InvalidPaymentMethodException(this.reason)\n      : super('支付方式无效');\n  \n  @override\n  String toString() => 'InvalidPaymentMethodException: $message';\n}\n\n// 处理支付，可能抛出不同类型的支付异常\nvoid processPayment(double amount, String method) {\n  print('  处理支付: 金额=$amount, 方式=$method');\n  \n  // 模拟余额不足\n  if (amount > 100) {\n    throw InsufficientFundsException(50, amount);\n  }\n  \n  // 模拟支付方式无效\n  if (method == 'expired_card') {\n    throw InvalidPaymentMethodException('卡片已过期');\n  }\n  \n  print('  支付成功');\n}\n\n// 嵌套调用中抛出异常以展示堆栈跟踪\nvoid deepFunction(int level) {\n  print('  进入第$level层函数');\n  if (level > 0) {\n    deepFunction(level - 1);\n  } else {\n    print('  到达最深层，抛出异常');\n    throw Exception('在深层函数中触发的异常');\n  }\n}", "explanation": "这个示例全面展示了Dart中抛出异常和自定义异常的各种方式。首先，展示了如何抛出内置异常，如ArgumentError和FormatException，用于验证用户输入。然后，演示了Dart允许抛出任何对象作为异常的特性，包括字符串和整数。接着，详细展示了如何创建自定义异常类，如AuthorizationException，它包含了与认证相关的特定信息和自定义方法。示例还演示了如何构建异常层次结构，通过继承基础异常类(PaymentException)来创建更具体的异常类型(InsufficientFundsException和InvalidPaymentMethodException)，使异常处理更加精确。最后，示例展示了如何捕获包含堆栈跟踪的异常，这对于调试和错误定位非常有用。"}, {"code": "void main() {\n  print('自定义异常在实际应用中的示例');\n  \n  // 创建一个简单的用户管理系统\n  UserManager userManager = UserManager();\n  \n  // 1. 测试用户注册\n  print('\\n1. 测试用户注册:');\n  try {\n    userManager.registerUser('alice', 'pass123', '<EMAIL>');\n    print('  用户注册成功');\n  } catch (e) {\n    handleUserException(e);\n  }\n  \n  // 尝试注册已存在的用户\n  try {\n    userManager.registerUser('alice', 'different', '<EMAIL>');\n    print('  用户注册成功'); // 不应该执行到这里\n  } catch (e) {\n    handleUserException(e);\n  }\n  \n  // 尝试使用无效的电子邮件注册\n  try {\n    userManager.registerUser('bob', 'pass456', 'invalid-email');\n    print('  用户注册成功'); // 不应该执行到这里\n  } catch (e) {\n    handleUserException(e);\n  }\n  \n  // 2. 测试用户登录\n  print('\\n2. 测试用户登录:');\n  try {\n    User loggedInUser = userManager.loginUser('alice', 'pass123');\n    print('  登录成功: ${loggedInUser.username}');\n  } catch (e) {\n    handleUserException(e);\n  }\n  \n  // 尝试使用错误的密码登录\n  try {\n    userManager.loginUser('alice', 'wrongpass');\n    print('  登录成功'); // 不应该执行到这里\n  } catch (e) {\n    handleUserException(e);\n  }\n  \n  // 尝试登录不存在的用户\n  try {\n    userManager.loginUser('nonexistent', 'anypass');\n    print('  登录成功'); // 不应该执行到这里\n  } catch (e) {\n    handleUserException(e);\n  }\n  \n  // 3. 测试用户权限\n  print('\\n3. 测试用户权限:');\n  try {\n    // 注册一个普通用户\n    userManager.registerUser('bob', 'pass456', '<EMAIL>');\n    User bobUser = userManager.loginUser('bob', 'pass456');\n    \n    // 尝试执行管理员操作\n    userManager.deleteUser(bobUser, 'alice');\n    print('  删除用户成功'); // 不应该执行到这里\n  } catch (e) {\n    handleUserException(e);\n  }\n  \n  // 创建管理员并尝试删除用户\n  try {\n    // 注册并设置为管理员\n    userManager.registerUser('admin', 'adminpass', '<EMAIL>');\n    User adminUser = userManager.loginUser('admin', 'adminpass');\n    adminUser.role = 'admin';\n    \n    // 执行管理员操作\n    userManager.deleteUser(adminUser, 'bob');\n    print('  删除用户成功');\n  } catch (e) {\n    handleUserException(e);\n  }\n  \n  print('\\n自定义异常在实际应用中的示例结束');\n}\n\n// 统一处理用户相关异常\nvoid handleUserException(dynamic e) {\n  if (e is UserException) {\n    print('  用户操作错误 [${e.code}]: ${e.message}');\n    if (e.details != null) {\n      print('  详细信息: ${e.details}');\n    }\n    // 可以基于错误代码执行特定操作\n    switch (e.code) {\n      case 'USER_EXISTS':\n        print('  建议: 尝试使用不同的用户名');\n        break;\n      case 'INVALID_EMAIL':\n        print('  建议: 请使用有效的电子邮件格式');\n        break;\n      case 'AUTH_FAILED':\n        print('  建议: 检查用户名和密码是否正确');\n        break;\n      case 'PERMISSION_DENIED':\n        print('  建议: 请联系管理员获取必要权限');\n        break;\n    }\n  } else {\n    print('  发生未知错误: $e');\n  }\n}\n\n// 用户模型\nclass User {\n  final String username;\n  final String password; // 注意：实际应用中应该加密存储\n  final String email;\n  String role; // 'user' 或 'admin'\n  \n  User(this.username, this.password, this.email, [this.role = 'user']);\n}\n\n// 用户管理器\nclass UserManager {\n  final Map<String, User> _users = {};\n  \n  // 注册新用户\n  void registerUser(String username, String password, String email) {\n    // 验证用户名是否已存在\n    if (_users.containsKey(username)) {\n      throw UserException(\n        'USER_EXISTS',\n        '用户名 \"$username\" 已被使用',\n        '请选择其他用户名'\n      );\n    }\n    \n    // 验证电子邮件格式\n    if (!email.contains('@')) {\n      throw UserException(\n        'INVALID_EMAIL',\n        '电子邮件格式无效',\n        '电子邮件必须包含 @ 符号'\n      );\n    }\n    \n    // 创建新用户\n    _users[username] = User(username, password, email);\n  }\n  \n  // 用户登录\n  User loginUser(String username, String password) {\n    // 检查用户是否存在\n    if (!_users.containsKey(username)) {\n      throw UserException(\n        'USER_NOT_FOUND',\n        '用户 \"$username\" 不存在',\n      );\n    }\n    \n    User user = _users[username]!;\n    \n    // 验证密码\n    if (user.password != password) {\n      throw UserException(\n        'AUTH_FAILED',\n        '认证失败',\n        '用户名或密码不正确'\n      );\n    }\n    \n    return user;\n  }\n  \n  // 删除用户 (需要管理员权限)\n  void deleteUser(User currentUser, String usernameToDelete) {\n    // 检查权限\n    if (currentUser.role != 'admin') {\n      throw UserException(\n        'PERMISSION_DENIED',\n        '权限不足',\n        '需要管理员权限才能删除用户'\n      );\n    }\n    \n    // 检查要删除的用户是否存在\n    if (!_users.containsKey(usernameToDelete)) {\n      throw UserException(\n        'USER_NOT_FOUND',\n        '用户 \"$usernameToDelete\" 不存在',\n      );\n    }\n    \n    // 删除用户\n    _users.remove(usernameToDelete);\n  }\n}\n\n// 自定义用户异常\nclass UserException implements Exception {\n  final String code;\n  final String message;\n  final String? details;\n  \n  UserException(this.code, this.message, [this.details]);\n  \n  @override\n  String toString() => 'UserException[$code]: $message';\n}", "explanation": "这个示例展示了如何在实际应用中使用自定义异常来处理用户管理系统中的各种错误情况。示例定义了UserException类，它包含错误代码、错误消息和可选的详细信息，使得错误处理更加结构化和信息丰富。用户管理器(UserManager)在不同操作中抛出特定的异常，如用户注册时检查用户名是否已存在和电子邮件格式是否有效，登录时验证用户凭据，以及删除用户时检查权限。示例还演示了如何统一处理这些异常，根据异常的错误代码提供适当的反馈和建议。这种方法使得错误处理更加一致，并提高了应用的用户体验。"}, {"code": "void main() {\n  print('自定义异常练习：');\n  \n  // 创建一个计算器，实现除法操作\n  // 要求：\n  // 1. 当除数为0时，抛出自定义的DivisionByZeroException异常\n  // 2. 当结果太大时（>1000），抛出自定义的ResultTooLargeException异常\n  // 3. 异常类应包含适当的错误信息和其他相关数据\n  \n  // 测试数据\n  List<Map<String, num>> testCases = [\n    {'a': 10, 'b': 2},    // 正常情况: 10 / 2 = 5\n    {'a': 10, 'b': 0},    // 除以零错误\n    {'a': 5000, 'b': 2},  // 结果太大: 5000 / 2 = 2500\n  ];\n  \n  // 在这里实现您的解决方案\n  Calculator calculator = Calculator();\n  \n  for (var testCase in testCases) {\n    try {\n      num a = testCase['a']!;\n      num b = testCase['b']!;\n      num result = calculator.divide(a, b);\n      print('$a / $b = $result');\n    } on DivisionByZeroException catch (e) {\n      print('错误: ${e.message}');\n      print('操作数: ${e.dividend}');\n    } on ResultTooLargeException catch (e) {\n      print('错误: ${e.message}');\n      print('计算结果: ${e.result}, 最大允许值: ${e.maxAllowed}');\n    } catch (e) {\n      print('发生未知错误: $e');\n    }\n  }\n}\n\n// 自定义除以零异常\nclass DivisionByZeroException implements Exception {\n  final String message;\n  final num dividend; // 被除数\n  \n  DivisionByZeroException(this.dividend)\n      : message = '不能除以零';\n  \n  @override\n  String toString() => 'DivisionByZeroException: $message';\n}\n\n// 自定义结果太大异常\nclass ResultTooLargeException implements Exception {\n  final String message;\n  final num result;\n  final num maxAllowed;\n  \n  ResultTooLargeException(this.result, this.maxAllowed)\n      : message = '计算结果超出允许范围';\n  \n  @override\n  String toString() => 'ResultTooLargeException: $message';\n}\n\n// 计算器类\nclass Calculator {\n  // 除法操作\n  num divide(num a, num b) {\n    if (b == 0) {\n      throw DivisionByZeroException(a);\n    }\n    \n    num result = a / b;\n    \n    if (result > 1000) {\n      throw ResultTooLargeException(result, 1000);\n    }\n    \n    return result;\n  }\n}", "explanation": "这个练习要求学生实现一个带有自定义异常处理的计算器。学生需要创建两个自定义异常类：DivisionByZeroException（处理除数为零的情况）和ResultTooLargeException（处理结果超出范围的情况）。这些异常类不仅包含错误消息，还包含相关的上下文数据：DivisionByZeroException包含被除数，ResultTooLargeException包含实际结果和最大允许值。Calculator类的divide方法会在适当的情况下抛出这些异常，主程序通过try-catch块捕获并处理这些异常，显示相关的错误信息和数据。这个练习帮助学生理解如何设计和使用自定义异常来提供更有意义的错误处理。"}]}}, {"name": "Async Exception Handling", "trans": ["异步异常捕获"], "usage": {"syntax": "// 使用try-catch捕获异步异常\ntry {\n  await 异步函数调用();\n} catch (e) {\n  // 处理异常\n}\n\n// 使用Future的catchError方法\n异步函数调用()\n  .then((result) => 处理结果)\n  .catchError((error) => 处理错误);\n\n// 使用onError处理Stream中的错误\nstream.listen(\n  (data) => 处理数据,\n  onError: (e) => 处理错误,\n  onDone: () => 完成处理\n);", "description": "在Dart中，异步代码的异常处理与同步代码有所不同。由于异步操作可能在未来的某个时间点完成，其异常也可能在未来产生，因此需要特殊的机制来捕获和处理这些异常。\n\nDart提供了多种方式来处理异步异常：\n\n1. 在async函数中使用try-catch：\n   在使用async/await的函数中，可以使用标准的try-catch块来捕获异步操作中抛出的异常。这使得异步代码的错误处理看起来与同步代码类似，提高了代码的可读性。\n\n2. 使用Future的catchError方法：\n   当使用Future API而不是async/await时，可以使用catchError方法来捕获和处理异常。catchError方法接收一个回调函数，当Future完成时如果有错误发生，这个回调函数会被调用。\n\n3. 处理Stream中的错误：\n   Stream提供了多种处理错误的方式：\n   - 使用listen方法的onError参数注册错误处理回调\n   - 在await for循环中使用try-catch\n   - 使用handleError方法转换流中的错误\n\n未捕获的异步异常可能导致整个程序崩溃，因此在异步编程中，错误处理尤为重要。特别是在处理网络请求、文件IO或其他可能失败的异步操作时，应该总是包含适当的错误处理代码。\n\n在某些情况下，可能需要将异步操作的错误重新抛出或转换为更具体的异常类型。在async函数中，可以直接使用throw语句；在使用Future API时，可以返回一个使用Future.error创建的新Future。", "parameters": [], "returnValue": "不同方法返回值不同", "examples": [{"code": "import 'dart:async';\n\nvoid main() async {\n  print('开始异步异常捕获示例');\n  \n  // 1. 在async函数中使用try-catch\n  print('\\n1. 在async函数中使用try-catch:');\n  try {\n    // 调用可能抛出异常的异步函数\n    String result = await fetchDataWithDelay(true);\n    print('  成功获取数据: $result');\n  } catch (e) {\n    print('  捕获到异步异常: $e');\n  } finally {\n    print('  异步操作的finally块');\n  }\n  \n  // 2. 使用Future的catchError方法\n  print('\\n2. 使用Future的catchError方法:');\n  fetchDataWithDelay(true)\n      .then((result) => print('  成功获取数据: $result'))\n      .catchError((error) => print('  处理Future错误: $error'))\n      .whenComplete(() => print('  Future操作完成 (类似finally)'));\n  \n  // 等待上面的异步操作完成\n  await Future.delayed(Duration(seconds: 2));\n  \n  // 3. 链式Future错误处理\n  print('\\n3. 链式Future错误处理:');\n  fetchDataWithDelay(false)\n      .then((result) => parseData(result)) // 这里不会抛出异常\n      .then((parsedData) => print('  解析后的数据: $parsedData'))\n      .catchError((error) => print('  处理任何步骤的错误: $error'));\n  \n  // 演示错误的传播\n  print('\\n  错误传播演示:');\n  fetchDataWithDelay(true) // 这会抛出异常\n      .then((result) => parseData(result)) // 这不会执行\n      .then((parsedData) => print('  最终数据: $parsedData')) // 这不会执行\n      .catchError((error) => print('  捕获传播的错误: $error'));\n  \n  // 等待上面的异步操作完成\n  await Future.delayed(Duration(seconds: 2));\n  \n  // 4. 特定类型的错误处理\n  print('\\n4. 特定类型的错误处理:');\n  try {\n    await fetchWithSpecificError();\n  } on FormatException catch (e) {\n    print('  捕获到格式异常: ${e.message}');\n  } on TimeoutException catch (e) {\n    print('  捕获到超时异常: ${e.message}');\n  } catch (e) {\n    print('  捕获到其他异常: $e');\n  }\n  \n  // 5. 在async函数中重新抛出异常\n  print('\\n5. 在async函数中重新抛出异常:');\n  try {\n    await processDataWithRethrow();\n  } catch (e) {\n    print('  捕获到重新抛出的异常: $e');\n  }\n  \n  // 6. 处理Stream中的错误\n  print('\\n6. 处理Stream中的错误:');\n  \n  // 6.1 使用listen的onError回调\n  print('  使用listen的onError回调:');\n  final errorStream = createErrorStream();\n  \n  final subscription = errorStream.listen(\n    (data) => print('  收到数据: $data'),\n    onError: (error) => print('  处理流错误: $error'),\n    onDone: () => print('  流处理完成'),\n  );\n  \n  // 等待Stream完成\n  await Future.delayed(Duration(seconds: 1));\n  await subscription.cancel();\n  \n  // 6.2 使用await for和try-catch\n  print('\\n  使用await for和try-catch:');\n  await processStreamWithTryCatch();\n  \n  // 6.3 使用handleError方法\n  print('\\n  使用handleError方法:');\n  final handledStream = createErrorStream().handleError(\n    (error) => print('  已处理的错误: $error'),\n    test: (error) => error is ArgumentError, // 可选：只处理特定类型的错误\n  );\n  \n  await for (final data in handledStream) {\n    print('  处理后的流数据: $data');\n  }\n  \n  // 7. 使用Future的超时处理\n  print('\\n7. Future的超时处理:');\n  try {\n    final result = await slowOperation().timeout(\n      Duration(milliseconds: 500),\n      onTimeout: () => '超时后的默认值',\n    );\n    print('  操作结果: $result');\n  } catch (e) {\n    print('  操作超时: $e');\n  }\n  \n  print('\\n异步异常捕获示例结束');\n}\n\n// 模拟网络请求，可能成功或失败\nFuture<String> fetchDataWithDelay(bool shouldFail) async {\n  print('  开始获取数据...');\n  // 模拟网络延迟\n  await Future.delayed(Duration(milliseconds: 500));\n  \n  if (shouldFail) {\n    print('  请求失败!');\n    throw Exception('网络请求失败');\n  }\n  \n  return '这是从服务器获取的数据';\n}\n\n// 解析数据的函数\nString parseData(String data) {\n  return '解析结果: ${data.toUpperCase()}';\n}\n\n// 抛出特定类型异常的异步函数\nFuture<void> fetchWithSpecificError() async {\n  final random = DateTime.now().millisecond % 3;\n  await Future.delayed(Duration(milliseconds: 300));\n  \n  if (random == 0) {\n    throw FormatException('数据格式无效');\n  } else if (random == 1) {\n    throw TimeoutException('操作超时', Duration(seconds: 10));\n  } else {\n    throw Exception('未知错误');\n  }\n}\n\n// 重新抛出异常的异步函数\nFuture<void> processDataWithRethrow() async {\n  try {\n    await fetchDataWithDelay(true);\n  } catch (e) {\n    print('  在中间层捕获异常: $e');\n    print('  添加上下文信息后重新抛出');\n    throw Exception('处理数据时出错: $e');\n  }\n}\n\n// 创建包含错误的Stream\nStream<int> createErrorStream() {\n  return Stream.periodic(Duration(milliseconds: 200), (count) {\n    if (count == 1) {\n      throw ArgumentError('流处理中的错误示例');\n    }\n    return count * 10;\n  }).take(4);\n}\n\n// 使用await for和try-catch处理Stream\nFuture<void> processStreamWithTryCatch() async {\n  final stream = createErrorStream();\n  \n  try {\n    await for (final data in stream) {\n      print('  使用await for接收到数据: $data');\n    }\n  } catch (e) {\n    print('  使用try-catch捕获流错误: $e');\n  }\n}\n\n// 模拟一个慢操作\nFuture<String> slowOperation() async {\n  print('  开始慢操作...');\n  await Future.delayed(Duration(seconds: 1));\n  return '慢操作完成';\n}", "explanation": "这个示例全面展示了Dart中处理异步异常的各种方法。首先，演示了在async函数中使用try-catch捕获异步异常，这种方式使异步代码的错误处理看起来与同步代码类似。然后，展示了如何使用Future的catchError方法来处理异步操作可能产生的错误，以及whenComplete方法，它类似于同步代码中的finally块。接着，示例展示了链式Future调用中的错误处理和错误传播机制，当链中的某个环节抛出异常时，错误会传播到最近的catchError处理程序。示例还演示了如何在async函数中捕获特定类型的异常，以及如何捕获并重新抛出带有附加上下文信息的异常。最后，示例展示了处理Stream中错误的三种主要方式：使用listen方法的onError回调、在await for循环中使用try-catch，以及使用handleError方法转换流中的错误。额外展示了如何处理异步操作的超时情况。"}, {"code": "import 'dart:async';\n\nvoid main() async {\n  print('异步异常处理实战示例');\n  \n  // 1. 模拟HTTP请求异常处理\n  print('\\n1. HTTP请求异常处理:');\n  final apiClient = ApiClient();\n  \n  // 尝试获取用户数据\n  try {\n    final userData = await apiClient.fetchUserData(userId: 123);\n    print('  用户数据: $userData');\n  } on ApiException catch (e) {\n    print('  API错误: ${e.message}');\n    print('  状态码: ${e.statusCode}');\n    print('  端点: ${e.endpoint}');\n    // 基于错误类型执行不同操作\n    if (e.statusCode == 401) {\n      print('  处理方式: 重新认证');\n    } else if (e.statusCode == 404) {\n      print('  处理方式: 显示用户不存在');\n    } else {\n      print('  处理方式: 通用错误处理');\n    }\n  } on TimeoutException catch (e) {\n    print('  请求超时: ${e.message}');\n    print('  处理方式: 显示网络连接问题');\n  } catch (e) {\n    print('  未知错误: $e');\n    print('  处理方式: 记录错误并显示通用错误消息');\n  }\n  \n  // 2. 异步数据验证与处理\n  print('\\n2. 异步数据验证与处理:');\n  final dataProcessor = DataProcessor();\n  \n  try {\n    await dataProcessor.processUserRegistration({\n      'username': 'alice',\n      'email': 'invalid-email',\n      'age': '25',\n    });\n    print('  注册成功'); // 不应该执行到这里\n  } catch (e) {\n    if (e is ValidationException) {\n      print('  验证错误: ${e.message}');\n      print('  字段: ${e.field}');\n      print('  处理方式: 向用户展示特定字段的错误');\n    } else {\n      print('  处理数据时出错: $e');\n    }\n  }\n  \n  // 使用有效数据再次尝试\n  try {\n    await dataProcessor.processUserRegistration({\n      'username': 'alice',\n      'email': '<EMAIL>',\n      'age': '25',\n    });\n    print('  注册成功');\n  } catch (e) {\n    print('  注册失败: $e'); // 不应该执行到这里\n  }\n  \n  // 3. 多个异步操作的错误处理\n  print('\\n3. 多个异步操作的错误处理:');\n  final resourceManager = ResourceManager();\n  \n  try {\n    print('  启动应用程序组件...');\n    await Future.wait([\n      resourceManager.initDatabase(),\n      resourceManager.loadConfiguration(),\n      resourceManager.connectToService(),\n    ]);\n    print('  所有组件已成功初始化');\n  } catch (e) {\n    print('  初始化失败: $e');\n    print('  正在清理资源...');\n    await resourceManager.cleanup();\n    print('  清理完成');\n  }\n  \n  // 4. 流式数据处理中的错误恢复\n  print('\\n4. 流式数据处理中的错误恢复:');\n  final streamProcessor = StreamProcessor();\n  \n  // 通过listen处理错误并继续\n  print('  开始处理数据流:');\n  final stream = streamProcessor.processDataStream();\n  \n  final subscription = stream.listen(\n    (data) => print('  处理数据项: $data'),\n    onError: (error) {\n      print('  流处理错误: $error');\n      print('  继续处理下一项...');\n    },\n    onDone: () => print('  数据流处理完成'),\n  );\n  \n  // 等待流处理完成\n  await Future.delayed(Duration(seconds: 1));\n  await subscription.cancel();\n  \n  print('\\n异步异常处理实战示例结束');\n}\n\n// API客户端模拟\nclass ApiClient {\n  Future<Map<String, dynamic>> fetchUserData({required int userId}) async {\n    print('  请求用户数据: $userId');\n    await Future.delayed(Duration(milliseconds: 300)); // 模拟网络延迟\n    \n    // 模拟不同的API响应情况\n    if (userId < 0) {\n      throw ApiException('无效的用户ID', 400, '/users/$userId');\n    } else if (userId == 0) {\n      throw ApiException('认证失败', 401, '/users/$userId');\n    } else if (userId > 1000) {\n      throw ApiException('用户不存在', 404, '/users/$userId');\n    } else if (userId == 500) {\n      throw TimeoutException('请求超时', Duration(seconds: 30));\n    } else if (userId == 999) {\n      throw Exception('服务器内部错误');\n    }\n    \n    // 模拟成功响应\n    return {\n      'id': userId,\n      'name': '用户$userId',\n      'email': 'user$<EMAIL>',\n    };\n  }\n}\n\n// API异常类\nclass ApiException implements Exception {\n  final String message;\n  final int statusCode;\n  final String endpoint;\n  \n  ApiException(this.message, this.statusCode, this.endpoint);\n  \n  @override\n  String toString() => 'ApiException: $message (Status: $statusCode)';\n}\n\n// 数据处理器模拟\nclass DataProcessor {\n  Future<void> processUserRegistration(Map<String, dynamic> userData) async {\n    print('  处理用户注册: $userData');\n    \n    // 模拟异步验证\n    await Future.delayed(Duration(milliseconds: 200));\n    \n    // 验证邮箱\n    final email = userData['email'] as String;\n    if (!email.contains('@')) {\n      throw ValidationException('邮箱格式无效', 'email');\n    }\n    \n    // 模拟数据处理\n    await Future.delayed(Duration(milliseconds: 300));\n    \n    // 模拟成功注册\n    print('  用户数据验证通过，注册处理完成');\n  }\n}\n\n// 验证异常类\nclass ValidationException implements Exception {\n  final String message;\n  final String field;\n  \n  ValidationException(this.message, this.field);\n  \n  @override\n  String toString() => 'ValidationException: $message (Field: $field)';\n}\n\n// 资源管理器模拟\nclass ResourceManager {\n  bool _initialized = false;\n  List<String> _activeResources = [];\n  \n  // 初始化数据库\n  Future<void> initDatabase() async {\n    print('  初始化数据库...');\n    await Future.delayed(Duration(milliseconds: 200));\n    _activeResources.add('database');\n    \n    // 模拟随机失败\n    if (DateTime.now().millisecond % 3 == 0) {\n      throw Exception('数据库连接失败');\n    }\n    \n    print('  数据库初始化成功');\n  }\n  \n  // 加载配置\n  Future<void> loadConfiguration() async {\n    print('  加载配置...');\n    await Future.delayed(Duration(milliseconds: 150));\n    _activeResources.add('config');\n    print('  配置加载成功');\n  }\n  \n  // 连接到服务\n  Future<void> connectToService() async {\n    print('  连接到远程服务...');\n    await Future.delayed(Duration(milliseconds: 250));\n    \n    // 模拟随机失败\n    if (DateTime.now().millisecond % 4 == 0) {\n      throw Exception('服务连接超时');\n    }\n    \n    _activeResources.add('service');\n    print('  服务连接成功');\n  }\n  \n  // 清理资源\n  Future<void> cleanup() async {\n    print('  正在关闭资源: $_activeResources');\n    \n    for (var resource in _activeResources) {\n      await Future.delayed(Duration(milliseconds: 100));\n      print('  关闭: $resource');\n    }\n    \n    _activeResources.clear();\n  }\n}\n\n// 流处理器模拟\nclass StreamProcessor {\n  // 创建包含错误的数据流\n  Stream<String> processDataStream() {\n    return Stream.fromIterable([\n      'item1',\n      'error_item', // 会导致错误的项\n      'item3',\n      'error_item2', // 另一个错误项\n      'item5',\n    ]).map((item) {\n      if (item.startsWith('error')) {\n        throw Exception('处理 \"$item\" 时出错');\n      }\n      return '处理后: $item';\n    });\n  }\n}", "explanation": "这个示例展示了异步异常处理在实际应用场景中的使用。首先，演示了如何处理HTTP请求中的异常，通过捕获特定类型的ApiException和TimeoutException来提供有针对性的错误处理，例如基于HTTP状态码执行不同的操作。接着，示例展示了异步数据验证与处理过程中的错误处理，通过捕获ValidationException来识别特定字段的验证问题并向用户提供精确的反馈。然后，示例演示了如何处理多个并发异步操作中的错误，使用Future.wait同时初始化多个组件，并在任何组件失败时进行适当的清理。最后，示例展示了在流式数据处理中如何处理错误并继续处理后续数据，通过Stream的onError回调允许程序从错误中恢复并继续处理剩余的数据项。这些示例展示了如何构建健壮的异步系统，能够优雅地处理各种错误情况。"}, {"code": "import 'dart:async';\n\nvoid main() async {\n  print('异步异常处理练习：');\n  \n  // 练习目标：创建一个异步函数，尝试从多个不同的'数据源'获取数据\n  // 如果第一个源失败，尝试第二个，依此类推\n  // 如果所有源都失败，则抛出一个合并了所有错误信息的异常\n  \n  // 用于测试的模拟数据源\n  List<Future<String> Function()> dataSources = [\n    () => fetchFromSource('源1', shouldFail: true),\n    () => fetchFromSource('源2', shouldFail: true),\n    () => fetchFromSource('源3', shouldFail: false), // 这个会成功\n    () => fetchFromSource('源4', shouldFail: false),\n  ];\n  \n  try {\n    String data = await fetchWithFallback(dataSources);\n    print('最终获取到的数据: $data');\n  } catch (e) {\n    print('所有数据源都失败: $e');\n  }\n}\n\n// 模拟从特定源获取数据\nFuture<String> fetchFromSource(String sourceName, {required bool shouldFail}) async {\n  print('尝试从$sourceName获取数据...');\n  await Future.delayed(Duration(milliseconds: 300)); // 模拟网络延迟\n  \n  if (shouldFail) {\n    print('$sourceName失败');\n    throw Exception('无法从$sourceName获取数据');\n  }\n  \n  print('$sourceName成功');\n  return '来自$sourceName的数据';\n}\n\n// 学生需要实现这个函数\nFuture<String> fetchWithFallback(List<Future<String> Function()> sources) async {\n  List<String> errors = [];\n  \n  for (var source in sources) {\n    try {\n      // 尝试当前数据源\n      return await source();\n    } catch (e) {\n      // 记录错误并继续尝试下一个源\n      errors.add(e.toString());\n    }\n  }\n  \n  // 如果所有源都失败，抛出合并的错误\n  throw Exception('所有数据源都失败: ${errors.join(', ')}');\n}", "explanation": "这个练习要求学生实现一个异步函数，该函数按顺序尝试从多个数据源获取数据，直到成功或所有源都失败。这种模式在实际应用中很常见，例如尝试从多个API端点获取数据，或者在主服务器不可用时尝试备用服务器。学生需要在fetchWithFallback函数中实现错误处理逻辑，使其能够在一个源失败时尝试下一个源，并在所有源都失败时提供有意义的错误信息。这个练习帮助学生理解如何在异步环境中处理和管理多个潜在的错误来源，以及如何设计能够从错误中恢复的健壮系统。"}]}}]}