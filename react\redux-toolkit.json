{"name": "Redux Toolkit", "trans": ["Redux工具包"], "methods": [{"name": "Configuring Store", "trans": ["配置Store"], "usage": {"syntax": "import { configureStore } from '@reduxjs/toolkit';\nconst store = configureStore({ reducer });", "description": "Redux Toolkit通过configureStore简化store配置，自动集成开发工具和中间件。", "parameters": [{"name": "reducer", "description": "根reducer或slice reducer对象"}], "returnValue": "配置好的Redux store对象", "examples": [{"code": "import { configureStore } from '@reduxjs/toolkit';\nimport counterReducer from './counterSlice';\n// 配置store，自动集成redux-devtools和thunk\nconst store = configureStore({\n  reducer: {\n    counter: counterReducer\n  }\n});", "explanation": "通过configureStore快速配置store。"}]}}, {"name": "createSlice API", "trans": ["createSlice API"], "usage": {"syntax": "import { createSlice } from '@reduxjs/toolkit';\nconst slice = createSlice({ name, initialState, reducers });", "description": "createSlice自动生成reducer和action，简化Redux模块开发。", "parameters": [{"name": "name", "description": "slice名称"}, {"name": "initialState", "description": "初始状态"}, {"name": "reducers", "description": "包含各种reducer的对象"}], "returnValue": "包含reducer和actions的slice对象", "examples": [{"code": "import { createSlice } from '@reduxjs/toolkit';\n// 创建计数器slice\nconst counterSlice = createSlice({\n  name: 'counter',\n  initialState: { value: 0 },\n  reducers: {\n    increment: state => { state.value += 1; },\n    decrement: state => { state.value -= 1; },\n    addBy: (state, action) => { state.value += action.payload; }\n  }\n});\n// 导出reducer和actions\nexport const { increment, decrement, addBy } = counterSlice.actions;\nexport default counterSlice.reducer;", "explanation": "createSlice自动生成reducer和action。"}]}}, {"name": "Creating Reducer and Action", "trans": ["创建Reducer和Action"], "usage": {"syntax": "// 通过slice.actions和slice.reducer导出", "description": "createSlice生成的actions和reducer可直接用于dispatch和store配置。", "parameters": [], "returnValue": "可直接使用的actions和reducer", "examples": [{"code": "// 导入slice\nimport counterReducer, { increment, decrement } from './counterSlice';\n// 配置store\nimport { configureStore } from '@reduxjs/toolkit';\nconst store = configureStore({ reducer: counterReducer });\n// 派发action\nstore.dispatch(increment());\nstore.dispatch(decrement());", "explanation": "直接使用slice导出的reducer和actions。"}]}}, {"name": "createAsyncThunk", "trans": ["createAsyncThunk"], "usage": {"syntax": "import { createAsyncThunk } from '@reduxjs/toolkit';\nconst fetchData = createAsyncThunk('slice/fetchData', async (arg, thunkAPI) => { ... });", "description": "createAsyncThunk用于生成异步action，自动处理pending/fulfilled/rejected三种状态。", "parameters": [{"name": "typePrefix", "description": "action类型前缀"}, {"name": "payloadCreator", "description": "异步处理函数"}], "returnValue": "异步action creator函数", "examples": [{"code": "import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';\n// 定义异步action\nexport const fetchUser = createAsyncThunk('user/fetch', async (id) => {\n  const res = await fetch(`/api/user/${id}`);\n  return await res.json();\n});\n// 在slice中处理异步状态\nconst userSlice = createSlice({\n  name: 'user',\n  initialState: { user: null, loading: false },\n  reducers: {},\n  extraReducers: builder => {\n    builder\n      .addCase(fetchUser.pending, state => { state.loading = true; })\n      .addCase(fetchUser.fulfilled, (state, action) => { state.user = action.payload; state.loading = false; })\n      .addCase(fetchUser.rejected, state => { state.loading = false; });\n  }\n});", "explanation": "createAsyncThunk结合extraReducers处理异步请求。"}]}}, {"name": "RTK Query", "trans": ["RTK Query"], "usage": {"syntax": "import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';\nconst api = createApi({ ... });", "description": "RTK Query用于高效数据获取和缓存，自动生成hooks和状态管理。", "parameters": [{"name": "createApi", "description": "创建API对象的函数"}, {"name": "fetchBase<PERSON>uery", "description": "基础请求配置"}], "returnValue": "自动生成的API hooks和状态", "examples": [{"code": "import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';\n// 创建API对象\nexport const userApi = createApi({\n  reducerPath: 'userApi',\n  baseQuery: fetchBaseQuery({ baseUrl: '/api' }),\n  endpoints: builder => ({\n    getUser: builder.query({ query: id => `user/${id}` })\n  })\n});\n// 导出自动生成的hook\nexport const { useGetUserQuery } = userApi;", "explanation": "RTK Query自动生成数据请求和缓存hooks。"}]}}, {"name": "Immutable Update Logic", "trans": ["不可变更新逻辑"], "usage": {"syntax": "// 直接修改state，RTK内部用Immer保证不可变性", "description": "Redux Toolkit内部集成Immer，可在reducer中直接修改state，自动生成不可变新对象。", "parameters": [], "returnValue": "不可变更新后的新状态", "examples": [{"code": "import { createSlice } from '@reduxjs/toolkit';\nconst slice = createSlice({\n  name: 'counter',\n  initialState: { value: 0 },\n  reducers: {\n    increment: state => { state.value += 1; }\n  }\n});", "explanation": "可直接修改state，RTK自动处理不可变性。"}]}}, {"name": "Performance Optimization", "trans": ["性能优化"], "usage": {"syntax": "// 拆分slice、选择性订阅、memo等优化", "description": "通过拆分slice、选择性订阅、memo等方式减少不必要的渲染，提高性能。", "parameters": [], "returnValue": "优化后的高性能Redux架构", "examples": [{"code": "// 选择性订阅state，避免全量渲染\nconst value = useSelector(state => state.counter.value);\n// 组件用React.memo包裹，避免无关更新", "explanation": "选择性订阅和memo提升性能。"}]}}, {"name": "作业：实现一个Redux Toolkit计数器", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 用Redux Toolkit实现计数器。\n// 2. 支持加一、减一。\n// 3. 代码结构清晰，注释完整。", "description": "请实现上述RTK计数器，提交时请附上完整代码和注释。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 示例代码略，请学生自行实现\n// 提示：用configureStore、createSlice、Provider、useSelector、useDispatch实现。", "explanation": "作业要求学生掌握RTK全流程。"}, {"code": "import React from 'react';\nimport { configureStore } from '@reduxjs/toolkit';\nimport { Provider, useSelector, useDispatch } from 'react-redux';\nimport { createSlice } from '@reduxjs/toolkit';\n// 1. 创建slice\nconst counterSlice = createSlice({\n  name: 'counter',\n  initialState: { value: 0 },\n  reducers: {\n    increment: state => { state.value += 1; },\n    decrement: state => { state.value -= 1; }\n  }\n});\n// 2. 导出actions和reducer\nconst { increment, decrement } = counterSlice.actions;\nconst counterReducer = counterSlice.reducer;\n// 3. 配置store\nconst store = configureStore({ reducer: counterReducer });\n// 4. 计数器组件\nfunction Counter() {\n  const value = useSelector(state => state.value);\n  const dispatch = useDispatch();\n  return (\n    <div>\n      <span>计数: {value}</span>\n      <button onClick={() => dispatch(increment())}>加一</button>\n      <button onClick={() => dispatch(decrement())}>减一</button>\n    </div>\n  );\n}\n// 5. 用法\nfunction App() {\n  return (\n    <Provider store={store}>\n      <Counter />\n    </Provider>\n  );\n}", "explanation": "完整实现Redux Toolkit计数器，支持加一减一。"}]}}]}