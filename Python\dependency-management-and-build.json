{"name": "Dependency Management and Build", "trans": ["依赖管理与构建"], "methods": [{"name": "requirements.txt and pipenv/poetry", "trans": ["requirements.txt与pipenv/poetry"], "usage": {"syntax": "pip install -r requirements.txt\npipenv install\npoetry add 包名", "description": "requirements.txt用于记录依赖包，pipenv和poetry是现代依赖管理工具，支持虚拟环境和依赖锁定。", "parameters": [{"name": "requirements.txt", "description": "依赖包列表文件"}, {"name": "pipenv/poetry", "description": "依赖和虚拟环境管理工具"}], "returnValue": "无返回值或安装结果", "examples": [{"code": "# 安装依赖\npip install -r requirements.txt\npipenv install\npoetry add requests", "explanation": "演示了三种依赖管理方式。"}]}}, {"name": "Virtual Environment Management", "trans": ["虚拟环境管理"], "usage": {"syntax": "python -m venv venv\nsource venv/bin/activate\npipenv shell\npoetry shell", "description": "虚拟环境用于隔离项目依赖，避免包冲突，常用venv、pipenv、poetry等工具。", "parameters": [{"name": "venv", "description": "标准虚拟环境工具"}, {"name": "pipenv/poetry", "description": "集成虚拟环境和依赖管理"}], "returnValue": "无返回值或虚拟环境激活状态", "examples": [{"code": "python -m venv venv\n# Windows下激活\nvenv\\Scripts\\activate\n# macOS/Linux下激活\nsource venv/bin/activate", "explanation": "演示了venv虚拟环境的创建与激活。"}]}}, {"name": "Build and Packaging Tools", "trans": ["构建与打包工具"], "usage": {"syntax": "python setup.py sdist bdist_wheel\npoetry build", "description": "setuptools、wheel、poetry等工具可将项目打包为可分发格式，便于发布和安装。", "parameters": [{"name": "setup.py", "description": "传统打包脚本"}, {"name": "poetry build", "description": "现代打包命令"}], "returnValue": "生成的分发包文件", "examples": [{"code": "python setup.py sdist bdist_wheel\npoetry build", "explanation": "演示了两种常用的打包方式。"}]}}, {"name": "Publish to PyPI", "trans": ["发布PyPI"], "usage": {"syntax": "twine upload dist/*\npoetry publish", "description": "twine和poetry可将打包好的项目上传到PyPI，供他人安装和使用。", "parameters": [{"name": "twine", "description": "上传工具"}, {"name": "poetry publish", "description": "一键发布命令"}], "returnValue": "发布结果或日志", "examples": [{"code": "twine upload dist/*\npoetry publish", "explanation": "演示了两种发布PyPI的方法。"}]}}, {"name": "Dependency Management and Build Assignment", "trans": ["依赖管理与构建练习"], "usage": {"syntax": "# 依赖管理与构建练习", "description": "完成以下练习，巩固依赖管理与构建相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 编写一个requirements.txt并安装依赖。\n2. 用venv创建虚拟环境。\n3. 用setup.py或poetry打包项目。\n4. 用twine或poetry发布到PyPI测试仓库。", "explanation": "练习要求动手实践依赖管理、虚拟环境、打包与发布等。"}]}}]}