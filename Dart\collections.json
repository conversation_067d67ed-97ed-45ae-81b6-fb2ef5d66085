{"name": "Collections", "trans": ["集合类型"], "methods": [{"name": "List Definition and Initialization", "trans": ["List定义与初始化"], "usage": {"syntax": "// 创建空List\nList<类型> 变量名 = [];\n\n// 使用字面量创建并初始化\nvar 变量名 = [值1, 值2, 值3];\n\n// 使用构造函数\nList<类型> 变量名 = List<类型>.filled(长度, 初始值);\nList<类型> 变量名 = List<类型>.generate(长度, (索引) => 生成值);\nList<类型> 变量名 = List<类型>.from(可迭代对象);\nList<类型> 变量名 = List<类型>.of(可迭代对象);\nList<类型> 变量名 = List<类型>.unmodifiable(可迭代对象); // 创建不可修改的List", "description": "List是Dart中最常用的集合类型之一，表示一个有序的对象集合，可以通过索引访问元素。Dart中的List是从0开始索引的，最后一个元素的索引是list.length - 1。List可以通过多种方式创建和初始化：使用字面量语法、构造函数或从其他集合转换。List可以是固定长度或可增长的，可以是可变的或不可变的。创建List时，可以指定元素的类型（如List<int>、List<String>等），也可以使用var让Dart自动推断类型。如果不指定类型，默认为List<dynamic>，可以存储任意类型的对象。Dart 2.3引入了集合展开运算符(...和...?)，可以在集合字面量中插入多个元素。List.filled创建指定长度且填充相同值的List；List.generate创建指定长度并使用生成函数填充的List；List.from和List.of从其他可迭代对象创建List；List.unmodifiable创建不可修改的List视图。", "parameters": [], "returnValue": "创建的List对象", "examples": [{"code": "void main() {\n  // 使用字面量创建List\n  var numbers = [1, 2, 3, 4, 5];\n  print('数字列表: $numbers');\n  \n  // 创建指定类型的List\n  List<String> fruits = ['苹果', '香蕉', '橙子'];\n  print('水果列表: $fruits');\n  \n  // 创建空List\n  var emptyList = <double>[];\n  print('空列表: $emptyList');\n  \n  // 使用List.filled创建固定长度的List\n  var filledList = List<int>.filled(5, 0); // 创建长度为5，所有元素都是0的List\n  print('填充列表: $filledList');\n  \n  // 注意：List.filled创建的是固定引用，如果元素是对象，所有位置引用的是同一个对象\n  var filledObjectList = List<List<int>>.filled(3, [0]);\n  print('填充对象列表(修改前): $filledObjectList');\n  \n  // 修改第一个元素会影响所有元素，因为它们引用同一个对象\n  filledObjectList[0].add(1);\n  print('填充对象列表(修改后): $filledObjectList'); // 所有内部列表都会被修改\n  \n  // 使用List.generate可以避免这个问题\n  var generatedList = List<List<int>>.generate(3, (index) => [0]);\n  print('生成列表(修改前): $generatedList');\n  \n  // 修改第一个元素不会影响其他元素\n  generatedList[0].add(1);\n  print('生成列表(修改后): $generatedList'); // 只有第一个内部列表被修改\n  \n  // 使用List.generate创建动态内容\n  var squares = List<int>.generate(5, (index) => index * index);\n  print('平方数列表: $squares'); // [0, 1, 4, 9, 16]\n  \n  // 从其他可迭代对象创建List\n  var setOfNumbers = {1, 2, 3, 4, 5}; // 这是一个Set\n  var listFromSet = List<int>.from(setOfNumbers);\n  print('从Set创建的List: $listFromSet');\n  \n  // List.of和List.from类似，但处理null值的方式不同\n  var iterableWithNull = [1, 2, null, 4];\n  var listFromIterable = List<int?>.of(iterableWithNull);\n  print('使用List.of创建: $listFromIterable');\n  \n  // 创建不可修改的List\n  var unmodifiableList = List<String>.unmodifiable(['a', 'b', 'c']);\n  print('不可修改List: $unmodifiableList');\n  // 尝试修改不可修改的List会抛出异常\n  // unmodifiableList.add('d'); // 这行会抛出UnsupportedError\n  \n  // 使用集合展开运算符\n  var list1 = [1, 2, 3];\n  var list2 = [4, 5];\n  var combined = [...list1, ...list2];\n  print('使用展开运算符合并: $combined'); // [1, 2, 3, 4, 5]\n  \n  // 使用空感知展开运算符\n  List<int>? nullableList;\n  var safeList = [0, ...?nullableList];\n  print('使用空感知展开运算符: $safeList'); // [0]\n  \n  // 使用条件元素\n  bool addExtra = true;\n  var conditionalList = [\n    'a',\n    'b',\n    if (addExtra) 'c',\n  ];\n  print('使用条件元素: $conditionalList'); // [a, b, c]\n  \n  // 使用循环元素\n  var listOfInts = [1, 2, 3];\n  var listWithForLoop = [\n    'x',\n    for (var i in listOfInts) 'y$i'\n  ];\n  print('使用循环元素: $listWithForLoop'); // [x, y1, y2, y3]\n}", "explanation": "这个示例展示了在Dart中创建和初始化List的多种方式。首先展示了使用字面量语法创建简单的数字和字符串列表，然后演示了创建空列表。接着使用List.filled创建固定长度的列表，并展示了当填充对象类型时可能遇到的引用问题。List.generate方法可以避免这个问题，并允许根据索引生成不同的值。示例还展示了从其他集合（如Set）创建List的方法，以及创建不可修改List的方法。最后，演示了Dart 2.3引入的集合展开运算符、空感知展开运算符、条件元素和循环元素的使用，这些特性使List的创建更加灵活和强大。"}]}}, {"name": "List Common Operations", "trans": ["List常用操作"], "usage": {"syntax": "// 访问元素\nvar 元素 = 列表[索引];\n\n// 修改元素\n列表[索引] = 新值;\n\n// 添加元素\n列表.add(元素);            // 在末尾添加单个元素\n列表.addAll(其他列表);      // 在末尾添加多个元素\n列表.insert(索引, 元素);    // 在指定位置插入单个元素\n列表.insertAll(索引, 其他列表); // 在指定位置插入多个元素\n\n// 删除元素\n列表.remove(元素);         // 删除第一个匹配的元素\n列表.removeAt(索引);       // 删除指定索引的元素\n列表.removeLast();        // 删除最后一个元素\n列表.removeWhere((元素) => 条件); // 删除满足条件的所有元素\n列表.clear();             // 删除所有元素\n\n// 查找元素\nint 索引 = 列表.indexOf(元素);   // 查找元素的索引\nbool 包含 = 列表.contains(元素);  // 检查是否包含元素\n\n// 获取列表信息\nint 长度 = 列表.length;     // 获取列表长度\nbool 是否为空 = 列表.isEmpty;  // 检查列表是否为空\nbool 是否不为空 = 列表.isNotEmpty; // 检查列表是否不为空\n\n// 列表切片\nList 子列表 = 列表.sublist(开始索引, 结束索引); // 获取子列表", "description": "Dart中的List提供了丰富的操作方法，用于访问、修改、添加和删除元素。可以使用方括号语法和索引访问或修改元素，索引从0开始。添加元素可以使用add()在末尾添加单个元素，或使用addAll()添加多个元素。insert()和insertAll()方法允许在指定位置插入元素。删除元素可以使用remove()删除第一个匹配的元素，removeAt()删除指定索引的元素，removeLast()删除最后一个元素，或使用removeWhere()删除满足特定条件的所有元素。clear()方法可以删除列表中的所有元素。indexOf()方法返回元素在列表中的索引，如果元素不存在则返回-1。contains()方法检查列表是否包含特定元素。length属性返回列表的长度，isEmpty和isNotEmpty属性检查列表是否为空。sublist()方法可以获取列表的一部分作为新列表。对于固定长度的列表，不能添加或删除元素，但可以修改现有元素。List的大多数操作方法都会修改原始列表，如果需要保持原始列表不变，应该先创建一个副本。", "parameters": [], "returnValue": "根据操作不同而不同", "examples": [{"code": "void main() {\n  // 创建一个示例列表\n  var fruits = ['苹果', '香蕉', '橙子', '草莓', '葡萄'];\n  print('原始水果列表: $fruits');\n  \n  // 访问元素\n  var firstFruit = fruits[0];\n  var lastFruit = fruits[fruits.length - 1];\n  print('第一个水果: $firstFruit');\n  print('最后一个水果: $lastFruit');\n  \n  // 安全访问（避免索引越界）\n  var index = 10;\n  var safeFruit = index < fruits.length ? fruits[index] : '索引越界';\n  print('安全访问索引$index: $safeFruit');\n  \n  // 修改元素\n  fruits[1] = '黄香蕉';\n  print('修改后的列表: $fruits');\n  \n  // 添加元素\n  fruits.add('芒果');\n  print('添加一个元素后: $fruits');\n  \n  // 添加多个元素\n  fruits.addAll(['西瓜', '桃子']);\n  print('添加多个元素后: $fruits');\n  \n  // 在指定位置插入元素\n  fruits.insert(2, '柠檬');\n  print('在索引2插入元素后: $fruits');\n  \n  // 在指定位置插入多个元素\n  fruits.insertAll(3, ['石榴', '樱桃']);\n  print('在索引3插入多个元素后: $fruits');\n  \n  // 删除元素\n  fruits.remove('葡萄');\n  print('删除葡萄后: $fruits');\n  \n  // 删除指定索引的元素\n  var removedItem = fruits.removeAt(1);\n  print('删除索引1的元素($removedItem)后: $fruits');\n  \n  // 删除最后一个元素\n  var lastRemoved = fruits.removeLast();\n  print('删除最后一个元素($lastRemoved)后: $fruits');\n  \n  // 删除满足条件的元素\n  fruits.removeWhere((fruit) => fruit.startsWith('草'));\n  print('删除以\"草\"开头的元素后: $fruits');\n  \n  // 查找元素\n  var orangeIndex = fruits.indexOf('橙子');\n  print('橙子的索引: $orangeIndex');\n  \n  var kiwiIndex = fruits.indexOf('猕猴桃');\n  print('猕猴桃的索引: $kiwiIndex'); // -1表示不存在\n  \n  // 检查是否包含元素\n  var containsApple = fruits.contains('苹果');\n  print('列表包含苹果: $containsApple');\n  \n  var containsBanana = fruits.contains('香蕉');\n  print('列表包含香蕉: $containsBanana');\n  \n  // 获取列表信息\n  print('列表长度: ${fruits.length}');\n  print('列表是否为空: ${fruits.isEmpty}');\n  print('列表是否不为空: ${fruits.isNotEmpty}');\n  \n  // 获取列表切片\n  var subFruits = fruits.sublist(1, 4);\n  print('子列表[1-4): $subFruits');\n  \n  // 替换部分列表\n  fruits.replaceRange(0, 2, ['红苹果', '绿苹果']);\n  print('替换前两个元素后: $fruits');\n  \n  // 清空列表\n  var fruitsCopy = List<String>.from(fruits); // 创建副本以保留原始数据\n  fruits.clear();\n  print('清空后的列表: $fruits');\n  print('原始列表的副本: $fruitsCopy');\n  \n  // 使用操作符修改列表\n  var numbers = [1, 2, 3];\n  numbers += [4, 5]; // 使用+=操作符添加元素\n  print('使用+=操作符后: $numbers');\n  \n  // 固定长度列表的操作\n  var fixedList = List<int>.filled(3, 0);\n  print('固定长度列表: $fixedList');\n  \n  // 修改固定长度列表的元素\n  fixedList[0] = 1;\n  fixedList[1] = 2;\n  fixedList[2] = 3;\n  print('修改后的固定长度列表: $fixedList');\n  \n  // 以下操作会抛出异常，因为固定长度列表不能改变大小\n  // fixedList.add(4); // 错误：不能添加元素\n  // fixedList.remove(1); // 错误：不能删除元素\n  \n  // 使用cascade操作符(..)进行链式操作\n  var cascadeList = [1, 2]\n    ..add(3)\n    ..add(4)\n    ..addAll([5, 6]);\n  print('使用cascade操作符创建的列表: $cascadeList');\n}", "explanation": "这个示例全面展示了Dart中List的常用操作。首先创建一个水果列表，然后演示了如何访问元素（包括安全访问以避免索引越界）和修改元素。接着展示了添加元素的不同方法：add()添加单个元素，addAll()添加多个元素，insert()在指定位置插入单个元素，insertAll()在指定位置插入多个元素。然后演示了删除元素的方法：remove()删除特定元素，removeAt()删除指定索引的元素，removeLast()删除最后一个元素，removeWhere()删除满足条件的元素。示例还展示了如何使用indexOf()查找元素的索引，使用contains()检查元素是否存在，以及获取列表的长度和检查列表是否为空。sublist()方法用于获取列表的一部分，replaceRange()方法用于替换列表的一部分。最后，展示了固定长度列表的限制（不能添加或删除元素）以及使用cascade操作符进行链式操作。"}]}}, {"name": "List Traversal and Transformation", "trans": ["List遍历与转换"], "usage": {"syntax": "// 基本遍历方法\nfor (var 元素 in 列表) {\n  // 处理每个元素\n}\n\n// 使用forEach方法\n列表.forEach((元素) {\n  // 处理每个元素\n});\n\n// 使用for循环和索引\nfor (var i = 0; i < 列表.length; i++) {\n  var 元素 = 列表[i];\n  // 处理每个元素\n}\n\n// 转换方法\nList<新类型> 新列表 = 列表.map<新类型>((元素) => 转换函数).toList();\nList 筛选列表 = 列表.where((元素) => 条件).toList();\nList 排序列表 = List.from(列表)..sort((a, b) => 比较函数);\n\n// 聚合方法\n结果类型 结果 = 列表.reduce((累积值, 元素) => 合并操作);\n结果类型 结果 = 列表.fold(初始值, (累积值, 元素) => 合并操作);", "description": "Dart提供了多种方式来遍历List和转换List。最常见的遍历方式包括：for-in循环、forEach方法和传统的for循环。for-in循环是最简洁的方式，适合大多数场景；forEach方法提供了函数式的遍历方式，更加灵活；传统for循环允许通过索引访问元素，适合需要同时处理索引和元素的场景。\n\nDart还提供了多种转换List的方法：map方法可以将List中的每个元素转换为新的类型或值，生成一个新的List；where方法可以根据条件筛选元素，返回符合条件的元素组成的新List；sort方法可以对List进行排序，默认为升序排序，可以提供自定义的比较函数实现复杂排序；reduce和fold方法可以将List中的所有元素合并成一个结果，reduce使用第一个元素作为初始值，fold允许指定初始值。\n\n除此之外，Dart还提供了其他实用的遍历和转换方法，如any()检查是否至少有一个元素满足条件，every()检查是否所有元素都满足条件，firstWhere()查找第一个满足条件的元素，lastWhere()查找最后一个满足条件的元素，skip()跳过前n个元素，take()获取前n个元素等。这些方法都采用了函数式编程的风格，可以组合使用，提高代码的可读性和维护性。", "parameters": [], "returnValue": "根据操作不同而不同", "examples": [{"code": "void main() {\n  // 创建一个示例列表\n  var numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];\n  print('原始数字列表: $numbers');\n  \n  // 使用for-in循环遍历\n  print('\\n使用for-in循环遍历:');\n  for (var number in numbers) {\n    print('处理元素: $number');\n  }\n  \n  // 使用forEach方法遍历\n  print('\\n使用forEach方法遍历:');\n  numbers.forEach((number) {\n    print('处理元素: $number');\n  });\n  \n  // 使用forEach的简化形式（箭头函数）\n  print('\\n使用forEach箭头函数:');\n  numbers.forEach((number) => print('处理元素: $number'));\n  \n  // 使用传统for循环和索引遍历\n  print('\\n使用传统for循环和索引遍历:');\n  for (var i = 0; i < numbers.length; i++) {\n    print('索引 $i: ${numbers[i]}');\n  }\n  \n  // 使用map转换元素\n  print('\\n使用map转换元素:');\n  var squares = numbers.map<int>((number) => number * number).toList();\n  print('原始列表: $numbers');\n  print('平方后: $squares');\n  \n  // 使用where筛选元素\n  print('\\n使用where筛选元素:');\n  var evenNumbers = numbers.where((number) => number % 2 == 0).toList();\n  print('所有偶数: $evenNumbers');\n  \n  // 组合使用map和where\n  print('\\n组合使用map和where:');\n  var squaresOfEven = numbers\n      .where((number) => number % 2 == 0)\n      .map<int>((number) => number * number)\n      .toList();\n  print('偶数的平方: $squaresOfEven');\n  \n  // 使用sort排序\n  print('\\n使用sort排序:');\n  var unsortedList = [5, 2, 8, 1, 9, 3];\n  print('排序前: $unsortedList');\n  \n  // 默认升序排序（直接修改原列表）\n  unsortedList.sort();\n  print('升序排序后: $unsortedList');\n  \n  // 自定义排序（降序）\n  unsortedList.sort((a, b) => b.compareTo(a));\n  print('降序排序后: $unsortedList');\n  \n  // 复杂对象排序\n  var people = [\n    {'name': '张三', 'age': 25},\n    {'name': '李四', 'age': 18},\n    {'name': '王五', 'age': 30},\n  ];\n  \n  // 按年龄排序\n  people.sort((a, b) => a['age'] as int - (b['age'] as int));\n  print('按年龄排序: $people');\n  \n  // 使用reduce合并元素\n  print('\\n使用reduce合并元素:');\n  var sum = numbers.reduce((sum, number) => sum + number);\n  print('列表元素之和: $sum');\n  \n  // 使用fold合并元素（可以指定初始值）\n  print('\\n使用fold合并元素:');\n  var sumWithInitial = numbers.fold(100, (sum, number) => sum + number);\n  print('列表元素之和（初始值100）: $sumWithInitial');\n  \n  // 使用any和every检查条件\n  print('\\n使用any和every检查条件:');\n  var hasEven = numbers.any((number) => number % 2 == 0);\n  print('列表中是否有偶数: $hasEven');\n  \n  var allEven = numbers.every((number) => number % 2 == 0);\n  print('列表中是否全是偶数: $allEven');\n  \n  // 使用firstWhere和lastWhere查找元素\n  print('\\n使用firstWhere和lastWhere查找元素:');\n  var firstEven = numbers.firstWhere(\n    (number) => number % 2 == 0,\n    orElse: () => -1, // 如果没找到，返回-1\n  );\n  print('第一个偶数: $firstEven');\n  \n  var lastEven = numbers.lastWhere(\n    (number) => number % 2 == 0,\n    orElse: () => -1, // 如果没找到，返回-1\n  );\n  print('最后一个偶数: $lastEven');\n  \n  // 使用skip和take获取部分元素\n  print('\\n使用skip和take获取部分元素:');\n  var skippedList = numbers.skip(3).toList(); // 跳过前3个元素\n  print('跳过前3个元素: $skippedList');\n  \n  var takenList = numbers.take(3).toList(); // 获取前3个元素\n  print('获取前3个元素: $takenList');\n  \n  // 链式组合使用\n  print('\\n链式组合使用:');\n  var result = numbers\n      .where((n) => n > 5) // 筛选大于5的数字\n      .take(3) // 获取前3个\n      .map((n) => n * 10) // 每个数字乘以10\n      .toList();\n  print('大于5的前3个数字乘以10: $result');\n  \n  // 使用asMap获取带索引的Map\n  print('\\n使用asMap获取带索引的Map:');\n  var numbersMap = numbers.asMap();\n  print('转换为Map: $numbersMap');\n  \n  // 遍历带索引的项\n  print('\\n同时获取索引和元素进行遍历:');\n  numbers.asMap().forEach((index, value) {\n    print('索引 $index: $value');\n  });\n  \n  // 使用indexed方法获取带索引的元素（Dart 2.18+）\n  print('\\n使用indexed方法获取带索引:');\n  for (var entry in [10, 20, 30].indexed) {\n    print('索引 ${entry.$1}: ${entry.$2}');\n  }\n  \n  // 列表扁平化\n  print('\\n列表扁平化:');\n  var nestedList = [\n    [1, 2],\n    [3, 4, 5],\n    [6]\n  ];\n  var flatList = nestedList.expand((list) => list).toList();\n  print('嵌套列表: $nestedList');\n  print('扁平化后: $flatList');\n  \n  // 列表分组\n  print('\\n列表分组:');\n  var mixedNumbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];\n  var grouped = {\n    'even': mixedNumbers.where((n) => n % 2 == 0).toList(),\n    'odd': mixedNumbers.where((n) => n % 2 != 0).toList(),\n  };\n  print('分组结果: $grouped');\n  \n  // 实际应用：数据处理管道\n  print('\\n实际应用：数据处理管道:');\n  var rawData = [\n    {'name': '产品A', 'price': 100, 'stock': 5},\n    {'name': '产品B', 'price': 200, 'stock': 0},\n    {'name': '产品C', 'price': 150, 'stock': 8},\n    {'name': '产品D', 'price': 120, 'stock': 2},\n  ];\n  \n  var availableProducts = rawData\n      .where((product) => product['stock'] as int > 0) // 筛选有库存的产品\n      .map((product) => {\n            'name': product['name'],\n            'price': product['price'],\n            'inStock': true,\n          }) // 转换为新格式\n      .toList();\n  \n  print('原始数据: $rawData');\n  print('处理后的产品列表: $availableProducts');\n  \n  // 作业练习：创建一个学生分数处理系统\n  print('\\n作业练习：学生分数处理系统');\n  var studentScores = [\n    {'name': '张三', 'math': 85, 'english': 76, 'science': 92},\n    {'name': '李四', 'math': 92, 'english': 88, 'science': 96},\n    {'name': '王五', 'math': 78, 'english': 64, 'science': 80},\n    {'name': '赵六', 'math': 65, 'english': 70, 'science': 72},\n  ];\n  \n  // TODO: 实现以下功能\n  // 1. 计算每个学生的平均分\n  // 2. 筛选出平均分大于等于80的学生\n  // 3. 按平均分从高到低排序\n  // 4. 为每个学生添加一个'rank'字段表示排名\n  \n  // 参考实现\n  var processedScores = studentScores\n      .map((student) {\n        // 计算平均分\n        var avg = ((student['math'] as int) +\n                (student['english'] as int) +\n                (student['science'] as int)) /\n            3;\n        return {...student, 'average': avg};\n      })\n      .where((student) => (student['average'] as double) >= 80) // 筛选平均分>=80的学生\n      .toList()\n      ..sort((a, b) => (b['average'] as double).compareTo(a['average'] as double)); // 按平均分降序排序\n  \n  // 添加排名\n  for (var i = 0; i < processedScores.length; i++) {\n    processedScores[i]['rank'] = i + 1;\n  }\n  \n  print('处理后的学生成绩单:');\n  processedScores.forEach((student) {\n    print('${student['name']}: 平均分 ${(student['average'] as double).toStringAsFixed(2)}, 排名 ${student['rank']}');\n  });\n}", "explanation": "这个示例全面展示了Dart中List的遍历与转换方法。首先展示了三种基本的遍历方式：for-in循环、forEach方法和传统for循环。然后演示了各种转换方法：map用于转换元素类型或值，where用于筛选元素，sort用于排序（包括自定义排序规则），reduce和fold用于合并元素。示例还展示了其他实用方法，如any和every用于条件检查，firstWhere和lastWhere用于查找元素，skip和take用于获取部分元素。最后通过一个实际的数据处理示例和学生成绩处理系统作业，展示了如何将这些方法组合使用，构建复杂的数据处理管道。"}]}}, {"name": "Set Definition and Operations", "trans": ["Set定义与操作"], "usage": {"syntax": "// 创建空Set\nSet<类型> 变量名 = {};\nvar 变量名 = <类型>{};\n\n// 使用字面量创建并初始化\nvar 变量名 = {值1, 值2, 值3};\n\n// 使用构造函数\nSet<类型> 变量名 = Set<类型>(); // 创建空Set\nSet<类型> 变量名 = Set<类型>.from(可迭代对象); // 从可迭代对象创建Set\nSet<类型> 变量名 = Set<类型>.of(可迭代对象); // 从可迭代对象创建Set\n\n// 添加元素\n集合.add(元素); // 添加单个元素\n集合.addAll(可迭代对象); // 添加多个元素\n\n// 删除元素\n集合.remove(元素); // 删除指定元素\n集合.removeAll(可迭代对象); // 删除多个元素\n集合.removeWhere((元素) => 条件); // 删除满足条件的元素\n集合.clear(); // 清空Set\n\n// 检查元素\nbool 包含 = 集合.contains(元素); // 检查是否包含元素\nbool 包含所有 = 集合.containsAll(可迭代对象); // 检查是否包含所有指定元素\n\n// 集合操作\nSet 交集 = 集合1.intersection(集合2); // 获取两个集合的交集\nSet 并集 = 集合1.union(集合2); // 获取两个集合的并集\nSet 差集 = 集合1.difference(集合2); // 获取两个集合的差集", "description": "Set是Dart中的一种无序集合，其中每个元素都是唯一的。Set特别适合需要存储唯一元素或进行集合操作（如并集、交集、差集）的场景。Set可以通过字面量语法或构造函数创建，创建时需要指定元素类型，例如Set<int>或Set<String>。\n\nSet的主要特点是：元素不重复，即使尝试添加重复元素也会被忽略；元素没有固定顺序，不能通过索引访问；支持标准的集合操作，如并集、交集和差集。\n\nSet提供了多种方法来添加和删除元素：add()添加单个元素，addAll()添加多个元素，remove()删除指定元素，removeAll()删除多个元素，removeWhere()删除满足条件的元素，clear()删除所有元素。\n\nSet还提供了contains()方法检查是否包含特定元素，containsAll()方法检查是否包含所有指定元素。此外，Set支持标准的集合操作：intersection()获取两个集合的交集，union()获取两个集合的并集，difference()获取两个集合的差集。\n\nSet是基于哈希表实现的，因此元素的添加、删除和查找操作通常具有O(1)的时间复杂度，比List的O(n)更高效。这使得Set特别适合需要频繁检查元素是否存在的场景。然而，由于Set是无序的，如果需要保持元素的插入顺序，可以使用LinkedHashSet。", "parameters": [], "returnValue": "根据操作不同而不同", "examples": [{"code": "void main() {\n  // 创建Set的不同方式\n  print('创建Set的不同方式:');\n  \n  // 使用字面量创建Set\n  var numbersSet = {1, 2, 3, 4, 5};\n  print('使用字面量创建的Set: $numbersSet');\n  \n  // 创建指定类型的空Set\n  var emptySet = <String>{};\n  print('空Set: $emptySet');\n  \n  // 注意：这样创建的是Map，不是Set\n  var notASet = {}; // 这是一个空Map，不是Set\n  print('notASet的类型: ${notASet.runtimeType}'); // 输出: _Map<dynamic, dynamic>\n  \n  // 使用构造函数创建Set\n  var constructedSet = Set<int>();\n  constructedSet.add(1);\n  constructedSet.add(2);\n  print('使用构造函数创建的Set: $constructedSet');\n  \n  // 从其他可迭代对象创建Set\n  var listOfNumbers = [1, 2, 2, 3, 3, 3, 4];\n  var setFromList = Set<int>.from(listOfNumbers);\n  print('原始List: $listOfNumbers');\n  print('从List创建的Set（自动去重）: $setFromList');\n  \n  // Set的基本操作\n  print('\\nSet的基本操作:');\n  \n  // 添加元素\n  var fruitsSet = {'苹果', '香蕉', '橙子'};\n  print('原始Set: $fruitsSet');\n  \n  fruitsSet.add('葡萄');\n  print('添加单个元素后: $fruitsSet');\n  \n  fruitsSet.addAll(['芒果', '西瓜']);\n  print('添加多个元素后: $fruitsSet');\n  \n  // 尝试添加重复元素（会被忽略）\n  fruitsSet.add('苹果');\n  print('尝试添加重复元素后: $fruitsSet'); // 不会改变，因为'苹果'已存在\n  \n  // 删除元素\n  fruitsSet.remove('香蕉');\n  print('删除香蕉后: $fruitsSet');\n  \n  fruitsSet.removeAll(['芒果', '西瓜']);\n  print('删除芒果和西瓜后: $fruitsSet');\n  \n  fruitsSet.removeWhere((fruit) => fruit.startsWith('橙'));\n  print('删除以\"橙\"开头的水果后: $fruitsSet');\n  \n  // 检查元素\n  print('\\n检查元素:');\n  print('Set包含苹果: ${fruitsSet.contains('苹果')}');\n  print('Set包含香蕉: ${fruitsSet.contains('香蕉')}');\n  \n  print('Set包含所有[苹果, 葡萄]: ${fruitsSet.containsAll(['苹果', '葡萄'])}');\n  print('Set包含所有[苹果, 香蕉]: ${fruitsSet.containsAll(['苹果', '香蕉'])}');\n  \n  // 集合操作\n  print('\\n集合操作:');\n  var set1 = {1, 2, 3, 4};\n  var set2 = {3, 4, 5, 6};\n  \n  print('set1: $set1');\n  print('set2: $set2');\n  \n  // 交集：同时存在于两个集合中的元素\n  var intersection = set1.intersection(set2);\n  print('交集 (set1 ∩ set2): $intersection');\n  \n  // 并集：存在于任一集合中的元素\n  var union = set1.union(set2);\n  print('并集 (set1 ∪ set2): $union');\n  \n  // 差集：存在于第一个集合但不存在于第二个集合的元素\n  var difference1 = set1.difference(set2);\n  print('差集 (set1 - set2): $difference1');\n  \n  var difference2 = set2.difference(set1);\n  print('差集 (set2 - set1): $difference2');\n  \n  // 遍历Set\n  print('\\n遍历Set:');\n  var colorsSet = {'红', '绿', '蓝'};\n  \n  // 使用for-in循环\n  print('使用for-in循环:');\n  for (var color in colorsSet) {\n    print('颜色: $color');\n  }\n  \n  // 使用forEach方法\n  print('\\n使用forEach方法:');\n  colorsSet.forEach((color) => print('颜色: $color'));\n  \n  // 转换Set\n  print('\\n转换Set:');\n  \n  // 转换为List\n  var colorsList = colorsSet.toList();\n  print('转换为List: $colorsList');\n  \n  // 使用map方法转换元素\n  var transformedSet = colorsSet.map((color) => '$color色').toSet();\n  print('转换后的Set: $transformedSet');\n  \n  // 使用where方法筛选元素\n  var filteredSet = colorsSet.where((color) => color != '绿').toSet();\n  print('筛选后的Set: $filteredSet');\n  \n  // 实际应用：去重\n  print('\\n实际应用：去重:');\n  var duplicatesList = ['苹果', '香蕉', '苹果', '橙子', '香蕉', '橙子', '葡萄'];\n  print('原始List: $duplicatesList');\n  \n  var uniqueItems = duplicatesList.toSet().toList();\n  print('去重后: $uniqueItems');\n  \n  // 实际应用：标签系统\n  print('\\n实际应用：标签系统:');\n  var posts = [\n    {'id': 1, 'title': '文章1', 'tags': {'技术', 'Dart', '编程'}},\n    {'id': 2, 'title': '文章2', 'tags': {'技术', 'Flutter', '移动开发'}},\n    {'id': 3, 'title': '文章3', 'tags': {'Dart', 'Flutter', '编程'}},\n  ];\n  \n  // 获取所有唯一标签\n  var allTags = <String>{};\n  for (var post in posts) {\n    allTags.addAll(post['tags'] as Set<String>);\n  }\n  print('所有唯一标签: $allTags');\n  \n  // 查找包含特定标签的文章\n  var searchTag = 'Flutter';\n  var matchingPosts = posts.where((post) {\n    var postTags = post['tags'] as Set<String>;\n    return postTags.contains(searchTag);\n  }).toList();\n  \n  print('包含\"$searchTag\"标签的文章:');\n  for (var post in matchingPosts) {\n    print('${post['id']}: ${post['title']}');\n  }\n  \n  // 查找包含多个标签的文章（必须同时包含所有指定标签）\n  var searchTags = {'Dart', 'Flutter'};\n  var multiTagPosts = posts.where((post) {\n    var postTags = post['tags'] as Set<String>;\n    return postTags.containsAll(searchTags);\n  }).toList();\n  \n  print('\\n同时包含${searchTags.join(\"和\")}的文章:');\n  for (var post in multiTagPosts) {\n    print('${post['id']}: ${post['title']}');\n  }\n  \n  // 使用LinkedHashSet保持插入顺序\n  print('\\n使用LinkedHashSet保持插入顺序:');\n  var linkedHashSet = LinkedHashSet<int>();\n  linkedHashSet.addAll([5, 3, 1, 4, 2]);\n  print('LinkedHashSet: $linkedHashSet'); // 会按照插入顺序保持：5, 3, 1, 4, 2\n  \n  var regularSet = <int>{};\n  regularSet.addAll([5, 3, 1, 4, 2]);\n  print('Regular Set: $regularSet'); // 可能会重新排序\n  \n  // 作业练习：单词频率分析器\n  print('\\n作业练习：单词频率分析器');\n  var text = \"\"\"Dart是由Google开发的编程语言，可用于开发Web、服务器、移动应用和物联网应用。\n  Dart是面向对象的、类定义的、垃圾收集的语言，语法类似C语言。\n  Dart可以编译成原生码或JavaScript，支持接口、混合、抽象类、具体化泛型和类型推断。\n  Dart的主要实现方式包括Dart VM虚拟机和dart2js编译器。\n  Dart是Flutter框架的编程语言，Flutter是Google的移动应用开发框架，用于为Android、iOS、Windows、Mac、Linux和Web开发应用。\"\"\";\n  \n  // TODO: 实现以下功能\n  // 1. 将文本分割成单词列表\n  // 2. 去除标点符号和空格\n  // 3. 统计每个单词出现的次数\n  // 4. 按出现频率从高到低排序\n  // 5. 输出前10个高频词\n  \n  // 参考实现\n  // 分割文本并清理\n  var words = text\n      .replaceAll(RegExp(r'[\\n\\r.,、，。？！：；\"\" ()]'), ' ')\n      .split(' ')\n      .where((word) => word.isNotEmpty)\n      .map((word) => word.toLowerCase())\n      .toList();\n  \n  // 统计频率\n  var wordCount = <String, int>{};\n  for (var word in words) {\n    wordCount[word] = (wordCount[word] ?? 0) + 1;\n  }\n  \n  // 排序\n  var sortedWords = wordCount.entries.toList()\n    ..sort((a, b) => b.value.compareTo(a.value));\n  \n  // 输出前10个高频词\n  print('文本中出现频率最高的10个单词:');\n  for (var i = 0; i < 10 && i < sortedWords.length; i++) {\n    var entry = sortedWords[i];\n    print('${i + 1}. ${entry.key}: ${entry.value}次');\n  }\n}", "explanation": "这个示例全面介绍了Dart中Set的定义、初始化和常用操作。首先展示了创建Set的不同方式，包括使用字面量、构造函数以及从其他集合创建。然后演示了Set的基本操作，如添加元素、删除元素和检查元素是否存在。接着展示了Set的核心特性——集合操作，包括交集、并集和差集。示例还介绍了如何遍历Set以及如何对Set进行转换和筛选。最后通过实际应用场景（数据去重、标签系统）展示了Set在实际开发中的用途，并介绍了LinkedHashSet如何保持元素的插入顺序。最后通过一个单词频率分析器的作业练习，综合运用了Set和Map的知识。"}]}}, {"name": "Map Definition and Operations", "trans": ["Map定义与操作"], "usage": {"syntax": "// 创建空Map\nMap<键类型, 值类型> 变量名 = {};\nvar 变量名 = <键类型, 值类型>{};\n\n// 使用字面量创建并初始化\nvar 变量名 = {\n  键1: 值1,\n  键2: 值2,\n  键3: 值3\n};\n\n// 使用构造函数\nMap<键类型, 值类型> 变量名 = Map<键类型, 值类型>(); // 创建空Map\nMap<键类型, 值类型> 变量名 = Map<键类型, 值类型>.from(其他Map); // 从其他Map创建\nMap<键类型, 值类型> 变量名 = Map<键类型, 值类型>.of(其他Map); // 从其他Map创建\n\n// 访问和修改元素\n值类型 值 = 映射[键]; // 获取值（如果键不存在则返回null）\n映射[键] = 新值; // 设置或更新值\n\n// 检查键是否存在\nbool 包含 = 映射.containsKey(键);\nbool 包含 = 映射.containsValue(值);\n\n// 添加和删除元素\n映射.addAll(其他Map); // 添加多个键值对\n映射.remove(键); // 删除指定键的键值对\n映射.removeWhere((键, 值) => 条件); // 删除满足条件的键值对\n映射.clear(); // 清空Map\n\n// 获取键和值的集合\nIterable<键类型> 键集合 = 映射.keys;\nIterable<值类型> 值集合 = 映射.values;\nIterable<MapEntry<键类型, 值类型>> 条目集合 = 映射.entries;\n\n// 遍历Map\n映射.forEach((键, 值) {\n  // 处理每个键值对\n});\n\n// 转换Map\nMap<新键类型, 新值类型> 新映射 = 映射.map((键, 值) => MapEntry(新键, 新值));", "description": "Map是Dart中的键值对集合，用于存储关联数据。Map中的每个元素都有一个唯一的键和与之关联的值。Map特别适合需要根据特定标识符（键）快速查找数据的场景。\n\nMap可以通过字面量语法或构造函数创建，创建时需要指定键和值的类型，例如Map<String, int>或Map<int, String>。Dart的Map是无序的，不保证键值对的顺序（除非使用LinkedHashMap）。\n\nMap提供了多种方法来访问和修改其元素：使用方括号语法[键]访问值，如果键不存在则返回null；使用方括号语法和赋值操作符更新或添加键值对；使用containsKey()检查键是否存在；使用containsValue()检查值是否存在。\n\nMap还提供了添加和删除元素的方法：addAll()添加多个键值对，remove()删除指定键的键值对，removeWhere()删除满足条件的键值对，clear()删除所有键值对。\n\nMap提供了获取键和值集合的属性：keys返回所有键的Iterable，values返回所有值的Iterable，entries返回所有键值对的Iterable（MapEntry对象）。这些属性可用于遍历Map的元素。\n\nMap也支持函数式的转换操作，如map()方法可以转换Map的键和值。此外，Map还提供了一些实用方法，如putIfAbsent()（如果键不存在则添加键值对），update()（更新现有键的值或添加新键值对），updateAll()（更新所有值）等。\n\n在Dart 2.3之后，Map支持集合展开运算符(...)和空感知展开运算符(...?)，使得合并Map和处理可能为null的Map更加方便。", "parameters": [], "returnValue": "根据操作不同而不同", "examples": [{"code": "void main() {\n  // 创建Map的不同方式\n  print('创建Map的不同方式:');\n  \n  // 使用字面量创建Map\n  var person = {\n    'name': '张三',\n    'age': 30,\n    'isStudent': false,\n  };\n  print('使用字面量创建的Map: $person');\n  \n  // 创建指定类型的Map\n  Map<String, int> scores = {\n    '语文': 95,\n    '数学': 88,\n    '英语': 90,\n  };\n  print('成绩Map: $scores');\n  \n  // 创建空Map\n  var emptyMap = <String, double>{};\n  print('空Map: $emptyMap');\n  \n  // 使用构造函数创建Map\n  var constructedMap = Map<String, bool>();\n  constructedMap['已完成'] = true;\n  constructedMap['已审核'] = false;\n  print('使用构造函数创建的Map: $constructedMap');\n  \n  // 从其他Map创建Map\n  var originalMap = {'a': 1, 'b': 2, 'c': 3};\n  var mapFromOther = Map<String, int>.from(originalMap);\n  print('从其他Map创建的Map: $mapFromOther');\n  \n  // Map的基本操作\n  print('\\nMap的基本操作:');\n  \n  // 访问元素\n  var student = {\n    'name': '李四',\n    'age': 22,\n    'grade': 'A',\n  };\n  print('学生Map: $student');\n  \n  // 使用方括号访问值\n  var studentName = student['name'];\n  print('学生姓名: $studentName');\n  \n  // 访问不存在的键\n  var address = student['address']; // 返回null\n  print('地址（不存在）: $address');\n  \n  // 安全访问（避免空值错误）\n  var studentGrade = student['grade'] ?? '未评分';\n  var studentCity = student['city'] ?? '未知';\n  print('成绩: $studentGrade');\n  print('城市: $studentCity');\n  \n  // 修改元素\n  student['age'] = 23; // 更新现有键的值\n  print('更新年龄后: $student');\n  \n  student['email'] = '<EMAIL>'; // 添加新键值对\n  print('添加邮箱后: $student');\n  \n  // 检查键是否存在\n  print('\\n检查键和值:');\n  print('Map包含\"name\"键: ${student.containsKey(\"name\")}');\n  print('Map包含\"phone\"键: ${student.containsKey(\"phone\")}');\n  \n  // 检查值是否存在\n  print('Map包含值\"李四\": ${student.containsValue(\"李四\")}');\n  print('Map包含值\"王五\": ${student.containsValue(\"王五\")}');\n  \n  // 添加多个键值对\n  student.addAll({\n    'phone': '123456789',\n    'address': '北京市',\n  });\n  print('添加多个键值对后: $student');\n  \n  // 删除键值对\n  student.remove('grade');\n  print('删除成绩后: $student');\n  \n  // 条件删除\n  student.removeWhere((key, value) => key.startsWith('a'));\n  print('删除以\"a\"开头的键后: $student');\n  \n  // 获取所有键\n  print('\\n获取键和值:');\n  var keys = student.keys;\n  print('所有键: $keys');\n  \n  // 获取所有值\n  var values = student.values;\n  print('所有值: $values');\n  \n  // 获取所有条目\n  var entries = student.entries;\n  print('所有条目: $entries');\n  \n  // 遍历Map\n  print('\\n遍历Map:');\n  \n  // 使用forEach\n  print('使用forEach:');\n  student.forEach((key, value) {\n    print('$key: $value');\n  });\n  \n  // 使用for-in遍历键\n  print('\\n遍历键:');\n  for (var key in student.keys) {\n    print('键: $key, 值: ${student[key]}');\n  }\n  \n  // 使用for-in遍历条目\n  print('\\n遍历条目:');\n  for (var entry in student.entries) {\n    print('${entry.key}: ${entry.value}');\n  }\n  \n  // Map转换\n  print('\\nMap转换:');\n  \n  // 转换值\n  var prices = {\n    'apple': 1.0,\n    'banana': 0.5,\n    'orange': 0.8,\n  };\n  \n  // 将所有价格转换为人民币（假设原价是美元）\n  var pricesInCNY = prices.map((key, value) {\n    return MapEntry(key, value * 7.0); // 1美元 = 7人民币\n  });\n  print('美元价格: $prices');\n  print('人民币价格: $pricesInCNY');\n  \n  // putIfAbsent方法\n  print('\\nputIfAbsent方法:');\n  var config = {'theme': 'dark', 'fontSize': 14};\n  print('原始配置: $config');\n  \n  // 如果键不存在，则添加默认值\n  config.putIfAbsent('language', () => 'zh-CN');\n  print('添加默认语言后: $config');\n  \n  // 如果键已存在，则不会更改\n  config.putIfAbsent('theme', () => 'light');\n  print('尝试更改主题后: $config'); // theme仍然是'dark'\n  \n  // update方法\n  print('\\nupdate和updateAll方法:');\n  var inventory = {'apples': 10, 'bananas': 5, 'oranges': 7};\n  print('原始库存: $inventory');\n  \n  // 更新现有键的值\n  inventory.update('apples', (value) => value + 5);\n  print('增加苹果后: $inventory');\n  \n  // 更新不存在的键（提供默认值）\n  inventory.update('mangoes', (value) => value + 2, ifAbsent: () => 3);\n  print('添加芒果后: $inventory');\n  \n  // 更新所有值\n  inventory.updateAll((key, value) => value * 2);\n  print('将所有库存翻倍后: $inventory');\n  \n  // 合并Map\n  print('\\n合并Map:');\n  var defaults = {'theme': 'light', 'fontSize': 12, 'language': 'en'};\n  var userSettings = {'theme': 'dark'};\n  \n  // 合并两个Map（用户设置覆盖默认设置）\n  var effectiveSettings = {...defaults, ...userSettings};\n  print('默认设置: $defaults');\n  print('用户设置: $userSettings');\n  print('最终设置: $effectiveSettings');\n  \n  // 使用空感知展开运算符\n  Map<String, int>? nullableMap;\n  var safeMap = {'count': 0, ...?nullableMap};\n  print('使用空感知展开运算符: $safeMap');\n  \n  // 复杂数据结构示例\n  print('\\n复杂数据结构示例:');\n  var products = [\n    {\n      'id': 1,\n      'name': '笔记本电脑',\n      'price': 5999.0,\n      'specs': {\n        'cpu': 'Intel i5',\n        'ram': '8GB',\n        'storage': '512GB SSD',\n      },\n      'inStock': true,\n    },\n    {\n      'id': 2,\n      'name': '智能手机',\n      'price': 2999.0,\n      'specs': {\n        'cpu': 'Snapdragon 8',\n        'ram': '6GB',\n        'storage': '128GB',\n      },\n      'inStock': false,\n    },\n  ];\n  \n  // 访问嵌套Map\n  print('产品列表:');\n  for (var product in products) {\n    print('\\n${product['name']}:');\n    print('  价格: ${product['price']}元');\n    print('  规格:');\n    \n    var specs = product['specs'] as Map<String, String>;\n    specs.forEach((key, value) {\n      print('    $key: $value');\n    });\n    \n    print('  库存状态: ${product['inStock'] == true ? \"有货\" : \"缺货\"}');\n  }\n  \n  // Map分组示例\n  print('\\nMap分组示例:');\n  var people = [\n    {'name': '张三', 'age': 25, 'city': '北京'},\n    {'name': '李四', 'age': 30, 'city': '上海'},\n    {'name': '王五', 'age': 22, 'city': '北京'},\n    {'name': '赵六', 'age': 28, 'city': '广州'},\n    {'name': '钱七', 'age': 35, 'city': '上海'},\n  ];\n  \n  // 按城市分组\n  var peopleByCity = <String, List<Map<String, dynamic>>>{};\n  \n  for (var person in people) {\n    var city = person['city'] as String;\n    \n    // 如果城市键不存在，初始化一个空列表\n    if (!peopleByCity.containsKey(city)) {\n      peopleByCity[city] = [];\n    }\n    \n    // 将人添加到相应城市的列表中\n    peopleByCity[city]!.add(person);\n  }\n  \n  // 打印分组结果\n  print('按城市分组的人员:');\n  peopleByCity.forEach((city, cityPeople) {\n    print('\\n$city:');\n    for (var person in cityPeople) {\n      print('  ${person['name']}, ${person['age']}岁');\n    }\n  });\n  \n  // 作业练习：创建一个简单的购物车\n  print('\\n作业练习：简单购物车');\n  \n  // TODO: 实现以下功能\n  // 1. 创建一个商品目录Map，键是商品ID，值是包含名称和价格的Map\n  // 2. 创建一个购物车Map，键是商品ID，值是购买数量\n  // 3. 实现添加商品到购物车的功能\n  // 4. 实现从购物车中移除商品的功能\n  // 5. 计算购物车中所有商品的总价\n  // 6. 打印购物小票，包括每种商品的名称、单价、数量和小计，以及总计金额\n  \n  // 参考实现\n  // 商品目录\n  var catalog = {\n    '001': {'name': 'T恤', 'price': 99.5},\n    '002': {'name': '牛仔裤', 'price': 199.0},\n    '003': {'name': '运动鞋', 'price': 299.0},\n    '004': {'name': '背包', 'price': 159.0},\n    '005': {'name': '帽子', 'price': 59.0},\n  };\n  \n  // 购物车\n  var cart = <String, int>{};\n  \n  // 添加商品到购物车\n  void addToCart(String productId, int quantity) {\n    if (!catalog.containsKey(productId)) {\n      print('商品不存在！');\n      return;\n    }\n    \n    // 如果商品已在购物车中，增加数量；否则添加新条目\n    cart[productId] = (cart[productId] ?? 0) + quantity;\n    print('已添加 ${quantity}件 ${catalog[productId]!['name']} 到购物车');\n  }\n  \n  // 从购物车移除商品\n  void removeFromCart(String productId, [int? quantity]) {\n    if (!cart.containsKey(productId)) {\n      print('购物车中没有此商品！');\n      return;\n    }\n    \n    if (quantity == null || quantity >= cart[productId]!) {\n      // 完全移除商品\n      var productName = catalog[productId]!['name'];\n      cart.remove(productId);\n      print('已从购物车移除所有 $productName');\n    } else {\n      // 减少数量\n      cart[productId] = cart[productId]! - quantity;\n      print('已从购物车移除 ${quantity}件 ${catalog[productId]!['name']}');\n    }\n  }\n  \n  // 计算总价\n  double calculateTotal() {\n    double total = 0;\n    cart.forEach((productId, quantity) {\n      var price = catalog[productId]!['price'] as double;\n      total += price * quantity;\n    });\n    return total;\n  }\n  \n  // 打印购物小票\n  void printReceipt() {\n    if (cart.isEmpty) {\n      print('购物车为空！');\n      return;\n    }\n    \n    print('\\n===== 购物小票 =====');\n    print('商品名称\\t单价\\t数量\\t小计');\n    \n    cart.forEach((productId, quantity) {\n      var product = catalog[productId]!;\n      var name = product['name'];\n      var price = product['price'] as double;\n      var subtotal = price * quantity;\n      \n      print('$name\\t¥${price.toStringAsFixed(2)}\\t$quantity\\t¥${subtotal.toStringAsFixed(2)}');\n    });\n    \n    print('------------------');\n    print('总计: ¥${calculateTotal().toStringAsFixed(2)}');\n    print('====================');\n  }\n  \n  // 使用购物车\n  addToCart('001', 2);  // 添加2件T恤\n  addToCart('003', 1);  // 添加1双运动鞋\n  addToCart('005', 3);  // 添加3顶帽子\n  addToCart('001', 1);  // 再添加1件T恤\n  \n  print('\\n当前购物车:');\n  cart.forEach((productId, quantity) {\n    print('${catalog[productId]!['name']}: $quantity件');\n  });\n  \n  removeFromCart('005', 2);  // 移除2顶帽子\n  removeFromCart('002');     // 尝试移除不存在的商品\n  \n  // 打印购物小票\n  printReceipt();\n}", "explanation": "这个示例全面介绍了Dart中Map的定义、初始化和常用操作。首先展示了创建Map的不同方式，包括使用字面量、构造函数以及从其他Map创建。然后演示了Map的基本操作，如访问和修改元素、检查键是否存在、添加和删除元素。接着展示了如何获取Map的键、值和条目集合，以及如何遍历Map。示例还介绍了Map的转换操作，包括map方法、putIfAbsent方法、update方法和updateAll方法，以及如何使用展开运算符合并Map。最后通过复杂数据结构示例和Map分组示例展示了Map在实际开发中的应用，并通过一个简单的购物车系统作业练习，综合运用了Map的各种操作。"}]}}]}