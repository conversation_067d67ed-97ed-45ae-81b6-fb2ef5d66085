# JavaScript学习路线与核心内容

## 1. 基础语法      //已完成

### 变量与常量
- var/let/const声明
- 变量作用域
- 变量提升
- 常量特性

### 数据类型
- 基本数据类型（Number、String、Boolean、Null、Undefined、Symbol、BigInt）
- 引用数据类型（Object、Array、Function、Date、RegExp等）
- 类型判断与转换
- 浅拷贝与深拷贝

### 运算符
- 算术运算符
- 赋值运算符
- 比较运算符
- 逻辑运算符
- 位运算符
- 三元运算符

### 流程控制
- if/else条件语句
- switch语句
- for/while/do...while循环
- break/continue
- return语句

## 2. 函数与作用域          //已完成

### 函数基础
- 函数声明与表达式
- 参数与默认值
- 剩余参数与arguments
- 匿名函数与箭头函数
- 立即执行函数（IIFE）

### 作用域与闭包
- 作用域链
- 闭包原理与应用
- this指向
- call/apply/bind
- 垃圾回收与内存泄漏

## 3. 对象与面向对象        //已完成

### 对象基础
- 对象字面量与属性
- 属性访问与操作
- 属性描述符
- 对象方法与this
- 构造函数与原型
- 原型链与继承

### 类与ES6面向对象
- class声明
- 构造函数与super
- 静态属性与方法
- 继承与多态
- getter/setter
- 实例与原型方法

## 4. 数组与集合    //已完成

### 数组基础
- 数组声明与初始化
- 数组遍历（for、forEach、map、filter、reduce等）
- 数组常用方法（push、pop、shift、unshift、splice、slice、concat、join、sort、reverse等）
- 多维数组与稀疏数组

### ES6集合类型
- Set与WeakSet
- Map与WeakMap
- 集合的常用操作

## 5. 字符串与正则表达式   //已完成

### 字符串操作
- 字符串声明与模板字符串
- 常用方法（length、charAt、slice、substring、substr、indexOf、replace、split、trim、padStart/padEnd等）
- 字符串遍历与处理

### 正则表达式
- 正则基础语法
- 常用正则方法（test、exec、match、replace、search、split）
- 正则表达式应用场景

## 6. ES6+新特性        //已完成

### 变量与解构
- let/const
- 解构赋值
- 扩展运算符

### 模板字符串与简写
- 模板字符串
- 属性简写与方法简写

### 箭头函数与this
- 箭头函数语法
- this绑定规则

### Promise与异步
- Promise基础
- async/await
- fetch与AJAX
- Generator函数
- 异步流程控制

### 模块化
- export/import
- CommonJS与ESM
- 动态import

### 其他ES6+特性
- Symbol
- Set/Map
- Proxy/Reflect
- 可选链与空值合并
- Array/Number/String新方法

## 7. 异步编程          //已完成

### 回调与Promise
- 回调函数与回调地狱
- Promise链式调用
- 错误处理

### async/await
- 基本用法
- 错误捕获与并发

### 事件循环与微任务
- 事件循环机制
- 宏任务与微任务
- setTimeout/setInterval
- requestAnimationFrame

## 8. DOM与BOM       //已完成

### DOM操作
- 节点获取与遍历
- 节点增删改查
- 属性与样式操作
- classList与dataset
- 事件绑定与委托
- 表单与输入处理

### BOM操作
- window对象
- location、history、navigator
- 本地存储（localStorage、sessionStorage、cookie）
- 弹窗与定时器

## 9. 事件与交互        //已完成

### 事件模型
- 事件捕获与冒泡
- 事件对象与属性
- 自定义事件
- 事件委托

### 常见事件类型

#### 鼠标事件
- `click`：当元素被点击时触发
- `dblclick`：当元素被双击时触发
- `mousedown`：当鼠标按钮被按下时触发
- `mouseup`：当鼠标按钮被释放时触发
- `mousemove`：当鼠标在元素内移动时触发
- `mouseover`：当鼠标移入元素时触发
- `mouseout`：当鼠标移出元素时触发
- `mouseenter`：当鼠标移入元素时触发（不冒泡）
- `mouseleave`：当鼠标移出元素时触发（不冒泡）
- `contextmenu`：当右键点击时触发

#### 键盘事件
- `keydown`：当键盘按键被按下时触发
- `keyup`：当键盘按键被释放时触发
- `keypress`：当键盘按键被按下并产生字符时触发（已弃用）

#### 表单事件
- `submit`：当表单提交时触发
- `reset`：当表单重置时触发
- `change`：当表单元素的值改变并失去焦点时触发
- `input`：当表单元素的值改变时实时触发
- `focus`：当元素获得焦点时触发
- `blur`：当元素失去焦点时触发
- `select`：当文本被选中时触发

#### 触摸与手势事件
- `touchstart`：当触摸开始时触发
- `touchmove`：当触摸点在屏幕上移动时触发
- `touchend`：当触摸结束时触发
- `touchcancel`：当触摸被中断时触发
- `gesturestart`：当手势（如捏合）开始时触发
- `gesturechange`：当手势变化时触发
- `gestureend`：当手势结束时触发

## 10. 工具函数与实用技巧   //已完成

### 常用工具函数
- 防抖与节流
- 深拷贝与浅拷贝
- 对象合并与克隆
- 数组去重与分组
- 函数柯里化与组合

### 编码规范与最佳实践
- 命名规范
- 代码风格
- 注释与文档
- 代码可读性与可维护性

## 11. 调试与性能优化       //已完成

### 调试技巧            
- console.log与断点调试
- 浏览器调试工具
- SourceMap
- 错误捕获与处理

### 性能优化
- 内存泄漏排查
- 代码分割与懒加载
- 资源压缩与缓存
- DOM优化
- 事件优化
- 异步优化

## 12. 工程化与生态         //已完成

### 包管理与构建
- npm/yarn/pnpm
- package.json配置
- 模块打包（Webpack、Vite、Rollup）
- Babel与转译
- Lint与格式化

### 单元测试与自动化
- Jest/Mocha基础
- 断言与Mock
- 覆盖率报告
- 持续集成

### 现代前端生态
- TypeScript集成
- 前端框架（React/Vue/Angular）
- 状态管理库
- 路由与数据流
- UI组件库
- 移动端与PWA 