{"name": "Local State Management", "trans": ["本地状态管理"], "methods": [{"name": "setState Usage", "trans": ["setState用法"], "usage": {"syntax": "setState(() { ... })", "description": "setState是最基础的状态管理方法，用于通知Flutter框架重建当前组件。", "parameters": [{"name": "callback", "description": "在回调中修改状态变量"}], "returnValue": "无返回值，状态变化后自动刷新界面。", "examples": [{"code": "// setState用法示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatefulWidget {\n  @override\n  _MyAppState createState() => _MyAppState();\n}\n\nclass _MyAppState extends State<MyApp> {\n  int _count = 0;\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      home: Scaffold(\n        appBar: AppBar(title: Text('setState示例')),\n        body: Center(\n          child: Column(\n            mainAxisAlignment: MainAxisAlignment.center,\n            children: [\n              Text('计数: \\$_count', style: TextStyle(fontSize: 24)),\n              ElevatedButton(\n                onPressed: () {\n                  setState(() { _count++; });\n                },\n                child: Text('加一'),\n              ),\n            ],\n          ),\n        ),\n      ),\n    );\n  }\n}", "explanation": "演示了setState的基本用法，点击按钮后计数加一并刷新界面。"}]}}, {"name": "InheritedWidget", "trans": ["InheritedWidget"], "usage": {"syntax": "class MyInherited extends InheritedWidget { ... }", "description": "InheritedWidget用于在组件树中向下传递数据，适合全局或祖先-后代通信。", "parameters": [{"name": "child", "description": "子组件"}, {"name": "of", "description": "静态方法获取InheritedWidget数据"}], "returnValue": "无返回值，子组件可通过of方法获取数据。", "examples": [{"code": "// InheritedWidget示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass CounterProvider extends InheritedWidget {\n  final int count;\n  final Function() increment;\n  CounterProvider({required this.count, required this.increment, required Widget child}) : super(child: child);\n  static CounterProvider of(BuildContext context) => context.dependOnInheritedWidgetOfExactType<CounterProvider>()!;\n  @override\n  bool updateShouldNotify(CounterProvider oldWidget) => count != oldWidget.count;\n}\n\nclass MyApp extends StatefulWidget {\n  @override\n  _MyAppState createState() => _MyAppState();\n}\n\nclass _MyAppState extends State<MyApp> {\n  int _count = 0;\n  void _inc() => setState(() { _count++; });\n  @override\n  Widget build(BuildContext context) {\n    return CounterProvider(\n      count: _count,\n      increment: _inc,\n      child: MaterialApp(home: HomePage()),\n    );\n  }\n}\n\nclass HomePage extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    final provider = CounterProvider.of(context);\n    return Scaffold(\n      appBar: AppBar(title: Text('InheritedWidget示例')),\n      body: Center(\n        child: Column(\n          mainAxisAlignment: MainAxisAlignment.center,\n          children: [\n            Text('计数: \\${provider.count}', style: TextStyle(fontSize: 24)),\n            ElevatedButton(onPressed: provider.increment, child: Text('加一')),\n          ],\n        ),\n      ),\n    );\n  }\n}", "explanation": "演示了InheritedWidget实现跨组件状态共享。"}]}}, {"name": "Provider", "trans": ["Provider"], "usage": {"syntax": "ChangeNotifierProvider(create: ...), context.watch<T>(), context.read<T>()", "description": "Provider是Flutter社区推荐的状态管理库，基于InheritedWidget封装，使用简单，适合中小型项目。", "parameters": [{"name": "ChangeNotifier", "description": "用于管理和通知状态变化的类"}, {"name": "Provider", "description": "提供状态的组件"}], "returnValue": "无返回值，通过context获取和监听状态。", "examples": [{"code": "// Provider状态管理示例\nimport 'package:flutter/material.dart';\nimport 'package:provider/provider.dart';\n\nvoid main() => runApp(MyApp());\n\nclass CounterModel extends ChangeNotifier {\n  int _count = 0;\n  int get count => _count;\n  void increment() { _count++; notifyListeners(); }\n}\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return ChangeNotifierProvider(\n      create: (_) => CounterModel(),\n      child: MaterialApp(home: HomePage()),\n    );\n  }\n}\n\nclass HomePage extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    final counter = context.watch<CounterModel>();\n    return Scaffold(\n      appBar: AppBar(title: Text('Provider示例')),\n      body: Center(\n        child: Column(\n          mainAxisAlignment: MainAxisAlignment.center,\n          children: [\n            Text('计数: \\${counter.count}', style: TextStyle(fontSize: 24)),\n            ElevatedButton(onPressed: counter.increment, child: Text('加一')),\n          ],\n        ),\n      ),\n    );\n  }\n}", "explanation": "演示了Provider的基本用法，实现响应式状态管理。"}]}}, {"name": "Riverpod", "trans": ["Riverpod"], "usage": {"syntax": "Provider/StateProvider/ConsumerWidget等，推荐使用hooks_riverpod。", "description": "Riverpod是Provider作者推出的新一代状态管理库，支持全局、局部、异步等多种场景，类型安全，易于测试。", "parameters": [{"name": "Provider", "description": "定义和提供状态"}, {"name": "ConsumerWidget", "description": "消费状态的组件"}], "returnValue": "无返回值，通过ref获取和监听状态。", "examples": [{"code": "// Riverpod状态管理示例\nimport 'package:flutter/material.dart';\nimport 'package:flutter_riverpod/flutter_riverpod.dart';\n\nfinal counterProvider = StateProvider<int>((ref) => 0);\n\nvoid main() => runApp(ProviderScope(child: MyApp()));\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(home: HomePage());\n  }\n}\n\nclass HomePage extends ConsumerWidget {\n  @override\n  Widget build(BuildContext context, WidgetRef ref) {\n    final count = ref.watch(counterProvider);\n    return Scaffold(\n      appBar: AppBar(title: Text('Riverpod示例')),\n      body: Center(\n        child: Column(\n          mainAxisAlignment: MainAxisAlignment.center,\n          children: [\n            Text('计数: \\${count}', style: TextStyle(fontSize: 24)),\n            ElevatedButton(onPressed: () => ref.read(counterProvider.notifier).state++, child: Text('加一')),\n          ],\n        ),\n      ),\n    );\n  }\n}\n", "explanation": "演示了Riverpod的基本用法，推荐新项目使用。"}]}}, {"name": "Bloc/Cubit", "trans": ["Bloc/Cubit"], "usage": {"syntax": "BlocProvider、BlocBuilder、<PERSON><PERSON>t等，推荐flutter_bloc库。", "description": "Bloc是Flutter社区流行的响应式状态管理方案，适合中大型项目，支持事件驱动和状态流转。Cubit是Bloc的简化版。", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "提供Bloc对象"}, {"name": "BlocBuilder", "description": "根据状态重建UI"}], "returnValue": "无返回值，通过Bloc/Cubit管理和监听状态。", "examples": [{"code": "// Bloc/Cubit状态管理示例\nimport 'package:flutter/material.dart';\nimport 'package:flutter_bloc/flutter_bloc.dart';\n\nclass CounterCubit extends Cubit<int> {\n  CounterCubit() : super(0);\n  void increment() => emit(state + 1);\n}\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return BlocProvider(\n      create: (_) => CounterCubit(),\n      child: MaterialApp(home: HomePage()),\n    );\n  }\n}\n\nclass HomePage extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('Bloc/Cubit示例')),\n      body: Center(\n        child: Column(\n          mainAxisAlignment: MainAxisAlignment.center,\n          children: [\n            BlocBuilder<CounterCubit, int>(\n              builder: (context, count) => Text('计数: \\${count}', style: TextStyle(fontSize: 24)),\n            ),\n            ElevatedButton(onPressed: () => context.read<CounterCubit>().increment(), child: Text('加一')),\n          ],\n        ),\n      ),\n    );\n  }\n}", "explanation": "演示了Bloc/Cubit的基本用法，适合复杂状态管理。"}]}}, {"name": "GetX", "trans": ["GetX"], "usage": {"syntax": "Obx、Get.put、Get.find等，推荐get包。", "description": "GetX是集成度很高的Flutter状态管理、路由和依赖注入方案，语法简洁，易于上手。", "parameters": [{"name": "Obx", "description": "响应式UI组件"}, {"name": "Get.put", "description": "注册控制器"}], "returnValue": "无返回值，通过GetX管理和监听状态。", "examples": [{"code": "// GetX状态管理示例\nimport 'package:flutter/material.dart';\nimport 'package:get/get.dart';\n\nclass CounterController extends GetxController {\n  var count = 0.obs;\n  void increment() => count++;\n}\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return GetMaterialApp(home: HomePage());\n  }\n}\n\nclass HomePage extends StatelessWidget {\n  final CounterController c = Get.put(CounterController());\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('GetX示例')),\n      body: Center(\n        child: Column(\n          mainAxisAlignment: MainAxisAlignment.center,\n          children: [\n            Obx(() => Text('计数: \\${c.count}', style: TextStyle(fontSize: 24))),\n            ElevatedButton(onPressed: c.increment, child: Text('加一')),\n          ],\n        ),\n      ),\n    );\n  }\n}", "explanation": "演示了GetX的基本用法，适合快速开发。"}]}}, {"name": "State Management Comparison and Choice", "trans": ["状态管理对比与选择"], "usage": {"syntax": "对比setState、Provider、Riverpod、Bloc、GetX等方案的特点和适用场景。", "description": "不同状态管理方案适用于不同项目规模和复杂度，需根据实际需求选择。", "parameters": [{"name": "方案", "description": "各状态管理方式"}, {"name": "适用场景", "description": "推荐使用场景"}], "returnValue": "无返回值，供学习和参考。", "examples": [{"code": "// 状态管理方案对比\n// setState：适合简单局部状态\n// InheritedWidget：适合全局或祖先-后代通信\n// Provider：中小型项目，响应式开发\n// Riverpod：新一代推荐，类型安全，灵活\n// Bloc/Cubit：中大型项目，事件驱动，解耦强\n// GetX：快速开发，集成路由和依赖注入\n", "explanation": "简要对比了主流状态管理方案的特点和适用场景。"}]}}, {"name": "Assignment", "trans": ["作业：实现一个计数器，分别用setState、Provider和GetX三种方式实现。"], "usage": {"syntax": "无", "description": "请分别用setState、Provider和GetX实现计数器页面，体验不同状态管理方式。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 作业参考实现（setState略，Provider和GetX见上方示例）\n// 1. setState：见setState用法示例\n// 2. Provider：见Provider示例\n// 3. GetX：见GetX示例\n// 运行并对比三种方式的代码结构和体验\n", "explanation": "作业要求用三种主流方式实现计数器，体会不同状态管理方案。"}]}}]}