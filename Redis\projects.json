{"name": "Exercises and Projects", "trans": ["练习与项目"], "methods": [{"name": "Simple Cache System Implementation", "trans": ["实现简单缓存系统"], "usage": {"syntax": "通过Redis实现带TTL的缓存系统，支持读写、过期和缓存穿透保护。", "description": "基于Redis构建简单缓存系统，包含缓存读写、自动过期、穿透防护等核心功能，可作为Web应用或微服务的缓存层。", "parameters": [{"name": "SET EX/GET", "description": "存取缓存数据。"}, {"name": "互斥锁/空值缓存", "description": "防止缓存穿透。"}], "returnValue": "无返回值", "examples": [{"code": "# 简单缓存实现\n# 获取缓存\ndata = GET user:123\nif data == nil\n  # 互斥锁防穿透\n  SET lock:user:123 1 NX EX 5\n  data = queryDB()\n  # 写入缓存\n  SET user:123 data EX 300\nend\n", "explanation": "使用GET/SET实现缓存，互斥锁防止缓存穿透。"}]}}, {"name": "<PERSON><PERSON> for Atomic Operations", "trans": ["编写Lua脚本实现原子操作"], "usage": {"syntax": "通过Lua脚本实现原子性操作，如限流、库存扣减等。", "description": "利用Redis的EVAL命令执行Lua脚本，实现原子性操作，适合复杂业务逻辑的高并发场景。", "parameters": [{"name": "EVAL", "description": "执行Lua脚本。"}, {"name": "SCRIPT LOAD", "description": "加载脚本到缓存。"}], "returnValue": "无返回值", "examples": [{"code": "# 库存扣减Lua脚本\nscript = \"\\\nlocal stock = redis.call('get', KEYS[1])\n\\\nif stock and tonumber(stock) >= tonumber(ARGV[1]) then\n\\\n  redis.call('decrby', KEYS[1], ARGV[1])\n\\\n  return 1\n\\\nelse\n\\\n  return 0\n\\\nend\"\n\n# 执行脚本\nEVAL script 1 product:123:stock 1\n", "explanation": "通过Lua脚本实现原子性库存扣减。"}]}}, {"name": "Master-Slave and Sentinel Configuration", "trans": ["配置主从复制与Sentinel"], "usage": {"syntax": "配置Redis主从复制和Sentinel高可用，实现数据同步和故障转移。", "description": "通过搭建主从复制和Sentinel集群，实现Redis的高可用，保障数据安全和服务稳定。", "parameters": [{"name": "replicaof", "description": "配置从节点。"}, {"name": "sentinel monitor", "description": "配置哨兵监控。"}], "returnValue": "无返回值", "examples": [{"code": "# 从节点配置\nreplicaof 127.0.0.1 6379\n\n# sentinel.conf配置\nsentinel monitor mymaster 127.0.0.1 6379 2\nsentinel down-after-milliseconds mymaster 5000\n", "explanation": "配置主从复制和Sentinel，实现高可用。"}]}}, {"name": "Persistence and Backup Recovery Drill", "trans": ["持久化与备份恢复演练"], "usage": {"syntax": "配置RDB/AOF持久化，模拟故障恢复，实现数据安全。", "description": "通过配置RDB/AOF持久化，备份文件，模拟故障恢复，确保数据安全和业务连续性。", "parameters": [{"name": "save", "description": "RDB持久化配置。"}, {"name": "appendonly", "description": "AOF持久化配置。"}], "returnValue": "无返回值", "examples": [{"code": "# 持久化配置\nsave 900 1\nappendonly yes\n\n# 备份与恢复演练\n1. 备份RDB/AOF文件\n2. 模拟Redis崩溃\n3. 使用备份文件恢复数据\n", "explanation": "配置持久化，进行备份恢复演练，保障数据安全。"}]}}, {"name": "Performance Analysis and Optimization Practice", "trans": ["性能分析与优化实践"], "usage": {"syntax": "通过监控工具和优化配置，提升Redis性能。", "description": "利用监控工具分析Redis性能瓶颈，通过配置优化、数据结构选择等手段提升性能。", "parameters": [{"name": "redis-cli --stat", "description": "性能统计。"}, {"name": "maxmemory/io-threads", "description": "内存和IO优化。"}], "returnValue": "无返回值", "examples": [{"code": "# 性能分析\nredis-cli --stat\nINFO memory\nSLOWLOG get\n\n# 优化配置\nmaxmemory 2gb\nmaxmemory-policy allkeys-lru\nio-threads 4\n", "explanation": "通过监控和配置优化，提升Redis性能。"}]}}, {"name": "Security Hardening and Access Control", "trans": ["安全加固与访问控制"], "usage": {"syntax": "通过密码、ACL、网络隔离等手段，提升Redis安全性。", "description": "通过配置密码、ACL权限控制、网络隔离等手段，全方位提升Redis安全性，防止未授权访问。", "parameters": [{"name": "requirepass", "description": "设置访问密码。"}, {"name": "ACL SETUSER", "description": "设置用户权限。"}, {"name": "bind", "description": "网络隔离。"}], "returnValue": "无返回值", "examples": [{"code": "# 安全配置\nrequirepass strongPass\nACL SETUSER admin on >admin123 ~* +@all\nbind 127.0.0.1\nprotected-mode yes\n", "explanation": "通过密码、ACL和网络隔离，加固Redis安全。"}]}}, {"name": "练习与项目作业", "trans": ["作业：练习与项目"], "usage": {"syntax": "请完成以下任务：\n1. 实现带穿透防护的缓存系统。\n2. 编写库存扣减Lua脚本。\n3. 搭建主从复制和Sentinel。\n4. 进行持久化和备份恢复演练。\n5. 分析和优化Redis性能。\n6. 进行安全加固。", "description": "通过实际操作综合应用Redis知识，提升实战能力。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 作业示例\n# 1. 实现缓存系统\n# 2. 编写Lua脚本\n# 3. 配置主从和Sentinel\n# 4. 进行备份恢复演练\n# 5. 分析和优化性能\n# 6. 进行安全加固\n", "explanation": "通过综合项目掌握Redis实战技能。"}]}}, {"name": "练习与项目正确实现示例", "trans": ["正确实现卡片"], "usage": {"syntax": "各项目的标准实现与验证流程。", "description": "展示如何标准实现缓存系统、Lua脚本、主从Sentinel、持久化备份、性能优化和安全加固。", "parameters": [{"name": "缓存系统", "description": "带穿透防护的缓存实现。"}, {"name": "<PERSON>a脚本", "description": "原子操作实现。"}, {"name": "主从Sentinel", "description": "高可用配置。"}, {"name": "持久化备份", "description": "数据安全保障。"}, {"name": "性能优化", "description": "性能提升方案。"}, {"name": "安全加固", "description": "全方位安全防护。"}], "returnValue": "无返回值", "examples": [{"code": "# 标准实现流程\n1. 缓存系统：GET/SET+互斥锁\n2. <PERSON><PERSON>脚本：原子性库存扣减\n3. 主从Sentinel：一主二从三哨兵\n4. 持久化备份：RDB+AOF混合\n5. 性能优化：内存/IO/网络优化\n6. 安全加固：密码+ACL+网络隔离\n", "explanation": "标准项目实现流程，全面应用Redis技术。"}]}}]}