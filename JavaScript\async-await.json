{"name": "Async/Await", "trans": ["异步/等待"], "methods": [{"name": "Basic Usage", "trans": ["基本用法"], "usage": {"syntax": "async function name() {\n  const result = await promise;\n  return value;\n}", "description": "async/await是ES2017引入的语法糖，使异步代码看起来像同步代码。async函数总是返回Promise，await暂停执行直到Promise解决。", "parameters": [{"name": "async", "description": "声明异步函数的关键字。"}, {"name": "await", "description": "等待Promise解决的关键字，只能在async函数内使用。"}], "returnValue": "async函数返回Promise对象，包装函数返回值。", "examples": [{"code": "// 基本用法\nasync function fetchUserData() {\n  const response = await fetch('https://api.example.com/user');\n  const userData = await response.json();\n  return userData;\n}\n\n// 调用异步函数\nfetchUserData().then(data => console.log(data));", "explanation": "async函数声明，使用await等待Promise解决，逐行执行，看起来像同步代码。"}, {"code": "// 与Promise对比\n// Promise方式\nfunction fetchUserDataPromise() {\n  return fetch('https://api.example.com/user')\n    .then(response => response.json());\n}\n\n// async/await方式\nasync function fetchUserDataAsync() {\n  const response = await fetch('https://api.example.com/user');\n  return await response.json();\n}", "explanation": "对比Promise链与async/await，后者代码更清晰易读。"}]}}, {"name": "Erro<PERSON>", "trans": ["错误捕获"], "usage": {"syntax": "async function name() {\n  try {\n    const result = await promise;\n  } catch (error) {\n    // 处理错误\n  }\n}", "description": "使用try/catch捕获await表达式中的错误，比Promise的catch更直观。也可以使用Promise的catch方法捕获整个async函数的错误。", "parameters": [{"name": "try/catch", "description": "捕获await表达式中的错误。"}], "returnValue": "根据函数实现返回值或错误。", "examples": [{"code": "// try/catch捕获错误\nasync function fetchData() {\n  try {\n    const response = await fetch('https://api.example.com/data');\n    if (!response.ok) throw new Error('请求失败');\n    return await response.json();\n  } catch (error) {\n    console.error('获取数据错误:', error);\n    return { error: true, message: error.message };\n  }\n}", "explanation": "使用try/catch捕获await表达式中的错误，提供错误处理逻辑。"}, {"code": "// Promise.catch捕获错误\nasync function fetchData() {\n  const response = await fetch('https://api.example.com/data');\n  if (!response.ok) throw new Error('请求失败');\n  return await response.json();\n}\n\nfetchData()\n  .then(data => console.log(data))\n  .catch(error => console.error('错误:', error));", "explanation": "使用Promise的catch方法捕获整个async函数的错误。"}]}}, {"name": "Concurrency", "trans": ["并发"], "usage": {"syntax": "// 并行执行\nconst [result1, result2] = await Promise.all([promise1, promise2]);\n\n// 竞态\nconst result = await Promise.race([promise1, promise2]);", "description": "使用Promise.all并行执行多个Promise，提高效率。使用Promise.race实现竞态，返回最先解决的Promise结果。", "parameters": [{"name": "Promise.all", "description": "并行执行多个Promise，全部完成后返回结果数组。"}, {"name": "Promise.race", "description": "返回最先解决的Promise结果。"}], "returnValue": "Promise.all返回结果数组，Promise.race返回最先解决的Promise结果。", "examples": [{"code": "// Promise.all并行请求\nasync function fetchMultipleData() {\n  try {\n    const [users, products] = await Promise.all([\n      fetch('https://api.example.com/users').then(r => r.json()),\n      fetch('https://api.example.com/products').then(r => r.json())\n    ]);\n    return { users, products };\n  } catch (error) {\n    console.error('并行请求错误:', error);\n    return null;\n  }\n}", "explanation": "使用Promise.all并行执行多个fetch请求，提高效率。"}, {"code": "// Promise.race超时处理\nasync function fetchWithTimeout(url, timeout = 5000) {\n  const fetchPromise = fetch(url).then(r => r.json());\n  const timeoutPromise = new Promise((_, reject) => {\n    setTimeout(() => reject(new Error('请求超时')), timeout);\n  });\n  \n  return await Promise.race([fetchPromise, timeoutPromise]);\n}", "explanation": "使用Promise.race实现请求超时处理，超过指定时间自动拒绝。"}]}}, {"name": "作业：实现异步数据处理", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 使用async/await获取数据\n// 2. 实现错误处理\n// 3. 实现并发请求", "description": "通过实践async/await语法，错误处理和并发请求，掌握现代JavaScript异步编程。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 创建async函数获取用户和订单\n// 2. 使用try/catch处理错误\n// 3. 使用Promise.all并发请求", "explanation": "作业提示，需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nasync function fetchUserAndOrders(userId) {\n  try {\n    // 并发请求用户和订单\n    const [user, orders] = await Promise.all([\n      fetch(`/api/users/${userId}`).then(r => r.json()),\n      fetch(`/api/orders?userId=${userId}`).then(r => r.json())\n    ]);\n    \n    // 处理数据\n    const result = {\n      username: user.name,\n      orderCount: orders.length,\n      recentOrder: orders[0] || null\n    };\n    \n    return result;\n  } catch (error) {\n    console.error('获取用户和订单失败:', error);\n    return {\n      username: '未知',\n      orderCount: 0,\n      recentOrder: null,\n      error: error.message\n    };\n  }\n}", "explanation": "完整实现，包含async/await语法、错误处理和Promise.all并发请求。"}]}}]}