{"name": "Component Basics", "trans": ["组件基础"], "methods": [{"name": "Component Definition and Registration", "trans": ["组件定义与注册"], "usage": {"syntax": "// 局部注册\nimport MyComponent from './MyComponent.vue'\nexport default {\n  components: { MyComponent }\n}\n// 全局注册\nimport Vue from 'vue'\nimport MyComponent from './MyComponent.vue'\nVue.component('MyComponent', MyComponent)", "description": "组件可以通过对象或单文件组件（SFC）定义。注册分为局部注册和全局注册，局部注册只在当前组件可用，全局注册在所有组件可用。", "parameters": [{"name": "components", "description": "局部注册的组件对象"}, {"name": "Vue.component", "description": "全局注册方法"}], "returnValue": "无返回值，注册后可在模板中直接使用组件标签", "examples": [{"code": "// 局部注册\nimport MyButton from './MyButton.vue'\nexport default {\n  components: { MyButton }\n}\n// 模板中使用\n<MyButton />", "explanation": "通过components选项注册MyButton组件后，可在当前组件模板中直接使用。"}]}}, {"name": "Single File Component (SFC)", "trans": ["单文件组件（SFC）"], "usage": {"syntax": "<template>...</template>\n<script>...</script>\n<style scoped>...</style>", "description": "单文件组件（SFC）是Vue推荐的组件组织方式，将模板、脚本和样式写在同一个.vue文件中，结构清晰，便于维护和复用。", "parameters": [{"name": ".vue文件", "description": "包含template、script、style三部分"}], "returnValue": "导出一个Vue组件对象，可直接注册和使用", "examples": [{"code": "// MyCard.vue\n<template>\n  <div class=\"card\">{{ title }}</div>\n</template>\n<script>\nexport default {\n  props: ['title']\n}\n</script>\n<style scoped>\n.card { color: #42b983; }\n</style>", "explanation": "SFC将结构、逻辑、样式分离，推荐用于实际开发。"}]}}, {"name": "Parent-Child Communication (props)", "trans": ["父子组件通信（props）"], "usage": {"syntax": "// 父组件模板\n<MyChild :msg=\"parentMsg\" />\n// 子组件props声明\nexport default { props: ['msg'] }", "description": "父组件通过props向子组件传递数据，子组件通过props选项声明接收。props是单向数据流，只能由父传子。", "parameters": [{"name": "props", "description": "子组件声明接收的属性名"}], "returnValue": "子组件可通过this.msg访问父组件传递的数据", "examples": [{"code": "// 父组件\n<template>\n  <MyChild :msg=\"'hello'\" />\n</template>\n// 子组件\n<script>\nexport default { props: ['msg'] }\n</script>", "explanation": "父组件通过:msg传递数据，子组件通过props接收。"}]}}, {"name": "Custom Events and Event Dispatch", "trans": ["事件派发与自定义事件"], "usage": {"syntax": "// 子组件中派发事件\nthis.$emit('事件名', 参数)\n// 父组件监听事件\n<MyChild @事件名=\"方法\" />", "description": "子组件通过$emit派发自定义事件，父组件通过@事件名监听并处理，实现父子间的通信。", "parameters": [{"name": "$emit", "description": "触发自定义事件的方法"}, {"name": "@事件名", "description": "父组件监听事件的写法"}], "returnValue": "无返回值，事件触发时父组件方法被调用", "examples": [{"code": "// 子组件\nthis.$emit('change', value)\n// 父组件\n<MyChild @change=\"onChange\" />", "explanation": "子组件通过$emit派发change事件，父组件通过@change监听并处理。"}]}}, {"name": "Slots", "trans": ["插槽（slot）"], "usage": {"syntax": "<slot></slot>\n<slot name=\"header\"></slot>", "description": "插槽用于父组件向子组件传递内容。默认插槽用于传递任意内容，具名插槽可传递多个区域内容。", "parameters": [{"name": "slot", "description": "插槽标签，定义内容插入点"}, {"name": "name", "description": "具名插槽的名称"}], "returnValue": "插槽内容在子组件指定位置渲染", "examples": [{"code": "// 子组件\n<template>\n  <div><slot></slot></div>\n</template>\n// 父组件\n<MyChild>\n  <span>插入内容</span>\n</MyChild>", "explanation": "父组件通过插槽向子组件传递内容。"}]}}, {"name": "Dynamic Components", "trans": ["动态组件"], "usage": {"syntax": "<component :is=\"componentName\"></component>", "description": "通过<component :is>动态切换渲染不同的组件，常用于Tab页、弹窗等场景。", "parameters": [{"name": ":is", "description": "要渲染的组件名或组件对象"}], "returnValue": "根据is的值动态渲染对应组件", "examples": [{"code": "<component :is=\"currentTab\"></component>", "explanation": "currentTab变量决定当前渲染哪个组件。"}]}}, {"name": "Component Lifecycle", "trans": ["组件生命周期"], "usage": {"syntax": "created、mounted、updated、destroyed等生命周期钩子", "description": "Vue组件有一系列生命周期钩子函数，可在不同阶段执行初始化、数据请求、清理等操作。", "parameters": [{"name": "生命周期钩子", "description": "如created、mounted等"}], "returnValue": "无返回值，钩子函数在特定阶段自动调用", "examples": [{"code": "export default {\n  mounted() {\n    console.log('组件已挂载')\n  }\n}", "explanation": "在mounted钩子中执行初始化逻辑。"}]}}, {"name": "Scoped CSS", "trans": ["组件样式作用域"], "usage": {"syntax": "<style scoped>...</style>", "description": "通过scoped属性让样式只作用于当前组件，避免全局污染。", "parameters": [{"name": "scoped", "description": "style标签的scoped属性"}], "returnValue": "样式仅影响当前组件DOM，不影响外部元素", "examples": [{"code": "<style scoped>\n.button { color: red; }\n</style>", "explanation": "scoped样式只影响当前组件内的.button类。"}]}}, {"name": "Component Basics Assignment", "trans": ["组件基础练习"], "usage": {"syntax": "# 组件基础练习", "description": "完成以下练习，巩固组件基础知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 定义一个自定义按钮组件并注册使用。\n2. 用props实现父子通信。\n3. 用$emit实现子传父。\n4. 用slot实现内容分发。\n5. 用动态组件切换不同内容。\n6. 在生命周期钩子中打印日志。\n7. 给组件添加scoped样式。", "explanation": "通过这些练习掌握组件定义、通信、插槽、动态组件、生命周期和样式作用域。"}]}}]}