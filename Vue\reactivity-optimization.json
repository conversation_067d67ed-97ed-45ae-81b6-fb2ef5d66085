{"name": "Reactivity Optimization", "trans": ["响应式优化"], "methods": [{"name": "Reactivity Granularity", "trans": ["响应式数据粒度"], "usage": {"syntax": "const count = ref(0)\nconst state = reactive({ a: 1, b: 2 })", "description": "根据实际需求选择ref或reactive，ref适合基本类型，reactive适合对象，避免大对象整体响应式，提升性能。", "parameters": [{"name": "ref", "description": "基本类型响应式"}, {"name": "reactive", "description": "对象响应式"}], "returnValue": "更细粒度的响应式数据，减少无效依赖", "examples": [{"code": "const count = ref(0)\nconst user = reactive({ name: '', age: 0 })", "explanation": "分别用ref和reactive管理不同粒度的数据。"}]}}, {"name": "Computed and Watch Optimization", "trans": ["computed与watch优化"], "usage": {"syntax": "const double = computed(() => count.value * 2)\nwatch(() => state.a, val => { ... })", "description": "合理使用computed和watch，computed用于派生数据，watch用于副作用，避免滥用watch导致性能下降。", "parameters": [{"name": "computed", "description": "计算属性，自动依赖收集"}, {"name": "watch", "description": "侦听变化，执行副作用"}], "returnValue": "高效的派生数据和副作用管理", "examples": [{"code": "const double = computed(() => count.value * 2)\nwatch(() => state.a, val => { console.log(val) })", "explanation": "用computed派生数据，用watch处理副作用。"}]}}, {"name": "Avoid Unnecessary Rendering", "trans": ["避免不必要的渲染"], "usage": {"syntax": "<template>\n  <Child v-if=\"show\" />\n</template>", "description": "通过条件渲染、key优化、拆分组件等方式，减少无关组件的渲染和更新，提升性能。", "parameters": [{"name": "v-if/v-show", "description": "条件渲染"}, {"name": "key", "description": "唯一标识，优化diff"}], "returnValue": "只渲染必要的组件，减少性能开销", "examples": [{"code": "<Child v-if=\"show\" :key=\"id\" />", "explanation": "通过v-if和key控制渲染粒度。"}]}}, {"name": "v-once and v-memo", "trans": ["v-once/v-memo"], "usage": {"syntax": "<div v-once>静态内容</div>\n<div v-memo=\"[deps]\">内容</div>", "description": "v-once只渲染一次，适合静态内容；v-memo缓存内容，依赖变化才重新渲染，提升性能。", "parameters": [{"name": "v-once", "description": "只渲染一次的指令"}, {"name": "v-memo", "description": "依赖变化才重新渲染"}], "returnValue": "减少不必要的DOM更新", "examples": [{"code": "<div v-once>静态内容</div>\n<div v-memo=\"[count]\">依赖count的内容</div>", "explanation": "静态内容用v-once，依赖变化用v-memo。"}]}}, {"name": "Event and Data Separation", "trans": ["事件与数据分离"], "usage": {"syntax": "<Child @click=\"handleClick\" :data=\"item\" />", "description": "将事件处理和数据传递分离，避免在模板中直接操作数据，提升可维护性和性能。", "parameters": [{"name": "@事件", "description": "事件监听器"}, {"name": ":data", "description": "数据传递"}], "returnValue": "更清晰的事件与数据流", "examples": [{"code": "<Child @click=\"handleClick\" :data=\"item\" />", "explanation": "事件和数据分离，便于维护和优化。"}]}}, {"name": "Reactivity Optimization Assignment", "trans": ["响应式优化练习"], "usage": {"syntax": "# 响应式优化练习", "description": "完成以下练习，巩固响应式优化相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用ref和reactive优化数据粒度。\n2. 用computed和watch优化派生和副作用。\n3. 用v-if和key减少渲染。\n4. 用v-once/v-memo优化静态内容。\n5. 实现事件与数据分离。", "explanation": "通过这些练习掌握响应式优化技巧。"}]}}]}