# Go语言学习路线与核心内容

## 1. Go基础

### 语言简介
- Go语言发展历史
- Go语言的特点
- Go与C/C++/Java的区别
- Go的应用领域

### 开发环境搭建
- Go安装与配置
- Go Modules管理
- 编辑器与IDE选择
- 第一个Go程序
- 项目结构与编译运行

### 基本语法
- main函数结构
- 语句与分号
- 注释写法
- 标识符与关键字
- 代码风格与gofmt

## 2. 数据类型与运算符

### 基本数据类型
- int、float32/64、bool、string
- byte、rune
- 类型推断与零值
- 类型转换
- 常量定义

### 变量与常量
- 变量声明与初始化
- 短变量声明
- 作用域与生命周期

### 运算符
- 算术运算符
- 关系运算符
- 逻辑运算符
- 位运算符
- 赋值运算符
- 其他运算符（&、*、<-等）

## 3. 流程控制

### 条件语句
- if/else语句
- switch语句
- 类型switch
- 嵌套条件判断

### 循环语句
- for循环
- range遍历
- break与continue
- 循环嵌套

### 跳转语句
- goto语句
- return语句

## 4. 函数

### 函数定义与调用
- 函数的基本结构
- 参数与返回值
- 多返回值
- 命名返回值
- 可变参数
- 匿名函数与闭包
- 递归函数

### 参数传递方式
- 值传递
- 指针传递

## 5. 复合数据类型

### 数组与切片
- 数组定义与初始化
- 切片的用法
- 切片扩容与底层原理
- 多维数组与切片

### map与集合
- map定义与操作
- map遍历与删除
- map的零值与初始化

### 字符串与字节处理
- 字符串不可变性
- 字符串与[]byte互转
- 字符串常用操作

## 6. 包与模块

### 包管理
- 标准库与第三方包
- import与包别名
- 包的初始化与可见性

### Go Modules
- go.mod与依赖管理
- 版本控制与代理
- 私有仓库配置

## 7. 面向对象编程

### 结构体
- 结构体定义与初始化
- 结构体方法
- 结构体嵌套与组合
- 匿名字段

### 接口
- 接口定义与实现
- 类型断言
- 空接口与泛型（Go1.18+）

### 方法与继承
- 方法定义与接收者
- 组合与多态
- 内嵌与扩展

## 8. 并发编程

### Goroutine
- 启动与调度
- 并发与并行

### Channel
- Channel定义与用法
- 无缓冲与有缓冲Channel
- select多路复用
- Channel关闭与遍历

### 并发安全
- 互斥锁sync.Mutex
- 读写锁sync.RWMutex
- 原子操作与sync/atomic
- WaitGroup与Once

## 9. 错误处理

### 错误类型
- error接口
- 自定义错误类型
- errors包与fmt.Errorf

### 错误处理模式
- 显式错误返回
- panic与recover
- defer用法

## 10. 文件与输入输出

### 文件操作
- 文件读写（os、io、bufio包）
- 路径与目录操作
- 文件权限与错误处理

### 标准输入输出
- fmt包输入输出
- bufio与Scanner
- 格式化输出

## 11. 测试

### 单元测试
- testing包用法
- 表驱动测试
- 基准测试Benchmark
- 覆盖率统计

### Mock与集成测试
- 测试用例组织
- 临时文件与目录
- 外部依赖Mock

## 12. 调试与常见错误

### 编译错误与警告
- 语法错误
- 类型不匹配
- nil指针异常
- 切片越界

### 调试技巧
- fmt调试输出
- GDB与Delve调试
- 日志与panic追踪

## 13. 项目实战与练习

### 实战案例
- 简单Web服务器实现
- 并发爬虫
- 文件批量处理工具
- RESTful API服务
- 数据结构（链表、栈、队列、map）实现

### 练习与项目
- 编写九九乘法表
- 实现冒泡排序与二分查找
- 并发下载器
- 自定义错误类型与处理
- Channel并发通信练习
- 单元测试与基准测试 