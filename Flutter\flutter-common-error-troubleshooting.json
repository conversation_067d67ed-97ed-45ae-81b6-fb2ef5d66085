{"name": "Common Error Troubleshooting", "trans": ["常见错误排查"], "methods": [{"name": "Build and Run Errors", "trans": ["编译与运行错误"], "usage": {"syntax": "flutter run / flutter build", "description": "编译与运行错误通常发生在项目构建或启动阶段，常见原因包括依赖未安装、代码语法错误、配置文件缺失等。", "parameters": [{"name": "依赖", "description": "pubspec.yaml中声明的包"}, {"name": "main.dart", "description": "应用入口文件"}, {"name": "配置文件", "description": "如AndroidManifest.xml、Info.plist等"}], "returnValue": "无返回值", "examples": [{"code": "// 运行Flutter项目\nflutter run\n// 构建release包\nflutter build apk\n// 常见错误示例：\n// Error: Cannot find 'main.dart'.\n// 解决：检查lib目录下是否有main.dart文件。\n// Error: pub get failed\n// 解决：运行 flutter pub get 安装依赖。", "explanation": "演示如何运行和构建Flutter项目，并给出常见编译与运行错误的排查方法。"}]}}, {"name": "Widget Rendering Exception", "trans": ["Widget渲染异常"], "usage": {"syntax": "Widget树构建与渲染", "description": "Widget渲染异常通常由布局嵌套错误、数据为空、类型不匹配等引起。常见报错有红屏、黄色警告等。", "parameters": [{"name": "Widget树", "description": "Flutter的UI组件树"}, {"name": "数据源", "description": "传递给Widget的数据"}, {"name": "布局约束", "description": "如Expanded、Flexible等的使用"}], "returnValue": "无返回值", "examples": [{"code": "// 常见渲染异常示例\n// 1. 数据为null导致异常\nText(user.name) // user为null时报错\n// 解决：加判空\nText(user?.name ?? '默认名')\n\n// 2. 布局嵌套错误\nExpanded(\n  child: ListView(\n    children: [...],\n  ),\n)\n// 解决：用Flexible或Container包裹，避免Expanded嵌套冲突", "explanation": "展示Widget渲染常见异常及其修复方法，如判空、调整布局嵌套等。"}]}}, {"name": "Network and Data Errors", "trans": ["网络与数据错误"], "usage": {"syntax": "http请求、json解析", "description": "网络与数据错误常见于接口请求失败、数据解析异常、无网络连接等场景。", "parameters": [{"name": "http请求", "description": "如Dio、http等库"}, {"name": "json解析", "description": "数据格式与模型不匹配"}, {"name": "网络状态", "description": "设备是否联网"}], "returnValue": "无返回值", "examples": [{"code": "// 网络请求异常示例\ntry {\n  final response = await http.get(Uri.parse('https://api.example.com/data'));\n  if (response.statusCode == 200) {\n    final data = jsonDecode(response.body);\n    // 处理数据\n  } else {\n    print('服务器错误: \\${response.statusCode}');\n  }\n} catch (e) {\n  print('网络异常: \\${e}');\n}", "explanation": "演示如何捕获网络请求和数据解析异常，避免程序崩溃。"}]}}, {"name": "State Synchronization Issues", "trans": ["状态同步问题"], "usage": {"syntax": "setState、Provider、Bloc等状态管理", "description": "状态同步问题常见于UI未及时刷新、数据未正确传递、异步操作未完成等。", "parameters": [{"name": "setState", "description": "手动刷新UI"}, {"name": "状态管理", "description": "如Provider、Bloc等"}, {"name": "异步操作", "description": "如Future、Stream"}], "returnValue": "无返回值", "examples": [{"code": "// 状态未同步示例\nint count = 0;\nElevatedButton(\n  onPressed: () {\n    count++;\n    // 忘记setState，UI不会刷新\n  },\n  child: Text('加1'),\n)\n// 正确做法：\nElevatedButton(\n  onPressed: () {\n    setState(() {\n      count++;\n    });\n  },\n  child: Text('加1'),\n)", "explanation": "演示状态未同步导致UI不刷新的问题，以及如何用setState等方法解决。"}]}}, {"name": "Assignment", "trans": ["作业：常见错误排查练习"], "usage": {"syntax": "无", "description": "请模拟一个Flutter项目，分别制造编译错误、渲染异常、网络错误和状态同步问题，并尝试修复它们。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 作业参考实现\n// 1. 故意写错变量名，观察编译报错\n// 2. Widget中传递null，观察渲染异常\n// 3. 网络请求写错URL，观察网络错误\n// 4. 忘记setState，观察UI不同步\n// 然后逐一修复这些问题，并写下你的排查思路。", "explanation": "通过动手制造和修复常见错误，掌握Flutter项目排查和调试能力。"}]}}]}