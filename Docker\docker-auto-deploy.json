{"name": "Automated Deployment", "trans": ["自动化部署"], "methods": [{"name": "Container Automated Deployment Flow", "trans": ["容器自动化部署流程"], "usage": {"syntax": "# 典型流程\n1. 构建镜像\n2. 推送镜像\n3. 拉取镜像\n4. 启动/更新容器", "description": "自动化部署通常包括镜像构建、推送、拉取和容器启动等步骤，可结合CI/CD工具实现全流程自动化。", "parameters": [{"name": "构建镜像", "description": "docker build命令。"}, {"name": "推送镜像", "description": "docker push命令。"}, {"name": "拉取镜像", "description": "docker pull命令。"}, {"name": "启动/更新容器", "description": "docker run/docker-compose up等。"}], "returnValue": "无返回值，完成自动化部署。", "examples": [{"code": "docker build -t myapp:1.0 .\ndocker push myapp:1.0\ndocker pull myapp:1.0\ndocker run -d --name myapp myapp:1.0", "explanation": "从构建到部署的自动化流程。"}]}}, {"name": "Blue-Green & Rolling Update", "trans": ["蓝绿部署与滚动更新"], "usage": {"syntax": "# 蓝绿部署\ndocker-compose -f docker-compose.blue.yml up -d\ndocker-compose -f docker-compose.green.yml up -d\n# 滚动更新\ndocker service update --image myapp:v2 myservice", "description": "蓝绿部署通过两套环境无缝切换，滚动更新逐步替换实例，保障服务可用性。", "parameters": [{"name": "docker-compose", "description": "多环境切换。"}, {"name": "docker service update", "description": "Swarm滚动更新服务。"}], "returnValue": "无返回值，平滑升级与回滚。", "examples": [{"code": "docker-compose -f docker-compose.blue.yml up -d\ndocker-compose -f docker-compose.green.yml up -d", "explanation": "蓝绿部署切换生产环境。"}, {"code": "docker service update --image myapp:v2 myservice", "explanation": "Swarm集群中滚动更新服务镜像。"}]}}, {"name": "Rollback Strategy", "trans": ["回滚策略"], "usage": {"syntax": "# 镜像回滚\ndocker service update --image myapp:old myservice\n# Compose回滚\ndocker-compose -f docker-compose.old.yml up -d", "description": "通过切换旧版本镜像或配置文件，实现服务快速回滚，降低故障影响。", "parameters": [{"name": "docker service update", "description": "Swarm服务回滚。"}, {"name": "docker-compose", "description": "切换旧配置回滚。"}], "returnValue": "无返回值，服务恢复到旧版本。", "examples": [{"code": "docker service update --image myapp:old myservice", "explanation": "Swarm集群回滚到旧镜像。"}, {"code": "docker-compose -f docker-compose.old.yml up -d", "explanation": "用旧配置文件回滚Compose服务。"}]}}, {"name": "Deployment Script Writing", "trans": ["部署脚本编写"], "usage": {"syntax": "#!/bin/bash\ndocker pull myapp:latest\ndocker stop myapp || true\ndocker rm myapp || true\ndocker run -d --name myapp myapp:latest", "description": "通过Shell脚本自动化拉取、停止、删除和启动容器，提升部署效率和一致性。", "parameters": [{"name": "docker pull", "description": "拉取最新镜像。"}, {"name": "docker stop/rm", "description": "停止并删除旧容器。"}, {"name": "docker run", "description": "启动新容器。"}], "returnValue": "无返回值，自动化部署脚本。", "examples": [{"code": "#!/bin/bash\ndocker pull myapp:latest\ndocker stop myapp || true\ndocker rm myapp || true\ndocker run -d --name myapp myapp:latest", "explanation": "一键完成拉取、替换和启动容器。"}]}}, {"name": "Automated Test & Verification", "trans": ["自动化测试与验证"], "usage": {"syntax": "docker run --rm myapp:latest npm test\ndocker inspect <容器名>\ndocker logs <容器名>", "description": "通过自动化测试命令、inspect和logs等手段验证部署结果，确保服务可用。", "parameters": [{"name": "docker run --rm", "description": "运行测试后自动删除容器。"}, {"name": "docker inspect", "description": "查看容器状态。"}, {"name": "docker logs", "description": "查看容器日志。"}], "returnValue": "无返回值，自动化验证部署效果。", "examples": [{"code": "docker run --rm myapp:latest npm test\ndocker inspect myapp\ndocker logs myapp", "explanation": "自动化测试、状态检查和日志验证。"}]}}, {"name": "Assignment: 自动化部署实践", "trans": ["作业：自动化部署实践"], "usage": {"syntax": "请完成以下任务：\n1. 编写自动化部署脚本实现镜像拉取、容器替换和启动\n2. 实现蓝绿部署或滚动更新\n3. 设计回滚方案并测试\n4. 自动化测试和验证部署结果", "description": "通过实际操作掌握自动化部署流程、蓝绿/滚动更新、回滚和验证。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 1. 自动化部署脚本\n#!/bin/bash\ndocker pull myapp:latest\ndocker stop myapp || true\ndocker rm myapp || true\ndocker run -d --name myapp myapp:latest\n# 2. 蓝绿部署\ndocker-compose -f blue.yml up -d\ndocker-compose -f green.yml up -d\n# 3. 回滚\ndocker service update --image myapp:old myservice\n# 4. 自动化测试\ndocker run --rm myapp:latest npm test", "explanation": "依次完成自动化部署、蓝绿/滚动更新、回滚和验证。"}]}}]}