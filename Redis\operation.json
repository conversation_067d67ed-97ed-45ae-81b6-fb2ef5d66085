{"name": "Operation and Maintenance", "trans": ["运维管理"], "methods": [{"name": "Backup and Restore", "trans": ["备份与恢复"], "usage": {"syntax": "通过RDB/AOF文件备份与恢复数据，支持手动和自动方式。", "description": "Redis支持通过RDB快照和AOF日志进行数据备份与恢复。可定期复制持久化文件，或通过命令手动备份和恢复。", "parameters": [{"name": "RDB文件", "description": "快照备份文件。"}, {"name": "AOF文件", "description": "追加日志备份文件。"}], "returnValue": "无返回值", "examples": [{"code": "# 手动备份RDB文件\ncp dump.rdb /backup/\n# 恢复时替换RDB文件并重启Redis\n", "explanation": "通过复制RDB文件实现数据备份与恢复。"}]}}, {"name": "Logging and Monitoring", "trans": ["日志与监控"], "usage": {"syntax": "通过配置日志文件和监控工具，实时掌握Redis运行状态。", "description": "Redis支持多级日志输出和多种监控方式。可通过配置logfile、loglevel参数，结合监控工具（如Prometheus、Grafana）实现实时监控。", "parameters": [{"name": "logfile", "description": "日志文件路径。"}, {"name": "loglevel", "description": "日志级别。"}, {"name": "监控工具", "description": "如Prometheus、<PERSON>ana等。"}], "returnValue": "无返回值", "examples": [{"code": "# redis.conf配置日志\nlogfile /var/log/redis.log\nloglevel notice\n# 结合Prometheus监控\n", "explanation": "通过配置日志和监控工具掌握Redis运行状态。"}]}}, {"name": "Common Operation Commands", "trans": ["常用运维命令"], "usage": {"syntax": "通过常用命令实现日常运维管理，如INFO、MONITOR、CONFIG等。", "description": "Redis提供丰富的运维命令，支持查看状态、监控、配置修改等，便于日常管理和故障排查。", "parameters": [{"name": "INFO", "description": "查看Redis运行状态。"}, {"name": "MONITOR", "description": "实时监控所有请求。"}, {"name": "CONFIG", "description": "动态修改配置。"}], "returnValue": "无返回值", "examples": [{"code": "# 查看状态\nINFO\n# 实时监控\nMONITOR\n# 修改配置\nCONFIG set maxmemory 512mb\n", "explanation": "通过常用命令实现运维管理和故障排查。"}]}}, {"name": "Upgrade and Migration", "trans": ["升级与迁移"], "usage": {"syntax": "通过主从切换、持久化文件等方式实现平滑升级和数据迁移。", "description": "Redis支持在线升级和数据迁移。可通过主从切换、RDB/AOF文件迁移、redis-shake等工具实现平滑过渡。", "parameters": [{"name": "主从切换", "description": "通过切换主从角色实现无缝升级。"}, {"name": "redis-shake", "description": "官方推荐的数据迁移工具。"}], "returnValue": "无返回值", "examples": [{"code": "# 主从切换升级\n1. 搭建新版本Redis为从节点\n2. 数据同步完成后切换为主节点\n# 使用redis-shake迁移数据\n", "explanation": "通过主从切换和工具实现升级与迁移。"}]}}, {"name": "运维管理作业", "trans": ["作业：运维管理"], "usage": {"syntax": "请完成以下任务：\n1. 备份并恢复RDB/AOF文件。\n2. 配置日志输出和监控。\n3. 使用常用命令管理Redis。\n4. 模拟升级与迁移流程。", "description": "通过实际操作理解运维管理的关键流程和工具。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 作业示例\n# 1. 备份RDB/AOF文件并恢复\n# 2. 配置logfile和loglevel\n# 3. 使用INFO、MONITOR命令\n# 4. 搭建主从切换升级流程\n", "explanation": "通过动手实验掌握运维管理的核心技能。"}]}}, {"name": "运维管理正确实现示例", "trans": ["正确实现卡片"], "usage": {"syntax": "运维管理的标准流程与命令。", "description": "展示如何标准执行备份、监控、升级等运维操作，确保Redis安全稳定运行。", "parameters": [{"name": "RDB/AOF备份", "description": "定期备份持久化文件。"}, {"name": "INFO/MONITOR", "description": "监控和排查故障。"}, {"name": "主从切换", "description": "平滑升级与迁移。"}], "returnValue": "无返回值", "examples": [{"code": "# 标准运维流程\n1. 定期备份RDB/AOF文件\n2. 配置日志和监控工具\n3. 使用INFO、MONITOR命令监控\n4. 通过主从切换实现升级\n", "explanation": "标准运维管理流程，保证Redis安全高效运行。"}]}}]}