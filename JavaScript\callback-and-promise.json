{"name": "Callback & Promise", "trans": ["回调与Promise"], "methods": [{"name": "Callback Function & Callback Hell", "trans": ["回调函数与回调地狱"], "usage": {"syntax": "function doSomething(callback) {\n  // 执行操作...\n  callback(result);\n}", "description": "回调函数是作为参数传递给另一个函数的函数，在特定事件完成后执行。回调地狱是多层嵌套回调导致的代码难以维护的问题。", "parameters": [{"name": "callback", "description": "作为参数传入的函数。"}], "returnValue": "无固定返回值，取决于具体实现。", "examples": [{"code": "// 简单回调\nfunction fetchData(callback) {\n  setTimeout(() => {\n    callback('数据');\n  }, 1000);\n}\n\nfetchData(data => console.log(data));", "explanation": "基本回调函数用法，异步获取数据后执行回调。"}, {"code": "// 回调地狱示例\ngetUser(userId, function(user) {\n  getOrders(user.id, function(orders) {\n    getOrderDetails(orders[0].id, function(details) {\n      getPayment(details.id, function(payment) {\n        console.log(payment);\n      });\n    });\n  });\n});", "explanation": "回调地狱示例，多层嵌套导致代码难以维护。"}]}}, {"name": "Promise Chain", "trans": ["Promise链式调用"], "usage": {"syntax": "new Promise((resolve, reject) => {\n  // 异步操作\n  resolve(value); // 成功\n  // 或\n  reject(error); // 失败\n})\n.then(result => {})\n.then(result => {})\n.catch(error => {});", "description": "Promise是异步编程的解决方案，可以链式调用避免回调地狱。通过then()处理成功，catch()处理错误。", "parameters": [{"name": "resolve", "description": "Promise成功时调用的函数。"}, {"name": "reject", "description": "Promise失败时调用的函数。"}, {"name": "then", "description": "处理Promise成功结果的方法。"}, {"name": "catch", "description": "处理Promise错误的方法。"}], "returnValue": "返回新的Promise对象，支持链式调用。", "examples": [{"code": "// Promise基本用法\nconst p = new Promise((resolve, reject) => {\n  setTimeout(() => {\n    resolve('成功数据');\n    // 或失败: reject(new Error('失败原因'));\n  }, 1000);\n});\n\np.then(data => console.log(data))\n .catch(err => console.error(err));", "explanation": "Promise基本用法，异步操作成功调用resolve，失败调用reject。"}, {"code": "// Promise链式调用\nfetchUser(userId)\n  .then(user => fetchOrders(user.id))\n  .then(orders => fetchOrderDetails(orders[0].id))\n  .then(details => fetchPayment(details.id))\n  .then(payment => console.log(payment))\n  .catch(error => console.error(error));", "explanation": "Promise链式调用，解决回调地狱问题，代码更清晰。"}]}}, {"name": "Erro<PERSON>", "trans": ["错误处理"], "usage": {"syntax": "promise\n  .then(onFulfilled)\n  .catch(onRejected)\n  .finally(onFinally);", "description": "Promise提供了catch()方法捕获链中的错误，finally()无论成功失败都会执行。try/catch可用于同步代码和async/await。", "parameters": [{"name": "catch", "description": "捕获Promise链中的错误。"}, {"name": "finally", "description": "无论成功失败都执行的方法。"}, {"name": "try/catch", "description": "同步代码和async/await的错误处理。"}], "returnValue": "返回新的Promise对象。", "examples": [{"code": "// Promise错误处理\nfetchData()\n  .then(data => {\n    // 处理数据\n    return processData(data);\n  })\n  .catch(err => {\n    console.error('发生错误:', err);\n    return fallbackData; // 提供备选数据\n  })\n  .finally(() => {\n    console.log('操作完成，清理资源');\n  });", "explanation": "使用catch捕获链中任何位置的错误，finally无论成功失败都执行。"}, {"code": "// async/await错误处理\nasync function getData() {\n  try {\n    const user = await fetchUser(userId);\n    const orders = await fetchOrders(user.id);\n    return orders;\n  } catch (error) {\n    console.error('获取数据失败:', error);\n    return [];\n  } finally {\n    console.log('操作完成');\n  }\n}", "explanation": "使用try/catch/finally处理async/await中的错误。"}]}}, {"name": "作业：回调改造与Promise实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 将回调地狱代码改造为Promise链\n// 2. 实现完善的错误处理机制", "description": "通过将回调地狱代码改造为Promise链，并添加错误处理，掌握Promise的优势和用法。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 原回调地狱代码\ngetData(function(a) {\n  getMoreData(a, function(b) {\n    getEvenMoreData(b, function(c) {\n      // ...\n    });\n  });\n});", "explanation": "作业提示，需要将回调地狱改造为Promise链，并添加错误处理。"}, {"code": "// 正确实现示例\n// 改造为Promise\nfunction getData() {\n  return new Promise((resolve, reject) => {\n    // 异步操作\n    setTimeout(() => resolve('数据a'), 1000);\n  });\n}\n\nfunction getMoreData(a) {\n  return new Promise((resolve, reject) => {\n    // 异步操作\n    setTimeout(() => resolve('数据b来自' + a), 1000);\n  });\n}\n\n// Promise链\ngetData()\n  .then(a => getMoreData(a))\n  .then(b => console.log(b))\n  .catch(err => console.error('错误:', err))\n  .finally(() => console.log('完成'));", "explanation": "将回调地狱改造为Promise链的正确实现，包含错误处理和finally。"}]}}]}