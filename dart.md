# Dart语言学习路线与核心内容

## 1. Dart基础      //已完成

### 语言简介
- Dart语言发展历史
- Dart的特点
- Dart与JavaScript/Java/Go的区别
- Dart的应用领域

### 开发环境搭建
- Dart SDK安装
- 编辑器与IDE选择
- 第一个Dart程序
- 项目结构与运行

### 基本语法
- main函数结构
- 变量与常量
- 数据类型
- 注释写法
- 标识符与关键字
- 代码风格与格式化

## 2. 数据类型与运算符      //已完成

### 基本数据类型
- int、double、num
- String
- bool
- dynamic与var
- 类型推断与转换
- 常量定义（const、final）

### 运算符
- 算术运算符
- 关系运算符
- 逻辑运算符
- 位运算符
- 赋值运算符
- 其他运算符（条件、??、is/as等）

## 3. 流程控制      //已完成

### 条件语句
- if/else语句
- switch/case语句
- 嵌套条件判断

### 循环语句
- for循环
- while循环
- do...while循环
- for-in与forEach
- break与continue
- 循环嵌套

### 跳转语句
- return语句
- throw语句

## 4. 函数      //已完成

### 函数定义与调用
- 函数的基本结构
- 参数与返回值
- 可选参数与默认值
- 命名参数与位置参数
- 匿名函数与箭头函数
- 闭包
- 递归函数

## 5. 面向对象编程  //已完成

### 类与对象
- 类的定义与实例化
- 构造函数与工厂构造
- 静态成员
- this关键字

### 继承与多态
- 继承与super
- 抽象类与接口
- mixin与with关键字
- 重写与多态

### 运算符重载
- 常见运算符重载

## 6. 集合类型

### List
- List定义与初始化
- List常用操作
- List遍历与转换

### Set
- Set定义与操作
- Set去重与集合运算

### Map
- Map定义与操作
- Map遍历与转换

## 7. 异步编程

### Future与async/await
- Future基本用法
- async/await语法
- 异步异常处理

### Stream
- Stream定义与监听
- 单订阅与广播流
- Stream常用操作

## 8. 异常处理

### 异常机制
- try/catch/finally
- throw与自定义异常
- 异步异常捕获

## 9. 文件与输入输出

### 文件操作
- 文件读写（dart:io）
- 路径与目录操作
- 文件异常处理

### 控制台输入输出
- print输出
- stdin输入

## 10. 调试与常见错误

### 编译错误与警告
- 语法错误
- 类型不匹配
- 空引用异常
- 越界与未初始化

### 调试技巧
- print调试
- IDE断点与单步执行
- 日志与异常追踪

## 11. 项目实战与练习

### 实战案例
- 简单计算器实现
- 学生成绩管理系统
- 文件批量处理工具
- 字符串查找与替换
- 数据结构（链表、栈、队列、map）实现

### 练习与项目
- 编写九九乘法表
- 实现冒泡排序与二分查找
- 自定义字符串函数
- 文件复制工具
- 异步文件下载器
- 单元测试与异常处理实践 