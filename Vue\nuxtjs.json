{"name": "Nuxt.js", "trans": ["Nuxt.js"], "methods": [{"name": "Server-Side Rendering (SSR)", "trans": ["服务端渲染(SSR)"], "usage": {"syntax": "export default defineNuxtConfig({ ssr: true })", "description": "通过配置ssr: true，启用Nuxt的服务端渲染，提升首屏速度和SEO。", "parameters": [{"name": "ssr", "description": "是否启用服务端渲染"}], "returnValue": "启用SSR的Nuxt应用，页面由服务端渲染输出HTML", "examples": [{"code": "export default defineNuxtConfig({ ssr: true })", "explanation": "配置Nuxt项目为服务端渲染模式。"}]}}, {"name": "Static Site Generation (SSG)", "trans": ["静态站点生成(SSG)"], "usage": {"syntax": "npx nuxi generate", "description": "通过nuxi generate命令，将所有页面预渲染为静态HTML，适合内容型网站和SEO优化。", "parameters": [{"name": "nuxi generate", "description": "生成静态站点的命令"}], "returnValue": "生成的静态HTML文件，可直接部署到静态服务器", "examples": [{"code": "npx nuxi generate", "explanation": "一键生成静态站点。"}]}}, {"name": "Automatic Routing", "trans": ["路由自动生成"], "usage": {"syntax": "// pages目录下创建.vue文件自动生成路由", "description": "Nuxt根据pages目录结构自动生成路由，无需手动配置，提升开发效率。", "parameters": [{"name": "pages目录", "description": "存放页面组件的目录"}], "returnValue": "自动生成的路由表", "examples": [{"code": "// 新建pages/about.vue，自动生成/about路由", "explanation": "无需手写路由配置，自动路由生效。"}]}}, {"name": "API Integration", "trans": ["API集成"], "usage": {"syntax": "const data = await $fetch('/api/user')", "description": "通过$fetch等方法集成API，支持服务端和客户端请求，适合数据驱动型页面。", "parameters": [{"name": "$fetch", "description": "Nuxt内置的API请求方法"}], "returnValue": "API返回的数据对象", "examples": [{"code": "const data = await $fetch('/api/user')", "explanation": "在页面或服务端获取用户数据。"}]}}, {"name": "Layouts and Pages", "trans": ["布局与页面"], "usage": {"syntax": "// layouts/default.vue定义全局布局\n// pages目录下为页面组件", "description": "通过layouts目录定义全局或自定义布局，pages目录下为具体页面，支持灵活页面结构。", "parameters": [{"name": "layouts目录", "description": "存放布局组件的目录"}, {"name": "pages目录", "description": "存放页面组件的目录"}], "returnValue": "灵活的页面与布局结构", "examples": [{"code": "// layouts/default.vue定义全局布局，pages/index.vue为首页页面", "explanation": "实现全局布局和页面内容分离。"}]}}, {"name": "Plugin System", "trans": ["插件机制"], "usage": {"syntax": "// plugins/myPlugin.ts\nexport default defineNuxtPlugin(() => { ... })", "description": "通过plugins目录和defineNuxtPlugin方法扩展全局功能，如注入全局方法、第三方库等。", "parameters": [{"name": "plugins目录", "description": "存放插件文件的目录"}, {"name": "defineNuxtPlugin", "description": "定义Nuxt插件的方法"}], "returnValue": "全局可用的插件功能", "examples": [{"code": "// plugins/axios.ts\nexport default defineNuxtPlugin(() => { ... })", "explanation": "通过插件机制扩展全局能力。"}]}}, {"name": "Performance Optimization", "trans": ["性能优化"], "usage": {"syntax": "export default defineNuxtConfig({\n  experimental: { payloadExtraction: true }\n})", "description": "通过配置payload提取、图片优化、懒加载等手段提升Nuxt应用性能。", "parameters": [{"name": "payloadExtraction", "description": "开启payload提取优化"}], "returnValue": "优化后的高性能Nuxt应用", "examples": [{"code": "export default defineNuxtConfig({\n  experimental: { payloadExtraction: true }\n})", "explanation": "开启payload提取，减少首屏体积。"}]}}, {"name": "Nuxt.js Assignment", "trans": ["Nuxt.js练习"], "usage": {"syntax": "# Nuxt.js练习", "description": "完成以下练习，巩固Nuxt.js相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 配置SSR和SSG模式。\n2. 新建页面体验路由自动生成。\n3. 用$fetch集成API。\n4. 实现自定义布局和插件。\n5. 配置性能优化选项。", "explanation": "通过这些练习掌握Nuxt.js核心用法。"}]}}]}