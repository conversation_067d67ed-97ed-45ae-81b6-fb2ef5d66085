{"name": "Framer Motion/React Spring", "trans": ["Framer Motion/React Spring"], "methods": [{"name": "Animation Basics", "trans": ["动画基础"], "usage": {"syntax": "import { motion } from 'framer-motion';\n<motion.div animate={{ x: 100 }} />", "description": "通过motion组件或useSpring/useTransition实现基础动画，支持属性过渡和物理动画。", "parameters": [{"name": "motion", "description": "动画组件工厂。"}, {"name": "animate", "description": "动画目标属性。"}], "returnValue": "返回带动画的React组件。", "examples": [{"code": "import { motion } from 'framer-motion';\nfunction Box() { return <motion.div animate={{ x: 100 }}>移动</motion.div>; }", "explanation": "用Framer Motion实现基础移动动画。"}]}}, {"name": "Transition Animations", "trans": ["转场动画"], "usage": {"syntax": "<motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }} />", "description": "通过initial、animate、exit等属性实现元素进出场转场动画。", "parameters": [{"name": "initial", "description": "初始状态。"}, {"name": "animate", "description": "动画目标状态。"}, {"name": "exit", "description": "离场状态。"}], "returnValue": "返回带转场动画的组件。", "examples": [{"code": "<motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>淡入淡出</motion.div>", "explanation": "实现元素的淡入淡出转场。"}]}}, {"name": "Gesture Interaction", "trans": ["手势交互"], "usage": {"syntax": "<motion.div drag dragConstraints={{ left: 0, right: 100 }} />", "description": "通过drag、whileTap、whileHover等属性实现拖拽、点击、悬停等手势交互。", "parameters": [{"name": "drag", "description": "启用拖拽。"}, {"name": "whileTap", "description": "点击时动画。"}, {"name": "whileHover", "description": "悬停时动画。"}], "returnValue": "返回带手势交互的组件。", "examples": [{"code": "<motion.div drag whileTap={{ scale: 0.9 }} whileHover={{ scale: 1.1 }}>拖拽和缩放</motion.div>", "explanation": "实现拖拽和点击/悬停缩放动画。"}]}}, {"name": "Page Transitions", "trans": ["页面转场"], "usage": {"syntax": "<AnimatePresence>...</AnimatePresence>", "description": "通过AnimatePresence和motion组件实现路由页面的进出场动画。", "parameters": [{"name": "AnimatePresence", "description": "管理组件进出场的动画容器。"}], "returnValue": "返回带页面转场动画的组件。", "examples": [{"code": "import { AnimatePresence, motion } from 'framer-motion';\nfunction Pages({ show }) {\n  return <AnimatePresence>{show && <motion.div exit={{ opacity: 0 }}>页面</motion.div>}</AnimatePresence>;\n}", "explanation": "用AnimatePresence实现页面转场。"}]}}, {"name": "Micro-interactions", "trans": ["微交互"], "usage": {"syntax": "<motion.button whileTap={{ scale: 0.95 }} />", "description": "通过whileTap、whileHover等实现按钮点击、悬停等微交互动画。", "parameters": [{"name": "whileTap", "description": "点击时动画。"}, {"name": "whileHover", "description": "悬停时动画。"}], "returnValue": "返回带微交互动画的组件。", "examples": [{"code": "<motion.button whileTap={{ scale: 0.95 }} whileHover={{ scale: 1.1 }}>按钮</motion.button>", "explanation": "实现按钮的点击和悬停动画。"}]}}, {"name": "Performance Optimization", "trans": ["性能优化"], "usage": {"syntax": "// 动画性能优化建议：\n// 1. 降低动画层级和数量\n// 2. 使用will-change或transform属性\n// 3. 合理拆分动画组件", "description": "关注动画渲染性能，合理拆分和优化动画，避免卡顿和掉帧。", "parameters": [], "returnValue": "无返回值，优化后提升性能。", "examples": [{"code": "// 使用transform代替top/left\n// 控制动画数量和复杂度", "explanation": "动画性能优化建议示例。"}]}}, {"name": "Accessibility Considerations", "trans": ["可访问性考虑"], "usage": {"syntax": "// 可访问性建议：\n// 1. 尊重用户系统的动画偏好\n// 2. 为动画元素添加aria属性\n// 3. 保证动画不影响可读性", "description": "动画应尊重用户的系统设置，保证无障碍体验，避免动画干扰内容可读性。", "parameters": [], "returnValue": "无返回值，提升可访问性。", "examples": [{"code": "// prefers-reduced-motion媒体查询\n// 为动画元素添加aria-hidden等属性", "explanation": "动画可访问性优化建议。"}]}}, {"name": "作业：动画库实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 实现基础动画、转场、手势、页面转场、微交互\n// 2. 优化动画性能和可访问性", "description": "通过实践动画基础、转场、手势、页面转场、微交互、性能优化和可访问性，掌握动画库的高效用法。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 用motion实现基础动画和转场\n// 2. 实现手势、页面转场、微交互和优化", "explanation": "作业提示，学生需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nimport { motion } from 'framer-motion';\nfunction Box() { return <motion.div animate={{ x: 100 }}>移动</motion.div>; }", "explanation": "动画库基础用法的正确实现示例。"}]}}]}