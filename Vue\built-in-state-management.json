{"name": "Built-in State Management", "trans": ["Vue内置状态管理"], "methods": [{"name": "Local Component State", "trans": ["组件本地状态"], "usage": {"syntax": "const count = ref(0)", "description": "每个组件可以通过ref、reactive等API管理自己的本地状态，状态仅在当前组件内可见。", "parameters": [{"name": "ref/reactive", "description": "声明响应式本地状态"}], "returnValue": "组件私有的响应式状态", "examples": [{"code": "<script setup>\nconst count = ref(0)\nfunction inc() { count.value++ }\n</script>\n<template>\n  <button @click=\"inc\">{{ count }}</button>\n</template>", "explanation": "组件内部通过ref管理本地计数状态。"}]}}, {"name": "Props Downward Passing", "trans": ["props向下传递"], "usage": {"syntax": "<Child :msg=\"message\" />", "description": "父组件通过props向子组件传递数据，实现单向数据流。", "parameters": [{"name": "props", "description": "父组件传递给子组件的数据"}], "returnValue": "子组件接收的props数据", "examples": [{"code": "// 父组件\n<Child :msg=\"message\" />\n// 子组件\nprops: ['msg']", "explanation": "父组件通过props向下传递数据。"}]}}, {"name": "Event Upward Emitting", "trans": ["事件向上传递"], "usage": {"syntax": "this.$emit('event', data)", "description": "子组件通过emit向父组件发送事件，实现数据和操作的向上传递。", "parameters": [{"name": "$emit", "description": "子组件触发的事件名和数据"}], "returnValue": "父组件监听到的事件和数据", "examples": [{"code": "// 子组件\nthis.$emit('change', value)\n// 父组件\n<Child @change=\"onChange\" />", "explanation": "子组件通过emit向父组件传递事件。"}]}}, {"name": "State Lifting", "trans": ["状态提升"], "usage": {"syntax": "// 父组件集中管理状态\nconst value = ref('')\n<Child :value=\"value\" @input=\"val => value = val\" />", "description": "当多个子组件需要共享状态时，将状态提升到最近的父组件，由父组件统一管理和分发。", "parameters": [{"name": "父组件状态", "description": "提升到父组件的共享状态"}], "returnValue": "多个子组件共享的状态", "examples": [{"code": "// 父组件\nconst value = ref('')\n<ChildA :value=\"value\" @input=\"val => value = val\" />\n<ChildB :value=\"value\" />", "explanation": "父组件集中管理value，实现子组件间状态同步。"}]}}, {"name": "Cross-component Communication", "trans": ["跨组件通信"], "usage": {"syntax": "// provide/inject 或 eventBus\nprovide('key', value)\nconst val = inject('key')", "description": "通过provide/inject、eventBus等方式实现非父子关系组件间的数据通信。", "parameters": [{"name": "provide/inject", "description": "跨层级依赖注入"}, {"name": "eventBus", "description": "全局事件通信"}], "returnValue": "实现任意组件间的数据共享和通信", "examples": [{"code": "// 祖先组件\nprovide('theme', 'dark')\n// 任意后代组件\nconst theme = inject('theme')", "explanation": "通过provide/inject实现跨组件通信。"}]}}, {"name": "Built-in State Management Assignment", "trans": ["内置状态管理练习"], "usage": {"syntax": "# 内置状态管理练习", "description": "完成以下练习，巩固Vue内置状态管理相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 在组件内管理本地状态。\n2. 用props向下传递数据。\n3. 用emit向上传递事件。\n4. 实现状态提升共享。\n5. 用provide/inject实现跨组件通信。", "explanation": "通过这些练习掌握Vue内置状态管理的各种方式。"}]}}]}