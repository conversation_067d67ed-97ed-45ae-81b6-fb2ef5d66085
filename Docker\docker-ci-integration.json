{"name": "CI Integration", "trans": ["与CI工具集成"], "methods": [{"name": "Jenkins Integration", "trans": ["<PERSON>集成Docker"], "usage": {"syntax": "pipeline {\n  agent any\n  stages {\n    stage('Build') {\n      steps {\n        sh 'docker build -t myapp:latest .'\n      }\n    }\n    stage('Push') {\n      steps {\n        withCredentials([usernamePassword(credentialsId: 'dockerhub', usernameVariable: 'USER', passwordVariable: 'PASS')]) {\n          sh 'echo $PASS | docker login -u $USER --password-stdin'\n          sh 'docker push myapp:latest'\n        }\n      }\n    }\n  }\n}", "description": "通过Jenkins流水线自动构建、推送Docker镜像，支持凭据安全管理。", "parameters": [{"name": "pipeline", "description": "Jenkins流水线脚本。"}, {"name": "withCredentials", "description": "安全注入<PERSON>er Hub凭据。"}], "returnValue": "无返回值，自动化CI流程。", "examples": [{"code": "pipeline {\n  agent any\n  stages {\n    stage('Build') {\n      steps {\n        sh 'docker build -t myapp:latest .'\n      }\n    }\n    stage('Push') {\n      steps {\n        withCredentials([usernamePassword(credentialsId: 'dockerhub', usernameVariable: 'USER', passwordVariable: 'PASS')]) {\n          sh 'echo $PASS | docker login -u $USER --password-stdin'\n          sh 'docker push myapp:latest'\n        }\n      }\n    }\n  }\n}", "explanation": "<PERSON>流水线自动构建并推送镜像到Docker Hub。"}]}}, {"name": "GitLab CI/CD Integration", "trans": ["GitLab CI/CD与Docker"], "usage": {"syntax": ".gitlab-ci.yml\nstages:\n  - build\n  - push\nbuild:\n  stage: build\n  script:\n    - docker build -t myapp:latest .\npush:\n  stage: push\n  script:\n    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY\n    - docker push myapp:latest", "description": "通过GitLab CI/CD自动构建和推送镜像，支持内置变量和私有仓库。", "parameters": [{"name": ".gitlab-ci.yml", "description": "GitLab CI配置文件。"}, {"name": "$CI_REGISTRY_USER", "description": "内置仓库用户名。"}], "returnValue": "无返回值，自动化CI/CD流程。", "examples": [{"code": ".gitlab-ci.yml\nstages:\n  - build\n  - push\nbuild:\n  stage: build\n  script:\n    - docker build -t myapp:latest .\npush:\n  stage: push\n  script:\n    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY\n    - docker push myapp:latest", "explanation": "GitLab流水线自动构建并推送镜像到注册表。"}]}}, {"name": "GitHub Actions Integration", "trans": ["GitHub Actions与Docker"], "usage": {"syntax": ".github/workflows/docker.yml\nname: CI\non: [push]\njobs:\n  build:\n    runs-on: ubuntu-latest\n    steps:\n      - uses: actions/checkout@v2\n      - name: Build Docker image\n        run: docker build -t myapp:latest .\n      - name: Login to Docker Hub\n        uses: docker/login-action@v2\n        with:\n          username: ${{ secrets.DOCKER_USER }}\n          password: ${{ secrets.DOCKER_PASS }}\n      - name: Push image\n        run: docker push myapp:latest", "description": "通过GitHub Actions自动构建、登录并推送Docker镜像，支持密钥安全管理。", "parameters": [{"name": ".github/workflows/docker.yml", "description": "GitHub Actions工作流文件。"}, {"name": "secrets", "description": "安全存储<PERSON>er Hub凭据。"}], "returnValue": "无返回值，自动化CI流程。", "examples": [{"code": ".github/workflows/docker.yml\nname: CI\non: [push]\njobs:\n  build:\n    runs-on: ubuntu-latest\n    steps:\n      - uses: actions/checkout@v2\n      - name: Build Docker image\n        run: docker build -t myapp:latest .\n      - name: Login to Docker Hub\n        uses: docker/login-action@v2\n        with:\n          username: ${{ secrets.DOCKER_USER }}\n          password: ${{ secrets.DOCKER_PASS }}\n      - name: Push image\n        run: docker push myapp:latest", "explanation": "GitHub Actions自动构建并推送镜像到Docker Hub。"}]}}, {"name": "Automated Build & Push", "trans": ["自动化构建与推送"], "usage": {"syntax": "docker build -t <镜像名>:<标签> .\ndocker push <镜像名>:<标签>", "description": "通过CI流程自动构建镜像并推送到仓库，实现持续集成与交付。", "parameters": [{"name": "docker build", "description": "构建镜像命令。"}, {"name": "docker push", "description": "推送镜像命令。"}], "returnValue": "无返回值，自动化交付镜像。", "examples": [{"code": "docker build -t myapp:1.0 .\ndocker push myapp:1.0", "explanation": "自动构建并推送myapp:1.0镜像。"}]}}, {"name": "Image Version Management", "trans": ["镜像版本管理"], "usage": {"syntax": "docker tag <镜像名>:<标签> <镜像名>:<新标签>\ndocker push <镜像名>:<新标签>", "description": "通过tag命令管理镜像版本，结合CI流程实现多版本推送和回滚。", "parameters": [{"name": "docker tag", "description": "打标签管理不同版本。"}, {"name": "docker push", "description": "推送指定版本镜像。"}], "returnValue": "无返回值，支持多版本管理和回滚。", "examples": [{"code": "docker tag myapp:latest myapp:v2.0\ndocker push myapp:v2.0", "explanation": "为镜像打v2.0标签并推送，实现版本管理。"}]}}, {"name": "Assignment: CI集成实践", "trans": ["作业：CI集成实践"], "usage": {"syntax": "请完成以下任务：\n1. 编写Jenkinsfile实现自动构建和推送\n2. 配置GitLab CI自动推送镜像\n3. 用GitHub Actions实现自动化构建\n4. 管理镜像多版本并推送\n5. 观察CI流程日志和结果", "description": "通过实际操作掌握Jenkins、GitLab、GitHub Actions与Docker集成、自动化构建与版本管理。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 1. Jenkinsfile\npipeline { ... }\n# 2. .gitlab-ci.yml\nstages: ...\n# 3. GitHub Actions\nname: CI ...\n# 4. 镜像打标签和推送\ndocker tag myapp:latest myapp:v2.0\ndocker push myapp:v2.0", "explanation": "依次完成CI集成与镜像管理的核心实践。"}]}}]}