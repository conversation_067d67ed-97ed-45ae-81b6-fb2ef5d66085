{"name": "Vue Router Basics", "trans": ["Vue Router基础"], "methods": [{"name": "Installation and Configuration", "trans": ["安装与配置"], "usage": {"syntax": "import { createRouter, createWebHistory } from 'vue-router'\nconst router = createRouter({ history: createWebHistory(), routes: [...] })\napp.use(router)", "description": "通过npm安装vue-router，创建router实例并挂载到Vue应用。", "parameters": [{"name": "createRouter", "description": "创建路由实例的方法"}, {"name": "createWebHistory", "description": "HTML5 history模式"}, {"name": "routes", "description": "路由配置数组"}], "returnValue": "router实例，挂载到Vue应用后全局可用", "examples": [{"code": "import { createRouter, createWebHistory } from 'vue-router'\nconst routes = [{ path: '/', component: Home }]\nconst router = createRouter({ history: createWebHistory(), routes })\napp.use(router)", "explanation": "创建并注册基础路由。"}]}}, {"name": "Router Mode", "trans": ["路由模式"], "usage": {"syntax": "createWebHistory() // history模式\ncreateWebHashHistory() // hash模式", "description": "Vue Router支持history和hash两种模式，history模式URL美观，hash模式兼容性好。", "parameters": [{"name": "createWebHistory", "description": "HTML5 history模式"}, {"name": "createWebHashHistory", "description": "hash模式"}], "returnValue": "不同的路由模式实例", "examples": [{"code": "const router = createRouter({ history: createWebHashHistory(), routes })", "explanation": "使用hash模式创建路由。"}]}}, {"name": "Routes and Nested Routes", "trans": ["路由表与嵌套路由"], "usage": {"syntax": "const routes = [\n  { path: '/parent', component: Parent, children: [\n    { path: 'child', component: Child }\n  ]}\n]", "description": "通过routes数组配置路由表，children属性实现嵌套路由。", "parameters": [{"name": "routes", "description": "路由配置数组"}, {"name": "children", "description": "嵌套路由配置"}], "returnValue": "完整的路由表结构", "examples": [{"code": "const routes = [\n  { path: '/parent', component: Parent, children: [\n    { path: 'child', component: Child }\n  ]}\n]", "explanation": "配置父子嵌套路由。"}]}}, {"name": "Dynamic Route", "trans": ["动态路由"], "usage": {"syntax": "{ path: '/user/:id', component: User }", "description": "通过:参数名定义动态路由，适合详情页、用户页等场景。", "parameters": [{"name": ":param", "description": "动态路由参数名"}], "returnValue": "可匹配多种参数的路由", "examples": [{"code": "{ path: '/user/:id', component: User }", "explanation": "定义用户详情页动态路由。"}]}}, {"name": "Route Params", "trans": ["路由参数"], "usage": {"syntax": "// 访问参数\n$route.params.id\n// setup中\nimport { useRoute } from 'vue-router'\nconst route = useRoute()\nroute.params.id", "description": "通过$route或useRoute获取路由参数，适合动态路由场景。", "parameters": [{"name": "$route.params", "description": "当前路由参数对象"}], "returnValue": "当前路由的参数值", "examples": [{"code": "// 选项式API\nthis.$route.params.id\n// 组合式API\nconst route = useRoute()\nroute.params.id", "explanation": "两种API下获取路由参数。"}]}}, {"name": "Programmatic Navigation", "trans": ["编程式导航"], "usage": {"syntax": "router.push('/path')\nrouter.replace('/path')", "description": "通过router.push/replace实现代码控制路由跳转，适合表单提交、按钮跳转等场景。", "parameters": [{"name": "router.push", "description": "跳转到新路由"}, {"name": "router.replace", "description": "替换当前路由"}], "returnValue": "导航到目标路由，无返回值", "examples": [{"code": "router.push('/home')", "explanation": "代码跳转到/home路由。"}]}}, {"name": "Navigation Guards", "trans": ["路由守卫"], "usage": {"syntax": "router.beforeEach((to, from, next) => { ... })", "description": "路由守卫可在路由跳转前后进行权限校验、登录检查等操作。", "parameters": [{"name": "beforeEach", "description": "全局前置守卫"}, {"name": "beforeEnter", "description": "路由独享守卫"}], "returnValue": "控制路由跳转流程", "examples": [{"code": "router.beforeEach((to, from, next) => {\n  if (!isLogin) next('/login')\n  else next()\n})", "explanation": "未登录跳转到登录页。"}]}}, {"name": "Route Meta Info", "trans": ["路由元信息"], "usage": {"syntax": "{ path: '/admin', component: Admin, meta: { requiresAuth: true } }", "description": "meta字段可为路由配置自定义元信息，常用于权限、标题等扩展。", "parameters": [{"name": "meta", "description": "自定义元信息对象"}], "returnValue": "可在守卫、组件等处访问的元信息", "examples": [{"code": "{ path: '/admin', component: Admin, meta: { requiresAuth: true } }", "explanation": "配置需要权限的路由元信息。"}]}}, {"name": "Vue Router Assignment", "trans": ["Vue Router练习"], "usage": {"syntax": "# Vue Router练习", "description": "完成以下练习，巩固Vue Router基础相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 安装并配置基础路由。\n2. 配置嵌套路由和动态路由。\n3. 获取路由参数。\n4. 实现编程式导航。\n5. 添加路由守卫和元信息。", "explanation": "通过这些练习掌握Vue Router的核心用法。"}]}}]}