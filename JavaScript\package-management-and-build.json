{"name": "Package Management and Build", "trans": ["包管理与构建"], "methods": [{"name": "npm/yarn/pnpm", "trans": ["npm/yarn/pnpm"], "usage": {"syntax": "npm install <pkg> / yarn add <pkg> / pnpm add <pkg>", "description": "npm、yarn、pnpm是主流的前端包管理工具，用于安装、升级、卸载依赖包，管理项目依赖。\n- npm：Node.js官方包管理器，最常用\n- yarn：Facebook推出，速度快、锁文件一致性好\n- pnpm：磁盘空间占用更低，依赖隔离更好", "parameters": [{"name": "<pkg>", "description": "要安装的包名"}], "returnValue": "无返回值", "examples": [{"code": "// 安装依赖包\nnpm install react\nyarn add react\npnpm add react\n\n// 全局安装\nnpm install -g typescript\n\n// 卸载依赖包\nnpm uninstall lodash\n\n// 查看已安装包\nnpm list --depth=0", "explanation": "示例展示了三种主流包管理工具的常用命令，包括安装、卸载、全局安装和查看依赖。"}]}}, {"name": "package.json Configuration", "trans": ["package.json配置"], "usage": {"syntax": "package.json文件结构与常用字段", "description": "package.json是Node.js项目的元数据文件，记录项目名称、版本、依赖、脚本等信息。常用字段：\n- name/version：项目名称和版本\n- scripts：自定义命令脚本\n- dependencies/devDependencies：生产/开发依赖\n- main/module：入口文件\n- engines：Node版本要求", "parameters": [{"name": "field", "description": "package.json中的字段名"}], "returnValue": "无返回值", "examples": [{"code": "{\n  \"name\": \"my-app\",\n  \"version\": \"1.0.0\",\n  \"scripts\": {\n    \"start\": \"node index.js\",\n    \"build\": \"webpack --mode production\"\n  },\n  \"dependencies\": {\n    \"react\": \"^18.0.0\"\n  },\n  \"devDependencies\": {\n    \"webpack\": \"^5.0.0\"\n  },\n  \"main\": \"index.js\",\n  \"engines\": {\n    \"node\": \">=16.0.0\"\n  }\n}", "explanation": "示例展示了package.json的常见结构和字段配置。"}]}}, {"name": "Module Bundling (Webpack, Vite, Rollup)", "trans": ["模块打包（Webpack、Vite、Rollup）"], "usage": {"syntax": "webpack.config.js / vite.config.js / rollup.config.js", "description": "模块打包工具将多个JS/TS/CSS等资源打包成浏览器可用的文件。\n- Webpack：功能最全，生态丰富\n- Vite：开发快，原生ESM，按需加载\n- Rollup：体积小，适合库打包", "parameters": [{"name": "config", "description": "打包工具的配置文件"}], "returnValue": "无返回值", "examples": [{"code": "// Webpack配置示例\nmodule.exports = {\n  entry: './src/index.js',\n  output: {\n    filename: 'bundle.js',\n    path: __dirname + '/dist'\n  },\n  module: {\n    rules: [\n      { test: /\\.js$/, use: 'babel-loader' }\n    ]\n  }\n};\n\n// Vite配置示例\nimport { defineConfig } from 'vite';\nexport default defineConfig({\n  root: './src',\n  build: { outDir: '../dist' }\n});\n\n// Rollup配置示例\nimport { defineConfig } from 'rollup';\nexport default defineConfig({\n  input: 'src/index.js',\n  output: { file: 'dist/bundle.js', format: 'esm' }\n});", "explanation": "示例展示了三种主流打包工具的基本配置。"}]}}, {"name": "Babel and Transpilation", "trans": ["Babel与转译"], "usage": {"syntax": "babel.config.js / npx babel src --out-dir dist", "description": "Babel是主流的JS转译工具，可将ES6+代码转为兼容旧浏览器的ES5代码。常用配置：\n- presets：预设转换规则\n- plugins：插件扩展功能\n- targets：目标环境", "parameters": [{"name": "option", "description": "Babel配置项或命令参数"}], "returnValue": "无返回值", "examples": [{"code": "// babel.config.js\nmodule.exports = {\n  presets: ['@babel/preset-env'],\n  plugins: ['@babel/plugin-transform-runtime']\n};\n\n// 命令行转译\nnpx babel src --out-dir dist", "explanation": "示例展示了Babel的配置和命令行转译用法。"}]}}, {"name": "Lint and Format", "trans": ["Lint与格式化"], "usage": {"syntax": "eslint . / prettier --write .", "description": "Lint工具用于检查代码规范和潜在错误，格式化工具用于统一代码风格。\n- ESLint：主流JS/TS代码规范检查工具\n- Prettier：自动格式化代码，提升可读性", "parameters": [{"name": "command", "description": "lint或格式化命令"}], "returnValue": "无返回值", "examples": [{"code": "// 检查并修复代码规范\nnpx eslint . --fix\n\n// 格式化所有文件\nnpx prettier --write .", "explanation": "示例展示了ESLint和Prettier的常用命令。"}]}}, {"name": "Package Management Assignment", "trans": ["包管理与构建练习"], "usage": {"syntax": "// 包管理与构建练习", "description": "完成以下练习，巩固包管理与构建相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "/*\n练习：\n1. 使用npm/yarn/pnpm初始化一个新项目\n2. 安装react和webpack依赖\n3. 配置一个简单的webpack打包脚本\n4. 使用ESLint检查并修复代码风格\n*/", "explanation": "练习要求动手实践包管理、依赖安装、打包配置和代码规范检查。"}]}}]}