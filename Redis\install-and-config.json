{"name": "Installation and Configuration", "trans": ["安装与环境配置"], "methods": [{"name": "Install Redis on Windows/Linux/Mac", "trans": ["Windows/Linux/Mac安装"], "usage": {"syntax": "# Linux\n$ sudo apt-get install redis-server\n# Mac\n$ brew install redis\n# Windows（使用WSL或第三方包）", "description": "根据操作系统选择合适的安装方式，Linux和Mac可直接包管理器安装，Windows推荐WSL或使用官方推荐的第三方包。", "parameters": [{"name": "操作系统", "description": "选择对应的安装命令"}], "returnValue": "安装成功的Redis服务端和客户端", "examples": [{"code": "# Linux安装\nsudo apt-get update\nsudo apt-get install redis-server\n# Mac安装\nbrew install redis\n# Windows推荐使用WSL安装Ubuntu后再安装redis-server", "explanation": "展示了不同操作系统下的安装命令。"}]}}, {"name": "Redis Configuration File", "trans": ["配置文件详解"], "usage": {"syntax": "/etc/redis/redis.conf 或 redis.windows.conf", "description": "Redis通过配置文件进行参数管理，常见配置包括端口、密码、持久化、最大内存、日志等。", "parameters": [{"name": "port", "description": "服务监听端口，默认6379"}, {"name": "requirepass", "description": "访问密码"}, {"name": "dir", "description": "数据文件存储目录"}, {"name": "maxmemory", "description": "最大内存限制"}], "returnValue": "生效的Redis配置参数", "examples": [{"code": "# 修改端口和密码\nport 6380\nrequirepass mypassword\ndir /var/lib/redis\nmaxmemory 256mb", "explanation": "展示了常用配置项的写法。"}]}}, {"name": "Start and Stop Redis", "trans": ["启动与关闭"], "usage": {"syntax": "$ redis-server [配置文件] # 启动\n$ redis-cli shutdown # 关闭", "description": "通过命令行启动Redis服务，指定配置文件可自定义参数。关闭可用redis-cli shutdown命令。", "parameters": [{"name": "配置文件", "description": "可选，指定自定义配置"}], "returnValue": "Redis服务的启动或关闭状态", "examples": [{"code": "$ redis-server /etc/redis/redis.conf # 启动\n$ redis-cli shutdown # 关闭", "explanation": "展示了启动和关闭Redis服务的常用命令。"}]}}, {"name": "Redis Client Tools", "trans": ["客户端工具选择"], "usage": {"syntax": "redis-cli、RedisInsight、第三方GUI工具", "description": "redis-cli为官方命令行客户端，RedisInsight为官方GUI工具，另有多种第三方可视化管理工具。", "parameters": [{"name": "redis-cli", "description": "命令行操作，适合脚本和调试"}, {"name": "RedisInsight", "description": "图形化界面，适合数据浏览和分析"}], "returnValue": "可用的Redis客户端工具", "examples": [{"code": "$ redis-cli\n# 连接本地Redis\n$ redis-cli -h 127.0.0.1 -p 6379\n# 使用RedisInsight连接并可视化管理Redis", "explanation": "展示了常用客户端工具的使用方法。"}]}}, {"name": "Practice: 安装与配置练习", "trans": ["安装与配置练习"], "usage": {"syntax": "# Redis安装与配置练习", "description": "完成以下练习，掌握Redis的安装、配置和启动。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 在本地安装Redis并启动服务。\n2. 修改配置文件，设置端口和密码。\n3. 使用redis-cli和RedisInsight连接Redis。\n4. 关闭Redis服务。", "explanation": "通过这些练习掌握Redis的安装与配置流程。"}, {"code": "# 正确实现示例\n$ sudo apt-get install redis-server\n$ redis-server /etc/redis/redis.conf\n# 修改配置文件后重启\n$ redis-cli -a mypassword\n$ redis-cli shutdown", "explanation": "展示了练习的标准实现过程。"}]}}]}