{"name": "Performance Optimization", "trans": ["性能优化"], "methods": [{"name": "Image Size Optimization", "trans": ["镜像体积优化"], "usage": {"syntax": "# 多阶段构建\nFROM node:18 AS build\n...\nFROM nginx:alpine\nCOPY --from=build ...\n# 清理无用文件\nRUN rm -rf /var/cache/apk/*", "description": "通过多阶段构建、选择精简基础镜像、清理无用文件等方式减小镜像体积，提高拉取和启动速度。", "parameters": [{"name": "多阶段构建", "description": "分离构建与运行环境，减少最终镜像体积。"}, {"name": "精简基础镜像", "description": "如alpine、scratch等。"}, {"name": "清理无用文件", "description": "构建后删除缓存和临时文件。"}], "returnValue": "无返回值，生成更小的镜像。", "examples": [{"code": "FROM node:18 AS build\nWORKDIR /app\nCOPY . .\nRUN npm install && npm run build\nFROM nginx:alpine\nCOPY --from=build /app/dist /usr/share/nginx/html", "explanation": "多阶段构建只保留最终产物，极大减小镜像体积。"}]}}, {"name": "Container Resource Allocation", "trans": ["容器资源分配"], "usage": {"syntax": "docker run --memory=512m --cpus=1 ...\ndocker update --memory=1g <容器名>", "description": "通过--memory、--cpus等参数限制和分配容器资源，防止资源争抢和性能瓶颈。", "parameters": [{"name": "--memory", "description": "限制容器可用内存。"}, {"name": "--cpus", "description": "限制容器可用CPU核数。"}, {"name": "docker update", "description": "动态调整运行中容器资源。"}], "returnValue": "无返回值，提升资源利用率和稳定性。", "examples": [{"code": "docker run --memory=512m --cpus=1 nginx\ndocker update --memory=1g myapp", "explanation": "限制和动态调整容器资源，防止单容器占用过多资源。"}]}}, {"name": "Network & Storage Optimization", "trans": ["网络与存储优化"], "usage": {"syntax": "# 网络优化\ndocker network create --driver bridge fastnet\n# 存储优化\ndocker volume create --driver local --opt type=tmpfs ...", "description": "通过自定义网络、优化驱动、选择高性能存储等手段提升容器间通信和数据读写效率。", "parameters": [{"name": "自定义网络", "description": "根据业务需求选择合适的网络驱动。"}, {"name": "高性能存储", "description": "如tmpfs、SSD等。"}, {"name": "网络隔离", "description": "提升安全性和性能。"}], "returnValue": "无返回值，提升网络和存储性能。", "examples": [{"code": "docker network create --driver bridge fastnet\ndocker volume create --driver local --opt type=tmpfs fastvol", "explanation": "自定义网络和高性能存储卷提升整体性能。"}]}}, {"name": "Build & Startup Speed Optimization", "trans": ["构建与启动速度优化"], "usage": {"syntax": "# 构建优化\n利用缓存、合并RUN指令、减少层数\n# 启动优化\nENTRYPOINT优化、精简启动命令", "description": "通过合理利用缓存、合并指令、减少镜像层数和优化启动命令，加快镜像构建和容器启动速度。", "parameters": [{"name": "缓存利用", "description": "合理拆分Dockerfile层，提升构建效率。"}, {"name": "合并RUN指令", "description": "减少镜像层数。"}, {"name": "ENTRYPOINT优化", "description": "加快容器启动。"}], "returnValue": "无返回值，提升构建和启动效率。", "examples": [{"code": "FROM node:18\nWORKDIR /app\nCOPY package.json ./\nRUN npm install\nCOPY . .\nRUN npm run build\nENTRYPOINT [\"npm\", \"start\"]", "explanation": "分层合理、利用缓存、精简启动命令提升效率。"}]}}, {"name": "Monitoring & Tuning Tools", "trans": ["监控与调优工具"], "usage": {"syntax": "docker stats\ndocker events\n第三方工具如cAdvisor、Prometheus、Grafana", "description": "通过docker stats、events及cAdvisor、Prometheus、Grafana等工具实时监控和分析容器性能，辅助调优。", "parameters": [{"name": "docker stats", "description": "实时监控容器资源使用。"}, {"name": "cAdvisor", "description": "可视化容器性能监控。"}, {"name": "Prometheus/Grafana", "description": "大规模监控与可视化。"}], "returnValue": "无返回值，辅助性能分析与调优。", "examples": [{"code": "docker stats\ncadvisor --port=8080\n# Prometheus+Grafana部署参考官方文档", "explanation": "结合多种工具实现容器性能监控与调优。"}]}}, {"name": "Assignment: 性能优化实践", "trans": ["作业：性能优化实践"], "usage": {"syntax": "请完成以下任务：\n1. 优化一个镜像体积并对比前后大小\n2. 设置容器资源限制并观察效果\n3. 配置自定义网络和高性能存储卷\n4. 优化构建和启动速度\n5. 部署并使用监控工具分析性能", "description": "通过实际操作掌握镜像、资源、网络、构建和监控等多方面的性能优化方法。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 1. 镜像体积优化\n多阶段构建...\n# 2. 资源限制\ndocker run --memory=512m ...\n# 3. 网络与存储优化\ndocker network create ...\ndocker volume create ...\n# 4. 构建与启动优化\n合并RUN指令...\n# 5. 监控工具\ncadvisor --port=8080", "explanation": "依次完成镜像、资源、网络、构建和监控的优化实践。"}]}}]}