{"name": "TypeScript and Vue", "trans": ["TypeScript与Vue"], "methods": [{"name": "Type Definition", "trans": ["类型定义"], "usage": {"syntax": "type User = { id: number; name: string }", "description": "通过type或interface定义对象、函数等类型，提升代码可读性和可维护性。", "parameters": [{"name": "type/interface", "description": "定义类型的关键字"}], "returnValue": "自定义的类型结构", "examples": [{"code": "type User = { id: number; name: string }", "explanation": "定义User类型，约束对象结构。"}]}}, {"name": "Component Type", "trans": ["组件类型"], "usage": {"syntax": "defineComponent<{ msg: string }>()", "description": "通过泛型或类型注解为Vue组件声明props、emit等类型，提升类型安全。", "parameters": [{"name": "defineComponent", "description": "定义组件的函数"}, {"name": "泛型参数", "description": "指定props等类型"}], "returnValue": "带类型约束的Vue组件", "examples": [{"code": "export default defineComponent<{ msg: string }>()", "explanation": "为组件props声明类型。"}]}}, {"name": "Props Type", "trans": ["Props类型"], "usage": {"syntax": "props: { msg: { type: String as PropType<string> } }", "description": "通过PropType为props指定类型，支持复杂对象和联合类型。", "parameters": [{"name": "PropType", "description": "Vue提供的类型辅助"}], "returnValue": "类型安全的props定义", "examples": [{"code": "props: { msg: { type: String as PropType<string> } }", "explanation": "为msg属性指定string类型。"}]}}, {"name": "Composition API Type", "trans": ["组合式API类型"], "usage": {"syntax": "const count = ref<number>(0)\nconst state = reactive<{ name: string }>({ name: '' })", "description": "为ref、reactive、computed等组合式API指定类型，提升类型推断和开发体验。", "parameters": [{"name": "ref/reactive/computed", "description": "组合式API"}, {"name": "泛型参数", "description": "指定数据类型"}], "returnValue": "类型安全的响应式数据", "examples": [{"code": "const count = ref<number>(0)\nconst state = reactive<{ name: string }>({ name: '' })", "explanation": "为ref和reactive指定类型。"}]}}, {"name": "Event Type", "trans": ["事件类型"], "usage": {"syntax": "const emit = defineEmits<{ (e: 'change', value: string): void }>()", "description": "通过泛型为defineEmits等事件声明类型，提升事件参数和返回值的类型安全。", "parameters": [{"name": "defineEmits", "description": "定义事件的组合式API"}, {"name": "泛型参数", "description": "指定事件类型"}], "returnValue": "类型安全的事件声明", "examples": [{"code": "const emit = defineEmits<{ (e: 'change', value: string): void }>()", "explanation": "为change事件声明参数类型。"}]}}, {"name": "Generic Component", "trans": ["通用组件"], "usage": {"syntax": "function useList<T>(list: T[]): T { return list[0] }", "description": "通过泛型定义通用组件或组合函数，提升代码复用性和类型灵活性。", "parameters": [{"name": "泛型T", "description": "通用类型参数"}], "returnValue": "类型灵活的通用组件或函数", "examples": [{"code": "function useList<T>(list: T[]): T { return list[0] }", "explanation": "定义通用的列表处理函数。"}]}}, {"name": "Type Assertion", "trans": ["类型断言"], "usage": {"syntax": "const el = ref(null) as Ref<HTMLInputElement | null>", "description": "通过as关键字进行类型断言，解决类型推断不准确的问题。", "parameters": [{"name": "as", "description": "类型断言关键字"}], "returnValue": "断言后的精确类型", "examples": [{"code": "const el = ref(null) as Ref<HTMLInputElement | null>", "explanation": "将ref断言为特定DOM类型。"}]}}, {"name": "TypeScript and Vue Assignment", "trans": ["TypeScript与Vue练习"], "usage": {"syntax": "# TypeScript与Vue练习", "description": "完成以下练习，巩固TypeScript与Vue相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 定义对象和组件类型。\n2. 为props和事件声明类型。\n3. 为ref/reactive指定类型。\n4. 编写通用组件和类型断言。", "explanation": "通过这些练习掌握TypeScript与Vue类型体系。"}]}}]}