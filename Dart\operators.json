{"name": "Operators", "trans": ["运算符"], "methods": [{"name": "Arithmetic Operators", "trans": ["算术运算符"], "usage": {"syntax": "// 基本算术运算符\na + b  // 加法\na - b  // 减法\na * b  // 乘法\na / b  // 除法（结果为double）\na ~/ b // 整除（结果为int）\na % b  // 取余\n\n// 自增自减运算符\n++a  // 前缀自增\na++  // 后缀自增\n--a  // 前缀自减\na--  // 后缀自减", "description": "算术运算符用于执行基本的数学运算。Dart提供以下算术运算符：1) 基本运算符：`+`（加法）、`-`（减法）、`*`（乘法）、`/`（除法，结果为double）、`~/`（整除，结果为int）、`%`（取余）；2) 自增自减运算符：`++`（自增，前缀或后缀）、`--`（自减，前缀或后缀）。前缀形式（如`++a`）先执行操作再返回值，后缀形式（如`a++`）先返回值再执行操作。", "parameters": [{"name": "+", "description": "加法运算符，将两个操作数相加"}, {"name": "-", "description": "减法运算符，从第一个操作数中减去第二个操作数"}, {"name": "*", "description": "乘法运算符，将两个操作数相乘"}, {"name": "/", "description": "除法运算符，将第一个操作数除以第二个操作数，结果为double类型"}, {"name": "~/", "description": "整除运算符，将第一个操作数除以第二个操作数，结果为int类型（向下取整）"}, {"name": "%", "description": "取余运算符，返回除法的余数"}, {"name": "++", "description": "自增运算符，将操作数的值增加1"}, {"name": "--", "description": "自减运算符，将操作数的值减少1"}], "returnValue": "根据操作数类型和运算符不同，返回相应类型的计算结果", "examples": [{"code": "void main() {\n  // 基本算术运算符\n  int a = 10;\n  int b = 3;\n  \n  // 加法\n  int sum = a + b;\n  print('a + b = $sum'); // 输出: a + b = 13\n  \n  // 减法\n  int difference = a - b;\n  print('a - b = $difference'); // 输出: a - b = 7\n  \n  // 乘法\n  int product = a * b;\n  print('a * b = $product'); // 输出: a * b = 30\n  \n  // 除法 (结果为double)\n  double quotient = a / b;\n  print('a / b = $quotient'); // 输出: a / b = 3.3333333333333335\n  \n  // 整除 (结果为int)\n  int integerDivision = a ~/ b;\n  print('a ~/ b = $integerDivision'); // 输出: a ~/ b = 3\n  \n  // 取余\n  int remainder = a % b;\n  print('a % b = $remainder'); // 输出: a % b = 1\n  \n  // 自增自减运算符\n  int x = 5;\n  int y;\n  \n  // 前缀自增 (先增加再使用)\n  y = ++x; // x增加到6，然后y = 6\n  print('++x: x = $x, y = $y'); // 输出: ++x: x = 6, y = 6\n  \n  x = 5; // 重置x\n  \n  // 后缀自增 (先使用再增加)\n  y = x++; // y = 5，然后x增加到6\n  print('x++: x = $x, y = $y'); // 输出: x++: x = 6, y = 5\n  \n  x = 5; // 重置x\n  \n  // 前缀自减 (先减少再使用)\n  y = --x; // x减少到4，然后y = 4\n  print('--x: x = $x, y = $y'); // 输出: --x: x = 4, y = 4\n  \n  x = 5; // 重置x\n  \n  // 后缀自减 (先使用再减少)\n  y = x--; // y = 5，然后x减少到4\n  print('x--: x = $x, y = $y'); // 输出: x--: x = 4, y = 5\n  \n  // 复合使用\n  int i = 1;\n  print(i); // 1\n  print(++i); // 2\n  print(i++); // 2 (打印后i变为3)\n  print(i); // 3\n  \n  // 数字类型间的算术运算\n  int intNum = 5;\n  double doubleNum = 2.5;\n  \n  // int和double混合运算结果为double\n  var result1 = intNum + doubleNum; // 7.5 (double)\n  var result2 = intNum * doubleNum; // 12.5 (double)\n  \n  print('int + double = $result1 (${result1.runtimeType})'); // int + double = 7.5 (double)\n  print('int * double = $result2 (${result2.runtimeType})'); // int * double = 12.5 (double)\n  \n  // 字符串和算术运算符\n  String str1 = 'Hello';\n  String str2 = 'Dart';\n  \n  // 字符串连接使用+运算符\n  String combined = str1 + ' ' + str2;\n  print('String连接: $combined'); // String连接: Hello Dart\n  \n  // 一元减号运算符\n  int positiveNum = 5;\n  int negativeNum = -positiveNum; // 一元减号\n  print('一元减号: $negativeNum'); // 一元减号: -5\n  \n  // 运算符优先级示例\n  int result = 2 + 3 * 4; // 乘法优先级高于加法\n  print('2 + 3 * 4 = $result'); // 2 + 3 * 4 = 14\n  \n  result = (2 + 3) * 4; // 使用括号改变优先级\n  print('(2 + 3) * 4 = $result'); // (2 + 3) * 4 = 20\n}", "explanation": "这个示例展示了Dart中算术运算符的使用。首先演示了基本算术运算符（+、-、*、/、~/、%）的用法，然后展示了自增自减运算符（++、--）的前缀和后缀形式的区别。示例还展示了不同数字类型间的运算规则（int和double混合运算结果为double）、字符串连接使用+运算符、一元减号运算符，以及运算符优先级和使用括号改变优先级的情况。通过这些示例，可以理解Dart算术运算符的基本行为和特点。"}]}}, {"name": "Relational Operators", "trans": ["关系运算符"], "usage": {"syntax": "a == b  // 等于\na != b  // 不等于\na > b   // 大于\na < b   // 小于\na >= b  // 大于等于\na <= b  // 小于等于", "description": "关系运算符用于比较两个值之间的关系，返回布尔值（true或false）。Dart提供了六种关系运算符：`==`（等于）、`!=`（不等于）、`>`（大于）、`<`（小于）、`>=`（大于等于）和`<=`（小于等于）。关系运算符常用于条件语句（如if、while）中。在Dart中，`==`运算符比较对象的值是否相等，而不是引用是否相同，这与许多其他语言不同。如果要比较引用是否为同一对象，应使用`identical()`函数。", "parameters": [{"name": "==", "description": "等于运算符，比较两个操作数的值是否相等"}, {"name": "!=", "description": "不等于运算符，比较两个操作数的值是否不相等"}, {"name": ">", "description": "大于运算符，判断左操作数是否大于右操作数"}, {"name": "<", "description": "小于运算符，判断左操作数是否小于右操作数"}, {"name": ">=", "description": "大于等于运算符，判断左操作数是否大于或等于右操作数"}, {"name": "<=", "description": "小于等于运算符，判断左操作数是否小于或等于右操作数"}], "returnValue": "布尔值（true或false），表示比较结果", "examples": [{"code": "void main() {\n  // 数字比较\n  int a = 10;\n  int b = 5;\n  \n  // 等于\n  bool isEqual = a == b;\n  print('a == b: $isEqual'); // 输出: a == b: false\n  \n  // 不等于\n  bool isNotEqual = a != b;\n  print('a != b: $isNotEqual'); // 输出: a != b: true\n  \n  // 大于\n  bool isGreater = a > b;\n  print('a > b: $isGreater'); // 输出: a > b: true\n  \n  // 小于\n  bool isLess = a < b;\n  print('a < b: $isLess'); // 输出: a < b: false\n  \n  // 大于等于\n  bool isGreaterOrEqual = a >= b;\n  print('a >= b: $isGreaterOrEqual'); // 输出: a >= b: true\n  \n  // 小于等于\n  bool isLessOrEqual = a <= b;\n  print('a <= b: $isLessOrEqual'); // 输出: a <= b: false\n  \n  // 相等性比较\n  int x = 5;\n  int y = 5;\n  double z = 5.0;\n  \n  // 不同类型数字的比较\n  print('x == z: ${x == z}'); // 输出: x == z: true (值相等)\n  \n  // 在条件语句中使用关系运算符\n  if (a > b) {\n    print('a大于b');\n  } else {\n    print('a不大于b');\n  }\n  \n  // 字符串比较\n  String str1 = 'apple';\n  String str2 = 'banana';\n  \n  print('str1 == str2: ${str1 == str2}'); // 输出: str1 == str2: false\n  print('str1 != str2: ${str1 != str2}'); // 输出: str1 != str2: true\n  \n  // 字符串按字母顺序比较\n  print('str1 < str2: ${str1 < str2}'); // 输出: str1 < str2: true (字母顺序)\n  print('str1 > str2: ${str1 > str2}'); // 输出: str1 > str2: false\n  \n  // 对象比较\n  var point1 = Point(2, 3);\n  var point2 = Point(2, 3);\n  var point3 = point1;\n  \n  // == 运算符比较值是否相等\n  print('point1 == point2: ${point1 == point2}'); // 根据Point类的==实现决定\n  \n  // identical()函数比较引用是否相同\n  print('identical(point1, point2): ${identical(point1, point2)}'); // 输出: false (不同对象)\n  print('identical(point1, point3): ${identical(point1, point3)}'); // 输出: true (同一对象)\n  \n  // 链式比较\n  int value = 7;\n  bool inRange = value >= 1 && value <= 10;\n  print('value在1到10之间: $inRange'); // 输出: value在1到10之间: true\n  \n  // 浮点数比较注意事项\n  double d1 = 0.1 + 0.2;\n  double d2 = 0.3;\n  \n  print('d1: $d1'); // 可能输出类似: d1: 0.30000000000000004\n  print('d2: $d2'); // 输出: d2: 0.3\n  print('d1 == d2: ${d1 == d2}'); // 可能输出: d1 == d2: false (浮点精度问题)\n  \n  // 处理浮点精度问题的更好方法\n  bool isApproximatelyEqual = (d1 - d2).abs() < 1e-10;\n  print('考虑精度后比较: $isApproximatelyEqual'); // 输出: 考虑精度后比较: true\n}\n\n// 自定义类，用于演示对象比较\nclass Point {\n  final int x;\n  final int y;\n  \n  Point(this.x, this.y);\n  \n  // 重写==运算符\n  @override\n  bool operator ==(Object other) {\n    if (identical(this, other)) return true;\n    return other is Point && other.x == x && other.y == y;\n  }\n  \n  // 重写hashCode\n  @override\n  int get hashCode => x.hashCode ^ y.hashCode;\n}", "explanation": "这个示例展示了Dart中关系运算符的使用。首先演示了基本的数字比较（==、!=、>、<、>=、<=），然后展示了不同类型数字的比较（int和double），以及在条件语句中使用关系运算符。示例还展示了字符串比较、对象比较（包括==运算符和identical()函数的区别）、链式比较，以及处理浮点数比较时需要注意的精度问题。通过Point类的示例，展示了如何正确重写==运算符以自定义对象比较行为。这些示例涵盖了Dart中关系运算符的主要用法和注意事项。"}]}}, {"name": "Logical Operators", "trans": ["逻辑运算符"], "usage": {"syntax": "!expr    // 逻辑非\nexpr1 && expr2  // 逻辑与\nexpr1 || expr2  // 逻辑或", "description": "逻辑运算符用于操作布尔值，返回布尔结果（true或false）。Dart提供三种主要的逻辑运算符：`!`（逻辑非，取反）、`&&`（逻辑与，两个操作数都为true时结果为true）、`||`（逻辑或，至少一个操作数为true时结果为true）。逻辑运算符常用于条件语句和复合条件表达式中。Dart中的逻辑运算符具有短路特性，即`&&`运算符在左操作数为false时不会计算右操作数，`||`运算符在左操作数为true时不会计算右操作数。", "parameters": [{"name": "!", "description": "逻辑非（NOT）运算符，取反操作数的布尔值"}, {"name": "&&", "description": "逻辑与（AND）运算符，两个操作数都为true时结果为true"}, {"name": "||", "description": "逻辑或（OR）运算符，至少一个操作数为true时结果为true"}], "returnValue": "布尔值（true或false），表示逻辑运算结果", "examples": [{"code": "void main() {\n  bool a = true;\n  bool b = false;\n  \n  // 逻辑非 (!)\n  bool notA = !a;\n  bool notB = !b;\n  \n  print('!a: $notA'); // 输出: !a: false\n  print('!b: $notB'); // 输出: !b: true\n  \n  // 逻辑与 (&&)\n  bool aAndB = a && b;\n  print('a && b: $aAndB'); // 输出: a && b: false\n  \n  // 逻辑或 (||)\n  bool aOrB = a || b;\n  print('a || b: $aOrB'); // 输出: a || b: true\n  \n  // 复合逻辑表达式\n  bool complexExpression = (a || b) && !b;\n  print('(a || b) && !b: $complexExpression'); // 输出: (a || b) && !b: true\n  \n  // 在条件语句中使用逻辑运算符\n  if (a && !b) {\n    print('a为true且b为false');\n  }\n  \n  if (a || b) {\n    print('a或b至少有一个为true');\n  }\n  \n  // 短路评估\n  bool shortCircuitAnd = evaluateWithSideEffect(false) && neverCalled();\n  print('短路与: $shortCircuitAnd'); // 输出: 短路与: false (neverCalled不会被调用)\n  \n  bool shortCircuitOr = evaluateWithSideEffect(true) || neverCalled();\n  print('短路或: $shortCircuitOr'); // 输出: 短路或: true (neverCalled不会被调用)\n  \n  // 非短路评估示例 (仅用于演示)\n  bool result1 = evaluateWithSideEffect(false);\n  bool result2 = evaluateWithSideEffect(true);\n  bool nonShortCircuit = result1 && result2; // 两个表达式都会被评估\n  \n  // 与关系运算符组合使用\n  int x = 5;\n  int y = 10;\n  int z = 15;\n  \n  bool isInRange = x > 0 && x < y && y < z;\n  print('x > 0 && x < y && y < z: $isInRange'); // 输出: x > 0 && x < y && y < z: true\n  \n  // 逻辑运算符的优先级\n  // !优先级最高，然后是&&，最后是||，可以使用括号改变优先级\n  bool precedence = !a || b && a;\n  bool withParentheses = (!a) || (b && a);\n  print('!a || b && a: $precedence'); // 输出: !a || b && a: false\n  print('(!a) || (b && a): $withParentheses'); // 输出: (!a) || (b && a): false (相同结果)\n  \n  bool differentPrecedence = !a || (b && a);\n  bool differentParentheses = (!a || b) && a;\n  print('!a || (b && a): $differentPrecedence'); // 输出: !a || (b && a): false\n  print('(!a || b) && a: $differentParentheses'); // 输出: (!a || b) && a: false (可能不同结果)\n  \n  // 非布尔操作数 (Dart要求操作数必须是布尔类型)\n  // 以下代码在Dart中会报错\n  // bool invalid = 1 && 0; // 错误: 操作数必须是bool类型\n  // bool invalid2 = \"\" || \"text\"; // 错误: 操作数必须是bool类型\n  \n  // 正确的方式是使用显式条件\n  int num1 = 1;\n  int num2 = 0;\n  bool valid = (num1 != 0) && (num2 != 0);\n  print('(num1 != 0) && (num2 != 0): $valid'); // 输出: (num1 != 0) && (num2 != 0): false\n  \n  // 逻辑运算符在条件表达式中的使用\n  String name = \"\";\n  String displayName = (name.isNotEmpty) ? name : \"Guest\";\n  print('displayName: $displayName'); // 输出: displayName: Guest\n  \n  // 使用&&进行空值检查\n  String? nullableName;\n  if (nullableName != null && nullableName.length > 5) {\n    print('Name is longer than 5 characters');\n  } else {\n    print('Name is null or not longer than 5 characters');\n  }\n}\n\n// 辅助函数，用于演示短路评估\nbool evaluateWithSideEffect(bool value) {\n  print('evaluateWithSideEffect($value)被调用');\n  return value;\n}\n\nbool neverCalled() {\n  print('neverCalled()被调用');\n  return true;\n}", "explanation": "这个示例展示了Dart中逻辑运算符的使用。首先演示了基本的逻辑运算符（!、&&、||）的用法和结果，然后展示了复合逻辑表达式和在条件语句中使用逻辑运算符。特别强调了短路评估的特性，即&&运算符在左操作数为false时不会评估右操作数，||运算符在左操作数为true时不会评估右操作数。示例还展示了逻辑运算符与关系运算符的组合使用、优先级规则（!高于&&高于||）和使用括号改变优先级的情况。通过与其他编程语言的对比，强调了Dart严格要求逻辑运算符的操作数必须是布尔类型。最后展示了逻辑运算符在条件表达式和空值检查中的常见用法。"}]}}, {"name": "Bitwise Operators", "trans": ["位运算符"], "usage": {"syntax": "a & b   // 按位与\na | b   // 按位或\na ^ b   // 按位异或\n~a      // 按位取反\na << b  // 左移\na >> b  // 右移\na >>> b // 无符号右移", "description": "位运算符用于操作二进制位。这些运算符主要用于整数类型，对二进制表示的每一位进行操作。Dart提供以下位运算符：`&`（按位与，对应位都为1时结果为1）、`|`（按位或，对应位至少有一个为1时结果为1）、`^`（按位异或，对应位不同时结果为1）、`~`（按位取反，将所有位取反）、`<<`（左移，将二进制表示向左移动指定位数）、`>>`（右移，将二进制表示向右移动指定位数，符号位保持不变）、`>>>`（无符号右移，仅Dart 2.14及以上版本支持，将0填充到最高位）。位运算符在底层编程、图形处理、加密算法和优化等场景中非常有用。", "parameters": [{"name": "&", "description": "按位与运算符，对应位都为1时结果为1，否则为0"}, {"name": "|", "description": "按位或运算符，对应位至少有一个为1时结果为1，否则为0"}, {"name": "^", "description": "按位异或运算符，对应位不同时结果为1，相同时为0"}, {"name": "~", "description": "按位取反运算符，将操作数的所有位取反（0变1，1变0）"}, {"name": "<<", "description": "左移运算符，将二进制表示向左移动指定位数，右侧补0"}, {"name": ">>", "description": "右移运算符，将二进制表示向右移动指定位数，左侧填充符号位"}, {"name": ">>>", "description": "无符号右移运算符，将二进制表示向右移动指定位数，左侧填充0"}], "returnValue": "整数，表示位运算的结果", "examples": [{"code": "void main() {\n  // 二进制表示的基本操作\n  int a = 12;  // 二进制：1100\n  int b = 5;   // 二进制：0101\n  \n  print('a (${a}) 的二进制表示: ${a.toRadixString(2).padLeft(4, '0')}');\n  print('b (${b}) 的二进制表示: ${b.toRadixString(2).padLeft(4, '0')}');\n  \n  // 按位与 (&)\n  int bitwiseAnd = a & b;\n  print('a & b = $bitwiseAnd'); // 输出: a & b = 4\n  print('(${a.toRadixString(2).padLeft(4, '0')}) & (${b.toRadixString(2).padLeft(4, '0')}) = ${bitwiseAnd.toRadixString(2).padLeft(4, '0')}');\n  // 1100 & 0101 = 0100 (4)\n  \n  // 按位或 (|)\n  int bitwiseOr = a | b;\n  print('a | b = $bitwiseOr'); // 输出: a | b = 13\n  print('(${a.toRadixString(2).padLeft(4, '0')}) | (${b.toRadixString(2).padLeft(4, '0')}) = ${bitwiseOr.toRadixString(2).padLeft(4, '0')}');\n  // 1100 | 0101 = 1101 (13)\n  \n  // 按位异或 (^)\n  int bitwiseXor = a ^ b;\n  print('a ^ b = $bitwiseXor'); // 输出: a ^ b = 9\n  print('(${a.toRadixString(2).padLeft(4, '0')}) ^ (${b.toRadixString(2).padLeft(4, '0')}) = ${bitwiseXor.toRadixString(2).padLeft(4, '0')}');\n  // 1100 ^ 0101 = 1001 (9)\n  \n  // 按位取反 (~)\n  // 在Dart中，~n = -(n+1)，这是因为Dart使用二进制补码表示整数\n  int bitwiseNot = ~a;\n  print('~a = $bitwiseNot'); // 输出: ~a = -13\n  // 对于12，按位取反后的二进制表示为11...110011（32位或64位，这里简化显示）\n  // 由于是补码表示，因此结果为-13\n  \n  // 左移 (<<)\n  int leftShift = a << 2;\n  print('a << 2 = $leftShift'); // 输出: a << 2 = 48\n  print('(${a.toRadixString(2).padLeft(4, '0')}) << 2 = ${leftShift.toRadixString(2).padLeft(6, '0')}');\n  // 1100 << 2 = 110000 (48)\n  \n  // 右移 (>>)\n  int rightShift = a >> 2;\n  print('a >> 2 = $rightShift'); // 输出: a >> 2 = 3\n  print('(${a.toRadixString(2).padLeft(4, '0')}) >> 2 = ${rightShift.toRadixString(2).padLeft(2, '0')}');\n  // 1100 >> 2 = 0011 (3)\n  \n  // 无符号右移 (>>>)，Dart 2.14及以上版本支持\n  // 对于正数，>>>和>>的结果相同\n  // 对于负数，>>>将符号位视为数据位，并用0填充最高位\n  int negativeNum = -12; // 二进制：11...110100（补码表示，前面是多个1）\n  \n  // 有符号右移保留符号位\n  int signedRightShift = negativeNum >> 2;\n  print('$negativeNum >> 2 = $signedRightShift'); // 输出: -12 >> 2 = -3\n  \n  // 无符号右移补0到最高位\n  // 注意：Dart 2.14之前不支持此运算符\n  // 以下代码仅在Dart 2.14及以上版本运行\n  try {\n    int unsignedRightShift = negativeNum >>> 2;\n    print('$negativeNum >>> 2 = $unsignedRightShift');\n  } catch (e) {\n    print('你的Dart版本可能不支持>>>运算符（需要Dart 2.14及以上）');\n  }\n  \n  // 位运算符的实际应用\n  \n  // 1. 检查奇偶性\n  int number = 42;\n  bool isEven = (number & 1) == 0;\n  print('$number是${isEven ? \"偶数\" : \"奇数\"}'); // 输出: 42是偶数\n  \n  // 2. 设置、清除和检查位\n  int flags = 0; // 初始所有标志都是0\n  \n  // 设置第1位和第3位（从0开始计数）\n  flags = flags | (1 << 1) | (1 << 3);\n  print('设置标志后: ${flags.toRadixString(2).padLeft(4, '0')}'); // 输出: 设置标志后: 1010\n  \n  // 检查第3位是否设置\n  bool isThirdBitSet = (flags & (1 << 3)) != 0;\n  print('第3位是否设置: $isThirdBitSet'); // 输出: 第3位是否设置: true\n  \n  // 清除第1位\n  flags = flags & ~(1 << 1);\n  print('清除第1位后: ${flags.toRadixString(2).padLeft(4, '0')}'); // 输出: 清除第1位后: 1000\n  \n  // 3. 交换两个变量的值（不使用临时变量）\n  int x = 10;\n  int y = 20;\n  \n  x = x ^ y;\n  y = x ^ y;\n  x = x ^ y;\n  \n  print('交换后: x = $x, y = $y'); // 输出: 交换后: x = 20, y = 10\n  \n  // 4. 使用掩码进行操作\n  int data = 0xABCD; // 十六进制数\n  int mask = 0xFF00; // 掩码，用于提取高8位\n  \n  int highByte = (data & mask) >> 8; // 提取并右移\n  print('高8位: ${highByte.toRadixString(16).toUpperCase()}'); // 输出: 高8位: AB\n}", "explanation": "这个示例详细展示了Dart中位运算符的使用。首先演示了基本的位运算符（&、|、^、~、<<、>>）在整数上的应用，并通过二进制表示直观地展示了各种操作的结果。示例还展示了无符号右移运算符（>>>）的用法，这是Dart 2.14版本引入的。之后展示了位运算符的几个实际应用场景：检查数字的奇偶性、设置/清除/检查特定位、不使用临时变量交换两个变量的值、使用掩码提取特定位。通过这些示例，可以理解位运算符在底层编程中的强大功能和应用场景。"}]}}, {"name": "Assignment Operators", "trans": ["赋值运算符"], "usage": {"syntax": "// 基本赋值\na = value\n\n// 复合赋值\na += b  // 等价于 a = a + b\na -= b  // 等价于 a = a - b\na *= b  // 等价于 a = a * b\na /= b  // 等价于 a = a / b\na ~/= b // 等价于 a = a ~/ b\na %= b  // 等价于 a = a % b\na &= b  // 等价于 a = a & b\na |= b  // 等价于 a = a | b\na ^= b  // 等价于 a = a ^ b\na <<= b // 等价于 a = a << b\na >>= b // 等价于 a = a >> b\na >>>= b // 等价于 a = a >>> b (Dart 2.14+)", "description": "赋值运算符用于将值分配给变量。Dart提供基本赋值运算符（=）和多种复合赋值运算符，将赋值与其他操作结合在一起。复合赋值运算符包括：`+=`（加法赋值）、`-=`（减法赋值）、`*=`（乘法赋值）、`/=`（除法赋值）、`~/=`（整除赋值）、`%=`（取余赋值）、`&=`（按位与赋值）、`|=`（按位或赋值）、`^=`（按位异或赋值）、`<<=`（左移赋值）、`>>=`（右移赋值）、`>>>=`（无符号右移赋值，仅Dart 2.14及以上版本支持）。复合赋值运算符不仅使代码更简洁，还可能在某些情况下提高性能，因为变量只需计算一次。", "parameters": [{"name": "=", "description": "基本赋值运算符，将右侧值赋给左侧变量"}, {"name": "+=", "description": "加法赋值运算符，将右侧值加到左侧变量上"}, {"name": "-=", "description": "减法赋值运算符，从左侧变量中减去右侧值"}, {"name": "*=", "description": "乘法赋值运算符，将左侧变量乘以右侧值"}, {"name": "/=", "description": "除法赋值运算符，将左侧变量除以右侧值（结果为double）"}, {"name": "~/=", "description": "整除赋值运算符，将左侧变量除以右侧值（结果为int）"}, {"name": "%=", "description": "取余赋值运算符，将左侧变量除以右侧值的余数赋给左侧变量"}, {"name": "&=", "description": "按位与赋值运算符，将左侧变量与右侧值进行按位与操作"}, {"name": "|=", "description": "按位或赋值运算符，将左侧变量与右侧值进行按位或操作"}, {"name": "^=", "description": "按位异或赋值运算符，将左侧变量与右侧值进行按位异或操作"}, {"name": "<<=", "description": "左移赋值运算符，将左侧变量左移右侧值指定的位数"}, {"name": ">>=", "description": "右移赋值运算符，将左侧变量右移右侧值指定的位数"}, {"name": ">>>=", "description": "无符号右移赋值运算符，将左侧变量无符号右移右侧值指定的位数"}], "returnValue": "赋值运算符返回赋值后的值", "examples": [{"code": "void main() {\n  // 基本赋值运算符\n  int a = 10;\n  print('a = $a'); // 输出: a = 10\n  \n  // 链式赋值\n  int b, c, d;\n  b = c = d = 5; // 从右向左赋值\n  print('b = $b, c = $c, d = $d'); // 输出: b = 5, c = 5, d = 5\n  \n  // 算术复合赋值运算符\n  \n  // 加法赋值 (+=)\n  int sum = 5;\n  sum += 3; // 等价于 sum = sum + 3;\n  print('sum += 3: $sum'); // 输出: sum += 3: 8\n  \n  // 减法赋值 (-=)\n  int difference = 10;\n  difference -= 4; // 等价于 difference = difference - 4;\n  print('difference -= 4: $difference'); // 输出: difference -= 4: 6\n  \n  // 乘法赋值 (*=)\n  int product = 3;\n  product *= 4; // 等价于 product = product * 4;\n  print('product *= 4: $product'); // 输出: product *= 4: 12\n  \n  // 除法赋值 (/=)\n  double quotient = 10;\n  quotient /= 2; // 等价于 quotient = quotient / 2;\n  print('quotient /= 2: $quotient'); // 输出: quotient /= 2: 5.0\n  \n  // 整除赋值 (~/=)\n  int intQuotient = 10;\n  intQuotient ~/= 3; // 等价于 intQuotient = intQuotient ~/ 3;\n  print('intQuotient ~/= 3: $intQuotient'); // 输出: intQuotient ~/= 3: 3\n  \n  // 取余赋值 (%=)\n  int remainder = 10;\n  remainder %= 3; // 等价于 remainder = remainder % 3;\n  print('remainder %= 3: $remainder'); // 输出: remainder %= 3: 1\n  \n  // 位运算复合赋值运算符\n  \n  // 按位与赋值 (&=)\n  int bitwiseAnd = 0xF0; // 二进制: 11110000\n  bitwiseAnd &= 0xAA;   // 二进制: 10101010, 结果: 10100000\n  print('bitwiseAnd &= 0xAA: ${bitwiseAnd.toRadixString(16).toUpperCase()}'); // 输出: bitwiseAnd &= 0xAA: A0\n  \n  // 按位或赋值 (|=)\n  int bitwiseOr = 0x0F; // 二进制: 00001111\n  bitwiseOr |= 0xA0;    // 二进制: 10100000, 结果: 10101111\n  print('bitwiseOr |= 0xA0: ${bitwiseOr.toRadixString(16).toUpperCase()}'); // 输出: bitwiseOr |= 0xA0: AF\n  \n  // 按位异或赋值 (^=)\n  int bitwiseXor = 0xFF; // 二进制: 11111111\n  bitwiseXor ^= 0xAA;   // 二进制: 10101010, 结果: 01010101\n  print('bitwiseXor ^= 0xAA: ${bitwiseXor.toRadixString(16).toUpperCase()}'); // 输出: bitwiseXor ^= 0xAA: 55\n  \n  // 左移赋值 (<<=)\n  int leftShift = 0x02; // 二进制: 00000010\n  leftShift <<= 2;      // 左移2位，结果: 00001000\n  print('leftShift <<= 2: ${leftShift.toRadixString(16).toUpperCase()}'); // 输出: leftShift <<= 2: 8\n  \n  // 右移赋值 (>>=)\n  int rightShift = 0x10; // 二进制: 00010000\n  rightShift >>= 2;      // 右移2位，结果: 00000100\n  print('rightShift >>= 2: ${rightShift.toRadixString(16).toUpperCase()}'); // 输出: rightShift >>= 2: 4\n  \n  // 无符号右移赋值 (>>>=) - Dart 2.14及以上版本支持\n  try {\n    int unsignedRightShift = -16; // 负数，二进制表示为补码\n    // unsignedRightShift >>>= 2;  // 无符号右移2位\n    // print('unsignedRightShift >>>= 2: $unsignedRightShift');\n    \n    // 由于部分环境可能不支持>>>= (需要Dart 2.14+)，我们使用普通赋值方式展示\n    int result = -16 >>> 2;  // 如果支持的话，这会计算无符号右移结果\n    print('result = -16 >>> 2: $result');\n  } catch (e) {\n    print('无符号右移赋值运算符可能不受支持（需要Dart 2.14及以上）');\n  }\n  \n  // 字符串和赋值运算符\n  String message = 'Hello';\n  message += ' World'; // 字符串连接赋值\n  print('message += \" World\": $message'); // 输出: message += \" World\": Hello World\n  \n  // 赋值运算符在链式操作中的使用\n  int x = 5;\n  int y = 3;\n  int z = 0;\n  \n  z += x += y; // 先执行 x += y (x变为8)，然后执行 z += x (z变为8)\n  print('z += x += y: x = $x, z = $z'); // 输出: z += x += y: x = 8, z = 8\n  \n  // 使用赋值运算符的返回值\n  int value = 0;\n  int result = (value = 10) * 2; // 先将10赋给value，然后用value乘以2\n  print('result = (value = 10) * 2: value = $value, result = $result'); // 输出: result = (value = 10) * 2: value = 10, result = 20\n  \n  // 在条件中使用赋值运算符\n  // 注意：这在Dart中并不常见，不同于C/C++\n  String? nullableString;\n  String nonNullString;\n  \n  if ((nonNullString = 'Default') != null) {\n    print('nonNullString被赋值为: $nonNullString');\n  }\n  \n  // 使用??=运算符(空值赋值运算符，见下一节)\n  String? name;\n  name ??= 'Guest'; // 如果name为null，则赋值为'Guest'\n  print('name ??= \"Guest\": $name'); // 输出: name ??= \"Guest\": Guest\n  \n  name ??= 'Admin'; // name不为null，所以不改变\n  print('name ??= \"Admin\": $name'); // 输出: name ??= \"Admin\": Guest\n}", "explanation": "这个示例全面展示了Dart中各种赋值运算符的使用。首先演示了基本赋值运算符(=)和链式赋值，然后依次展示了算术复合赋值运算符(+=、-=、*=、/=、~/=、%=)和位运算复合赋值运算符(&=、|=、^=、<<=、>>=、>>>=)的用法。示例还展示了赋值运算符在字符串连接、链式操作中的使用，以及赋值运算符的返回值特性。最后简要介绍了??=运算符（空值赋值运算符），这将在下一节\"其他运算符\"中详细讲解。通过这些示例，可以理解赋值运算符如何使代码更简洁，以及它们在不同数据类型和场景中的应用。"}]}}, {"name": "Other Operators", "trans": ["其他运算符"], "usage": {"syntax": "// 条件运算符\ncondition ? expr1 : expr2  // 三元条件运算符\n\n// 空值相关运算符\nexpr1 ?? expr2  // 空值合并运算符\nobj?.prop      // 条件成员访问\nobj?..method() // 条件级联\nfunc?.call()   // 条件调用\nvalue as Type  // 类型转换\nvalue is Type  // 类型检查\nvalue is! Type // 类型否定检查", "description": "Dart提供多种特殊运算符用于不同场景：1) **条件运算符**：`?:`（三元条件运算符，根据条件选择不同表达式）；2) **空值相关运算符**：`??`（空值合并，左侧为null时返回右侧值）、`?.`（条件成员访问，对象为null时安全地访问成员）、`?..`（条件级联，对象为null时安全地级联操作）、`?.call()`（条件调用，函数为null时安全地调用）；3) **类型相关运算符**：`as`（类型转换）、`is`（类型检查，检查对象是否为指定类型）、`is!`（类型否定检查，检查对象是否不是指定类型）。这些运算符在处理可能为null的值、根据条件选择不同值，以及进行类型操作时非常有用，是Dart空安全特性的重要组成部分。", "parameters": [{"name": "?:", "description": "三元条件运算符，根据条件选择不同的表达式"}, {"name": "??", "description": "空值合并运算符，如果左侧表达式为null，则返回右侧表达式的值"}, {"name": "??=", "description": "空值赋值运算符，如果变量为null，则赋予右侧值"}, {"name": "?.", "description": "条件成员访问运算符，安全地访问可能为null的对象的成员"}, {"name": "?..", "description": "条件级联运算符，安全地对可能为null的对象进行级联操作"}, {"name": "?.call()", "description": "条件调用运算符，安全地调用可能为null的函数"}, {"name": "as", "description": "类型转换运算符，将对象转换为指定类型"}, {"name": "is", "description": "类型检查运算符，检查对象是否为指定类型"}, {"name": "is!", "description": "类型否定检查运算符，检查对象是否不是指定类型"}], "returnValue": "根据具体运算符不同，返回值也不同", "examples": [{"code": "void main() {\n  // 三元条件运算符 (?:)\n  int a = 10;\n  int b = 5;\n  \n  int max = a > b ? a : b;\n  print('较大值: $max'); // 输出: 较大值: 10\n  \n  String status = a > b ? '大于' : a < b ? '小于' : '等于';\n  print('a $status b'); // 输出: a 大于 b\n  \n  // 空值合并运算符 (??)\n  String? nullableStr;\n  String nonNullStr = 'Default';\n  \n  String result1 = nullableStr ?? 'Fallback';\n  String result2 = nonNullStr ?? 'Fallback';\n  \n  print('result1: $result1'); // 输出: result1: Fallback\n  print('result2: $result2'); // 输出: result2: Default\n  \n  // 链式使用空值合并运算符\n  String? first;\n  String? second;\n  String third = 'Third';\n  \n  String result3 = first ?? second ?? third ?? 'Default';\n  print('result3: $result3'); // 输出: result3: Third\n  \n  // 空值赋值运算符 (??=)\n  String? name;\n  name ??= 'Guest'; // 如果name为null，则赋值为'Guest'\n  print('name: $name'); // 输出: name: Guest\n  \n  name ??= 'Admin'; // name不为null，所以不改变\n  print('name: $name'); // 输出: name: Guest\n  \n  // 条件成员访问运算符 (?.)\n  Person? person;\n  String? personName = person?.name; // 安全访问，person为null时返回null\n  print('personName: $personName'); // 输出: personName: null\n  \n  person = Person('Alice', 30);\n  personName = person?.name; // person不为null，返回name属性\n  print('personName: $personName'); // 输出: personName: Alice\n  \n  // 常规方式访问（不安全）\n  // String unsafeName = person.name; // 如果person为null，会抛出异常\n  \n  // 条件级联运算符 (?..) - Dart 2.12及以上版本支持\n  Person? nullablePerson;\n  \n  // 安全级联，如果nullablePerson为null，则整个表达式返回null\n  nullablePerson?..name = 'Bob'\n               ..age = 25;\n  \n  print('nullablePerson: $nullablePerson'); // 输出: nullablePerson: null\n  \n  // 常规级联\n  person = Person('Alice', 30);\n  person..name = 'Carol'\n       ..age = 35;\n  \n  print('person: ${person.name}, ${person.age}'); // 输出: person: Carol, 35\n  \n  // 条件调用运算符 (?.call)\n  Function? callback;\n  \n  // 安全调用，如果callback为null，则不调用\n  callback?.call('Hello');\n  \n  callback = (String message) {\n    print('回调被调用: $message');\n  };\n  \n  // callback不为null，会被调用\n  callback?.call('Hello'); // 输出: 回调被调用: Hello\n  \n  // 类型转换运算符 (as)\n  Object obj = 'Hello';\n  String str = obj as String; // 将Object类型转换为String类型\n  print('str: $str'); // 输出: str: Hello\n  \n  // 如果类型不兼容，会抛出异常\n  try {\n    Object numObj = 42;\n    // String numStr = numObj as String; // 会抛出异常\n    // print(numStr);\n  } catch (e) {\n    print('类型转换失败: $e');\n  }\n  \n  // 类型检查运算符 (is)\n  Object strObj = 'Test';\n  Object numObj = 42;\n  \n  if (strObj is String) {\n    // 在这个范围内，strObj被视为String类型\n    print('strObj是String，长度为: ${strObj.length}'); // 输出: strObj是String，长度为: 4\n  }\n  \n  if (numObj is int) {\n    // 在这个范围内，numObj被视为int类型\n    print('numObj是int，值为: ${numObj + 1}'); // 输出: numObj是int，值为: 43\n  }\n  \n  // 类型否定检查运算符 (is!)\n  if (strObj is! int) {\n    print('strObj不是int类型'); // 输出: strObj不是int类型\n  }\n  \n  // 组合使用多种运算符\n  String? nullableInput;\n  int? length = nullableInput?.length;\n  int displayLength = length ?? 0;\n  \n  print('输入长度: $displayLength'); // 输出: 输入长度: 0\n  \n  nullableInput = 'Hello, Dart!';\n  length = nullableInput?.length;\n  displayLength = length ?? 0;\n  \n  print('输入长度: $displayLength'); // 输出: 输入长度: 12\n  \n  // 在条件语句中使用类型检查\n  dynamic value = 'dynamic value';\n  \n  if (value is String) {\n    print('value是String: ${value.toUpperCase()}'); // 输出: value是String: DYNAMIC VALUE\n  } else if (value is int) {\n    print('value是int: ${value * 2}');\n  } else {\n    print('value是其他类型: ${value.runtimeType}');\n  }\n}\n\n// 用于演示条件成员访问的类\nclass Person {\n  String name;\n  int age;\n  \n  Person(this.name, this.age);\n  \n  @override\n  String toString() => 'Person(name: $name, age: $age)';\n}", "explanation": "这个示例全面展示了Dart中各种特殊运算符的使用。首先演示了三元条件运算符(?:)用于根据条件选择不同值。然后展示了空值相关运算符，包括空值合并运算符(??)、空值赋值运算符(??=)、条件成员访问运算符(?.)、条件级联运算符(?..，Dart 2.12及以上版本支持)和条件调用运算符(?.call)，这些运算符在处理可能为null的值时非常有用，是Dart空安全特性的重要组成部分。最后展示了类型相关运算符，包括类型转换运算符(as)、类型检查运算符(is)和类型否定检查运算符(is!)，这些运算符用于安全地进行类型操作。示例还展示了如何组合使用多种运算符来处理更复杂的场景。"}]}}, {"name": "Assignment", "trans": ["实践作业"], "usage": {"syntax": "// 运算符实践", "description": "完成以下任务，练习Dart中各种运算符的使用：1) 使用算术运算符计算两个数字的和、差、积、商和余数；2) 使用关系运算符比较不同类型的值，包括数字、字符串和对象；3) 使用逻辑运算符组合多个条件；4) 使用位运算符操作二进制数据；5) 使用各种赋值运算符简化代码；6) 使用条件运算符和空值相关运算符处理可能为null的值。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "void main() {\n  // 1. 算术运算符\n  int a = 15;\n  int b = 4;\n  \n  print('a + b = ${a + b}'); // 加法: 19\n  print('a - b = ${a - b}'); // 减法: 11\n  print('a * b = ${a * b}'); // 乘法: 60\n  print('a / b = ${a / b}'); // 除法: 3.75\n  print('a ~/ b = ${a ~/ b}'); // 整除: 3\n  print('a % b = ${a % b}'); // 取余: 3\n  \n  // 2. 关系运算符\n  int x = 10;\n  double y = 10.0;\n  String str1 = 'apple';\n  String str2 = 'banana';\n  \n  print('x == y: ${x == y}'); // true (值相等)\n  print('str1 == str2: ${str1 == str2}'); // false\n  print('str1 < str2: ${str1 < str2}'); // true (字母顺序)\n  \n  // 创建两个相同内容的对象比较\n  var p1 = Point(2, 3);\n  var p2 = Point(2, 3);\n  var p3 = p1;\n  \n  print('p1 == p2: ${p1 == p2}'); // 取决于Point类的==实现\n  print('identical(p1, p3): ${identical(p1, p3)}'); // true (同一对象)\n  \n  // 3. 逻辑运算符\n  bool condition1 = x > 5; // true\n  bool condition2 = y < 5; // false\n  \n  print('condition1 && condition2: ${condition1 && condition2}'); // false\n  print('condition1 || condition2: ${condition1 || condition2}'); // true\n  print('!condition1: ${!condition1}'); // false\n  \n  // 复合逻辑条件\n  bool isValid = (x > 0 && y > 0) || (str1.length > 3);\n  print('isValid: $isValid'); // true\n  \n  // 4. 位运算符\n  int bits1 = 0x0F; // 二进制: 00001111\n  int bits2 = 0xA5; // 二进制: 10100101\n  \n  print('bits1 & bits2: ${(bits1 & bits2).toRadixString(16)}'); // 按位与\n  print('bits1 | bits2: ${(bits1 | bits2).toRadixString(16)}'); // 按位或\n  print('bits1 ^ bits2: ${(bits1 ^ bits2).toRadixString(16)}'); // 按位异或\n  print('~bits1: ${(~bits1).toRadixString(16)}'); // 按位取反\n  print('bits1 << 2: ${(bits1 << 2).toRadixString(16)}'); // 左移\n  print('bits2 >> 2: ${(bits2 >> 2).toRadixString(16)}'); // 右移\n  \n  // 5. 赋值运算符\n  int value = 5;\n  value += 3; // 等价于 value = value + 3\n  print('value += 3: $value'); // 8\n  \n  value -= 2; // 等价于 value = value - 2\n  print('value -= 2: $value'); // 6\n  \n  value *= 2; // 等价于 value = value * 2\n  print('value *= 2: $value'); // 12\n  \n  // 6. 条件运算符和空值相关运算符\n  int score = 85;\n  String grade = score >= 90 ? 'A' : score >= 80 ? 'B' : 'C';\n  print('grade: $grade'); // B\n  \n  String? nullableText;\n  String displayText = nullableText ?? 'No text available';\n  print('displayText: $displayText'); // No text available\n  \n  nullableText = 'Hello, Dart!';\n  displayText = nullableText ?? 'No text available';\n  print('displayText: $displayText'); // Hello, Dart!\n  \n  String? username;\n  username ??= 'Guest';\n  print('username: $username'); // Guest\n  \n  // 条件成员访问\n  User? user;\n  String? userEmail = user?.email;\n  print('userEmail: $userEmail'); // null\n  \n  user = User('<EMAIL>');\n  userEmail = user?.email;\n  print('userEmail: $userEmail'); // <EMAIL>\n  \n  // 类型检查和转换\n  dynamic anyValue = 'This is a string';\n  \n  if (anyValue is String) {\n    // 在这个范围内，编译器知道anyValue是String类型\n    print('String length: ${anyValue.length}');\n  }\n  \n  try {\n    // 尝试类型转换\n    dynamic numValue = 42;\n    String strValue = numValue as String; // 会抛出异常\n    print(strValue);\n  } catch (e) {\n    print('类型转换错误: $e');\n  }\n}\n\n// 用于示例的类\nclass Point {\n  final int x;\n  final int y;\n  \n  Point(this.x, this.y);\n  \n  // 重写==运算符\n  @override\n  bool operator ==(Object other) {\n    if (identical(this, other)) return true;\n    return other is Point && other.x == x && other.y == y;\n  }\n  \n  // 重写hashCode\n  @override\n  int get hashCode => x.hashCode ^ y.hashCode;\n}\n\nclass User {\n  final String email;\n  \n  User(this.email);\n}", "explanation": "这个参考实现展示了Dart中各种运算符的综合使用。任务1展示了算术运算符进行基本数学运算；任务2展示了关系运算符比较不同类型的值，包括数字、字符串和自定义对象；任务3展示了逻辑运算符组合多个条件表达式；任务4展示了位运算符处理二进制数据；任务5展示了赋值运算符简化变量操作；任务6展示了条件运算符、空值合并运算符、空值赋值运算符、条件成员访问运算符，以及类型检查和转换运算符的使用。通过这个综合示例，可以理解Dart中各类运算符的实际应用场景。"}]}}]}