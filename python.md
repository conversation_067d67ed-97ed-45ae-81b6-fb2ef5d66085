# Python学习路线与核心内容

## 1. Python基础        //  已完成

### 基础语法
- Python解释器与运行方式
- 注释与缩进
- 标识符与关键字
- 代码块与语句
- 输入与输出
- 交互式编程

### 数据类型
- 数字类型（int、float、complex）
- 字符串（str）
- 布尔值（bool）
- 列表（list）
- 元组（tuple）
- 集合（set、frozenset）
- 字典（dict）
- 类型转换
- 不可变与可变类型

### 运算符
- 算术运算符
- 赋值运算符
- 比较运算符
- 逻辑运算符
- 位运算符
- 成员运算符
- 身份运算符

### 流程控制
- if条件语句
- for循环
- while循环
- break与continue
- pass语句
- 列表推导式
- 条件表达式

## 2. 函数与作用域         //已完成

### 函数基础
- 函数定义与调用
- 参数类型（位置、关键字、默认、可变）
- 返回值
- 变量作用域（LEGB规则）
- global与nonlocal
- 匿名函数lambda
- 函数文档字符串
- 函数作为对象

### 高阶函数
- map、filter、reduce
- sorted与自定义key
- 闭包
- 装饰器
- 偏函数
- 递归函数

## 3. 面向对象编程      //已完成

### 类与对象
- 类的定义
- 实例与属性
- 方法与self
- 构造方法__init__
- 类属性与实例属性
- 类方法与静态方法
- 魔术方法（__str__、__repr__等）

### 继承与多态
- 单继承与多继承
- 方法重写
- super()函数
- 多态与鸭子类型
- 抽象类与接口
- MRO与C3线性化

## 4. 模块与包          //已完成

### 模块基础
- 模块的创建与导入
- import与from...import
- __name__与主程序判断
- 模块搜索路径
- 标准库与第三方库

### 包管理
- 包的结构与__init__.py
- 相对与绝对导入
- 虚拟环境venv
- pip包管理工具
- requirements.txt
- 发布与安装自定义包

## 5. 文件与输入输出        //已完成

### 文件操作
- 打开与关闭文件
- 读写文本文件
- 读写二进制文件
- with语句与上下文管理器
- 文件指针与seek
- 文件编码

### 输入输出
- input函数
- print函数
- 格式化输出（%、format、f-string）
- 标准输入输出与重定向

## 6. 异常处理      //已完成

### 异常基础
- try...except结构
- else与finally
- 多异常捕获
- 主动抛出异常raise
- 自定义异常类
- 异常链与traceback

## 7. 常用标准库    //已完成

### 内置模块
- sys与os
- math与random
- datetime与time
- collections
- itertools与functools
- re正则表达式
- json与pickle
- logging日志
- argparse命令行参数

### 网络与并发
- requests库
- urllib
- socket编程
- threading与multiprocessing
- asyncio异步编程
- queue队列

## 8. 进阶特性      //已完成

### 迭代器与生成器
- 迭代器协议
- 生成器函数与yield
- 生成器表达式
- itertools高级用法

### 装饰器与上下文管理器
- 函数装饰器
- 类装饰器
- contextlib模块
- 自定义上下文管理器

### 元编程
- 动态属性与方法
- getattr/setattr
- type与元类
- 动态导入与反射

## 9. 测试与调试    //已完成

### 单元测试
- unittest框架
- pytest基础
- 断言与测试用例
- 测试夹具fixture
- Mock与patch
- 覆盖率报告

### 调试技巧
- print与断点调试
- logging调试
- pdb交互调试
- IDE调试工具
- 性能分析与profile

## 10. 工程化与生态     //已完成

### 项目结构与规范
- 代码组织结构
- 命名规范与PEP8
- 文档与注释
- 代码风格检查flake8
- 自动格式化black/isort

### 依赖管理与构建
- requirements.txt与pipenv/poetry
- 虚拟环境管理
- 构建与打包工具
- 发布PyPI

### 持续集成与部署
- Git与版本管理
- 自动化测试
- CI/CD流程（GitHub Actions、Travis CI等）
- 容器化与Docker
- 云部署与服务器运维

### 现代Python生态
- 科学计算（NumPy、Pandas、Matplotlib）
- Web开发（Django、Flask、FastAPI）
- 数据分析与机器学习（scikit-learn、TensorFlow、PyTorch）
- 爬虫与自动化（Scrapy、Selenium）
- 脚本与自动化运维 

## 综合实战         //已完成