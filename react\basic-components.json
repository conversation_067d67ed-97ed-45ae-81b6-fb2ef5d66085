{"name": "Basic Components", "trans": ["组件基础"], "methods": [{"name": "Functional Components", "trans": ["函数组件"], "usage": {"syntax": "function ComponentName(props) {\n  return <div>...</div>;\n}\n\n// 或使用箭头函数\nconst ComponentName = (props) => {\n  return <div>...</div>;\n};", "description": "函数组件是最简单的React组件类型，本质上是接收props对象并返回React元素的JavaScript函数。", "parameters": [{"name": "props", "description": "一个包含组件属性的对象，通常包含传递给组件的数据和回调函数"}], "returnValue": "返回React元素（JSX）", "examples": [{"code": "// 基本函数组件\nimport React from 'react';\n\nfunction Greeting(props) {\n  return <h1>Hello, {props.name}!</h1>;\n}\n\n// 使用箭头函数\nconst Button = (props) => {\n  return (\n    <button \n      className={props.className} \n      onClick={props.onClick}\n    >\n      {props.children}\n    </button>\n  );\n};\n\n// 使用解构赋值简化props访问\nfunction Profile({ name, avatar, bio }) {\n  return (\n    <div className=\"profile\">\n      <img src={avatar} alt={name} />\n      <h2>{name}</h2>\n      <p>{bio}</p>\n    </div>\n  );\n}\n\n// 使用默认值\nfunction Heading({ title = 'Default Title', size = 'medium' }) {\n  return <h1 className={`heading-${size}`}>{title}</h1>;\n}\n\n// 在父组件中使用函数组件\nfunction App() {\n  return (\n    <div>\n      <Greeting name=\"Alice\" />\n      <Profile \n        name=\"Bob\" \n        avatar=\"https://example.com/avatar.jpg\"\n        bio=\"Frontend developer\"\n      />\n      <Heading title=\"Welcome\" size=\"large\" />\n      <Button \n        className=\"primary\" \n        onClick={() => console.log('Button clicked')}\n      >\n        Click Me\n      </Button>\n    </div>\n  );\n}", "explanation": "这个例子展示了几种定义和使用函数组件的方式：基本函数组件、箭头函数组件、使用解构赋值简化props访问、设置默认值，以及在父组件中组合使用这些组件。函数组件简洁明了，是React中最常用的组件类型。"}]}}, {"name": "Class Components", "trans": ["类组件"], "usage": {"syntax": "class ComponentName extends React.Component {\n  render() {\n    return <div>...</div>;\n  }\n}", "description": "类组件是使用ES6类语法定义的React组件，必须继承自React.Component并实现render方法。类组件提供了更多特性，如状态管理和生命周期方法。", "parameters": [], "returnValue": "render方法返回React元素（JSX）", "examples": [{"code": "// 基本类组件\nimport React from 'react';\n\nclass Greeting extends React.Component {\n  render() {\n    return <h1>Hello, {this.props.name}!</h1>;\n  }\n}\n\n// 带状态的类组件\nclass Counter extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { count: 0 };\n    // 绑定方法到实例\n    this.increment = this.increment.bind(this);\n  }\n\n  increment() {\n    this.setState(state => ({ count: state.count + 1 }));\n  }\n\n  render() {\n    return (\n      <div>\n        <p>Count: {this.state.count}</p>\n        <button onClick={this.increment}>Increment</button>\n      </div>\n    );\n  }\n}\n\n// 使用生命周期方法的类组件\nclass Clock extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { date: new Date() };\n  }\n\n  componentDidMount() {\n    this.timerID = setInterval(() => this.tick(), 1000);\n  }\n\n  componentWillUnmount() {\n    clearInterval(this.timerID);\n  }\n\n  tick() {\n    this.setState({ date: new Date() });\n  }\n\n  render() {\n    return (\n      <div>\n        <h2>Current time:</h2>\n        <p>{this.state.date.toLocaleTimeString()}</p>\n      </div>\n    );\n  }\n}\n\n// 使用类属性语法简化构造函数（需要Babel支持）\nclass Button extends React.Component {\n  // 初始状态，无需构造函数\n  state = { isHovered: false };\n\n  // 使用箭头函数自动绑定this\n  handleMouseEnter = () => {\n    this.setState({ isHovered: true });\n  };\n\n  handleMouseLeave = () => {\n    this.setState({ isHovered: false });\n  };\n\n  render() {\n    const buttonStyle = {\n      backgroundColor: this.state.isHovered ? '#0056b3' : '#007bff',\n      color: 'white',\n      padding: '10px 15px',\n      border: 'none',\n      borderRadius: '4px',\n      cursor: 'pointer'\n    };\n\n    return (\n      <button\n        style={buttonStyle}\n        onClick={this.props.onClick}\n        onMouseEnter={this.handleMouseEnter}\n        onMouseLeave={this.handleMouseLeave}\n      >\n        {this.props.children}\n      </button>\n    );\n  }\n}", "explanation": "这个例子展示了类组件的多种用法：基本类组件、带状态的类组件、使用生命周期方法的类组件以及使用类属性语法简化的类组件。类组件提供了更多功能，如内部状态、生命周期方法和更复杂的逻辑处理能力。"}]}}, {"name": "Component Rendering", "trans": ["组件的渲染过程"], "usage": {"syntax": "ReactDOM.render(<ComponentName />, domContainer);", "description": "React组件的渲染是将组件转换为DOM元素并显示在页面上的过程。React使用虚拟DOM来优化渲染性能。", "parameters": [], "returnValue": "DOM更新", "examples": [{"code": "// 基本渲染过程\nimport React from 'react';\nimport ReactDOM from 'react-dom';\n\nfunction App() {\n  return <h1>Hello, world!</h1>;\n}\n\n// 将React元素渲染到DOM中\nReactDOM.render(<App />, document.getElementById('root'));\n\n// 使用React 18的新API\nimport { createRoot } from 'react-dom/client';\n\nconst root = createRoot(document.getElementById('root'));\nroot.render(<App />);\n\n// 组件的条件渲染\nfunction Greeting({ isLoggedIn }) {\n  if (isLoggedIn) {\n    return <h1>Welcome back!</h1>;\n  }\n  return <h1>Please sign in.</h1>;\n}\n\n// 组件的列表渲染\nfunction NumberList({ numbers }) {\n  const listItems = numbers.map((number) =>\n    <li key={number.toString()}>{number}</li>\n  );\n  return <ul>{listItems}</ul>;\n}\n\n// 嵌套组件的渲染\nfunction Page() {\n  return (\n    <div>\n      <Header />\n      <Content />\n      <Footer />\n    </div>\n  );\n}\n\n// 渲染组件时发生了什么：\n// 1. React调用组件函数，获取React元素\n// 2. React递归处理所有子元素\n// 3. React创建虚拟DOM表示\n// 4. React比较新旧虚拟DOM（协调过程）\n// 5. 只更新实际发生变化的DOM节点", "explanation": "这个例子展示了React组件的渲染过程，包括基本渲染API、条件渲染、列表渲染和嵌套组件渲染。React的渲染过程利用虚拟DOM和高效的协调算法，只更新实际需要变化的DOM部分，从而提高应用性能。"}]}}, {"name": "Component Import and Export", "trans": ["组件的导入和导出"], "usage": {"syntax": "// 导出\nexport default ComponentName;\nexport { ComponentName };\n\n// 导入\nimport ComponentName from './path/to/component';\nimport { ComponentName } from './path/to/component';", "description": "React应用通常由多个组件文件组成，通过ES6模块系统的导入和导出功能组织代码。", "parameters": [], "returnValue": "", "examples": [{"code": "// Button.js - 默认导出\nimport React from 'react';\n\nfunction Button(props) {\n  return (\n    <button className={props.className}>\n      {props.children}\n    </button>\n  );\n}\n\nexport default Button;\n\n// Header.js - 命名导出\nimport React from 'react';\n\nexport function Logo() {\n  return <img src=\"/logo.png\" alt=\"Logo\" />;\n}\n\nexport function Navigation() {\n  return (\n    <nav>\n      <ul>\n        <li><a href=\"/\">Home</a></li>\n        <li><a href=\"/about\">About</a></li>\n        <li><a href=\"/contact\">Contact</a></li>\n      </ul>\n    </nav>\n  );\n}\n\nexport function Header() {\n  return (\n    <header>\n      <Logo />\n      <Navigation />\n    </header>\n  );\n}\n\n// App.js - 导入组件\nimport React from 'react';\nimport Button from './Button';  // 导入默认导出\nimport { Header, Logo } from './Header';  // 导入命名导出\n\nfunction App() {\n  return (\n    <div>\n      <Header />\n      <main>\n        <h1>Welcome to our site</h1>\n        <p>This is an example of component imports.</p>\n        <Button className=\"primary\">Click me</Button>\n      </main>\n      <footer>\n        <Logo />\n        <p>© 2023 Component Example</p>\n      </footer>\n    </div>\n  );\n}\n\nexport default App;\n\n// 导入和重命名\nimport React from 'react';\nimport MyButton from './Button';  // 导入默认导出并重命名\nimport { Header as PageHeader } from './Header';  // 导入并重命名\n\n// 导入所有命名导出\nimport * as HeaderComponents from './Header';\n// 使用: <HeaderComponents.Logo />, <HeaderComponents.Navigation />", "explanation": "这个例子展示了React组件的导入和导出模式。组件可以通过默认导出（一个文件只能有一个）或命名导出（一个文件可以有多个）方式共享。导入时可以导入默认导出、特定的命名导出、重命名导入的组件或导入所有命名导出。这种模块化方法使得代码组织更加清晰和可维护。"}]}}, {"name": "Component Composition", "trans": ["组件的组合"], "usage": {"syntax": "<ParentComponent>\n  <ChildComponent />\n</ParentComponent>", "description": "组件组合是React的核心概念之一，允许通过组合简单组件来构建复杂UI，而不是继承。组件可以包含其他组件，形成组件树。", "parameters": [], "returnValue": "", "examples": [{"code": "// 基本组件组合\nimport React from 'react';\n\nfunction Button(props) {\n  return (\n    <button className={props.className}>\n      {props.children}\n    </button>\n  );\n}\n\nfunction Card(props) {\n  return (\n    <div className=\"card\">\n      {props.children}\n    </div>\n  );\n}\n\nfunction App() {\n  return (\n    <div>\n      <Card>\n        <h2>Card Title</h2>\n        <p>Card content goes here.</p>\n        <Button className=\"primary\">Learn More</Button>\n      </Card>\n    </div>\n  );\n}\n\n// 特殊化组件\nfunction Dialog(props) {\n  return (\n    <div className={`dialog dialog-${props.type}`}>\n      <h2>{props.title}</h2>\n      <div className=\"dialog-content\">\n        {props.children}\n      </div>\n      <div className=\"dialog-actions\">\n        {props.actions}\n      </div>\n    </div>\n  );\n}\n\nfunction AlertDialog(props) {\n  return (\n    <Dialog \n      type=\"alert\"\n      title={props.title}\n      actions={\n        <Button className=\"primary\" onClick={props.onConfirm}>\n          OK\n        </Button>\n      }\n    >\n      {props.message}\n    </Dialog>\n  );\n}\n\nfunction ConfirmDialog(props) {\n  return (\n    <Dialog \n      type=\"confirm\"\n      title={props.title}\n      actions={\n        <>\n          <Button className=\"secondary\" onClick={props.onCancel}>\n            Cancel\n          </Button>\n          <Button className=\"primary\" onClick={props.onConfirm}>\n            Confirm\n          </Button>\n        </>\n      }\n    >\n      {props.message}\n    </Dialog>\n  );\n}\n\n// 布局组件\nfunction Layout(props) {\n  return (\n    <div className=\"layout\">\n      <header className=\"header\">\n        {props.header}\n      </header>\n      <nav className=\"sidebar\">\n        {props.sidebar}\n      </nav>\n      <main className=\"content\">\n        {props.children}\n      </main>\n      <footer className=\"footer\">\n        {props.footer}\n      </footer>\n    </div>\n  );\n}\n\nfunction AppWithLayout() {\n  return (\n    <Layout\n      header={<h1>My App</h1>}\n      sidebar={<ul><li>Home</li><li>About</li></ul>}\n      footer={<p>© 2023 My App</p>}\n    >\n      <h2>Welcome to My App</h2>\n      <p>This is the main content area.</p>\n    </Layout>\n  );\n}", "explanation": "这个例子展示了React中组件组合的不同方式：基本组件组合（Card包含Button）、特殊化组件（AlertDialog和ConfirmDialog基于Dialog）以及布局组件（Layout提供应用结构）。组件组合是React中构建UI的主要方式，遵循'组合优于继承'的设计原则，使代码更加模块化和可重用。"}]}}, {"name": "Component Splitting", "trans": ["组件的拆分"], "usage": {"syntax": "// 将大组件拆分为多个小组件\nfunction LargeComponent() {\n  return (\n    <div>\n      <SmallComponentA />\n      <SmallComponentB />\n      <SmallComponentC />\n    </div>\n  );\n}", "description": "组件拆分是将大型、复杂的组件分解为更小、更专注的组件的过程，有助于提高代码可维护性、可测试性和重用性。", "parameters": [], "returnValue": "", "examples": [{"code": "// 拆分前的大组件\nimport React, { useState } from 'react';\n\nfunction UserDashboard({ user }) {\n  const [activeTab, setActiveTab] = useState('profile');\n  \n  return (\n    <div className=\"dashboard\">\n      <header className=\"dashboard-header\">\n        <h1>Welcome, {user.name}</h1>\n        <div className=\"user-avatar\">\n          <img src={user.avatar} alt={user.name} />\n        </div>\n        <div className=\"user-stats\">\n          <div className=\"stat\">\n            <span className=\"stat-value\">{user.followers}</span>\n            <span className=\"stat-label\">Followers</span>\n          </div>\n          <div className=\"stat\">\n            <span className=\"stat-value\">{user.following}</span>\n            <span className=\"stat-label\">Following</span>\n          </div>\n          <div className=\"stat\">\n            <span className=\"stat-value\">{user.posts}</span>\n            <span className=\"stat-label\">Posts</span>\n          </div>\n        </div>\n      </header>\n      \n      <nav className=\"dashboard-nav\">\n        <ul>\n          <li>\n            <button \n              className={activeTab === 'profile' ? 'active' : ''}\n              onClick={() => setActiveTab('profile')}\n            >\n              Profile\n            </button>\n          </li>\n          <li>\n            <button \n              className={activeTab === 'activity' ? 'active' : ''}\n              onClick={() => setActiveTab('activity')}\n            >\n              Activity\n            </button>\n          </li>\n          <li>\n            <button \n              className={activeTab === 'settings' ? 'active' : ''}\n              onClick={() => setActiveTab('settings')}\n            >\n              Settings\n            </button>\n          </li>\n        </ul>\n      </nav>\n      \n      <div className=\"dashboard-content\">\n        {activeTab === 'profile' && (\n          <div className=\"profile-tab\">\n            <h2>Profile Information</h2>\n            <div className=\"profile-field\">\n              <label>Name:</label>\n              <span>{user.name}</span>\n            </div>\n            <div className=\"profile-field\">\n              <label>Email:</label>\n              <span>{user.email}</span>\n            </div>\n            <div className=\"profile-field\">\n              <label>Location:</label>\n              <span>{user.location}</span>\n            </div>\n            <div className=\"profile-field\">\n              <label>Bio:</label>\n              <p>{user.bio}</p>\n            </div>\n          </div>\n        )}\n        \n        {activeTab === 'activity' && (\n          <div className=\"activity-tab\">\n            <h2>Recent Activity</h2>\n            <ul className=\"activity-list\">\n              {user.activities.map(activity => (\n                <li key={activity.id} className=\"activity-item\">\n                  <div className=\"activity-time\">{activity.time}</div>\n                  <div className=\"activity-description\">{activity.description}</div>\n                </li>\n              ))}\n            </ul>\n          </div>\n        )}\n        \n        {activeTab === 'settings' && (\n          <div className=\"settings-tab\">\n            <h2>Account Settings</h2>\n            <form className=\"settings-form\">\n              <div className=\"form-group\">\n                <label>Email Notifications</label>\n                <input type=\"checkbox\" defaultChecked={user.settings.emailNotifications} />\n              </div>\n              <div className=\"form-group\">\n                <label>Profile Visibility</label>\n                <select defaultValue={user.settings.profileVisibility}>\n                  <option value=\"public\">Public</option>\n                  <option value=\"private\">Private</option>\n                  <option value=\"friends\">Friends Only</option>\n                </select>\n              </div>\n              <button type=\"submit\" className=\"save-button\">Save Settings</button>\n            </form>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\n// 拆分后的组件\n\n// 1. UserHeader组件\nfunction UserHeader({ user }) {\n  return (\n    <header className=\"dashboard-header\">\n      <h1>Welcome, {user.name}</h1>\n      <UserAvatar user={user} />\n      <UserStats user={user} />\n    </header>\n  );\n}\n\n// 2. UserAvatar组件\nfunction UserAvatar({ user }) {\n  return (\n    <div className=\"user-avatar\">\n      <img src={user.avatar} alt={user.name} />\n    </div>\n  );\n}\n\n// 3. UserStats组件\nfunction UserStats({ user }) {\n  return (\n    <div className=\"user-stats\">\n      <Stat value={user.followers} label=\"Followers\" />\n      <Stat value={user.following} label=\"Following\" />\n      <Stat value={user.posts} label=\"Posts\" />\n    </div>\n  );\n}\n\n// 4. Stat组件\nfunction Stat({ value, label }) {\n  return (\n    <div className=\"stat\">\n      <span className=\"stat-value\">{value}</span>\n      <span className=\"stat-label\">{label}</span>\n    </div>\n  );\n}\n\n// 5. TabNavigation组件\nfunction TabNavigation({ activeTab, onTabChange }) {\n  const tabs = ['profile', 'activity', 'settings'];\n  \n  return (\n    <nav className=\"dashboard-nav\">\n      <ul>\n        {tabs.map(tab => (\n          <li key={tab}>\n            <button \n              className={activeTab === tab ? 'active' : ''}\n              onClick={() => onTabChange(tab)}\n            >\n              {tab.charAt(0).toUpperCase() + tab.slice(1)}\n            </button>\n          </li>\n        ))}\n      </ul>\n    </nav>\n  );\n}\n\n// 6. ProfileTab组件\nfunction ProfileTab({ user }) {\n  return (\n    <div className=\"profile-tab\">\n      <h2>Profile Information</h2>\n      <ProfileField label=\"Name:\" value={user.name} />\n      <ProfileField label=\"Email:\" value={user.email} />\n      <ProfileField label=\"Location:\" value={user.location} />\n      <ProfileField label=\"Bio:\" value={user.bio} isParagraph />\n    </div>\n  );\n}\n\n// 7. ProfileField组件\nfunction ProfileField({ label, value, isParagraph = false }) {\n  return (\n    <div className=\"profile-field\">\n      <label>{label}</label>\n      {isParagraph ? <p>{value}</p> : <span>{value}</span>}\n    </div>\n  );\n}\n\n// 8. ActivityTab组件\nfunction ActivityTab({ activities }) {\n  return (\n    <div className=\"activity-tab\">\n      <h2>Recent Activity</h2>\n      <ul className=\"activity-list\">\n        {activities.map(activity => (\n          <ActivityItem key={activity.id} activity={activity} />\n        ))}\n      </ul>\n    </div>\n  );\n}\n\n// 9. ActivityItem组件\nfunction ActivityItem({ activity }) {\n  return (\n    <li className=\"activity-item\">\n      <div className=\"activity-time\">{activity.time}</div>\n      <div className=\"activity-description\">{activity.description}</div>\n    </li>\n  );\n}\n\n// 10. SettingsTab组件\nfunction SettingsTab({ userSettings, onSave }) {\n  return (\n    <div className=\"settings-tab\">\n      <h2>Account Settings</h2>\n      <SettingsForm settings={userSettings} onSave={onSave} />\n    </div>\n  );\n}\n\n// 11. SettingsForm组件\nfunction SettingsForm({ settings, onSave }) {\n  const [formState, setFormState] = useState(settings);\n  \n  const handleSubmit = (e) => {\n    e.preventDefault();\n    onSave(formState);\n  };\n  \n  return (\n    <form className=\"settings-form\" onSubmit={handleSubmit}>\n      <div className=\"form-group\">\n        <label>Email Notifications</label>\n        <input \n          type=\"checkbox\" \n          checked={formState.emailNotifications}\n          onChange={(e) => setFormState({\n            ...formState,\n            emailNotifications: e.target.checked\n          })} \n        />\n      </div>\n      <div className=\"form-group\">\n        <label>Profile Visibility</label>\n        <select \n          value={formState.profileVisibility}\n          onChange={(e) => setFormState({\n            ...formState,\n            profileVisibility: e.target.value\n          })}\n        >\n          <option value=\"public\">Public</option>\n          <option value=\"private\">Private</option>\n          <option value=\"friends\">Friends Only</option>\n        </select>\n      </div>\n      <button type=\"submit\" className=\"save-button\">Save Settings</button>\n    </form>\n  );\n}\n\n// 重构后的主组件\nfunction UserDashboard({ user }) {\n  const [activeTab, setActiveTab] = useState('profile');\n  const [userSettings, setUserSettings] = useState(user.settings);\n  \n  const handleSaveSettings = (newSettings) => {\n    setUserSettings(newSettings);\n    // 在这里可以调用API保存设置\n    console.log('Saving settings:', newSettings);\n  };\n  \n  return (\n    <div className=\"dashboard\">\n      <UserHeader user={user} />\n      <TabNavigation activeTab={activeTab} onTabChange={setActiveTab} />\n      \n      <div className=\"dashboard-content\">\n        {activeTab === 'profile' && <ProfileTab user={user} />}\n        {activeTab === 'activity' && <ActivityTab activities={user.activities} />}\n        {activeTab === 'settings' && (\n          <SettingsTab \n            userSettings={userSettings} \n            onSave={handleSaveSettings} \n          />\n        )}\n      </div>\n    </div>\n  );\n}", "explanation": "这个例子展示了组件拆分的过程，将一个大型、复杂的UserDashboard组件拆分为多个更小、更专注的组件。拆分后的组件结构更清晰，每个组件只负责一个特定功能，更容易理解、测试和维护。组件拆分遵循单一职责原则，提高了代码的可重用性和可组合性。"}]}}]}