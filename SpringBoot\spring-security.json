{"name": "Spring Security", "trans": ["Spring Security"], "methods": [{"name": "Dependency and Basic Configuration", "trans": ["依赖与基本配置"], "usage": {"syntax": "pom.xml添加spring-boot-starter-security依赖，配置基本参数", "description": "Spring Boot通过spring-boot-starter-security集成Spring Security，需配置安全相关参数。", "parameters": [{"name": "spring-boot-starter-security", "description": "安全核心依赖"}, {"name": "spring.security.*", "description": "安全相关配置参数"}], "returnValue": "自动配置的安全拦截与认证机制", "examples": [{"code": "<!-- pom.xml -->\n<dependency>\n  <groupId>org.springframework.boot</groupId>\n  <artifactId>spring-boot-starter-security</artifactId>\n</dependency>\n\n# application.properties\nspring.security.user.name=admin\nspring.security.user.password=123456", "explanation": "引入依赖后，默认启用表单登录和基本认证。"}]}}, {"name": "User Authentication and Authorization", "trans": ["用户认证与授权"], "usage": {"syntax": "实现UserDetailsService和配置HttpSecurity实现认证与授权", "description": "通过实现UserDetailsService接口自定义用户认证逻辑，配置HttpSecurity实现URL权限控制。", "parameters": [{"name": "UserDetailsService", "description": "自定义用户认证逻辑"}, {"name": "HttpSecurity", "description": "配置URL访问权限"}], "returnValue": "自定义的认证与授权机制", "examples": [{"code": "@Service\npublic class MyUserDetailsService implements UserDetailsService {\n    @Override\n    public UserDetails loadUserByUsername(String username) {\n        // 查询用户并返回UserDetails\n    }\n}\n\n@Configuration\npublic class SecurityConfig extends WebSecurityConfigurerAdapter {\n    @Override\n    protected void configure(HttpSecurity http) throws Exception {\n        http.authorizeRequests().antMatchers(\"/admin/**\").hasRole(\"ADMIN\")\n            .anyRequest().authenticated();\n    }\n}", "explanation": "自定义用户认证和URL权限控制。"}]}}, {"name": "Custom Login and Logout", "trans": ["自定义登录/登出"], "usage": {"syntax": "配置HttpSecurity自定义登录页、登出逻辑", "description": "可通过HttpSecurity配置自定义登录页面、登录成功/失败处理、登出URL等。", "parameters": [{"name": "loginPage", "description": "自定义登录页URL"}, {"name": "logoutUrl", "description": "自定义登出URL"}], "returnValue": "自定义的登录与登出流程", "examples": [{"code": "@Configuration\npublic class SecurityConfig extends WebSecurityConfigurerAdapter {\n    @Override\n    protected void configure(HttpSecurity http) throws Exception {\n        http.formLogin().loginPage(\"/login\").permitAll()\n            .defaultSuccessUrl(\"/index\")\n            .failureUrl(\"/login?error\");\n        http.logout().logoutUrl(\"/logout\").logoutSuccessUrl(\"/login\");\n    }\n}", "explanation": "可自定义登录页、成功/失败跳转和登出逻辑。"}]}}, {"name": "Permission Annotations (@PreAuthorize etc.)", "trans": ["权限注解（@PreAuthorize等）"], "usage": {"syntax": "@PreAuthorize/@Secured注解实现方法级权限控制", "description": "Spring Security支持@PreAuthorize、@Secured等注解实现方法级权限校验，需开启@EnableGlobalMethodSecurity。", "parameters": [{"name": "@PreAuthorize", "description": "表达式方式权限控制"}, {"name": "@Secured", "description": "基于角色的权限控制"}], "returnValue": "方法级权限校验结果", "examples": [{"code": "@PreAuthorize(\"hasRole('ADMIN')\")\npublic void adminMethod() {\n    // 仅管理员可访问\n}\n@Secured(\"ROLE_USER\")\npublic void userMethod() {\n    // 仅普通用户可访问\n}", "explanation": "@PreAuthorize和@Secured可灵活实现方法权限控制。"}]}}, {"name": "Password Encryption and Storage", "trans": ["密码加密与存储"], "usage": {"syntax": "使用PasswordEncoder接口实现密码加密与校验", "description": "Spring Security推荐使用PasswordEncoder接口（如BCryptPasswordEncoder）对密码加密存储，提升安全性。", "parameters": [{"name": "PasswordEncoder", "description": "密码加密与校验接口"}], "returnValue": "加密后的密码字符串", "examples": [{"code": "@Bean\npublic PasswordEncoder passwordEncoder() {\n    return new BCryptPasswordEncoder();\n}\n// 加密密码\nString hash = passwordEncoder.encode(\"123456\");\n// 校验密码\nboolean matches = passwordEncoder.matches(\"123456\", hash);", "explanation": "BCryptPasswordEncoder可安全加密和校验密码。"}]}}, {"name": "Session Management", "trans": ["会话管理"], "usage": {"syntax": "HttpSecurity配置sessionManagement相关参数", "description": "可通过HttpSecurity配置最大会话数、并发登录、会话失效等参数，提升系统安全性。", "parameters": [{"name": "maximumSessions", "description": "最大会话数"}, {"name": "expiredUrl", "description": "会话失效跳转URL"}], "returnValue": "受控的会话管理机制", "examples": [{"code": "@Configuration\npublic class SecurityConfig extends WebSecurityConfigurerAdapter {\n    @Override\n    protected void configure(HttpSecurity http) throws Exception {\n        http.sessionManagement().maximumSessions(1).expiredUrl(\"/login?expired\");\n    }\n}", "explanation": "可限制同一账号同时在线数，提升安全性。"}]}}, {"name": "CSRF Protection", "trans": ["跨站请求伪造（CSRF）防护"], "usage": {"syntax": "HttpSecurity默认开启CSRF防护，可自定义配置", "description": "Spring Security默认开启CSRF防护，防止恶意网站伪造请求攻击。可通过HttpSecurity自定义关闭或配置。", "parameters": [{"name": "csrf", "description": "CSRF防护配置对象"}], "returnValue": "受保护的表单和接口请求", "examples": [{"code": "@Configuration\npublic class SecurityConfig extends WebSecurityConfigurerAdapter {\n    @Override\n    protected void configure(HttpSecurity http) throws Exception {\n        http.csrf().disable(); // 关闭CSRF防护\n    }\n}", "explanation": "可根据实际需求开启或关闭CSRF防护。"}]}}, {"name": "Spring Security Assignment", "trans": ["Spring Security练习"], "usage": {"syntax": "# Spring Security练习", "description": "完成以下练习，巩固Spring Boot安全相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 配置Spring Security依赖和参数。\n2. 实现自定义用户认证与授权。\n3. 自定义登录页和登出逻辑。\n4. 用@PreAuthorize实现方法权限控制。\n5. 实现密码加密与校验。\n6. 配置会话管理和CSRF防护。", "explanation": "通过这些练习掌握Spring Boot安全开发的核心用法。"}]}}]}