{"name": "Context API", "trans": ["Context API"], "methods": [{"name": "Context Design Pattern", "trans": ["Context设计模式"], "usage": {"syntax": "const MyContext = React.createContext(defaultValue);", "description": "Context API用于在组件树中跨层级传递数据，避免多层props传递，适合全局状态、主题、用户信息等场景。", "parameters": [{"name": "defaultValue", "description": "Context的默认值，未被Provider包裹时生效"}], "returnValue": "返回一个包含Provider和Consumer的Context对象", "examples": [{"code": "// 创建一个Context对象，默认值为null\nconst UserContext = React.createContext(null);\n// UserContext.Provider和UserContext.Consumer可用于后续组件树中数据传递", "explanation": "通过createContext创建Context对象，便于全局共享数据。"}]}}, {"name": "Creating and Providing Context", "trans": ["创建和提供Context"], "usage": {"syntax": "<MyContext.Provider value={value}>\n  {/* 子组件树 */}\n</MyContext.Provider>", "description": "通过Provider组件将数据注入到Context，所有后代组件都可消费该数据。通常Provider包裹整个需要访问Context的组件树。", "parameters": [{"name": "value", "description": "要传递给后代组件的上下文数据"}], "returnValue": "渲染包裹的子组件树，子组件可访问Context值", "examples": [{"code": "// 创建Context对象\nconst ThemeContext = React.createContext('light');\n// 顶层组件提供Context\nfunction App() {\n  // 主题值可以是state、props或常量\n  const theme = 'dark';\n  return (\n    // 用Provider包裹需要访问Context的组件\n    <ThemeContext.Provider value={theme}>\n      <Toolbar /> // Toolbar及其后代都能访问theme\n    </ThemeContext.Provider>\n  );\n}\n// Toolbar组件内部无需显式传递theme", "explanation": "通过Provider将数据注入Context，后代组件可直接消费。"}]}}, {"name": "Consuming Context", "trans": ["消费Context"], "usage": {"syntax": "const value = React.useContext(MyContext);", "description": "通过useContext钩子或Context.Consumer组件消费Context数据。useContext更简洁，推荐在函数组件中使用。", "parameters": [{"name": "MyContext", "description": "要消费的Context对象"}], "returnValue": "当前Context的值", "examples": [{"code": "// 创建Context对象\nconst UserContext = React.createContext();\n// 在函数组件中消费Context\nfunction UserProfile() {\n  // 通过useContext获取Context的当前值\n  const user = React.useContext(UserContext);\n  // 判断user是否存在\n  if (!user) return <div>未登录</div>;\n  return <div>用户名: {user.name}</div>;\n}\n// 用法\nfunction App() {\n  const user = { name: '张三' };\n  return (\n    <UserContext.Provider value={user}>\n      <UserProfile />\n    </UserContext.Provider>\n  );\n}", "explanation": "通过useContext钩子消费Context，获取全局用户信息。"}]}}, {"name": "Dynamic Context", "trans": ["动态Context"], "usage": {"syntax": "<MyContext.Provider value={state}>...</MyContext.Provider>", "description": "Context的value可以是state、props或计算值，实现动态响应。常用于主题切换、登录状态等场景。", "parameters": [{"name": "value", "description": "动态变化的上下文数据"}], "returnValue": "动态变化的Context值", "examples": [{"code": "// 创建ThemeContext对象，默认值为'light'\nconst ThemeContext = React.createContext('light');\n\n// 动态切换主题的组件\nfunction ThemeSwitcher() {\n  // 用useState管理主题状态\n  const [theme, setTheme] = React.useState('light');\n  // 切换主题的函数\n  function toggleTheme() {\n    setTheme(t => (t === 'light' ? 'dark' : 'light'));\n  }\n  return (\n    // Provider的value为动态state，所有后代组件都能响应theme变化\n    <ThemeContext.Provider value={theme}>\n      {/* 切换主题按钮 */}\n      <button onClick={toggleTheme}>切换主题</button>\n      {/* 显示当前主题的组件 */}\n      <ThemeDisplay />\n    </ThemeContext.Provider>\n  );\n}\n\n// 消费Context的组件\nfunction ThemeDisplay() {\n  // 通过useContext获取当前主题\n  const theme = React.useContext(ThemeContext);\n  return <div>当前主题: {theme}</div>;\n}\n\n// 用法示例\nfunction App() {\n  return <ThemeSwitcher />;\n}", "explanation": "通过state动态控制Context，实现主题切换。"}]}}, {"name": "Nested Contexts", "trans": ["嵌套Context"], "usage": {"syntax": "<AContext.Provider value={a}>\n  <BContext.Provider value={b}>\n    {/* 子组件 */}\n  </BContext.Provider>\n</AContext.Provider>", "description": "多个Context可嵌套使用，子组件可分别消费不同的Context，适合复杂全局状态管理。", "parameters": [{"name": "AContext", "description": "第一个Context对象"}, {"name": "BContext", "description": "第二个Context对象"}], "returnValue": "多个Context的值", "examples": [{"code": "// 创建两个Context对象\nconst LangContext = React.createContext('zh');\nconst ThemeContext = React.createContext('light');\n// 嵌套Provider\nfunction App() {\n  return (\n    <LangContext.Provider value=\"en\"> // 提供语言Context\n      <ThemeContext.Provider value=\"dark\"> // 提供主题Context\n        <Page /> // Page可消费两个Context\n      </ThemeContext.Provider>\n    </LangContext.Provider>\n  );\n}\n// 消费多个Context\nfunction Page() {\n  // 分别消费两个Context\n  const lang = React.useContext(LangContext);\n  const theme = React.useContext(ThemeContext);\n  return <div>语言: {lang}, 主题: {theme}</div>;\n}", "explanation": "通过嵌套Provider和useContext消费多个全局状态。"}]}}, {"name": "Performance Optimization", "trans": ["性能优化"], "usage": {"syntax": "// 拆分Context，避免不必要的重渲染", "description": "Context更新会导致所有消费组件重渲染。可通过拆分Context、memo、选择性传递value等方式优化性能。", "parameters": [], "returnValue": "优化后的Context使用方式", "examples": [{"code": "// 拆分Context，分别管理不同数据\nconst UserContext = React.createContext();\nconst ThemeContext = React.createContext();\n// 只在需要的地方消费对应Context\nfunction App() {\n  const [user] = React.useState({ name: '张三' });\n  const [theme] = React.useState('dark');\n  return (\n    <UserContext.Provider value={user}>\n      <ThemeContext.Provider value={theme}>\n        <UserProfile />\n        <ThemeDisplay />\n      </ThemeContext.Provider>\n    </UserContext.Provider>\n  );\n}\n// 只消费UserContext，theme变化不会导致重渲染\nfunction UserProfile() {\n  const user = React.useContext(UserContext);\n  return <div>用户: {user.name}</div>;\n}\n// 只消费ThemeContext，user变化不会导致重渲染\nfunction ThemeDisplay() {\n  const theme = React.useContext(ThemeContext);\n  return <div>主题: {theme}</div>;\n}", "explanation": "通过拆分Context和精准消费，减少不必要的组件重渲染。"}]}}, {"name": "作业：实现一个多主题切换的Context方案", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 实现一个ThemeContext，支持light/dark切换。\n// 2. 提供切换按钮，所有消费组件自动响应。\n// 3. 代码结构清晰，注释完整。", "description": "请实现上述多主题切换Context方案，提交时请附上完整代码和注释。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 示例代码略，请学生自行实现\n// 提示：用useState和useContext实现。", "explanation": "作业要求学生掌握Context的动态切换和全局响应。"}, {"code": "import React, { useState, useContext, createContext } from 'react';\n// 创建ThemeContext对象\nconst ThemeContext = createContext('light');\n// 主题切换按钮组件\nfunction ThemeToggle() {\n  // 获取setTheme函数用于切换主题\n  const { theme, setTheme } = useContext(ThemeContext);\n  return (\n    <button onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}>\n      切换主题\n    </button>\n  );\n}\n// 显示当前主题的组件\nfunction ThemeDisplay() {\n  // 获取当前主题\n  const { theme } = useContext(ThemeContext);\n  return <div>当前主题: {theme}</div>;\n}\n// 顶层组件，提供Context\nfunction App() {\n  // 主题状态\n  const [theme, setTheme] = useState('light');\n  // Context值为对象，包含theme和setTheme\n  const contextValue = { theme, setTheme };\n  return (\n    // Provider包裹所有需要访问主题的组件\n    <ThemeContext.Provider value={contextValue}>\n      <ThemeToggle />\n      <ThemeDisplay />\n    </ThemeContext.Provider>\n  );\n}", "explanation": "完整实现多主题切换Context，所有消费组件自动响应主题变化。"}]}}]}