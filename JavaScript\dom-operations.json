{"name": "DOM Operations", "trans": ["DOM操作"], "methods": [{"name": "Node Selection & Traversal", "trans": ["节点获取与遍历"], "usage": {"syntax": "document.getElementById('id');\ndocument.querySelector('selector');\ndocument.querySelectorAll('selector');\nparentNode.children;\nnode.parentElement;\nnode.nextSibling / node.nextElementSibling;", "description": "DOM提供多种方法获取和遍历节点。getElementById获取单个元素，querySelector/querySelectorAll使用CSS选择器，children/parentElement/nextElementSibling用于节点间导航。", "parameters": [{"name": "selector", "description": "CSS选择器字符串，如'#id'、'.class'、'div > p'等。"}, {"name": "id", "description": "元素的id属性值。"}], "returnValue": "返回HTMLElement、NodeList或HTMLCollection对象。", "examples": [{"code": "// 获取元素\nconst main = document.getElementById('main');\nconst firstBtn = document.querySelector('button');\nconst allBtns = document.querySelectorAll('.btn');\n\n// 遍历元素\nallBtns.forEach(btn => {\n  console.log(btn.textContent);\n});\n\n// 节点导航\nconst parent = firstBtn.parentElement;\nconst nextEl = firstBtn.nextElementSibling;\nconst children = parent.children;\n\n// 从父元素查找子元素\nconst nested = main.querySelector('.nested');", "explanation": "展示多种获取元素的方法和在DOM树中导航的方式。"}, {"code": "// 遍历DOM树\nfunction traverseDOM(element, callback, level = 0) {\n  callback(element, level);\n  \n  for (let i = 0; i < element.children.length; i++) {\n    traverseDOM(element.children[i], callback, level + 1);\n  }\n}\n\n// 使用示例\ntraverseDOM(document.body, (el, level) => {\n  console.log('层级:', level, '标签:', el.tagName, \n            '类名:', el.className);\n});", "explanation": "递归遍历DOM树，对每个元素执行回调函数。"}]}}, {"name": "Node CRUD Operations", "trans": ["节点增删改查"], "usage": {"syntax": "// 创建\ndocument.createElement('tagName');\n// 插入\nparent.appendChild(node);\nparent.insertBefore(node, referenceNode);\n// 替换\nparent.replaceChild(newNode, oldNode);\n// 删除\nparent.removeChild(node);\nnode.remove();", "description": "DOM提供创建、插入、替换和删除节点的方法。createElement创建元素，appendChild/insertBefore添加节点，replaceChild替换节点，removeChild/remove删除节点。", "parameters": [{"name": "tagName", "description": "要创建的HTML元素标签名。"}, {"name": "node/newNode", "description": "要插入、替换或删除的节点。"}, {"name": "referenceNode", "description": "插入新节点的参考位置。"}, {"name": "oldNode", "description": "要被替换的节点。"}], "returnValue": "创建方法返回新元素，插入/替换方法返回被插入/替换的节点。", "examples": [{"code": "// 创建元素\nconst div = document.createElement('div');\ndiv.className = 'container';\ndiv.textContent = '新元素';\n\n// 插入元素\nconst parent = document.getElementById('parent');\nparent.appendChild(div);\n\n// 在特定位置插入\nconst firstChild = parent.firstElementChild;\nconst newEl = document.createElement('p');\nnewEl.textContent = '插入到第一个子元素之前';\nparent.insertBefore(newEl, firstChild);\n\n// 替换元素\nconst oldEl = document.getElementById('old');\nconst replacement = document.createElement('span');\nreplacement.textContent = '替换后的内容';\noldEl.parentNode.replaceChild(replacement, oldEl);\n\n// 删除元素\nconst toRemove = document.getElementById('remove');\n// 两种方式\ntoRemove.parentNode.removeChild(toRemove); // 旧方式\n// 或\ntoRemove.remove(); // 新方式", "explanation": "展示DOM节点的创建、插入、替换和删除操作。"}, {"code": "// 使用DocumentFragment提高性能\nfunction createList(items) {\n  const fragment = document.createDocumentFragment();\n  \n  items.forEach(item => {\n    const li = document.createElement('li');\n    li.textContent = item;\n    fragment.appendChild(li);\n  });\n  \n  return fragment;\n}\n\n// 使用示例\nconst list = document.getElementById('myList');\nconst items = ['苹果', '香蕉', '橙子', '葡萄'];\nlist.appendChild(createList(items));", "explanation": "使用DocumentFragment批量创建元素，减少DOM重绘次数，提高性能。"}]}}, {"name": "Attributes & Style Manipulation", "trans": ["属性与样式操作"], "usage": {"syntax": "// 属性操作\nelement.getAttribute('attr');\nelement.setAttribute('attr', 'value');\nelement.removeAttribute('attr');\n// 直接属性访问\nelement.id = 'newId';\n// 样式操作\nelement.style.property = 'value';\ngetComputedStyle(element).property;", "description": "DOM提供多种操作元素属性和样式的方法。getAttribute/setAttribute/removeAttribute操作标准属性，也可直接访问属性，style对象修改内联样式，getComputedStyle获取计算后样式。", "parameters": [{"name": "attr", "description": "HTML属性名称。"}, {"name": "value", "description": "设置的属性值。"}, {"name": "property", "description": "CSS属性名称（使用驼峰命名）。"}], "returnValue": "getAttribute返回属性值，getComputedStyle返回CSSStyleDeclaration对象。", "examples": [{"code": "// 属性操作\nconst link = document.querySelector('a');\n\n// 获取属性\nconst href = link.getAttribute('href');\n\n// 设置属性\nlink.setAttribute('target', '_blank');\nlink.setAttribute('data-custom', 'value');\n\n// 删除属性\nlink.removeAttribute('title');\n\n// 直接属性访问\nlink.id = 'main-link';\nlink.href = 'https://example.com';\n\n// 样式操作\nconst box = document.getElementById('box');\n\n// 设置内联样式\nbox.style.backgroundColor = 'red';\nbox.style.width = '100px';\nbox.style.padding = '10px';\nbox.style.borderRadius = '5px'; // 注意使用驼峰命名\n\n// 获取计算样式\nconst styles = getComputedStyle(box);\nconsole.log(styles.width);\nconsole.log(styles.backgroundColor);", "explanation": "展示获取、设置、删除属性，以及操作元素样式的多种方法。"}, {"code": "// 批量设置样式\nfunction setStyles(element, stylesObj) {\n  Object.entries(stylesObj).forEach(([prop, value]) => {\n    element.style[prop] = value;\n  });\n}\n\n// 使用示例\nconst card = document.querySelector('.card');\nsetStyles(card, {\n  display: 'flex',\n  flexDirection: 'column',\n  padding: '16px',\n  boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n  borderRadius: '8px'\n});", "explanation": "创建辅助函数批量设置样式，提高代码可读性和维护性。"}]}}, {"name": "classList & dataset", "trans": ["classList与dataset"], "usage": {"syntax": "// classList操作\nelement.classList.add('class');\nelement.classList.remove('class');\nelement.classList.toggle('class');\nelement.classList.contains('class');\n// dataset操作\nelement.dataset.name = 'value';\nconst value = element.dataset.name;", "description": "classList提供类名操作方法，比直接修改className更方便。dataset提供访问data-*自定义属性的简便方式，自动处理命名转换。", "parameters": [{"name": "class", "description": "CSS类名。"}, {"name": "name", "description": "data-*属性名（不含data-前缀）。"}, {"name": "value", "description": "设置的属性值。"}], "returnValue": "classList.contains返回布尔值，dataset访问返回字符串。", "examples": [{"code": "// classList操作\nconst card = document.querySelector('.card');\n\n// 添加类\ncard.classList.add('active');\ncard.classList.add('highlight', 'visible'); // 可添加多个\n\n// 删除类\ncard.classList.remove('inactive');\n\n// 切换类（有则删，无则加）\ncard.classList.toggle('selected'); // 添加selected\ncard.classList.toggle('selected'); // 删除selected\ncard.classList.toggle('expanded', window.innerWidth > 768); // 条件切换\n\n// 检查是否包含类\nif (card.classList.contains('active')) {\n  console.log('卡片处于激活状态');\n}\n\n// 替换类\ncard.classList.replace('old-class', 'new-class');", "explanation": "展示classList的添加、删除、切换、检查和替换类的方法。"}, {"code": "// dataset操作\nconst user = document.querySelector('.user');\n\n// 设置data属性\nuser.dataset.id = '123';\nuser.dataset.userName = 'john_doe'; // 自动转换为data-user-name\nuser.dataset.lastLogin = '2023-01-15';\n\n// 获取data属性\nconsole.log(user.dataset.id); // '123'\nconsole.log(user.dataset.userName); // 'john_doe'\n\n// 检查data属性是否存在\nif ('lastLogin' in user.dataset) {\n  console.log('上次登录时间:', user.dataset.lastLogin);\n}\n\n// 删除data属性\ndelete user.dataset.lastLogin;\n\n// HTML中的等效表示\n// <div class=\"user\" data-id=\"123\" data-user-name=\"john_doe\"></div>", "explanation": "展示dataset API操作data-*自定义属性的方法，包括设置、获取和删除。"}]}}, {"name": "Event Binding & Delegation", "trans": ["事件绑定与委托"], "usage": {"syntax": "// 事件绑定\nelement.addEventListener('event', handler, options);\nelement.removeEventListener('event', handler);\n// 事件委托\nparent.addEventListener('event', e => {\n  if (e.target.matches('selector')) {\n    // 处理事件\n  }\n});", "description": "addEventListener添加事件监听器，removeEventListener移除监听器。事件委托是将事件监听器添加到父元素，利用事件冒泡处理子元素事件，适合动态元素和大量元素。", "parameters": [{"name": "event", "description": "事件类型，如'click'、'input'、'submit'等。"}, {"name": "handler", "description": "事件处理函数，接收Event对象作为参数。"}, {"name": "options", "description": "可选配置对象，如{capture: true, once: true, passive: true}。"}], "returnValue": "无返回值。", "examples": [{"code": "// 基本事件绑定\nconst button = document.querySelector('#submitBtn');\n\nfunction handleClick(event) {\n  event.preventDefault(); // 阻止默认行为\n  console.log('按钮被点击');\n  console.log('事件对象:', event);\n}\n\n// 添加事件监听器\nbutton.addEventListener('click', handleClick);\n\n// 移除事件监听器\n// button.removeEventListener('click', handleClick);\n\n// 只触发一次的事件\nbutton.addEventListener('click', e => {\n  console.log('这个处理函数只会执行一次');\n}, { once: true });\n\n// 阻止事件冒泡\nconst inner = document.querySelector('.inner');\ninner.addEventListener('click', e => {\n  e.stopPropagation();\n  console.log('内部元素被点击，不会冒泡');\n});", "explanation": "展示添加和移除事件监听器，以及事件对象的常用方法。"}, {"code": "// 事件委托\nconst list = document.querySelector('#taskList');\n\n// 为父元素添加一个事件监听器\nlist.addEventListener('click', event => {\n  // 检查点击的是否为按钮\n  if (event.target.matches('.delete-btn')) {\n    const taskItem = event.target.closest('li');\n    console.log('删除任务:', taskItem.textContent);\n    taskItem.remove();\n  } else if (event.target.matches('.edit-btn')) {\n    const taskItem = event.target.closest('li');\n    console.log('编辑任务:', taskItem.textContent);\n    // 编辑逻辑\n  } else if (event.target.matches('li') || event.target.matches('.task-text')) {\n    const taskItem = event.target.closest('li');\n    taskItem.classList.toggle('completed');\n  }\n});\n\n// 添加新任务（动态添加的元素无需单独绑定事件）\nfunction addTask(text) {\n  const li = document.createElement('li');\n  li.innerHTML = `\n    <span class=\"task-text\">${text}</span>\n    <button class=\"edit-btn\">编辑</button>\n    <button class=\"delete-btn\">删除</button>\n  `;\n  list.appendChild(li);\n}", "explanation": "使用事件委托处理列表项的点击事件，适用于动态添加的元素。"}]}}, {"name": "Form & Input Handling", "trans": ["表单与输入处理"], "usage": {"syntax": "// 表单获取\ndocument.forms['formName'];\ndocument.getElementById('formId');\n// 表单事件\nform.addEventListener('submit', handler);\n// 表单元素\nform.elements['inputName'];\ninput.value;\ncheckbox.checked;\nselect.selectedIndex;\nselect.options[index].value;", "description": "DOM提供多种方式获取和处理表单数据。可通过forms集合或常规选择器获取表单，通过elements集合或选择器获取表单元素，监听submit事件处理表单提交。", "parameters": [{"name": "formName/formId", "description": "表单的name或id属性。"}, {"name": "inputName", "description": "表单元素的name属性。"}, {"name": "handler", "description": "事件处理函数。"}], "returnValue": "表单元素的value、checked等属性返回相应值。", "examples": [{"code": "// 获取表单和表单元素\nconst form = document.forms['userForm']; // 通过name获取\n// 或\nconst form = document.getElementById('userForm'); // 通过ID获取\n\n// 获取表单元素\nconst username = form.elements['username']; // 通过name获取\nconst email = form.querySelector('#email'); // 通过选择器获取\n\n// 获取/设置表单值\nconsole.log(username.value);\nemail.value = '<EMAIL>';\n\n// 处理不同类型的输入\nconst remember = form.elements['remember']; // 复选框\nconsole.log(remember.checked);\n\nconst role = form.elements['role']; // 下拉选择框\nconsole.log(role.value);\nconsole.log(role.options[role.selectedIndex].text);\n\nconst gender = form.elements['gender']; // 单选按钮组\nlet selectedGender;\nfor (const radio of gender) {\n  if (radio.checked) {\n    selectedGender = radio.value;\n    break;\n  }\n}", "explanation": "展示获取表单和表单元素，以及处理不同类型输入的方法。"}, {"code": "// 表单验证与提交\nconst form = document.getElementById('registrationForm');\n\nform.addEventListener('submit', function(event) {\n  event.preventDefault(); // 阻止默认提交\n  \n  // 获取表单数据\n  const formData = new FormData(form);\n  const userData = Object.fromEntries(formData);\n  \n  // 验证表单\n  let isValid = true;\n  const errors = {};\n  \n  if (!userData.username || userData.username.length < 3) {\n    errors.username = '用户名至少需要3个字符';\n    isValid = false;\n  }\n  \n  if (!userData.email || !userData.email.includes('@')) {\n    errors.email = '请输入有效的邮箱地址';\n    isValid = false;\n  }\n  \n  if (!userData.password || userData.password.length < 6) {\n    errors.password = '密码至少需要6个字符';\n    isValid = false;\n  }\n  \n  // 显示错误或提交表单\n  if (isValid) {\n    console.log('表单数据:', userData);\n    // 这里可以发送AJAX请求或提交表单\n    // form.submit();\n  } else {\n    // 显示错误信息\n    displayErrors(errors);\n  }\n});\n\nfunction displayErrors(errors) {\n  // 清除所有错误\n  document.querySelectorAll('.error-message').forEach(el => el.remove());\n  \n  // 显示新错误\n  for (const [field, message] of Object.entries(errors)) {\n    const input = form.elements[field];\n    const errorDiv = document.createElement('div');\n    errorDiv.className = 'error-message';\n    errorDiv.textContent = message;\n    input.parentNode.insertBefore(errorDiv, input.nextSibling);\n    input.classList.add('error');\n  }\n}", "explanation": "展示表单验证和提交处理，包括阻止默认提交、收集表单数据、验证和显示错误。"}]}}, {"name": "作业：交互式待办事项列表", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 创建待办事项列表\n// 2. 实现添加、编辑、删除、标记完成功能\n// 3. 使用事件委托处理交互", "description": "通过实现交互式待办事项列表，综合应用DOM操作、事件处理、表单处理等知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 创建HTML结构\n// 2. 实现添加任务功能\n// 3. 实现任务状态切换\n// 4. 实现删除和编辑功能", "explanation": "作业提示，需结合本节内容完成实现。"}, {"code": "// 正确实现示例\n// HTML结构\n/*\n<div class=\"todo-app\">\n  <form id=\"task-form\">\n    <input type=\"text\" id=\"task-input\" placeholder=\"添加新任务...\" required>\n    <button type=\"submit\">添加</button>\n  </form>\n  <ul id=\"task-list\"></ul>\n</div>\n*/\n\n// JavaScript实现\nconst taskForm = document.getElementById('task-form');\nconst taskInput = document.getElementById('task-input');\nconst taskList = document.getElementById('task-list');\n\n// 添加任务\ntaskForm.addEventListener('submit', function(e) {\n  e.preventDefault();\n  \n  if (taskInput.value.trim() === '') return;\n  \n  addTask(taskInput.value.trim());\n  taskInput.value = '';\n});\n\n// 使用事件委托处理任务操作\ntaskList.addEventListener('click', function(e) {\n  const target = e.target;\n  const taskItem = target.closest('li');\n  \n  if (!taskItem) return;\n  \n  // 处理完成状态切换\n  if (target.matches('.task-text')) {\n    taskItem.classList.toggle('completed');\n  }\n  \n  // 处理删除\n  if (target.matches('.delete-btn')) {\n    taskItem.remove();\n  }\n  \n  // 处理编辑\n  if (target.matches('.edit-btn')) {\n    const taskText = taskItem.querySelector('.task-text');\n    const currentText = taskText.textContent;\n    \n    const newText = prompt('编辑任务:', currentText);\n    if (newText !== null && newText.trim() !== '') {\n      taskText.textContent = newText.trim();\n    }\n  }\n});\n\n// 添加任务函数\nfunction addTask(text) {\n  const li = document.createElement('li');\n  li.innerHTML = `\n    <span class=\"task-text\">${text}</span>\n    <div class=\"task-actions\">\n      <button class=\"edit-btn\">编辑</button>\n      <button class=\"delete-btn\">删除</button>\n    </div>\n  `;\n  taskList.appendChild(li);\n}", "explanation": "完整实现交互式待办事项列表，包含添加、编辑、删除和标记完成功能，使用事件委托处理交互。"}]}}]}