{"name": "Architecture and Principles", "trans": ["基本架构与原理"], "methods": [{"name": "Single-threaded Model", "trans": ["单线程模型"], "usage": {"syntax": "Redis 采用单线程事件驱动架构，所有命令串行执行。", "description": "Redis 使用单线程处理客户端请求，避免了多线程锁竞争，保证了高性能和数据一致性。适合高并发读写场景。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 查看Redis进程\nps aux | grep redis\n# 只会有一个主进程负责命令处理", "explanation": "展示了Redis单线程运行的特点。"}]}}, {"name": "Memory Management Mechanism", "trans": ["内存管理机制"], "usage": {"syntax": "Redis 所有数据存储于内存，采用高效的数据结构和内存分配策略。", "description": "Redis 通过内存分配器（如jemalloc）管理内存，支持最大内存限制和LRU/LFU等淘汰策略，保证高效利用内存资源。", "parameters": [{"name": "maxmemory", "description": "最大内存限制"}, {"name": "maxmemory-policy", "description": "内存淘汰策略，如volatile-lru、allkeys-lru等"}], "returnValue": "高效的内存使用和数据淘汰机制", "examples": [{"code": "# 配置最大内存和淘汰策略\nmaxmemory 256mb\nmaxmemory-policy allkeys-lru", "explanation": "展示了内存管理相关配置。"}]}}, {"name": "Network Communication Principle", "trans": ["网络通信原理"], "usage": {"syntax": "基于TCP协议，采用RESP（Redis序列化协议）进行客户端与服务端通信。", "description": "Redis 使用TCP长连接，客户端通过RESP协议与服务端交互，支持高并发连接和高效命令解析。", "parameters": [{"name": "port", "description": "监听端口，默认6379"}, {"name": "bind", "description": "绑定IP地址"}], "returnValue": "高效的网络通信能力", "examples": [{"code": "# 查看监听端口\nnetstat -an | grep 6379\n# 使用telnet连接Redis\ntelnet 127.0.0.1 6379", "explanation": "展示了Redis网络通信的基本方式。"}]}}, {"name": "Data Persistence Principle", "trans": ["数据持久化原理"], "usage": {"syntax": "RDB快照和AOF日志两种持久化机制。", "description": "Redis 支持RDB（快照）和AOF（追加日志）两种持久化方式，保证数据在重启后可恢复。可单独或混合使用。", "parameters": [{"name": "save", "description": "RDB快照触发条件"}, {"name": "appendonly", "description": "是否开启AOF持久化"}], "returnValue": "持久化的数据文件（dump.rdb、appendonly.aof）", "examples": [{"code": "# 配置RDB和AOF\nsave 900 1\nappendonly yes", "explanation": "展示了持久化相关配置。"}]}}, {"name": "Practice: 架构与原理练习", "trans": ["架构与原理练习"], "usage": {"syntax": "# Redis架构与原理练习", "description": "完成以下练习，理解Redis的核心架构和原理。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 通过ps命令验证Redis为单线程。\n2. 配置最大内存和淘汰策略。\n3. 使用telnet连接Redis并发送命令。\n4. 配置RDB和AOF持久化。", "explanation": "通过这些练习掌握Redis的架构与原理。"}, {"code": "# 正确实现示例\n$ ps aux | grep redis\n$ redis-cli config set maxmemory 128mb\n$ redis-cli config set maxmemory-policy allkeys-lru\n$ telnet 127.0.0.1 6379\n$ redis-cli config set save '900 1'\n$ redis-cli config set appendonly yes", "explanation": "展示了练习的标准实现过程。"}]}}]}