{"name": "useRef", "trans": ["useRef钩子"], "methods": [{"name": "DOM References", "trans": ["DOM引用"], "usage": {"syntax": "const refContainer = useRef(initialValue)", "description": "useRef 是 React 的一个 Hook，用于创建一个可变的引用对象，其 .current 属性被初始化为传入的参数（initialValue）。返回的对象在组件的整个生命周期内保持不变。useRef 最常见的用途是访问 DOM 元素，可以将 ref 对象传递给 JSX 中的元素，React 会在元素挂载时设置 .current 属性为对应的 DOM 节点，在卸载时设置回 null。", "parameters": [{"name": "initialValue", "description": "ref 对象的 .current 属性的初始值，可以是任何类型"}], "returnValue": "返回一个可变的 ref 对象，其 .current 属性被设置为传入的初始值，在整个组件生命周期内保持不变", "examples": [{"code": "import React, { useRef, useEffect } from 'react';\n\nfunction TextInputWithFocusButton() {\n  // 创建一个 ref 用于存储文本输入 DOM 元素的引用\n  const inputRef = useRef(null);\n  \n  // 定义一个函数来聚焦输入框\n  const focusInput = () => {\n    // 通过 .current 访问 DOM 节点\n    if (inputRef.current) {\n      inputRef.current.focus();\n    }\n  };\n  \n  // 组件挂载后自动聚焦\n  useEffect(() => {\n    // 组件挂载后执行\n    console.log('组件已挂载，输入框引用:', inputRef.current);\n    focusInput();\n    // 没有依赖项，只在挂载时执行一次\n  }, []);\n  \n  return (\n    <div>\n      {/* 将 ref 属性设置为我们创建的 ref 对象 */}\n      <input \n        ref={inputRef} \n        type=\"text\" \n        placeholder=\"请输入文本\"\n      />\n      <button onClick={focusInput}>\n        聚焦输入框\n      </button>\n    </div>\n  );\n}", "explanation": "这个例子展示了 useRef 的基本用法，用于引用 DOM 元素。我们创建了一个 inputRef 并将其分配给输入框的 ref 属性。通过 inputRef.current 可以访问到实际的 DOM 节点，这样我们就可以调用 DOM API（如 focus()）来操作这个元素。当点击按钮时，focusInput 函数会使输入框获得焦点。此外，使用 useEffect 在组件挂载后自动聚焦输入框。"}]}}, {"name": "Accessing DOM Properties and Methods", "trans": ["访问DOM属性和方法"], "usage": {"syntax": "const ref = useRef(null);\n// 之后可以通过 ref.current 访问 DOM 属性和方法", "description": "通过 useRef 创建的引用可以访问 DOM 元素的所有原生属性和方法。这使得我们可以读取元素尺寸、滚动位置、样式值，或者调用元素的方法如 focus()、blur()、play()、pause() 等。使用 ref 访问 DOM 是一种命令式编程方式，应该尽量避免过度使用，只在声明式 React 范式无法满足需求时才使用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import React, { useRef, useState } from 'react';\n\nfunction DOMPropertiesAccess() {\n  const divRef = useRef(null);\n  const videoRef = useRef(null);\n  const inputRef = useRef(null);\n  \n  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });\n  const [inputValue, setInputValue] = useState('');\n  \n  // 获取元素尺寸\n  const measureElement = () => {\n    if (divRef.current) {\n      const { offsetWidth, offsetHeight } = divRef.current;\n      setDimensions({\n        width: offsetWidth,\n        height: offsetHeight\n      });\n    }\n  };\n  \n  // 控制视频播放\n  const toggleVideo = () => {\n    if (videoRef.current) {\n      if (videoRef.current.paused) {\n        videoRef.current.play();\n      } else {\n        videoRef.current.pause();\n      }\n    }\n  };\n  \n  // 获取输入值和选择范围\n  const getInputInfo = () => {\n    if (inputRef.current) {\n      const value = inputRef.current.value;\n      const selectionStart = inputRef.current.selectionStart;\n      const selectionEnd = inputRef.current.selectionEnd;\n      \n      setInputValue(\n        `值: \"${value}\", 选择范围: ${selectionStart}-${selectionEnd}`\n      );\n    }\n  };\n  \n  return (\n    <div>\n      <h3>访问 DOM 属性和方法</h3>\n      \n      {/* 测量元素尺寸 */}\n      <div \n        ref={divRef}\n        style={{ \n          width: '80%', \n          height: '100px',\n          backgroundColor: 'lightblue',\n          padding: '10px',\n          margin: '10px 0'\n        }}\n      >\n        可以测量我的尺寸\n      </div>\n      <button onClick={measureElement}>测量元素尺寸</button>\n      <p>尺寸: 宽度 {dimensions.width}px, 高度 {dimensions.height}px</p>\n      \n      {/* 控制视频播放 */}\n      <video \n        ref={videoRef}\n        width=\"300\"\n        controls\n        src=\"https://interactive-examples.mdn.mozilla.net/media/cc0-videos/flower.mp4\"\n      >\n        您的浏览器不支持视频标签\n      </video>\n      <button onClick={toggleVideo}>播放/暂停</button>\n      \n      {/* 获取输入值和选择范围 */}\n      <div>\n        <input \n          ref={inputRef} \n          type=\"text\" \n          defaultValue=\"Hello, React!\"\n          style={{ margin: '10px 0' }}\n        />\n        <button onClick={getInputInfo}>获取输入信息</button>\n        <p>{inputValue}</p>\n      </div>\n    </div>\n  );", "explanation": "这个例子展示了如何使用 useRef 访问不同 DOM 元素的各种属性和方法。我们创建了三个 ref：divRef 用于测量 div 元素的尺寸，videoRef 用于控制视频的播放和暂停，inputRef 用于获取输入框的值和选择范围。通过 ref.current 可以访问元素的原生属性（如 offsetWidth、offsetHeight、value、selectionStart）和调用原生方法（如 play()、pause()）。"}]}}, {"name": "Mutable Value Container", "trans": ["可变值容器"], "usage": {"syntax": "const valueRef = useRef(initialValue)", "description": "除了引用 DOM 元素外，useRef 还可以用于在组件渲染之间保存任何可变值。与 useState 不同，更新 ref 的 .current 属性不会触发组件重新渲染。这使得 useRef 非常适合存储那些不需要触发UI更新的可变数据，如定时器ID、动画帧请求ID、上一次的状态值等。ref 的内容变化时不会通知你，这是它与 state 的主要区别。", "parameters": [{"name": "initialValue", "description": "ref 对象的 .current 属性的初始值，可以是任何类型"}], "returnValue": "返回一个可变的 ref 对象，其 .current 属性被设置为传入的初始值", "examples": [{"code": "import React, { useState, useRef, useEffect } from 'react';\n\nfunction StopwatchExample() {\n  const [time, setTime] = useState(0);\n  const [isRunning, setIsRunning] = useState(false);\n  \n  // 使用 useRef 存储 interval ID\n  const intervalRef = useRef(null);\n  // 使用 useRef 存储开始时间\n  const startTimeRef = useRef(0);\n  // 使用 useRef 记录累计时间\n  const accumulatedTimeRef = useRef(0);\n  \n  // 启动计时器\n  const start = () => {\n    if (isRunning) return;\n    \n    setIsRunning(true);\n    startTimeRef.current = Date.now() - accumulatedTimeRef.current;\n    \n    // 存储 interval ID 以便后续清除\n    intervalRef.current = setInterval(() => {\n      setTime(Date.now() - startTimeRef.current);\n    }, 10);\n  };\n  \n  // 暂停计时器\n  const pause = () => {\n    if (!isRunning) return;\n    \n    clearInterval(intervalRef.current);\n    accumulatedTimeRef.current = Date.now() - startTimeRef.current;\n    setIsRunning(false);\n  };\n  \n  // 重置计时器\n  const reset = () => {\n    clearInterval(intervalRef.current);\n    setIsRunning(false);\n    setTime(0);\n    accumulatedTimeRef.current = 0;\n  };\n  \n  // 组件卸载时清除定时器\n  useEffect(() => {\n    return () => clearInterval(intervalRef.current);\n  }, []);\n  \n  // 格式化时间显示\n  const formatTime = () => {\n    const milliseconds = Math.floor((time % 1000) / 10);\n    const seconds = Math.floor((time / 1000) % 60);\n    const minutes = Math.floor((time / (1000 * 60)) % 60);\n    \n    return (\n      String(minutes).padStart(2, '0') + ':' +\n      String(seconds).padStart(2, '0') + '.' +\n      String(milliseconds).padStart(2, '0')\n    );\n  };\n  \n  return (\n    <div>\n      <h3>秒表示例</h3>\n      <div style={{ fontSize: '2em', fontFamily: 'monospace' }}>\n        {formatTime()}\n      </div>\n      <div>\n        {!isRunning ? (\n          <button onClick={start}>开始</button>\n        ) : (\n          <button onClick={pause}>暂停</button>\n        )}\n        <button onClick={reset} style={{ marginLeft: '10px' }}>\n          重置\n        </button>\n      </div>\n    </div>\n  );\n}", "explanation": "这个例子展示了如何使用 useRef 作为可变值容器。我们创建了三个 ref：intervalRef 用于存储定时器的 ID，以便在需要时清除定时器；startTimeRef 用于存储计时器开始的时间戳；accumulatedTimeRef 用于在暂停/恢复之间累计已经流逝的时间。这些值需要在渲染之间保持，但它们的变化不需要触发组件重新渲染，因此使用 useRef 比 useState 更合适。"}]}}, {"name": "Storing Previous Values", "trans": ["存储前一个值"], "usage": {"syntax": "const previousValueRef = useRef(initialValue);\n// 在每次渲染后更新 previousValueRef.current", "description": "useRef 的一个常见用例是存储组件上一次渲染时的 props 或 state 值。这可以让你比较当前值和前一个值，在值变化时执行特定操作。由于 ref 在组件的整个生命周期内保持不变，你可以在一次渲染中保存值，然后在下一次渲染中访问它。通常结合 useEffect 使用，在渲染完成后更新 ref 中保存的前一个值。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import React, { useState, useRef, useEffect } from 'react';\n\n// 自定义 Hook 用于跟踪前一个值\nfunction usePrevious(value) {\n  const ref = useRef();\n  \n  useEffect(() => {\n    // 在渲染完成后，将当前值保存到 ref 中\n    ref.current = value;\n  }, [value]); // 只有当值变化时才更新 ref\n  \n  // 返回前一个值（在第一次渲染时是 undefined）\n  return ref.current;\n}\n\nfunction CounterWithPreviousValue() {\n  const [count, setCount] = useState(0);\n  // 使用自定义 Hook 跟踪 count 的前一个值\n  const previousCount = usePrevious(count);\n  \n  // 计算增量（当前值与前一个值的差）\n  const increment = previousCount !== undefined ? count - previousCount : 0;\n  \n  return (\n    <div>\n      <h3>带有前一个值的计数器</h3>\n      <p>当前值: {count}</p>\n      <p>前一个值: {previousCount !== undefined ? previousCount : '无'}</p>\n      <p>增量: {increment}</p>\n      <div>\n        <button onClick={() => setCount(count + 1)}>增加</button>\n        <button \n          onClick={() => setCount(count - 1)} \n          style={{ marginLeft: '10px' }}\n        >\n          减少\n        </button>\n        <button \n          onClick={() => setCount(count + 5)} \n          style={{ marginLeft: '10px' }}\n        >\n          +5\n        </button>\n      </div>\n    </div>\n  );\n}\n\n// 另一个存储前一个值的例子：检测 props 变化\nfunction DataFetcher({ userId }) {\n  const [userData, setUserData] = useState(null);\n  const [loading, setLoading] = useState(false);\n  \n  // 存储前一个 userId\n  const previousUserIdRef = useRef();\n  \n  useEffect(() => {\n    // 获取前一个 userId\n    const previousUserId = previousUserIdRef.current;\n    \n    // 如果 userId 变化了，重新获取数据\n    if (previousUserId !== userId) {\n      console.log(`用户ID从 ${previousUserId} 变为 ${userId}，重新获取数据`);\n      setLoading(true);\n      \n      // 模拟 API 调用\n      setTimeout(() => {\n        setUserData({ id: userId, name: `用户${userId}`, age: 20 + parseInt(userId) });\n        setLoading(false);\n      }, 1000);\n    }\n    \n    // 更新前一个 userId 的引用\n    previousUserIdRef.current = userId;\n  }, [userId]); // 依赖于 userId\n  \n  return (\n    <div>\n      <h3>数据获取示例</h3>\n      {loading ? (\n        <p>加载中...</p>\n      ) : userData ? (\n        <div>\n          <p>ID: {userData.id}</p>\n          <p>姓名: {userData.name}</p>\n          <p>年龄: {userData.age}</p>\n        </div>\n      ) : (\n        <p>无数据</p>\n      )}\n    </div>\n  );\n}", "explanation": "这个例子展示了两种使用 useRef 存储前一个值的方法。首先，我们创建了一个自定义 Hook usePrevious，它使用 useRef 和 useEffect 来在每次渲染后保存当前值，并返回前一个值。在 CounterWithPreviousValue 组件中，我们使用这个 Hook 来跟踪计数器的前一个值，并计算增量。其次，在 DataFetcher 组件中，我们直接使用 useRef 来存储前一个 userId，并在 useEffect 中比较当前 userId 和前一个 userId，只有在 userId 变化时才重新获取数据。"}]}}, {"name": "Combining with useEffect", "trans": ["与useEffect结合"], "usage": {"syntax": "const ref = useRef(value);\nuseEffect(() => {\n  // 在这里使用或更新 ref.current\n}, [dependencies]);", "description": "useRef 和 useEffect 的组合是 React 中非常强大的模式。useEffect 让你可以在组件渲染后执行副作用，而 useRef 允许你在这些副作用之间共享数据，而不会触发额外的渲染。常见用例包括：设置和清理定时器、事件监听器、观察 DOM 元素变化、执行动画效果、以及处理外部订阅等。在组件卸载时，你可以使用 useEffect 的清理函数来进行必要的清理工作。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import React, { useRef, useEffect, useState } from 'react';\n\nfunction WindowResizeTracker() {\n  const [dimensions, setDimensions] = useState({\n    width: window.innerWidth,\n    height: window.innerHeight\n  });\n  \n  // 使用 ref 跟踪事件监听器是否已附加\n  const hasSetupListenerRef = useRef(false);\n  // 使用 ref 存储节流计时器 ID\n  const resizeTimeoutRef = useRef(null);\n  \n  useEffect(() => {\n    // 为窗口大小调整设置节流函数\n    const handleResize = () => {\n      // 清除之前的定时器（如果存在）\n      if (resizeTimeoutRef.current) {\n        clearTimeout(resizeTimeoutRef.current);\n      }\n      \n      // 创建新的定时器\n      resizeTimeoutRef.current = setTimeout(() => {\n        setDimensions({\n          width: window.innerWidth,\n          height: window.innerHeight\n        });\n        console.log('窗口大小已更新');\n      }, 200); // 200ms 的节流延迟\n    };\n    \n    // 只在第一次渲染时设置事件监听器\n    if (!hasSetupListenerRef.current) {\n      console.log('设置窗口大小调整监听器');\n      window.addEventListener('resize', handleResize);\n      hasSetupListenerRef.current = true;\n    }\n    \n    // 清理函数\n    return () => {\n      // 只在组件卸载时移除事件监听器\n      if (hasSetupListenerRef.current) {\n        console.log('移除窗口大小调整监听器');\n        window.removeEventListener('resize', handleResize);\n        hasSetupListenerRef.current = false;\n      }\n      \n      // 清除任何挂起的定时器\n      if (resizeTimeoutRef.current) {\n        clearTimeout(resizeTimeoutRef.current);\n      }\n    };\n  }, []); // 空依赖数组，只在挂载和卸载时运行\n  \n  return (\n    <div>\n      <h3>窗口大小跟踪器</h3>\n      <p>窗口宽度: {dimensions.width}px</p>\n      <p>窗口高度: {dimensions.height}px</p>\n      <p>调整浏览器窗口大小来查看变化</p>\n    </div>\n  );\n}\n\nfunction IntersectionObserverExample() {\n  const [isVisible, setIsVisible] = useState(false);\n  const targetRef = useRef(null);\n  const observerRef = useRef(null);\n  \n  useEffect(() => {\n    // 创建 Intersection Observer 实例\n    observerRef.current = new IntersectionObserver(\n      (entries) => {\n        // 当目标元素的可见性变化时调用\n        const [entry] = entries;\n        setIsVisible(entry.isIntersecting);\n      },\n      {\n        // 配置选项\n        root: null, // 使用视口作为根\n        rootMargin: '0px',\n        threshold: 0.1 // 当 10% 的目标元素可见时触发\n      }\n    );\n    \n    // 获取目标元素的引用\n    const currentTarget = targetRef.current;\n    \n    // 如果有目标元素，开始观察它\n    if (currentTarget) {\n      observerRef.current.observe(currentTarget);\n    }\n    \n    // 清理函数\n    return () => {\n      if (observerRef.current && currentTarget) {\n        observerRef.current.unobserve(currentTarget);\n        observerRef.current.disconnect();\n      }\n    };\n  }, []); // 空依赖数组，只在挂载和卸载时运行\n  \n  return (\n    <div style={{ height: '200vh' }}>\n      <h3>Intersection Observer 示例</h3>\n      <p>向下滚动页面来查看元素的可见性变化</p>\n      \n      <div style={{ height: '100vh' }}>\n        这是一个占位符，滚动到底部以查看目标元素\n      </div>\n      \n      <div\n        ref={targetRef}\n        style={{\n          height: '200px',\n          backgroundColor: isVisible ? 'green' : 'red',\n          color: 'white',\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          transition: 'background-color 0.5s ease'\n        }}\n      >\n        {isVisible ? '我现在可见！元素已进入视口。' : '我不可见，请向下滚动查看我。'}\n      </div>\n    </div>\n  );\n}", "explanation": "这个例子展示了两种 useRef 和 useEffect 结合使用的模式。在 WindowResizeTracker 组件中，我们使用 hasSetupListenerRef 来跟踪是否已经设置了窗口大小调整的事件监听器，以避免重复添加；使用 resizeTimeoutRef 来存储节流定时器的 ID，以便在组件卸载或新的调整大小事件发生时清除它。在 IntersectionObserverExample 组件中，我们使用 targetRef 引用要观察的 DOM 元素，使用 observerRef 存储 Intersection Observer 实例，以便在组件卸载时正确清理资源。这些例子展示了 useRef 如何帮助我们在副作用之间持久保存值，以及如何在清理函数中正确使用这些引用。"}]}}, {"name": "Imperative Operations", "trans": ["命令式操作"], "usage": {"syntax": "const ref = useRef();\n// 使用 ref.current 执行命令式操作", "description": "在 React 的声明式编程模型中，有时需要执行一些命令式操作，例如直接调用组件实例的方法或访问内部状态。useRef 可以帮助我们实现这些操作，特别是当我们需要与第三方库集成、使用非受控组件或实现某些复杂交互时。虽然这种方法打破了 React 的声明式范式，但在某些情况下它是必要的。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import React, { useRef, useState } from 'react';\n\nfunction ImperativeForm() {\n  const formRef = useRef(null);\n  const [message, setMessage] = useState('');\n  // 命令式地重置表单\n  const resetForm = () => {\n    if (formRef.current) {\n      formRef.current.reset();\n      setMessage('表单已重置');\n    }\n  };\n  // 命令式地获取表单数据\n  const getFormData = () => {\n    if (formRef.current) {\n      const formData = new FormData(formRef.current);\n      const values = {};\n      for (let [key, value] of formData.entries()) {\n        values[key] = value;\n      }\n      setMessage(`表单数据: ${JSON.stringify(values)}`);\n    }\n  };\n  // 命令式地验证表单\n  const validateForm = () => {\n    if (formRef.current) {\n      const isValid = formRef.current.checkValidity();\n      setMessage(isValid ? '表单有效' : '表单无效，请检查输入');\n      if (!isValid) {\n        formRef.current.reportValidity();\n      }\n    }\n  };\n  return (\n    <div>\n      <h3>命令式表单操作</h3>\n      <form ref={formRef} onSubmit={(e) => e.preventDefault()}>\n        <input name=\"name\" required minLength=\"2\" placeholder=\"姓名\" />\n        <input name=\"email\" type=\"email\" required placeholder=\"邮箱\" />\n        <input name=\"age\" type=\"number\" min=\"18\" max=\"100\" placeholder=\"年龄\" />\n      </form>\n      <button onClick={resetForm}>重置表单</button>\n      <button onClick={validateForm}>验证表单</button>\n      <button onClick={getFormData}>获取数据</button>\n      {message && <p>{message}</p>}\n    </div>\n  );\n}", "explanation": "这个例子展示了如何使用 useRef 来执行表单的命令式操作。我们使用 formRef 引用 form 元素，然后可以调用 DOM API 如 reset()、checkValidity() 和 reportValidity() 来操作表单。我们还使用 FormData API 以命令式的方式获取表单数据。虽然 React 通常推荐使用受控组件（通过 state 管理表单值），但有时命令式的方法可能更简单或更适合特定需求，特别是当处理复杂的表单或与第三方库集成时。"}]}}, {"name": "Avoiding Pitfalls", "trans": ["避免的陷阱"], "usage": {"syntax": "const ref = useRef(initialValue)", "description": "useRef 虽然强大，但也有一些常见陷阱需要避免：1. 不要用 ref 代替 state 管理 UI 变化；2. ref.current 变化不会触发组件重新渲染；3. 不要在渲染期间直接读取或写入 ref.current（应在副作用中操作）；4. ref 不是响应式的，不能用于数据流转。正确理解 ref 的用途，能让你的代码更健壮。", "parameters": [{"name": "initialValue", "description": "ref 的初始值"}], "returnValue": "返回一个 ref 对象", "examples": [{"code": "// 错误用法示例\nimport React, { useRef } from 'react';\nfunction WrongRefUsage() {\n  const countRef = useRef(0);\n  // 直接用 ref.current 作为 UI 状态（错误）\n  return (\n    <div>\n      <p>计数: {countRef.current}</p>\n      <button onClick={() => { countRef.current++; }}>增加</button>\n    </div>\n  );\n}\n// 正确用法：用 state 管理 UI，ref 只做持久存储", "explanation": "这个例子说明了不要用 ref 代替 state 管理 UI。ref.current 变化不会触发组件重新渲染，导致 UI 不更新。正确做法是用 state 管理 UI，ref 只做持久存储。"}]}}, {"name": "作业：实现一个图片轮播组件", "trans": ["作业"], "usage": {"syntax": "// 需求：实现一个图片轮播组件，要求：\n// 1. 使用 useRef 获取图片 DOM 引用，实现左右切换时的动画。\n// 2. 支持自动播放和手动切换。\n// 3. 切换时高亮当前图片。\n// 4. 代码结构清晰，注释完整。", "description": "请实现一个图片轮播组件，要求如上。提交时请附上完整代码和注释。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 示例代码略，请学生自行实现\n// 提示：可以用 useRef 保存图片容器的 DOM 节点，实现动画和切换。", "explanation": "作业要求学生综合运用 useRef，巩固对 ref 的理解和实际应用能力。"}, {"code": "import React, { useRef, useState, useEffect } from 'react';\n\nconst images = [\n  'https://images.unsplash.com/photo-1506744038136-46273834b3fb',\n  'https://images.unsplash.com/photo-1465101046530-73398c7f28ca',\n  'https://images.unsplash.com/photo-1519125323398-675f0ddb6308'\n];\n\nfunction Carousel() {\n  const [current, setCurrent] = useState(0);\n  const containerRef = useRef(null);\n  const timerRef = useRef(null);\n\n  const goTo = (idx) => { setCurrent(idx); };\n  const prev = () => { setCurrent((prev) => (prev - 1 + images.length) % images.length); };\n  const next = () => { setCurrent((prev) => (prev + 1) % images.length); };\n\n  useEffect(() => {\n    timerRef.current = setInterval(() => {\n      setCurrent((prev) => (prev + 1) % images.length);\n    }, 3000);\n    return () => clearInterval(timerRef.current);\n  }, []);\n\n  useEffect(() => {\n    if (containerRef.current) {\n      containerRef.current.style.transition = 'transform 0.5s';\n      containerRef.current.style.transform = 'translateX(-' + (current * 100) + '%)';\n    }\n  }, [current]);\n\n  return (\n    <div style={{ width: 400, overflow: 'hidden', position: 'relative', margin: '0 auto' }}>\n      <div\n        ref={containerRef}\n        style={{ display: 'flex', width: (images.length * 100) + '%' }}\n      >\n        {images.map((src, idx) => (\n          <img\n            key={src}\n            src={src}\n            alt={'slide-' + idx}\n            style={{ width: 400, height: 240, objectFit: 'cover' }}\n          />\n        ))}\n      </div>\n      <button\n        onClick={prev}\n        style={{ position: 'absolute', top: '50%', left: 10, transform: 'translateY(-50%)' }}\n      >\n        {'◀'}\n      </button>\n      <button\n        onClick={next}\n        style={{ position: 'absolute', top: '50%', right: 10, transform: 'translateY(-50%)' }}\n      >\n        {'▶'}\n      </button>\n      <div style={{ textAlign: 'center', marginTop: 8 }}>\n        {images.map((_, idx) => (\n          <span\n            key={idx}\n            onClick={() => goTo(idx)}\n            style={{\n              display: 'inline-block',\n              width: 12,\n              height: 12,\n              borderRadius: '50%',\n              background: idx === current ? '#1890ff' : '#ccc',\n              margin: '0 4px',\n              cursor: 'pointer',\n              border: idx === current ? '2px solid #1890ff' : '2px solid #ccc'\n            }}\n          />\n        ))}\n      </div>\n    </div>\n  );\n}\n\nexport default Carousel;\n", "explanation": "这是一个完整的图片轮播组件实现：\n- 使用 useRef 获取图片容器的 DOM 节点，实现平滑动画切换。\n- 支持自动播放和手动切换。\n- 当前图片高亮指示。\n- 结构清晰，注释详细，便于理解 useRef 在实际项目中的应用。"}]}}]}