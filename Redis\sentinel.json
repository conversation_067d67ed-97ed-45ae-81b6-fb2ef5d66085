{"name": "Sentinel High Availability", "trans": ["Sentinel高可用"], "methods": [{"name": "Sentinel Architecture", "trans": ["Sentinel架构"], "usage": {"syntax": "Sentinel由多个哨兵节点组成，监控主从Redis实例，实现自动故障转移和通知。", "description": "Redis Sentinel是一套分布式高可用解决方案，通过多个哨兵节点监控主从实例的健康状态，自动完成主从切换和通知。", "parameters": [{"name": "sentinel", "description": "哨兵进程，负责监控和故障转移。"}, {"name": "monitor", "description": "指定被监控的主节点。"}], "returnValue": "无返回值", "examples": [{"code": "# sentinel.conf配置示例\nsentinel monitor mymaster 127.0.0.1 6379 2  # 监控主节点，至少2个哨兵同意才判定故障\n", "explanation": "通过sentinel monitor命令配置哨兵监控主节点。"}]}}, {"name": "Automatic Failover", "trans": ["自动故障转移"], "usage": {"syntax": "当主节点故障时，Sentinel自动将某个从节点提升为新主节点，并通知客户端。", "description": "Sentinel检测到主节点不可用后，会自动选举新的主节点，并将其他从节点指向新主节点，实现高可用。", "parameters": [{"name": "failover-timeout", "description": "故障转移超时时间。"}, {"name": "notification-script", "description": "故障转移时执行的通知脚本。"}], "returnValue": "无返回值", "examples": [{"code": "# 故障转移配置\nsentinel failover-timeout mymaster 60000  # 60秒超时\n# 故障发生时自动切换主节点\n", "explanation": "配置故障转移超时，Sentinel自动完成主从切换。"}]}}, {"name": "Configuration and Monitoring", "trans": ["配置与监控"], "usage": {"syntax": "通过sentinel.conf配置哨兵参数，并可实时监控主从状态。", "description": "Sentinel支持多种配置参数，如监控主节点、通知脚本、故障判定等。可通过命令实时查看哨兵和主从状态，保障高可用。", "parameters": [{"name": "sentinel.conf", "description": "哨兵配置文件。"}, {"name": "SENTINEL命令", "description": "用于查询和管理哨兵状态。"}], "returnValue": "无返回值", "examples": [{"code": "# sentinel.conf常用配置\nsentinel monitor mymaster 127.0.0.1 6379 2\nsentinel down-after-milliseconds mymaster 10000\n\n# 监控命令\nSENTINEL masters  # 查看所有主节点状态\nSENTINEL slaves mymaster  # 查看从节点状态\n", "explanation": "通过配置文件和命令监控哨兵和主从状态。"}]}}, {"name": "Sentinel高可用作业", "trans": ["作业：Sentinel高可用"], "usage": {"syntax": "请完成以下任务：\n1. 部署3个Sentinel节点监控一个主节点。\n2. 模拟主节点故障，观察自动切换。\n3. 使用SENTINEL命令监控状态。", "description": "通过实际操作理解Sentinel高可用的部署、故障转移和监控。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 作业示例\n# 1. 启动3个Sentinel进程，配置监控同一主节点\n# 2. 停止主节点，观察从节点自动提升为主\n# 3. 使用SENTINEL masters/slaves命令查看状态\n", "explanation": "通过动手实验掌握Sentinel高可用的配置和监控。"}]}}, {"name": "Sentinel高可用正确实现示例", "trans": ["正确实现卡片"], "usage": {"syntax": "Sentinel高可用的标准部署与监控流程。", "description": "展示如何标准部署Sentinel集群，并通过命令监控主从切换和状态，确保高可用。", "parameters": [{"name": "sentinel monitor", "description": "配置哨兵监控主节点。"}, {"name": "SENTINEL命令", "description": "监控和管理哨兵状态。"}], "returnValue": "无返回值", "examples": [{"code": "# sentinel.conf标准配置\nsentinel monitor mymaster 127.0.0.1 6379 2\n\n# 监控流程\n# 1. 启动多个Sentinel进程\n# 2. 使用SENTINEL命令监控主从切换\n", "explanation": "标准Sentinel高可用配置和监控流程，保证主从自动切换和高可用。"}]}}]}