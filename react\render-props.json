{"name": "Render Props Pattern", "trans": ["Render Props模式"], "methods": [{"name": "Render Props Concept", "trans": ["render props概念"], "usage": {"syntax": "<DataProvider render={data => <Child data={data} />} />", "description": "Render Props是一种通过函数作为props实现组件间逻辑复用的模式。父组件将渲染权交给子组件，子组件通过调用render函数决定渲染内容。", "parameters": [{"name": "render", "description": "一个接收数据并返回React节点的函数"}], "returnValue": "渲染由render函数返回的内容", "examples": [{"code": "function MouseTracker(props) {\n  const [pos, setPos] = React.useState({ x: 0, y: 0 });\n  return (\n    <div onMouseMove={e => setPos({ x: e.clientX, y: e.clientY })}>\n      {props.render(pos)}\n    </div>\n  );\n}\n// 用法\n<MouseTracker render={pos => <p>鼠标位置: {pos.x}, {pos.y}</p>} />", "explanation": "MouseTracker通过render props将鼠标位置传递给子组件，由子组件决定如何渲染。"}]}}, {"name": "Implementing Render Props", "trans": ["实现render props"], "usage": {"syntax": "function Provider({ render }) {\n  // ...\n  return render(data);\n}", "description": "实现render props时，组件接收一个函数类型的props（通常命名为render或children），在内部调用该函数并传递需要共享的数据或方法。", "parameters": [{"name": "render", "description": "用于渲染内容的函数"}], "returnValue": "渲染render函数返回的内容", "examples": [{"code": "function DataProvider({ render }) {\n  const [data] = React.useState(42);\n  return render(data);\n}\n// 用法\n<DataProvider render={data => <div>数据: {data}</div>} />", "explanation": "DataProvider通过render props将数据传递给子组件。"}]}}, {"name": "Controlling Render Logic", "trans": ["控制渲染逻辑"], "usage": {"syntax": "<Provider render={data => data > 0 ? <A /> : <B />} />", "description": "Render props允许调用方完全控制渲染逻辑，可以根据传入的数据动态决定渲染内容，实现灵活的UI组合。", "parameters": [{"name": "render", "description": "用于自定义渲染的函数"}], "returnValue": "渲染自定义逻辑后的内容", "examples": [{"code": "function ToggleProvider({ render }) {\n  const [on, setOn] = React.useState(false);\n  return (\n    <div>\n      <button onClick={() => setOn(o => !o)}>切换</button>\n      {render(on)}\n    </div>\n  );\n}\n// 用法\n<ToggleProvider render={on => on ? <span>开</span> : <span>关</span>} />", "explanation": "ToggleProvider通过render props让调用方自定义开关状态的渲染。"}]}}, {"name": "Render Props vs HOC", "trans": ["与HOC对比"], "usage": {"syntax": "// HOC: withFeature(Component)\n// Render Props: <FeatureProvider render={...} />", "description": "Render Props和HOC都能实现逻辑复用。HOC通过包裹组件增强功能，Render Props通过函数参数灵活传递数据和方法。Render Props更适合需要灵活渲染的场景。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// HOC方式\nfunction withUser(Component) {\n  return function Wrapper(props) {\n    const user = { name: '张三' };\n    return <Component {...props} user={user} />;\n  };\n}\n// Render Props方式\nfunction UserProvider({ render }) {\n  const user = { name: '张三' };\n  return render(user);\n}\n// 用法\n<UserProvider render={user => <div>用户名: {user.name}</div>} />", "explanation": "对比HOC和Render Props两种逻辑复用方式。"}]}}, {"name": "Composing Render Props", "trans": ["组合render props"], "usage": {"syntax": "<A render={a => <B render={b => <C a={a} b={b} />} />} />", "description": "可以通过嵌套多个render props组件，实现复杂的数据流和逻辑组合。也可以用自定义Hook简化嵌套。", "parameters": [], "returnValue": "渲染组合后的内容", "examples": [{"code": "<Mouse render={mouse =>\n  <Cat render={cat =>\n    <div>\n      鼠标: {mouse.x},{mouse.y}，猫: {cat.name}\n    </div>\n  } />\n} />", "explanation": "通过嵌套render props实现多数据源组合。"}]}}, {"name": "Performance Considerations", "trans": ["性能考虑"], "usage": {"syntax": "// 避免每次渲染都创建新函数\n<Provider render={useCallback(data => <Child data={data} />, [data])} />", "description": "Render Props模式下，每次渲染都会创建新函数，可能导致子组件不必要的更新。可以用useCallback优化，或用memo避免性能问题。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "function Provider({ render }) {\n  const [data] = React.useState(1);\n  const stableRender = React.useCallback(render, [render]);\n  return stableRender(data);\n}", "explanation": "通过useCallback保证render函数稳定，减少不必要的渲染。"}]}}, {"name": "作业：实现一个可复用的Hover组件", "trans": ["作业"], "usage": {"syntax": "// 需求：实现一个Hover组件\n// 1. 通过render props暴露hover状态。\n// 2. 支持任意子元素。\n// 3. 代码结构清晰，注释完整。", "description": "请实现一个Hover组件，要求如上。提交时请附上完整代码和注释。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 示例代码略，请学生自行实现\n// 提示：用onMouseEnter/onMouseLeave和render props实现。", "explanation": "作业要求学生掌握render props的状态暴露和事件处理。"}, {"code": "import React, { useState } from 'react';\nfunction Hover({ render }) {\n  const [hovered, setHovered] = useState(false);\n  return (\n    <div\n      onMouseEnter={() => setHovered(true)}\n      onMouseLeave={() => setHovered(false)}\n    >\n      {render(hovered)}\n    </div>\n  );\n}\n// 用法示例\nfunction Demo() {\n  return (\n    <Hover render={hovered =>\n      <button style={{ background: hovered ? '#1890ff' : '#eee' }}>\n        {hovered ? '悬停中' : '未悬停'}\n      </button>\n    } />\n  );\n}", "explanation": "这是一个完整的Hover组件实现，通过render props暴露hover状态。"}]}}]}