{"name": "Hybrid Persistence", "trans": ["混合持久化"], "methods": [{"name": "Configuration and Principle", "trans": ["配置与原理"], "usage": {"syntax": "混合持久化通过配置rdb-aof-load-policy参数，结合RDB和AOF两种方式，提升数据恢复速度和完整性。", "description": "混合持久化是Redis 4.0后引入的新机制，将RDB快照和AOF日志结合，生成混合格式的AOF文件。这样既能快速恢复大部分数据，又能保证数据的完整性。", "parameters": [{"name": "rdb-aof-load-policy", "description": "决定Redis启动时优先加载RDB还是AOF或混合文件。"}, {"name": "aof-use-rdb-preamble", "description": "AOF重写时是否采用RDB前缀，生成混合AOF文件。"}], "returnValue": "无返回值", "examples": [{"code": "# redis.conf配置混合持久化\naof-use-rdb-preamble yes  # AOF重写时采用RDB前缀\n\n# 启动时加载策略\nrdb-aof-load-policy default  # 默认优先加载AOF\n", "explanation": "通过配置aof-use-rdb-preamble和rdb-aof-load-policy实现混合持久化。"}]}}, {"name": "Application Scenarios", "trans": ["适用场景"], "usage": {"syntax": "混合持久化适用于既要求恢复速度快，又要求数据完整性的场景。", "description": "混合持久化适合对数据安全性和恢复速度都有较高要求的生产环境，尤其是大数据量、频繁写入的业务。", "parameters": [{"name": "高可靠性业务", "description": "如金融、电商等对数据安全要求极高的场景。"}, {"name": "大数据量恢复", "description": "需要快速恢复大量数据的场景。"}], "returnValue": "无返回值", "examples": [{"code": "# 典型场景\n# 1. 金融系统，要求数据零丢失且恢复迅速\n# 2. 电商促销高并发写入，需快速恢复服务\n", "explanation": "混合持久化适合高可靠性和大数据量恢复场景。"}]}}, {"name": "混合持久化作业", "trans": ["作业：混合持久化"], "usage": {"syntax": "请完成以下任务：\n1. 配置Redis开启混合持久化。\n2. 观察AOF重写后文件格式。\n3. 总结混合持久化的优缺点。", "description": "通过实际操作理解混合持久化的配置和应用场景。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 作业示例\n# 1. 修改redis.conf，设置aof-use-rdb-preamble yes\n# 2. 执行BGREWRITEAOF\n# 3. 查看AOF文件前缀格式\n# 4. 总结混合持久化的优缺点\n", "explanation": "通过动手实验掌握混合持久化的配置和原理。"}]}}, {"name": "混合持久化正确实现示例", "trans": ["正确实现卡片"], "usage": {"syntax": "混合持久化的标准配置与验证流程。", "description": "展示如何标准配置混合持久化，并验证AOF文件为混合格式，确保数据安全和恢复效率。", "parameters": [{"name": "aof-use-rdb-preamble", "description": "设置为yes以启用混合AOF文件。"}], "returnValue": "无返回值", "examples": [{"code": "# redis.conf标准配置\naof-use-rdb-preamble yes\n\n# 验证流程\n# 1. 执行BGREWRITEAOF\n# 2. 检查AOF文件头部为RDB格式\n", "explanation": "标准混合持久化配置和验证流程，保证数据安全和恢复效率。"}]}}]}