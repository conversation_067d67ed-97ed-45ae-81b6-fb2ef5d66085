{"name": "CSS-in-JS and Style Solutions", "trans": ["CSS-in-JS与样式方案"], "methods": [{"name": "Scoped Style", "trans": ["scoped样式"], "usage": {"syntax": "<style scoped>\n.button { color: red; }\n</style>", "description": "通过scoped属性让样式只作用于当前组件，避免全局污染。", "parameters": [{"name": "scoped", "description": "限定样式作用域的属性"}], "returnValue": "仅当前组件生效的样式", "examples": [{"code": "<style scoped>\n.button { color: red; }\n</style>", "explanation": "button样式只影响当前组件。"}]}}, {"name": "CSS Modules", "trans": ["CSS Modules"], "usage": {"syntax": "<style module>\n.title { color: blue; }\n</style>\n<div :class=\"$style.title\"></div>", "description": "通过module属性启用CSS Modules，类名自动哈希隔离，避免冲突。", "parameters": [{"name": "module", "description": "启用CSS Modules的属性"}, {"name": "$style", "description": "访问模块化类名的对象"}], "returnValue": "哈希隔离的样式类名", "examples": [{"code": "<style module>\n.title { color: blue; }\n</style>\n<div :class=\"$style.title\"></div>", "explanation": "title类名自动哈希，避免全局冲突。"}]}}, {"name": "Dynamic Style", "trans": ["动态样式"], "usage": {"syntax": "<div :style=\"{ color: active ? 'red' : 'gray' }\"></div>", "description": "通过:style或:class动态绑定样式，实现状态驱动的样式变化。", "parameters": [{"name": ":style", "description": "动态绑定内联样式"}, {"name": ":class", "description": "动态绑定类名"}], "returnValue": "根据状态变化的样式", "examples": [{"code": "<div :style=\"{ color: active ? 'red' : 'gray' }\"></div>", "explanation": "active为true时字体为红色，否则为灰色。"}]}}, {"name": "Global Style", "trans": ["全局样式"], "usage": {"syntax": "<style>\nbody { background: #f5f5f5; }\n</style>", "description": "不加scoped的<style>标签定义全局样式，影响所有页面。", "parameters": [{"name": "<style>", "description": "全局样式标签"}], "returnValue": "全局生效的样式", "examples": [{"code": "<style>\nbody { background: #f5f5f5; }\n</style>", "explanation": "设置全局背景色。"}]}}, {"name": "Style Reuse", "trans": ["样式复用"], "usage": {"syntax": "@import './common.css';\n// 或使用CSS变量、mixin等", "description": "通过@import、CSS变量、mixin等方式实现样式复用，提升开发效率。", "parameters": [{"name": "@import", "description": "导入外部样式文件"}, {"name": "CSS变量/mixin", "description": "可复用的样式片段"}], "returnValue": "可复用的样式方案", "examples": [{"code": "@import './common.css';\n:root { --main-color: #42b983; }\n.button { color: var(--main-color); }", "explanation": "导入公共样式和使用CSS变量。"}]}}, {"name": "CSS-in-JS", "trans": ["CSS-in-JS"], "usage": {"syntax": "import { css } from '@emotion/css';\nconst className = css`color: green;`\n<div :class=\"className\"></div>", "description": "通过emotion、styled-components等库在JS中定义样式，支持动态和主题化。", "parameters": [{"name": "css", "description": "生成样式类名的函数"}], "returnValue": "动态生成的样式类名", "examples": [{"code": "import { css } from '@emotion/css';\nconst className = css`color: green;`\n<div :class=\"className\"></div>", "explanation": "用emotion实现CSS-in-JS。"}]}}, {"name": "SSR Style Handling", "trans": ["SSR样式处理"], "usage": {"syntax": "// Nuxt等框架自动注入样式\n// CSS-in-JS库需服务端收集样式注入HTML", "description": "SSR场景下需确保样式正确注入到HTML，Nuxt等框架自动处理，CSS-in-JS需用专用API收集样式。", "parameters": [{"name": "SSR样式注入", "description": "服务端渲染时的样式处理"}], "returnValue": "服务端渲染下的完整样式输出", "examples": [{"code": "// Nuxt自动注入<style>，emotion等需用extractCritical收集样式", "explanation": "确保SSR页面样式完整。"}]}}, {"name": "CSS-in-JS and Style Assignment", "trans": ["CSS-in-JS与样式方案练习"], "usage": {"syntax": "# CSS-in-JS与样式方案练习", "description": "完成以下练习，巩固CSS-in-JS与样式方案相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用scoped和CSS Modules实现样式隔离。\n2. 实现动态和全局样式。\n3. 复用样式片段。\n4. 用CSS-in-JS实现动态样式。\n5. 理解SSR下样式处理。", "explanation": "通过这些练习掌握Vue样式方案。"}]}}]}