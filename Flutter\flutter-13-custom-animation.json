{"name": "Custom Animation", "trans": ["自定义动画"], "methods": [{"name": "Custom Animation Implementation", "trans": ["自定义动画实现"], "usage": {"syntax": "AnimationController + Tween + AnimatedBuilder", "description": "通过AnimationController、Tween和AnimatedBuilder组合，可以实现高度自定义的动画效果。AnimatedBuilder用于监听动画并重建UI。", "parameters": [{"name": "controller", "description": "动画控制器，控制动画的启动、停止等"}, {"name": "tween", "description": "定义动画的起止值"}, {"name": "builder", "description": "构建动画UI的回调函数"}], "returnValue": "Widget，动态响应动画值的UI。", "examples": [{"code": "// 自定义动画实现示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(home: CustomAnimationDemo());\n  }\n}\n\nclass CustomAnimationDemo extends StatefulWidget {\n  @override\n  _CustomAnimationDemoState createState() => _CustomAnimationDemoState();\n}\n\nclass _CustomAnimationDemoState extends State<CustomAnimationDemo> with SingleTickerProviderStateMixin {\n  late AnimationController _controller;\n  late Animation<double> _animation;\n  @override\n  void initState() {\n    super.initState();\n    _controller = AnimationController(vsync: this, duration: Duration(seconds: 2));\n    _animation = Tween(begin: 0.0, end: 200.0).animate(_controller);\n  }\n  @override\n  void dispose() {\n    _controller.dispose();\n    super.dispose();\n  }\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('自定义动画实现')),\n      body: Center(\n        child: AnimatedBuilder(\n          animation: _animation,\n          builder: (context, child) {\n            return Container(\n              width: _animation.value,\n              height: _animation.value,\n              color: Colors.blue,\n              child: child,\n            );\n          },\n          child: Icon(Icons.star, color: Colors.white, size: 40),\n        ),\n      ),\n      floatingActionButton: FloatingActionButton(\n        onPressed: () => _controller.forward(from: 0),\n        child: Icon(Icons.play_arrow),\n      ),\n    );\n  }\n}\n", "explanation": "通过AnimationController和Tween配合AnimatedBuilder，实现了一个自定义大小变化的动画。点击按钮触发动画。"}]}}, {"name": "Animation and State Combination", "trans": ["动画与状态结合"], "usage": {"syntax": "动画值驱动setState或与业务状态结合", "description": "动画不仅可以用于视觉效果，还可以与业务状态结合，实现更复杂的交互。例如动画结束后改变页面状态，或根据状态动态调整动画。", "parameters": [{"name": "animation", "description": "动画对象，监听其状态或数值变化"}, {"name": "setState", "description": "在动画回调中更新页面状态"}], "returnValue": "无返回值，驱动UI和业务逻辑联动。", "examples": [{"code": "// 动画与状态结合示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(home: AnimationStateDemo());\n  }\n}\n\nclass AnimationStateDemo extends StatefulWidget {\n  @override\n  _AnimationStateDemoState createState() => _AnimationStateDemoState();\n}\n\nclass _AnimationStateDemoState extends State<AnimationStateDemo> with SingleTickerProviderStateMixin {\n  late AnimationController _controller;\n  late Animation<double> _animation;\n  String _status = '未开始';\n  @override\n  void initState() {\n    super.initState();\n    _controller = AnimationController(vsync: this, duration: Duration(seconds: 1));\n    _animation = Tween(begin: 0.0, end: 1.0).animate(_controller)\n      ..addStatusListener((status) {\n        if (status == AnimationStatus.completed) {\n          setState(() { _status = '动画完成'; });\n        } else if (status == AnimationStatus.forward) {\n          setState(() { _status = '动画进行中'; });\n        }\n      });\n  }\n  @override\n  void dispose() {\n    _controller.dispose();\n    super.dispose();\n  }\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('动画与状态结合')),\n      body: Center(\n        child: Column(\n          mainAxisAlignment: MainAxisAlignment.center,\n          children: [\n            FadeTransition(\n              opacity: _animation,\n              child: Icon(Icons.star, size: 80, color: Colors.orange),\n            ),\n            SizedBox(height: 20),\n            Text(_status, style: TextStyle(fontSize: 20)),\n            SizedBox(height: 20),\n            ElevatedButton(onPressed: () => _controller.forward(from: 0), child: Text('播放动画'))\n          ],\n        ),\n      ),\n    );\n  }\n}\n", "explanation": "演示动画状态与页面业务状态的结合，动画播放时和完成后页面状态会自动更新。"}]}}, {"name": "Assignment", "trans": ["作业：实现一个自定义动画与状态结合的页面。"], "usage": {"syntax": "无", "description": "请实现一个页面，要求自定义动画（如大小、颜色、透明度等）与页面状态结合，动画完成后自动显示提示。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 作业参考实现（简化版）\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(home: CustomAssignmentDemo());\n  }\n}\n\nclass CustomAssignmentDemo extends StatefulWidget {\n  @override\n  _CustomAssignmentDemoState createState() => _CustomAssignmentDemoState();\n}\n\nclass _CustomAssignmentDemoState extends State<CustomAssignmentDemo> with SingleTickerProviderStateMixin {\n  late AnimationController _controller;\n  late Animation<double> _animation;\n  String _status = '未开始';\n  @override\n  void initState() {\n    super.initState();\n    _controller = AnimationController(vsync: this, duration: Duration(seconds: 2));\n    _animation = Tween(begin: 0.0, end: 1.0).animate(_controller)\n      ..addStatusListener((status) {\n        if (status == AnimationStatus.completed) {\n          setState(() { _status = '动画完成'; });\n        } else if (status == AnimationStatus.forward) {\n          setState(() { _status = '动画进行中'; });\n        }\n      });\n  }\n  @override\n  void dispose() {\n    _controller.dispose();\n    super.dispose();\n  }\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('作业实现')),\n      body: Center(\n        child: Column(\n          mainAxisAlignment: MainAxisAlignment.center,\n          children: [\n            AnimatedBuilder(\n              animation: _animation,\n              builder: (context, child) {\n                return Opacity(\n                  opacity: _animation.value,\n                  child: Container(\n                    width: 100 + 100 * _animation.value,\n                    height: 100 + 100 * _animation.value,\n                    color: Colors.blue,\n                    child: child,\n                  ),\n                );\n              },\n              child: Icon(Icons.star, color: Colors.white, size: 40),\n            ),\n            SizedBox(height: 20),\n            Text(_status, style: TextStyle(fontSize: 20)),\n            SizedBox(height: 20),\n            ElevatedButton(onPressed: () => _controller.forward(from: 0), child: Text('播放动画'))\n          ],\n        ),\n      ),\n    );\n  }\n}\n", "explanation": "作业要求自定义动画与状态结合，动画完成后自动显示提示。"}]}}]}