{"name": "Operators and Expressions", "trans": ["运算符与表达式"], "methods": [{"name": "Arithmetic Operators", "trans": ["算术运算符"], "usage": {"syntax": "+, -, *, /, //, %, **", "description": "算术运算符用于数值计算，包括加(+)、减(-)、乘(*)、除(/)、整除(//)、取余(%)、幂(**)。", "parameters": [{"name": "a, b", "description": "任意数字类型操作数"}], "returnValue": "运算结果，类型取决于操作数类型", "examples": [{"code": "a = 10\nb = 3\nprint(a + b)  # 13\nprint(a - b)  # 7\nprint(a * b)  # 30\nprint(a / b)  # 3.333...\nprint(a // b) # 3\nprint(a % b)  # 1\nprint(a ** b) # 1000", "explanation": "演示了常见算术运算符的用法和结果。"}]}}, {"name": "Assignment Operators", "trans": ["赋值运算符"], "usage": {"syntax": "=, +=, -=, *=, /=, //=, %=, **=", "description": "赋值运算符用于给变量赋值或在原有基础上进行运算并赋值。", "parameters": [{"name": "变量, 值", "description": "任意变量和可赋值的值"}], "returnValue": "赋值后变量的新值", "examples": [{"code": "a = 5\na += 2  # 等价于 a = a + 2\nprint(a)  # 7\na *= 3\nprint(a)  # 21", "explanation": "演示了常见赋值运算符的用法。"}]}}, {"name": "Comparison Operators", "trans": ["比较运算符"], "usage": {"syntax": ">, <, >=, <=, ==, !=", "description": "比较运算符用于判断两个值的大小或相等关系，结果为布尔值。", "parameters": [{"name": "a, b", "description": "任意可比较的对象"}], "returnValue": "布尔值True或False", "examples": [{"code": "a = 5\nb = 3\nprint(a > b)   # True\nprint(a == b)  # False\nprint(a != b)  # True", "explanation": "演示了常见比较运算符的用法和结果。"}]}}, {"name": "Logical Operators", "trans": ["逻辑运算符"], "usage": {"syntax": "and, or, not", "description": "逻辑运算符用于布尔值的逻辑运算，包括与(and)、或(or)、非(not)。", "parameters": [{"name": "x, y", "description": "布尔值或可转换为布尔值的对象"}], "returnValue": "布尔值结果", "examples": [{"code": "x = True\ny = False\nprint(x and y)  # False\nprint(x or y)   # True\nprint(not x)    # False", "explanation": "演示了逻辑运算符的用法和结果。"}]}}, {"name": "Bitwise Operators", "trans": ["位运算符"], "usage": {"syntax": "&, |, ^, ~, <<, >>", "description": "位运算符用于整数的二进制位操作，包括与(&)、或(|)、异或(^)、取反(~)、左移(<<)、右移(>>)。", "parameters": [{"name": "a, b", "description": "整数类型操作数"}], "returnValue": "整数结果", "examples": [{"code": "a = 5  # 0b0101\nb = 3  # 0b0011\nprint(a & b)  # 1\nprint(a | b)  # 7\nprint(a ^ b)  # 6\nprint(~a)     # -6\nprint(a << 1) # 10\nprint(a >> 1) # 2", "explanation": "演示了常见位运算符的用法和结果。"}]}}, {"name": "Membership Operators", "trans": ["成员运算符"], "usage": {"syntax": "in, not in", "description": "成员运算符用于判断某元素是否属于某序列（如字符串、列表、元组、字典、集合等）。", "parameters": [{"name": "elem, seq", "description": "元素和序列"}], "returnValue": "布尔值True或False", "examples": [{"code": "lst = [1, 2, 3]\nprint(2 in lst)     # True\nprint(5 not in lst) # True\ns = 'hello'\nprint('e' in s)     # True", "explanation": "演示了成员运算符的用法。"}]}}, {"name": "Identity Operators", "trans": ["身份运算符"], "usage": {"syntax": "is, is not", "description": "身份运算符用于判断两个对象的内存地址是否相同（即是否为同一对象）。", "parameters": [{"name": "a, b", "description": "任意对象"}], "returnValue": "布尔值True或False", "examples": [{"code": "a = [1, 2]\nb = a\nc = [1, 2]\nprint(a is b)      # True\nprint(a is c)      # False\nprint(a == c)      # True", "explanation": "演示了is和==的区别。is判断对象身份，==判断值相等。"}]}}, {"name": "Operators Assignment", "trans": ["运算符练习"], "usage": {"syntax": "# 运算符练习", "description": "完成以下练习，巩固运算符相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 计算7的3次方并取余5。\n2. 判断一个元素是否在列表中。\n3. 比较两个变量的值和身份。\n4. 用位运算实现两个数的交换。", "explanation": "练习要求动手实践算术、成员、身份、位运算等常用运算符。"}]}}]}