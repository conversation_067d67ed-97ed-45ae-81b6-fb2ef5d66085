{"name": "Avoid Unnecessary Renders", "trans": ["避免不必要的渲染"], "methods": [{"name": "Rendering Optimization Principles", "trans": ["渲染优化原则"], "usage": {"syntax": "// 渲染优化原则：\n// 1. 只更新必要的组件\n// 2. 避免无关状态变化导致的重渲染\n// 3. 合理拆分组件和状态", "description": "渲染优化的核心是减少无意义的重渲染，聚焦于状态最小化、组件拆分、props稳定、记忆化等原则。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 优化前：父组件状态变化导致所有子组件重渲染\nfunction Parent() {\n  const [count, setCount] = useState(0);\n  return (\n    <div>\n      <button onClick={() => setCount(c => c + 1)}>增加</button>\n      <ChildA />\n      <ChildB />\n    </div>\n  );\n}\n// 优化后：将状态下移到需要的子组件\nfunction Parent() {\n  return (\n    <div>\n      <ChildA />\n      <ChildB />\n    </div>\n  );\n}\nfunction ChildA() {\n  const [count, setCount] = useState(0);\n  return <button onClick={() => setCount(c => c + 1)}>A: {count}</button>;\n}", "explanation": "通过状态下移和组件拆分，减少无关组件的重渲染。"}]}}, {"name": "Using React DevTools for Analysis", "trans": ["使用React DevTools分析"], "usage": {"syntax": "// 使用React DevTools：\n// 1. 组件高亮重渲染\n// 2. 查看props/state变化\n// 3. 性能分析面板(Profiler)", "description": "React DevTools可用于分析组件树、props/state变化、重渲染原因和性能瓶颈，是优化的必备工具。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 使用Profiler分析渲染\nimport { Profiler } from 'react';\nfunction onRender(id, phase, actualDuration) {\n  console.log(id, phase, actualDuration);\n}\n<Profiler id=\"App\" onRender={onRender}>\n  <App />\n</Profiler>;", "explanation": "通过Profiler组件分析每次渲染的耗时和原因。"}]}}, {"name": "Identifying Re-render Causes", "trans": ["重渲染原因识别"], "usage": {"syntax": "// 常见重渲染原因：\n// 1. 父组件重渲染\n// 2. props引用变化\n// 3. context变化\n// 4. key变化", "description": "识别重渲染的根本原因是优化的前提，常见包括父组件重渲染、props引用变化、context变化、key变化等。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// props引用变化导致重渲染\nfunction Parent() {\n  const arr = [1,2,3];\n  return <Child arr={arr} />;\n}\n// 优化：用useMemo缓存引用\nfunction Parent() {\n  const arr = useMemo(() => [1,2,3], []);\n  return <Child arr={arr} />;\n}", "explanation": "通过useMemo缓存props引用，避免无意义的重渲染。"}]}}, {"name": "State Design Optimization", "trans": ["状态设计优化"], "usage": {"syntax": "// 状态设计优化：\n// 1. 状态最小化\n// 2. 局部化状态\n// 3. 避免全局状态滥用", "description": "合理设计状态结构，减少全局状态和无关依赖，能显著降低重渲染频率。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 状态最小化示例\nfunction App() {\n  // 只保存必要的状态\n  const [filter, setFilter] = useState('');\n  const filtered = useMemo(() => items.filter(i => i.includes(filter)), [items, filter]);\n  return ...\n}", "explanation": "只保存filter，避免将filtered保存为状态，减少依赖和重渲染。"}]}}, {"name": "Component Splitting Strategies", "trans": ["组件分割策略"], "usage": {"syntax": "// 组件分割策略：\n// 1. 按功能拆分\n// 2. 按状态/props依赖拆分\n// 3. 复用性高的独立组件", "description": "将大组件拆分为小而独立的子组件，按状态和props依赖分区，提升可维护性和性能。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 组件分割示例\nfunction App() {\n  return (\n    <Header />\n    <MainContent />\n    <Sidebar />\n    <Footer />\n  );\n}", "explanation": "将页面按功能和依赖拆分为独立组件，便于优化和复用。"}]}}, {"name": "Batch Updates", "trans": ["批量更新"], "usage": {"syntax": "// 批量更新：\n// 1. React 18自动批量更新\n// 2. 手动批量更新(如unstable_batchedUpdates)\n// 3. 合理合并setState", "description": "React 18及以上自动批量处理多次setState，减少渲染次数。也可手动合并更新，提升性能。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 批量更新示例\nimport { unstable_batchedUpdates } from 'react-dom';\nfunction handleClick() {\n  unstable_batchedUpdates(() => {\n    setA(a => a + 1);\n    setB(b => b + 1);\n  });\n}\n// React 18后setState自动批量处理", "explanation": "多次setState在事件/异步中会自动批量处理，减少渲染次数。"}]}}, {"name": "作业：优化渲染实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 用DevTools分析重渲染原因\n// 2. 优化状态设计和组件拆分\n// 3. 用useMemo、memo等减少重渲染\n// 4. 实现批量更新和性能对比", "description": "通过实践渲染优化原则、工具分析、状态设计、组件拆分和批量更新，掌握高效React开发。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 用Profiler分析渲染\n// 2. 优化props和状态依赖\n// 3. 用memo、useMemo减少重渲染\n// 4. 合理拆分组件和批量更新", "explanation": "作业提示，学生需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nimport React, { useState, useMemo, Profiler } from 'react';\nconst Child = React.memo(({ value }) => {\n  console.log('渲染Child', value);\n  return <div>{value}</div>;\n});\nfunction App() {\n  const [a, setA] = useState(0);\n  const [b, setB] = useState(0);\n  const memoB = useMemo(() => b * 2, [b]);\n  function onRender(id, phase, duration) {\n    console.log(id, phase, duration);\n  }\n  return (\n    <Profiler id=\"App\" onRender={onRender}>\n      <button onClick={() => setA(a + 1)}>A</button>\n      <button onClick={() => setB(b + 1)}>B</button>\n      <Child value={a} />\n      <Child value={memoB} />\n    </Profiler>\n  );\n}\nexport default App;", "explanation": "完整实现了渲染优化、依赖分析、memo和批量更新。"}]}}]}