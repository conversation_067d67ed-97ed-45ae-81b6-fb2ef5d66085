{"name": "Jump Statements", "trans": ["跳转语句"], "methods": [{"name": "Return Statement", "trans": ["return语句"], "usage": {"syntax": "// 无返回值的函数\nvoid functionName() {\n  // 函数体\n  return; // 可选，结束函数执行\n}\n\n// 有返回值的函数\nreturnType functionName() {\n  // 函数体\n  return expression; // 表达式类型必须与returnType兼容\n}\n\n// 箭头函数中的隐式return\nreturnType functionName() => expression; // 相当于 return expression;", "description": "return语句用于结束函数的执行并返回一个值给调用者。在void函数中，return语句可以不带表达式，仅用于提前结束函数执行；在有返回值的函数中，return语句必须返回与函数声明的返回类型兼容的值。如果函数声明了返回类型（非void）但没有显式的return语句，Dart会隐式返回null（对于可空返回类型）或抛出错误（对于非空返回类型）。在箭头函数（=>）中，表达式的值会被自动返回，无需显式的return关键字。return语句会立即终止当前函数的执行，包括所有循环和嵌套块，并将控制权交回给调用者。", "parameters": [], "returnValue": "与函数声明的返回类型兼容的值，或者无返回值（void函数）", "examples": [{"code": "void main() {\n  // 调用带返回值的函数\n  int sum = addNumbers(5, 3);\n  print('5 + 3 = $sum');\n  \n  // 调用返回字符串的函数\n  String greeting = getGreeting('Alice');\n  print(greeting);\n  \n  // 调用void函数\n  printInfo('这是一条信息');\n  \n  // 调用使用提前return的函数\n  int result = calculateWithCheck(10, 0);\n  print('计算结果: $result');\n  \n  result = calculateWithCheck(10, 5);\n  print('计算结果: $result');\n  \n  // 调用递归函数\n  int factorial = calculateFactorial(5);\n  print('5的阶乘是: $factorial');\n  \n  // 调用返回布尔值的函数\n  bool isEven = checkIfEven(4);\n  print('4是偶数? $isEven');\n  \n  // 调用箭头函数\n  int doubled = doubleNumber(7);\n  print('7的两倍是: $doubled');\n  \n  // 多个return语句示例\n  String grade = getGrade(85);\n  print('85分的等级是: $grade');\n  \n  // return语句在循环中\n  int firstEven = findFirstEven([1, 3, 5, 7, 8, 9]);\n  print('第一个偶数是: $firstEven');\n  \n  // 使用return语句检查列表中是否包含元素\n  bool containsValue = contains([10, 20, 30, 40], 30);\n  print('列表包含30? $containsValue');\n  \n  // 返回列表的函数\n  List<int> squares = getSquares(5);\n  print('前5个数的平方: $squares');\n  \n  // 返回Map的函数\n  Map<String, int> countMap = countCharacters('hello');\n  print('字符频率: $countMap');\n  \n  // 返回函数的函数\n  var adder = createAdder(10);\n  print('10 + 5 = ${adder(5)}');\n  \n  // 返回多个值（使用记录/元组）\n  var stats = calculateStats([1, 5, 3, 9, 2]);\n  print('最小值: ${stats.$1}, 最大值: ${stats.$2}, 平均值: ${stats.$3}');\n  \n  // 在异步函数中使用return\n  testAsyncReturn();\n}\n\n// 带返回值的基本函数\nint addNumbers(int a, int b) {\n  return a + b;\n}\n\n// 返回字符串的函数\nString getGreeting(String name) {\n  return 'Hello, $name!';\n}\n\n// 无返回值的函数(void)\nvoid printInfo(String message) {\n  print('信息: $message');\n  // return; // 可选的，通常省略\n}\n\n// 使用条件提前return的函数\nint calculateWithCheck(int a, int b) {\n  // 安全检查，避免除以零\n  if (b == 0) {\n    print('错误: 除数不能为零');\n    return 0; // 提前返回，不执行后续代码\n  }\n  \n  // 只有在b不为零时才会执行这里的代码\n  return a ~/ b;\n}\n\n// 递归函数使用return\nint calculateFactorial(int n) {\n  // 基本情况\n  if (n <= 1) {\n    return 1;\n  }\n  \n  // 递归情况\n  return n * calculateFactorial(n - 1);\n}\n\n// 返回布尔值的函数\nbool checkIfEven(int number) {\n  return number % 2 == 0;\n}\n\n// 使用箭头语法的隐式return\nint doubleNumber(int number) => number * 2;\n\n// 多个return语句的函数\nString getGrade(int score) {\n  if (score >= 90) {\n    return 'A';\n  } else if (score >= 80) {\n    return 'B';\n  } else if (score >= 70) {\n    return 'C';\n  } else if (score >= 60) {\n    return 'D';\n  } else {\n    return 'F';\n  }\n}\n\n// 在循环中使用return提前退出\nint findFirstEven(List<int> numbers) {\n  for (int number in numbers) {\n    if (number % 2 == 0) {\n      return number; // 找到第一个偶数就返回\n    }\n  }\n  return -1; // 如果没有偶数，返回-1\n}\n\n// 检查列表是否包含元素\nbool contains(List<int> list, int value) {\n  for (int item in list) {\n    if (item == value) {\n      return true;\n    }\n  }\n  return false;\n}\n\n// 返回列表的函数\nList<int> getSquares(int count) {\n  List<int> result = [];\n  for (int i = 1; i <= count; i++) {\n    result.add(i * i);\n  }\n  return result;\n}\n\n// 返回Map的函数\nMap<String, int> countCharacters(String text) {\n  Map<String, int> result = {};\n  for (int i = 0; i < text.length; i++) {\n    String char = text[i];\n    result[char] = (result[char] ?? 0) + 1;\n  }\n  return result;\n}\n\n// 返回函数的函数\nFunction createAdder(int addBy) {\n  // 返回一个匿名函数\n  return (int x) {\n    return x + addBy;\n  };\n}\n\n// 使用记录（元组）返回多个值\n(int, int, double) calculateStats(List<int> numbers) {\n  if (numbers.isEmpty) {\n    return (0, 0, 0.0);\n  }\n  \n  int min = numbers[0];\n  int max = numbers[0];\n  int sum = 0;\n  \n  for (int num in numbers) {\n    if (num < min) min = num;\n    if (num > max) max = num;\n    sum += num;\n  }\n  \n  double average = sum / numbers.length;\n  return (min, max, average);\n}\n\n// 异步函数中的return\nFuture<void> testAsyncReturn() async {\n  print('开始异步函数');\n  \n  await Future.delayed(Duration(milliseconds: 100));\n  print('异步操作完成');\n  \n  // 异步函数中的return\n  return; // 可选的，表示异步函数完成\n}", "explanation": "这个示例全面展示了Dart中return语句的多种用法。首先展示了基本的带返回值函数和无返回值（void）函数。然后演示了条件提前return的用法，用于避免无效操作或错误情况。示例还包括递归函数中的return、箭头函数中的隐式return以及具有多个return语句的函数。此外，示例展示了在循环中使用return提前退出、返回不同类型的值（整数、布尔值、字符串、列表、Map）的函数，以及返回函数的高阶函数。最后，示例展示了使用记录（元组）返回多个值的方式和在异步函数中使用return的情况。这些示例说明了return语句如何控制函数的执行流程并将值传递回调用者。"}]}}, {"name": "Throw Statement", "trans": ["throw语句"], "usage": {"syntax": "// 抛出异常\nthrow exception;\n\n// 例如:\nthrow Exception('发生错误');\nthrow '这是一个错误消息'; // 可以抛出任何对象\n\n// 重新抛出异常\ntry {\n  // 代码\n} catch (e) {\n  // 处理\n  throw; // 重新抛出当前捕获的异常\n}", "description": "throw语句用于抛出（引发）一个异常，表示程序遇到了错误或异常情况。在Dart中，可以抛出任何对象作为异常，但通常建议抛出实现了Error或Exception接口的对象。当抛出异常时，程序的正常执行流程会被中断，控制权会沿着调用栈向上传递，直到找到匹配的catch块处理该异常，或者程序终止。throw语句常用于表示不可恢复的错误、参数验证失败、资源不可用等情况。throw语句可以与try-catch-finally结构结合使用，以实现错误处理和资源清理。在catch块中使用不带参数的throw可以重新抛出当前捕获的异常，这在需要执行一些清理操作但仍希望让调用者处理异常的情况下很有用。", "parameters": [], "returnValue": "无返回值，会中断当前执行流程", "examples": [{"code": "void main() {\n  // 基本异常处理\n  try {\n    print('调用可能抛出异常的函数');\n    int result = divideNumbers(10, 0);\n    print('结果: $result'); // 这行不会执行\n  } catch (e) {\n    print('捕获到异常: $e');\n  } finally {\n    print('finally块总是执行');\n  }\n  \n  print('\\n验证参数示例:');\n  try {\n    validateAge(-5);\n  } catch (e) {\n    print('年龄验证失败: $e');\n  }\n  \n  try {\n    validateAge(25); // 有效年龄\n    print('年龄验证通过');\n  } catch (e) {\n    print('这不应该发生');\n  }\n  \n  print('\\n抛出不同类型的异常:');\n  try {\n    throwDifferentExceptions(1);\n  } catch (e) {\n    print('捕获到异常类型1: $e');\n  }\n  \n  try {\n    throwDifferentExceptions(2);\n  } catch (e) {\n    print('捕获到异常类型2: $e');\n  }\n  \n  try {\n    throwDifferentExceptions(3);\n  } catch (e) {\n    print('捕获到异常类型3: $e');\n  }\n  \n  print('\\n捕获特定类型的异常:');\n  try {\n    throwDifferentExceptions(2);\n  } on FormatException {\n    print('捕获到FormatException');\n  } on ArgumentError {\n    print('捕获到ArgumentError');\n  } catch (e) {\n    print('捕获到其他类型的异常: $e');\n  }\n  \n  print('\\n重新抛出异常示例:');\n  try {\n    try {\n      throwDifferentExceptions(1);\n    } catch (e) {\n      print('内部catch: $e');\n      print('执行一些清理操作');\n      throw; // 重新抛出当前异常\n    }\n  } catch (e) {\n    print('外部catch: $e');\n  }\n  \n  print('\\n自定义异常示例:');\n  try {\n    validateUser('', 'password');\n  } catch (e) {\n    if (e is InvalidUserException) {\n      print('用户验证失败: ${e.message}');\n      print('错误代码: ${e.errorCode}');\n    } else {\n      print('发生未知错误: $e');\n    }\n  }\n  \n  print('\\n异常堆栈示例:');\n  try {\n    throwWithStackTrace();\n  } catch (e, stackTrace) {\n    print('异常: $e');\n    print('堆栈跟踪:');\n    print(stackTrace);\n  }\n  \n  print('\\n完成');\n}\n\n// 可能抛出异常的函数\nint divideNumbers(int a, int b) {\n  if (b == 0) {\n    throw Exception('除数不能为零');\n  }\n  return a ~/ b;\n}\n\n// 参数验证示例\nvoid validateAge(int age) {\n  if (age < 0) {\n    throw ArgumentError('年龄不能为负数');\n  }\n  if (age > 120) {\n    throw ArgumentError('年龄不合理');\n  }\n  // 验证通过，继续执行\n}\n\n// 抛出不同类型的异常\nvoid throwDifferentExceptions(int type) {\n  switch (type) {\n    case 1:\n      throw ArgumentError('参数错误');\n    case 2:\n      throw FormatException('格式错误');\n    case 3:\n      throw '这是一个字符串异常'; // 可以抛出任何对象\n    default:\n      throw Exception('未知错误');\n  }\n}\n\n// 自定义异常类\nclass InvalidUserException implements Exception {\n  final String message;\n  final int errorCode;\n  \n  InvalidUserException(this.message, this.errorCode);\n  \n  @override\n  String toString() => 'InvalidUserException: $message (错误代码: $errorCode)';\n}\n\n// 使用自定义异常的函数\nvoid validateUser(String username, String password) {\n  if (username.isEmpty) {\n    throw InvalidUserException('用户名不能为空', 1001);\n  }\n  if (password.isEmpty) {\n    throw InvalidUserException('密码不能为空', 1002);\n  }\n  if (password.length < 6) {\n    throw InvalidUserException('密码长度不能少于6个字符', 1003);\n  }\n  // 验证通过\n}\n\n// 包含堆栈跟踪的异常\nvoid throwWithStackTrace() {\n  helperFunction();\n}\n\nvoid helperFunction() {\n  deeperFunction();\n}\n\nvoid deeperFunction() {\n  throw Exception('这个异常包含完整的堆栈跟踪');\n}\n\n// 以下是更多相关的异常处理示例\n\n// 在异步函数中抛出异常\nFuture<void> asyncFunction() async {\n  await Future.delayed(Duration(milliseconds: 100));\n  throw Exception('异步操作中的错误');\n}\n\n// 使用rethrow关键字（效果与throw相同，但语义更明确）\nvoid rethrowExample() {\n  try {\n    throw Exception('原始异常');\n  } catch (e) {\n    print('记录错误: $e');\n    rethrow; // 等同于throw，但更明确表示这是重新抛出\n  }\n}\n\n// 在构造函数中抛出异常\nclass User {\n  final String name;\n  final int age;\n  \n  User(this.name, this.age) {\n    if (name.isEmpty) {\n      throw ArgumentError('名称不能为空');\n    }\n    if (age < 0) {\n      throw ArgumentError('年龄不能为负数');\n    }\n  }\n}\n\n// 断言（在开发模式下会抛出AssertionError）\nvoid assertExample(int value) {\n  assert(value >= 0, '值不能为负数'); // 仅在开发模式下检查\n  print('值是: $value');\n}", "explanation": "这个示例全面展示了Dart中throw语句的多种用法。首先展示了基本的异常抛出和捕获机制，包括try-catch-finally结构。然后演示了如何使用throw进行参数验证，抛出不同类型的异常（包括内置异常类型和自定义对象），以及如何捕获特定类型的异常。示例还包括重新抛出异常的用法，展示了如何在处理异常后将其传递给上层调用者。此外，示例展示了如何创建和使用自定义异常类，以提供更多关于错误的信息，如错误消息和错误代码。最后，示例展示了如何获取和使用异常的堆栈跟踪信息，以及在异步函数、构造函数中抛出异常和使用assert断言的情况。这些示例说明了throw语句如何用于表示和处理程序中的异常情况。"}]}}, {"name": "Assignment", "trans": ["实践作业"], "usage": {"syntax": "// 跳转语句实践", "description": "完成以下任务，练习Dart中跳转语句的使用：1) 创建一个函数，接受一个整数数组和一个目标值，使用return语句实现查找目标值并返回其索引（如果不存在则返回-1）；2) 创建一个自定义异常类，实现验证用户输入的函数，对无效输入抛出适当的异常；3) 实现一个函数，使用try-catch-finally处理可能的异常情况，并确保资源被正确释放；4) 挑战：实现一个计算器类，支持基本运算操作，使用throw处理无效操作（如除以零），并使用适当的异常类型提供有用的错误信息。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "void main() {\n  // 1. 查找目标值并返回索引\n  List<int> numbers = [5, 8, 2, 10, 15, 3, 7];\n  int target = 10;\n  int index = findIndex(numbers, target);\n  \n  if (index != -1) {\n    print('找到目标值 $target，位于索引 $index');\n  } else {\n    print('未找到目标值 $target');\n  }\n  \n  // 测试目标值不存在的情况\n  int missingTarget = 20;\n  int missingIndex = findIndex(numbers, missingTarget);\n  print('查找目标值 $missingTarget 的结果: ${missingIndex == -1 ? \"未找到\" : \"找到在索引 $missingIndex\"}');\n  \n  // 2. 用户输入验证与自定义异常\n  try {\n    validateUserInput('john_doe', 'pass'); // 密码太短\n  } catch (e) {\n    print('验证失败: $e');\n  }\n  \n  try {\n    validateUserInput('jo', 'password123'); // 用户名太短\n  } catch (e) {\n    print('验证失败: $e');\n  }\n  \n  try {\n    validateUserInput('john_doe', 'password123'); // 有效输入\n    print('用户输入验证通过');\n  } catch (e) {\n    print('验证失败: $e');\n  }\n  \n  // 3. 资源管理与异常处理\n  processFile('example.txt');\n  processFile('non_existent.txt'); // 文件不存在\n  processFile('corrupt.txt'); // 文件损坏\n  \n  // 4. 计算器类\n  var calculator = Calculator();\n  \n  try {\n    print('5 + 3 = ${calculator.calculate(5, 3, Operation.add)}');\n    print('10 - 4 = ${calculator.calculate(10, 4, Operation.subtract)}');\n    print('6 * 7 = ${calculator.calculate(6, 7, Operation.multiply)}');\n    print('20 / 4 = ${calculator.calculate(20, 4, Operation.divide)}');\n    print('10 / 0 = ${calculator.calculate(10, 0, Operation.divide)}'); // 除以零\n  } catch (e) {\n    print('计算错误: $e');\n  }\n  \n  try {\n    print('7 ^ 2 = ${calculator.calculate(7, 2, Operation.power)}');\n    print('操作 \"模\" = ${calculator.calculate(10, 3, Operation.modulo)}');\n    print('无效操作 = ${calculator.calculate(5, 5, \"invalid\" as Operation)}'); // 无效操作\n  } catch (e) {\n    print('计算错误: $e');\n  }\n}\n\n// 1. 查找目标值返回索引的函数\nint findIndex(List<int> numbers, int target) {\n  if (numbers == null || numbers.isEmpty) {\n    return -1;\n  }\n  \n  for (int i = 0; i < numbers.length; i++) {\n    if (numbers[i] == target) {\n      return i; // 找到目标值，返回索引\n    }\n  }\n  \n  return -1; // 未找到目标值\n}\n\n// 2. 自定义异常类\nclass UserInputException implements Exception {\n  final String message;\n  final String field;\n  final int code;\n  \n  UserInputException(this.message, this.field, this.code);\n  \n  @override\n  String toString() => 'UserInputException[$code]: $field - $message';\n}\n\n// 用户输入验证函数\nvoid validateUserInput(String username, String password) {\n  // 验证用户名\n  if (username == null || username.isEmpty) {\n    throw UserInputException('用户名不能为空', 'username', 100);\n  }\n  if (username.length < 3) {\n    throw UserInputException('用户名长度不能少于3个字符', 'username', 101);\n  }\n  if (username.contains(' ')) {\n    throw UserInputException('用户名不能包含空格', 'username', 102);\n  }\n  \n  // 验证密码\n  if (password == null || password.isEmpty) {\n    throw UserInputException('密码不能为空', 'password', 200);\n  }\n  if (password.length < 6) {\n    throw UserInputException('密码长度不能少于6个字符', 'password', 201);\n  }\n  \n  // 如果验证通过，函数正常结束\n}\n\n// 3. 模拟文件处理和资源管理的函数\nvoid processFile(String filename) {\n  print('\\n处理文件: $filename');\n  var resource;\n  \n  try {\n    // 模拟打开文件\n    resource = openFile(filename);\n    \n    // 模拟读取和处理文件内容\n    String content = readFileContent(resource, filename);\n    print('文件内容: $content');\n    \n    // 模拟处理文件内容\n    processContent(content);\n    \n    print('文件处理成功');} catch (e) {\n    print('处理文件出错: $e');\n  } finally {\n    // 确保资源被释放，无论是否发生异常\n    if (resource != null) {\n      print('关闭文件资源');\n      closeFile(resource);\n    } else {\n      print('没有资源需要关闭');\n    }\n  }\n}\n\n// 模拟打开文件的函数\nString openFile(String filename) {\n  if (filename == 'non_existent.txt') {\n    throw FileSystemException('文件不存在', filename);\n  }\n  print('打开文件: $filename');\n  return 'file_handle_$filename'; // 模拟文件句柄\n}\n\n// 模拟读取文件内容的函数\nString readFileContent(String resource, String filename) {\n  if (filename == 'corrupt.txt') {\n    throw FormatException('文件格式损坏', filename);\n  }\n  print('读取文件内容');\n  return '这是$filename的内容';\n}\n\n// 模拟处理文件内容的函数\nvoid processContent(String content) {\n  print('处理内容: $content');\n}\n\n// 模拟关闭文件的函数\nvoid closeFile(String resource) {\n  print('关闭文件: $resource');\n}\n\n// 4. 计算器实现\n\n// 操作枚举\nenum Operation {\n  add,\n  subtract,\n  multiply,\n  divide,\n  power,\n  modulo\n}\n\n// 计算器异常类\nclass CalculatorException implements Exception {\n  final String message;\n  final Operation? operation;\n  \n  CalculatorException(this.message, [this.operation]);\n  \n  @override\n  String toString() {\n    if (operation != null) {\n      return 'CalculatorException: $message (操作: ${operation.toString().split('.').last})';\n    }\n    return 'CalculatorException: $message';\n  }\n}\n\n// 计算器类\nclass Calculator {\n  double calculate(double a, double b, Operation operation) {\n    switch (operation) {\n      case Operation.add:\n        return a + b;\n      case Operation.subtract:\n        return a - b;\n      case Operation.multiply:\n        return a * b;\n      case Operation.divide:\n        if (b == 0) {\n          throw CalculatorException('除数不能为零', operation);\n        }\n        return a / b;\n      case Operation.power:\n        return _power(a, b);\n      case Operation.modulo:\n        if (b == 0) {\n          throw CalculatorException('模运算的除数不能为零', operation);\n        }\n        return a % b;\n      default:\n        throw CalculatorException('不支持的操作');\n    }\n  }\n  \n  // 计算幂的辅助方法\n  double _power(double base, double exponent) {\n    if (exponent.toInt() != exponent) {\n      throw CalculatorException('指数必须是整数', Operation.power);\n    }\n    \n    if (exponent < 0) {\n      throw CalculatorException('暂不支持负指数', Operation.power);\n    }\n    \n    double result = 1;\n    for (int i = 0; i < exponent; i++) {\n      result *= base;\n    }\n    return result;\n  }\n}", "explanation": "这个参考实现完成了四个跳转语句练习任务。第一个任务实现了一个使用return语句查找目标值并返回其索引的函数。第二个任务创建了一个自定义的UserInputException异常类，并实现了一个验证用户输入的函数，对无效输入抛出适当的异常。第三个任务实现了一个处理文件的函数，使用try-catch-finally结构确保无论是否发生异常，资源都能被正确释放。最后的挑战任务实现了一个计算器类，支持多种运算操作，使用自定义的CalculatorException处理无效操作和错误情况。这些实现展示了return和throw语句在实际应用中的用法，以及如何设计和使用异常处理机制来提高程序的健壮性。"}]}}]}