# MySQL学习路线与核心内容

## 1. MySQL基础     //已完成

### 数据库与表
- 数据库概念
- 创建数据库
- 删除数据库
- 创建表
- 删除表
- 查看表结构
- 修改表结构

### 数据类型
- 数值类型
- 字符串类型
- 日期与时间类型
- 枚举与集合类型
- NULL与默认值

### 基本增删改查（CRUD）
- 插入数据（INSERT）
- 查询数据（SELECT）
- 更新数据（UPDATE）
- 删除数据（DELETE）
- 条件查询（WHERE）
- 排序与限制（ORDER BY, LIMIT）

### 约束与主键
- 主键（PRIMARY KEY）
- 唯一约束（UNIQUE）
- 非空约束（NOT NULL）
- 默认值（DEFAULT）
- 外键约束（FOREIGN KEY）

### 简单练习
- 创建一个学生信息表，包含学号、姓名、性别、年龄，插入3条数据，并查询所有学生信息。

---

## 2. 查询进阶      //已完成

### 多表查询
- 内连接（INNER JOIN）
- 左连接（LEFT JOIN）
- 右连接（RIGHT JOIN）
- 自连接（SELF JOIN）

### 聚合与分组
- COUNT、SUM、AVG、MAX、MIN
- GROUP BY分组
- HAVING条件

### 子查询
- 标量子查询
- 列子查询
- 行子查询
- IN/EXISTS子查询

### 视图
- 创建视图
- 查询视图
- 更新视图
- 删除视图

### 查询进阶练习
- 查询每个班级的学生人数，找出人数大于2的班级。

---

## 3. 索引与性能优化  //已完成

### 索引基础
- 创建索引
- 唯一索引
- 复合索引
- 删除索引
- 查看索引

### 查询优化
- EXPLAIN分析
- 查询重写
- 覆盖索引
- 慢查询日志

### 表设计优化
- 规范化与反规范化
- 合理选择数据类型
- 分区表

### 性能优化练习
- 为学生表的学号和姓名建立合适的索引，并用EXPLAIN分析查询效果。

---

## 4. 事务与锁      //已完成

### 事务基础
- 事务的ACID特性
- 开启与提交事务
- 回滚事务

### 锁机制
- 行级锁与表级锁
- 共享锁与排他锁
- 死锁与解决

### 隔离级别
- READ UNCOMMITTED
- READ COMMITTED
- REPEATABLE READ
- SERIALIZABLE

### 事务与锁练习
- 演示两个会话同时修改同一条数据，观察锁的行为。

---

## 5. 用户管理与安全    //已完成

### 用户与权限
- 创建用户
- 授权与回收权限
- 查看权限
- 删除用户

### 安全设置
- 密码策略
- 防SQL注入
- 数据备份与恢复

### 用户管理练习
- 创建一个只读用户，只允许查询学生表。

---

## 6. 数据备份与恢复    //已完成

### 备份方式
- mysqldump命令
- 物理备份

### 恢复数据
- 恢复SQL文件
- 恢复物理备份

### 备份与恢复练习
- 用mysqldump导出学生表数据，并恢复到新表。

---

## 7. 高级应用   //已完成

### 存储过程与函数
- 创建存储过程
- 调用存储过程
- 创建函数
- 使用函数

### 触发器
- 创建触发器
- 删除触发器
- 触发器应用场景

### 事件调度器
- 创建事件
- 定时任务

### JSON与全文索引
- JSON数据类型
- JSON查询
- 全文索引与搜索

### 高级应用练习
- 编写一个存储过程，自动为新插入的学生分配学号。

---

## 8. 运维与监控 //已完成

### 日志管理
- 错误日志
- 查询日志
- 慢查询日志

### 监控工具
- SHOW STATUS
- SHOW PROCESSLIST
- information_schema

### 常见故障排查
- 连接数过多
- 死锁检测
- 性能瓶颈分析

### 运维练习
- 用SHOW命令监控当前数据库连接和慢查询。

---

## 9. 综合实战      //已完成

### 项目实战
- 设计并实现一个学生成绩管理系统，包含学生、课程、成绩三张表，支持增删改查、成绩统计、排名等功能。

### 综合练习
- 完成一个完整的数据库设计、数据录入、查询优化、权限分配和备份恢复流程。 