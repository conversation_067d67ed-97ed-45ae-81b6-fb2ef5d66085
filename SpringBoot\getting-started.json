{"name": "Getting Started", "trans": ["入门与环境搭建"], "methods": [{"name": "Introduction to <PERSON> Boot", "trans": ["Spring Boot简介"], "usage": {"syntax": "Spring Boot = Spring框架 + 自动配置 + 快速开发工具", "description": "Spring Boot是基于Spring的快速开发框架，简化配置，内嵌服务器，开箱即用，适合微服务和企业级应用。", "parameters": [{"name": "Spring Boot", "description": "Spring官方推出的快速开发框架"}], "returnValue": "简化开发、自动配置、内嵌服务器的Spring应用", "examples": [{"code": "// Spring Boot的核心特性：\n// 1. 自动配置（AutoConfiguration）\n// 2. 内嵌Web服务器（Tomcat/Jetty/Undertow）\n// 3. 无需XML配置，约定优于配置\n// 4. 一键启动和打包部署\n// 5. 丰富的Starter依赖，集成常用技术栈", "explanation": "Spring Boot让Java后端开发更高效、易维护。"}]}}, {"name": "Version Selection and Compatibility", "trans": ["版本选择与兼容性"], "usage": {"syntax": "选择与JDK、Spring Framework兼容的Spring Boot版本", "description": "根据项目需求和依赖选择合适的Spring Boot版本，注意与JDK、Spring、第三方库的兼容性。", "parameters": [{"name": "Spring Boot版本", "description": "如2.7.x、3.x"}, {"name": "JDK版本", "description": "如JDK8、JDK11、JDK17"}], "returnValue": "兼容的Spring Boot开发环境", "examples": [{"code": "// 版本兼容性示例：\n// Spring Boot 2.7.x 支持 JDK8/11/17\n// Spring Boot 3.x 仅支持 JDK17及以上，基于Spring Framework 6\n// 选择版本时参考官方文档：https://spring.io/projects/spring-boot#support", "explanation": "合理选择版本，避免依赖冲突和兼容性问题。"}]}}, {"name": "Development Environment Setup", "trans": ["开发环境搭建"], "usage": {"syntax": "1. 安装JDK\n2. 安装IDE（IntelliJ IDEA/VSCode/Eclipse）\n3. 配置Maven/Gradle\n4. 配置环境变量", "description": "搭建Spring Boot开发环境需安装JDK、开发工具、构建工具，并配置环境变量。", "parameters": [{"name": "JDK", "description": "Java开发工具包"}, {"name": "IDE", "description": "集成开发环境"}, {"name": "<PERSON><PERSON>/<PERSON>radle", "description": "项目构建工具"}], "returnValue": "可用于Spring Boot开发的本地环境", "examples": [{"code": "// 1. 安装JDK17（以Windows为例）\n// 下载地址：https://www.oracle.com/java/technologies/downloads/\n// 安装后配置JAVA_HOME和PATH环境变量\n\n// 2. 安装IntelliJ IDEA（推荐）\n// 3. 安装Maven\n// 下载地址：https://maven.apache.org/download.cgi\n// 配置MAVEN_HOME和PATH\n\n// 4. 验证环境\njava -version\nmvn -version", "explanation": "确保JDK和Maven/Gradle环境可用，推荐使用IDEA进行开发。"}]}}, {"name": "First Spring Boot Project", "trans": ["第一个Spring Boot项目"], "usage": {"syntax": "1. 使用Spring Initializr创建项目\n2. 编写主类和Controller\n3. 启动应用", "description": "通过Spring Initializr快速生成Spring Boot项目，编写主类和简单的REST接口，启动并访问。", "parameters": [{"name": "Spring Initializr", "description": "官方项目生成器"}, {"name": "@SpringBootApplication", "description": "主启动类注解"}, {"name": "@RestController", "description": "声明REST接口"}], "returnValue": "可运行的Spring Boot Web应用", "examples": [{"code": "// 1. 访问 https://start.spring.io 选择依赖生成项目\n// 2. 主类示例：\npackage com.example.demo;\n\nimport org.springframework.boot.SpringApplication;\nimport org.springframework.boot.autoconfigure.SpringBootApplication;\n\n@SpringBootApplication\npublic class DemoApplication {\n    public static void main(String[] args) {\n        SpringApplication.run(DemoApplication.class, args);\n    }\n}\n\n// 3. 编写Controller：\npackage com.example.demo.controller;\n\nimport org.springframework.web.bind.annotation.GetMapping;\nimport org.springframework.web.bind.annotation.RestController;\n\n@RestController\npublic class HelloController {\n    @GetMapping(\"hello\")\n    public String hello() {\n        return \"Hello, Spring Boot!\";\n    }\n}\n\n// 4. 启动项目并访问 http://localhost:8080/hello\n// 输出：Hello, Spring Boot!", "explanation": "完整流程：项目生成、主类、Controller、启动和访问。"}]}}, {"name": "Project Structure Analysis", "trans": ["目录结构解析"], "usage": {"syntax": "src/main/java  // 业务代码\nsrc/main/resources // 配置和资源\npom.xml // Maven依赖管理", "description": "Spring Boot项目采用标准Maven/Gradle结构，分为代码、资源、依赖配置等部分。", "parameters": [{"name": "src/main/java", "description": "存放Java源代码"}, {"name": "src/main/resources", "description": "存放配置文件和静态资源"}, {"name": "pom.xml/build.gradle", "description": "依赖和构建配置"}], "returnValue": "清晰分层的项目目录结构", "examples": [{"code": "// 典型Spring Boot项目结构：\nmy-demo/\n├── src/\n│   ├── main/\n│   │   ├── java/com/example/demo/\n│   │   │   ├── DemoApplication.java\n│   │   │   └── controller/HelloController.java\n│   │   └── resources/\n│   │       ├── application.properties\n│   │       └── static/\n│   └── test/\n│       └── java/\n├── pom.xml\n", "explanation": "每一层目录的作用清晰，便于维护和扩展。"}]}}, {"name": "Startup Principle", "trans": ["启动原理"], "usage": {"syntax": "SpringApplication.run(主类.class, args)", "description": "Spring Boot通过SpringApplication.run启动，自动加载配置、扫描组件、启动内嵌服务器。", "parameters": [{"name": "SpringApplication.run", "description": "启动Spring Boot应用的静态方法"}, {"name": "@SpringBootApplication", "description": "复合注解，包含配置、自动装配、组件扫描"}], "returnValue": "自动初始化和运行的Spring Boot应用", "examples": [{"code": "// 启动流程详解：\n// 1. 执行main方法，调用SpringApplication.run\n// 2. 加载application.properties/yml配置\n// 3. 扫描@SpringBootApplication所在包及子包的组件\n// 4. 自动装配各种Bean和依赖\n// 5. 启动内嵌Tomcat并监听端口\n// 6. 应用启动完成，Ready to service\n\n// 关键代码：\n@SpringBootApplication\npublic class DemoApplication {\n    public static void main(String[] args) {\n        SpringApplication.run(DemoApplication.class, args);\n    }\n}", "explanation": "Spring Boot启动过程自动完成配置、装配和服务启动。"}]}}, {"name": "Getting Started Assignment", "trans": ["入门与环境搭建练习"], "usage": {"syntax": "# 入门与环境搭建练习", "description": "完成以下练习，巩固Spring Boot入门与环境搭建相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 选择合适的Spring Boot和JDK版本。\n2. 本地搭建开发环境。\n3. 用Spring Initializr创建第一个Spring Boot项目。\n4. 编写主类和简单Controller。\n5. 理解并画出项目目录结构。\n6. 详细分析Spring Boot启动流程。", "explanation": "通过这些练习掌握Spring Boot开发的基础。"}]}}]}