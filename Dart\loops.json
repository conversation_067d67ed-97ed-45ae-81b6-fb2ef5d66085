{"name": "Loops", "trans": ["循环语句"], "methods": [{"name": "For Loop", "trans": ["for循环"], "usage": {"syntax": "// 标准for循环\nfor (初始化; 条件; 增量) {\n  // 循环体\n}\n\n// 例如:\nfor (int i = 0; i < 5; i++) {\n  // 循环体\n}\n\n// 无限循环\nfor (;;) {\n  // 无限执行，需要通过break或return退出\n}\n\n// 多变量初始化和多变量增量\nfor (int i = 0, j = 10; i < j; i++, j--) {\n  // 循环体\n}", "description": "for循环是一种用于重复执行特定代码块的控制结构，通常用于已知迭代次数的场景。for循环由三个部分组成：初始化语句（执行一次）、条件表达式（每次迭代前检查）和增量表达式（每次迭代后执行）。Dart的for循环与C、Java等语言类似，但更加灵活，支持多变量初始化和增量。for循环可以嵌套使用，并且可以使用break、continue等语句控制循环流程。for循环适合处理基于索引的迭代，如数组或列表的遍历，以及执行特定次数的操作。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "void main() {\n  // 基本for循环\n  print('基本for循环:');\n  for (int i = 0; i < 5; i++) {\n    print('i = $i');\n  }\n  \n  // for循环遍历列表\n  List<String> fruits = ['苹果', '香蕉', '橙子', '葡萄'];\n  print('\\n使用for循环遍历列表:');\n  for (int i = 0; i < fruits.length; i++) {\n    print('水果 ${i+1}: ${fruits[i]}');\n  }\n  \n  // 使用for循环计算总和\n  List<int> numbers = [10, 20, 30, 40, 50];\n  int sum = 0;\n  for (int i = 0; i < numbers.length; i++) {\n    sum += numbers[i];\n  }\n  print('\\n数字总和: $sum');\n  \n  // 倒序for循环\n  print('\\n倒序for循环:');\n  for (int i = 5; i > 0; i--) {\n    print('倒数: $i');\n  }\n  \n  // 步长不为1的for循环\n  print('\\n步长为2的for循环:');\n  for (int i = 0; i < 10; i += 2) {\n    print('i = $i');\n  }\n  \n  // 多变量for循环\n  print('\\n多变量for循环:');\n  for (int i = 0, j = 10; i < 5; i++, j--) {\n    print('i = $i, j = $j');\n  }\n  \n  // 在for循环中使用break\n  print('\\n在for循环中使用break:');\n  for (int i = 0; i < 10; i++) {\n    if (i == 5) {\n      print('达到5，跳出循环');\n      break;\n    }\n    print('i = $i');\n  }\n  \n  // 在for循环中使用continue\n  print('\\n在for循环中使用continue:');\n  for (int i = 0; i < 5; i++) {\n    if (i == 2) {\n      print('跳过2');\n      continue;\n    }\n    print('i = $i');\n  }\n  \n  // 嵌套for循环\n  print('\\n嵌套for循环:');\n  for (int i = 1; i <= 3; i++) {\n    for (int j = 1; j <= 3; j++) {\n      print('i = $i, j = $j');\n    }\n  }\n  \n  // 打印乘法表\n  print('\\n乘法表:');\n  for (int i = 1; i <= 9; i++) {\n    String row = '';\n    for (int j = 1; j <= i; j++) {\n      row += '$j×$i=${j*i} ';\n    }\n    print(row);\n  }\n  \n  // 使用for循环初始化二维列表\n  print('\\n使用for循环初始化二维列表:');\n  List<List<int>> matrix = [];\n  for (int i = 0; i < 3; i++) {\n    List<int> row = [];\n    for (int j = 0; j < 3; j++) {\n      row.add(i * 3 + j);\n    }\n    matrix.add(row);\n  }\n  \n  // 打印二维列表\n  for (int i = 0; i < matrix.length; i++) {\n    print(matrix[i]);\n  }\n  \n  // 使用for循环和条件语句\n  print('\\n使用for循环和条件语句:');\n  for (int i = 1; i <= 10; i++) {\n    if (i % 2 == 0) {\n      print('$i 是偶数');\n    } else {\n      print('$i 是奇数');\n    }\n  }\n  \n  // 使用for循环处理字符串\n  String message = 'Hello';\n  String encoded = '';\n  print('\\n使用for循环处理字符串:');\n  for (int i = 0; i < message.length; i++) {\n    encoded += message[i] + '*';\n  }\n  print('编码后: $encoded');\n  \n  // 空的for循环\n  print('\\n空的for循环:');\n  int counter = 0;\n  for (; counter < 3; counter++) {\n    print('counter = $counter');\n  }\n  \n  // for循环中的局部变量\n  print('\\n局部变量示例:');\n  for (int i = 0; i < 3; i++) {\n    // i只在循环内部可见\n    String message = '第 ${i+1} 次迭代';\n    print(message);\n  }\n  // print(i); // 错误: i在这里不可见\n  \n  // 省略for循环的某些部分\n  print('\\n省略条件的for循环:');\n  int j = 0;\n  for (; j < 3;) {\n    print('j = $j');\n    j++; // 在循环体中增加j\n  }\n  \n  // 无限循环示例(通过break退出)\n  print('\\n无限循环示例:');\n  int count = 0;\n  for (;;) {\n    print('count = $count');\n    count++;\n    if (count >= 3) {\n      print('退出无限循环');\n      break;\n    }\n  }\n}", "explanation": "这个示例全面展示了Dart中for循环的多种用法。首先演示了基本for循环的结构和用于遍历列表的方法，然后展示了如何使用for循环计算总和、倒序遍历和使用不同步长。示例还包括多变量for循环、break和continue的使用、嵌套for循环（如打印乘法表和创建二维列表）、在for循环中使用条件语句、处理字符串等。此外，示例还展示了一些特殊情况，如省略for循环的某些部分、空的for循环、for循环中的局部变量以及无限循环（通过break退出）。这些示例涵盖了for循环在各种场景下的应用，展示了它的灵活性和强大功能。"}]}}, {"name": "While Loop", "trans": ["while循环"], "usage": {"syntax": "// while循环\nwhile (条件) {\n  // 循环体\n}\n\n// 例如:\nint i = 0;\nwhile (i < 5) {\n  // 循环体\n  i++;\n}\n\n// 无限循环\nwhile (true) {\n  // 无限执行，需要通过break或return退出\n}", "description": "while循环是一种先判断后执行的循环结构，它在每次迭代前检查条件，如果条件为true则执行循环体，否则跳过循环。while循环非常适合当迭代次数事先未知或取决于循环内部计算的情况。与for循环不同，while循环没有内置的初始化和增量部分，这些必须在循环外部或循环体内手动处理。while循环也可以使用break和continue语句控制流程，可以嵌套使用，并可以与其他控制语句结合。在使用while循环时，需要特别注意确保循环条件最终会变为false，否则可能导致无限循环。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "void main() {\n  // 基本while循环\n  print('基本while循环:');\n  int i = 0;\n  while (i < 5) {\n    print('i = $i');\n    i++;\n  }\n  \n  // 使用while循环计算总和\n  print('\\n使用while循环计算总和:');\n  List<int> numbers = [10, 20, 30, 40, 50];\n  int sum = 0;\n  int index = 0;\n  \n  while (index < numbers.length) {\n    sum += numbers[index];\n    index++;\n  }\n  print('数字总和: $sum');\n  \n  // 条件处理while循环\n  print('\\n条件处理while循环:');\n  int value = 1;\n  while (value < 100) {\n    print('value = $value');\n    value *= 2; // 每次迭代将value乘以2\n  }\n  \n  // 在while循环中使用break\n  print('\\n在while循环中使用break:');\n  int count = 0;\n  while (true) { // 无限循环\n    print('count = $count');\n    count++;\n    if (count >= 5) {\n      print('达到5，跳出循环');\n      break;\n    }\n  }\n  \n  // 在while循环中使用continue\n  print('\\n在while循环中使用continue:');\n  int j = 0;\n  while (j < 5) {\n    j++;\n    if (j == 3) {\n      print('跳过3');\n      continue;\n    }\n    print('j = $j');\n  }\n  \n  // 嵌套while循环\n  print('\\n嵌套while循环:');\n  int a = 1;\n  while (a <= 3) {\n    int b = 1;\n    while (b <= 3) {\n      print('a = $a, b = $b');\n      b++;\n    }\n    a++;\n  }\n  \n  // 用while循环处理用户输入模拟\n  print('\\n用while循环处理用户输入模拟:');\n  String input = simulateUserInput();\n  while (input != 'exit') {\n    print('处理输入: $input');\n    input = simulateUserInput(input);\n  }\n  print('用户输入exit，退出循环');\n  \n  // while循环与列表处理\n  print('\\n使用while循环删除列表中的特定元素:');\n  List<int> data = [1, 2, 3, 4, 5, 2, 3, 4, 2];\n  print('原始列表: $data');\n  \n  int removeValue = 2;\n  int position = 0;\n  while (position < data.length) {\n    if (data[position] == removeValue) {\n      data.removeAt(position);\n      // 不增加position，因为当前元素被移除后，后面的元素会前移\n    } else {\n      position++;\n    }\n  }\n  print('移除$removeValue后的列表: $data');\n  \n  // 条件控制的while循环示例\n  print('\\n条件控制的while循环示例:');\n  int x = 10;\n  int y = 0;\n  \n  while (x > 0 && y < 10) {\n    print('x = $x, y = $y');\n    x--;\n    y++;\n  }\n  \n  // 无限循环中的错误处理模拟\n  print('\\n无限循环中的错误处理模拟:');\n  int attempts = 0;\n  bool success = false;\n  \n  while (!success) {\n    attempts++;\n    try {\n      // 模拟操作，可能成功或失败\n      if (attempts == 3) {\n        print('操作成功!');\n        success = true;\n      } else {\n        throw '操作失败，尝试次数: $attempts';\n      }\n    } catch (e) {\n      print('捕获错误: $e');\n      if (attempts >= 5) {\n        print('达到最大尝试次数，退出循环');\n        break;\n      }\n    }\n  }\n  \n  // while与null值处理\n  print('\\n处理可能为null的值:');\n  String? message = 'Hello';\n  while (message != null) {\n    print('处理消息: $message');\n    message = getNextMessage(message);\n  }\n  print('没有更多消息');\n}\n\n// 模拟用户输入的辅助函数\nString simulateUserInput([String? previous]) {\n  if (previous == null) {\n    return 'start';\n  } else if (previous == 'start') {\n    return 'continue';\n  } else if (previous == 'continue') {\n    return 'almost done';\n  } else {\n    return 'exit';\n  }\n}\n\n// 获取下一个消息的辅助函数\nString? getNextMessage(String current) {\n  if (current == 'Hello') {\n    return 'World';\n  } else if (current == 'World') {\n    return 'Dart';\n  } else {\n    return null; // 没有更多消息\n  }\n}", "explanation": "这个示例全面展示了Dart中while循环的多种用法。首先展示了基本while循环的结构，然后演示了如何使用while循环计算列表元素总和和进行条件处理。示例还包括在while循环中使用break和continue控制流程、嵌套while循环、用while循环处理模拟的用户输入和列表操作。此外，示例展示了条件控制的while循环、在无限循环中进行错误处理以及处理可能为null的值。这些示例涵盖了while循环在各种场景下的应用，特别是在迭代次数不确定的情况下，以及需要基于条件控制的循环流程。"}]}}, {"name": "Do-While Loop", "trans": ["do...while循环"], "usage": {"syntax": "// do-while循环\ndo {\n  // 循环体\n} while (条件);\n\n// 例如:\nint i = 0;\ndo {\n  // 循环体\n  i++;\n} while (i < 5);", "description": "do-while循环是一种先执行后判断的循环结构，它首先执行循环体，然后检查条件，如果条件为true则再次执行循环体，否则结束循环。do-while循环与while循环的主要区别在于，do-while循环会至少执行一次循环体，而while循环在条件为false时可能一次也不执行。do-while循环适用于需要至少执行一次操作的场景，如用户输入验证、菜单选择等。与其他循环结构一样，do-while循环也可以使用break和continue语句控制流程，可以嵌套使用，并可以与其他控制语句结合。在使用do-while循环时，同样需要注意避免无限循环的情况。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "void main() {\n  // 基本do-while循环\n  print('基本do-while循环:');\n  int i = 0;\n  do {\n    print('i = $i');\n    i++;\n  } while (i < 5);\n  \n  // 与while循环对比：条件开始就为false的情况\n  print('\\n条件为false时的do-while循环:');\n  int a = 10;\n  \n  // while循环一次都不会执行\n  print('while循环:');\n  while (a < 5) {\n    print('这行不会被执行');\n    a++;\n  }\n  \n  // do-while循环会至少执行一次\n  print('do-while循环:');\n  do {\n    print('a = $a'); // 会执行一次\n    a++;\n  } while (a < 5);\n  \n  // 使用do-while循环处理用户输入模拟\n  print('\\n使用do-while循环处理用户输入模拟:');\n  String input;\n  do {\n    input = simulateUserInput();\n    print('处理输入: $input');\n  } while (input != 'exit');\n  \n  // 使用do-while循环计算总和直到达到特定值\n  print('\\n计算总和直到达到或超过100:');\n  List<int> numbers = [10, 20, 30, 40, 50, 60, 70];\n  int sum = 0;\n  int index = 0;\n  \n  do {\n    if (index < numbers.length) {\n      sum += numbers[index];\n      print('添加 ${numbers[index]}, 当前总和: $sum');\n      index++;\n    } else {\n      print('已处理所有数字，但总和仍未达到目标');\n      break;\n    }\n  } while (sum < 100);\n  \n  // 使用do-while实现菜单选择\n  print('\\n菜单选择模拟:');\n  int choice;\n  do {\n    printMenu();\n    choice = simulateMenuChoice();\n    \n    switch (choice) {\n      case 1:\n        print('执行选项1');\n        break;\n      case 2:\n        print('执行选项2');\n        break;\n      case 3:\n        print('执行选项3');\n        break;\n      case 0:\n        print('退出菜单');\n        break;\n      default:\n        print('无效选择，请重试');\n    }\n  } while (choice != 0);\n  \n  // 使用do-while循环进行输入验证模拟\n  print('\\n输入验证模拟:');\n  String password;\n  int attempts = 0;\n  \n  do {\n    password = simulatePasswordInput(attempts);\n    attempts++;\n    \n    if (password == 'correct') {\n      print('密码正确！');\n    } else {\n      print('密码错误，请重试');\n      if (attempts >= 3) {\n        print('尝试次数过多，退出验证');\n        break;\n      }\n    }\n  } while (password != 'correct');\n  \n  // 使用do-while处理文件读取模拟\n  print('\\n文件读取模拟:');\n  String line;\n  int lineNumber = 0;\n  \n  do {\n    line = simulateReadLine(lineNumber);\n    if (line != 'EOF') {\n      print('读取行 ${lineNumber+1}: $line');\n    }\n    lineNumber++;\n  } while (line != 'EOF');\n  \n  // 在do-while循环中使用continue\n  print('\\n在do-while循环中使用continue:');\n  int count = 0;\n  do {\n    count++;\n    if (count % 2 == 0) {\n      print('跳过偶数: $count');\n      continue;\n    }\n    print('处理奇数: $count');\n  } while (count < 5);\n  \n  // 嵌套do-while循环\n  print('\\n嵌套do-while循环:');\n  int outer = 1;\n  do {\n    int inner = 1;\n    do {\n      print('outer = $outer, inner = $inner');\n      inner++;\n    } while (inner <= 3);\n    outer++;\n  } while (outer <= 2);\n}\n\n// 菜单打印辅助函数\nvoid printMenu() {\n  print('\\n===== 菜单 =====');\n  print('1. 选项1');\n  print('2. 选项2');\n  print('3. 选项3');\n  print('0. 退出');\n  print('===============');\n}\n\n// 模拟用户输入的辅助函数\nString simulateUserInput() {\n  // 模拟用户输入，这里简化为固定序列\n  static int count = 0;\n  count++;\n  \n  if (count == 1) return 'start';\n  if (count == 2) return 'continue';\n  if (count == 3) return 'almost done';\n  return 'exit';\n}\n\n// 模拟菜单选择的辅助函数\nint simulateMenuChoice() {\n  // 模拟用户选择，这里简化为固定序列\n  static int callCount = 0;\n  callCount++;\n  \n  switch (callCount) {\n    case 1: return 1;\n    case 2: return 2;\n    case 3: return 3;\n    case 4: return 99; // 无效选择\n    default: return 0; // 退出\n  }\n}\n\n// 模拟密码输入的辅助函数\nString simulatePasswordInput(int attempt) {\n  // 模拟密码输入，第三次输入正确密码\n  if (attempt == 2) {\n    return 'correct';\n  } else {\n    return 'wrong';\n  }\n}\n\n// 模拟文件读取的辅助函数\nString simulateReadLine(int lineNumber) {\n  // 模拟读取文件行，有5行内容\n  if (lineNumber < 5) {\n    return '这是文件的第 ${lineNumber+1} 行内容';\n  } else {\n    return 'EOF'; // 文件结束标记\n  }\n}", "explanation": "这个示例全面展示了Dart中do-while循环的多种用法。首先展示了基本do-while循环的结构，并与while循环进行对比，特别是在条件开始就为false的情况下，do-while会至少执行一次循环体，而while循环一次都不会执行。示例展示了do-while循环在多种实际场景中的应用，包括用户输入处理、计算总和直到达到特定值、菜单选择系统、输入验证和文件读取模拟。此外，示例还展示了在do-while循环中使用continue语句和嵌套do-while循环的用法。这些示例强调了do-while循环特别适用于那些需要至少执行一次操作，然后再根据条件决定是否继续的场景。"}]}}, {"name": "For-In and ForEach", "trans": ["for-in与forEach"], "usage": {"syntax": "// for-in循环\nfor (变量 in 可迭代对象) {\n  // 循环体\n}\n\n// 例如:\nfor (var item in items) {\n  // 对每个item进行操作\n}\n\n// forEach方法\nitems.forEach((item) {\n  // 对每个item进行操作\n});\n\n// 使用箭头函数的forEach\nitems.forEach((item) => 处理item的表达式);", "description": "for-in循环和forEach方法是Dart中用于遍历集合（如List、Set和Map）的便捷方式。for-in循环（也称为for-each循环）提供了一种简洁的语法来迭代集合中的每个元素，而不需要使用索引或迭代器。forEach是集合类的一个方法，它接受一个函数参数，该函数将应用于集合中的每个元素。for-in循环和forEach的主要区别在于：for-in是一个语句，可以使用break和continue控制流程；而forEach是一个方法，一旦开始就会处理所有元素，不能中途跳出（除非抛出异常）。这两种方式都比传统的for循环更简洁，尤其适用于不需要关心索引位置的情况。", "parameters": [], "returnValue": "for-in循环无返回值；forEach方法本身返回void，但可以在回调函数中对外部变量进行修改", "examples": [{"code": "void main() {\n  // 基本for-in循环\n  print('基本for-in循环:');\n  List<String> fruits = ['苹果', '香蕉', '橙子', '葡萄'];\n  \n  for (String fruit in fruits) {\n    print('水果: $fruit');\n  }\n  \n  // 使用var关键字的for-in循环\n  print('\\n使用var的for-in循环:');\n  for (var fruit in fruits) {\n    print('水果: $fruit');\n  }\n  \n  // 使用forEach方法\n  print('\\n使用forEach方法:');\n  fruits.forEach((fruit) {\n    print('水果: $fruit');\n  });\n  \n  // 使用箭头函数的forEach\n  print('\\n使用箭头函数的forEach:');\n  fruits.forEach((fruit) => print('水果: $fruit'));\n  \n  // 在for-in循环中使用break\n  print('\\n在for-in循环中使用break:');\n  for (var fruit in fruits) {\n    if (fruit == '橙子') {\n      print('找到橙子，跳出循环');\n      break;\n    }\n    print('水果: $fruit');\n  }\n  \n  // 在for-in循环中使用continue\n  print('\\n在for-in循环中使用continue:');\n  for (var fruit in fruits) {\n    if (fruit == '香蕉') {\n      print('跳过香蕉');\n      continue;\n    }\n    print('水果: $fruit');\n  }\n  \n  // 注意：forEach方法不能使用break或continue\n  // 以下代码会导致编译错误：\n  // fruits.forEach((fruit) {\n  //   if (fruit == '橙子') break; // 错误\n  // });\n  \n  // 遍历Map的键值对\n  print('\\n遍历Map的键值对:');\n  Map<String, int> prices = {\n    '苹果': 5,\n    '香蕉': 3,\n    '橙子': 4,\n    '葡萄': 8\n  };\n  \n  // 遍历Map的entries\n  for (var entry in prices.entries) {\n    print('${entry.key}: ${entry.value}元');\n  }\n  \n  // 分别遍历Map的keys和values\n  print('\\n分别遍历Map的keys和values:');\n  print('所有水果:');\n  for (var key in prices.keys) {\n    print('- $key');\n  }\n  \n  print('\\n所有价格:');\n  for (var value in prices.values) {\n    print('- $value元');\n  }\n  \n  // 使用forEach遍历Map\n  print('\\n使用forEach遍历Map:');\n  prices.forEach((key, value) {\n    print('$key: $value元');\n  });\n  \n  // 遍历Set\n  print('\\n遍历Set:');\n  Set<int> numbers = {1, 2, 3, 4, 5};\n  \n  for (var number in numbers) {\n    print('数字: $number');\n  }\n  \n  numbers.forEach((number) => print('数字(forEach): $number'));\n  \n  // 在for-in循环中修改元素（无效）\n  print('\\n在for-in循环中修改元素（无效）:');\n  List<int> scores = [10, 20, 30, 40, 50];\n  \n  // 注意：这不会修改原始列表中的元素\n  for (var score in scores) {\n    score += 5; // 只修改了循环变量，不影响原列表\n  }\n  print('尝试修改后的scores: $scores'); // 原列表不变\n  \n  // 使用普通for循环修改元素（有效）\n  for (int i = 0; i < scores.length; i++) {\n    scores[i] += 5; // 实际修改了列表元素\n  }\n  print('实际修改后的scores: $scores');\n  \n  // 使用for-in与索引\n  print('\\n使用for-in与索引:');\n  List<String> colors = ['红', '绿', '蓝', '黄'];\n  \n  // 如果需要索引，可以结合使用for-in和索引列表\n  for (var entry in colors.asMap().entries) {\n    print('索引 ${entry.key}: ${entry.value}');\n  }\n  \n  // forEach方法获取索引\n  print('\\nforEach方法获取索引:');\n  colors.asMap().forEach((index, color) {\n    print('索引 $index: $color');\n  });\n  \n  // 使用for-in处理字符串的字符\n  print('\\n使用for-in处理字符串的字符:');\n  String message = 'Hello';\n  \n  for (var char in message.split('')) {\n    print('字符: $char');\n  }\n  \n  // 组合使用for-in和条件语句\n  print('\\n组合使用for-in和条件语句:');\n  List<int> mixedNumbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];\n  \n  print('偶数:');\n  for (var num in mixedNumbers) {\n    if (num % 2 == 0) {\n      print(num);\n    }\n  }\n  \n  // 使用where方法过滤后再遍历\n  print('\\n使用where方法过滤后再遍历:');\n  print('奇数:');\n  for (var num in mixedNumbers.where((n) => n % 2 != 0)) {\n    print(num);\n  }\n  \n  // 使用forEach处理带索引的操作\n  print('\\n使用forEach处理带索引的操作:');\n  List<int> values = [10, 20, 30, 40, 50];\n  \n  values.asMap().forEach((index, value) {\n    print('values[$index] * 2 = ${value * 2}');\n  });\n}", "explanation": "这个示例全面展示了Dart中for-in循环和forEach方法的多种用法。首先展示了基本的for-in循环和forEach方法遍历列表的方式，包括使用普通函数和箭头函数的语法。然后演示了for-in循环中使用break和continue控制流程，强调了forEach方法不支持这些流程控制语句。示例还包括遍历不同类型的集合（List、Map、Set），展示了如何访问Map的键值对、单独遍历键或值，以及如何遍历Set。此外，示例强调了for-in循环中直接修改循环变量不会影响原始集合，对比了普通for循环的修改方式。最后，示例展示了如何在for-in和forEach中获取索引信息，如何处理字符串的字符，以及如何结合条件语句和过滤方法使用for-in和forEach。这些示例凸显了for-in循环和forEach方法在处理集合时的简洁性和灵活性。"}]}}, {"name": "Break and Continue", "trans": ["break与continue"], "usage": {"syntax": "// break语句\nfor/while/do-while/switch 语句 {\n  if (条件) {\n    break; // 跳出循环或switch\n  }\n}\n\n// continue语句\nfor/while/do-while 循环 {\n  if (条件) {\n    continue; // 跳过本次迭代\n  }\n}\n\n// 带标签的break\n标签: for/while/do-while 外层循环 {\n  for/while/do-while 内层循环 {\n    if (条件) {\n      break 标签; // 跳出外层循环\n    }\n  }\n}\n\n// 带标签的continue\n标签: for/while/do-while 外层循环 {\n  for/while/do-while 内层循环 {\n    if (条件) {\n      continue 标签; // 跳到外层循环的下一次迭代\n    }\n  }\n}", "description": "break和continue是控制循环流程的关键字。break用于完全跳出循环或switch语句，而continue用于跳过当前迭代，直接进入下一次迭代。在嵌套循环中，break和continue默认只影响最内层循环，但可以通过标签（label）来指定要控制的循环层级。break可以用于for、while、do-while循环和switch语句中，而continue只能用于循环语句中。这两个关键字在处理特殊情况、优化循环性能和实现复杂的控制逻辑时非常有用。合理使用break和continue可以使代码更加清晰和高效，但过度使用可能会降低代码的可读性。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "void main() {\n  // 基本break示例\n  print('基本break示例:');\n  for (int i = 0; i < 10; i++) {\n    if (i == 5) {\n      print('遇到5，跳出循环');\n      break;\n    }\n    print('i = $i');\n  }\n  \n  // 基本continue示例\n  print('\\n基本continue示例:');\n  for (int i = 0; i < 5; i++) {\n    if (i == 2) {\n      print('跳过2');\n      continue;\n    }\n    print('i = $i');\n  }\n  \n  // 在while循环中使用break\n  print('\\n在while循环中使用break:');\n  int j = 0;\n  while (j < 10) {\n    if (j == 5) {\n      print('遇到5，跳出循环');\n      break;\n    }\n    print('j = $j');\n    j++;\n  }\n  \n  // 在do-while循环中使用continue\n  print('\\n在do-while循环中使用continue:');\n  int k = 0;\n  do {\n    k++;\n    if (k == 3) {\n      print('跳过3');\n      continue;\n    }\n    print('k = $k');\n  } while (k < 5);\n  \n  // 在for-in循环中使用break\n  print('\\n在for-in循环中使用break:');\n  List<String> fruits = ['苹果', '香蕉', '橙子', '葡萄'];\n  for (String fruit in fruits) {\n    if (fruit == '橙子') {\n      print('找到橙子，跳出循环');\n      break;\n    }\n    print('水果: $fruit');\n  }\n  \n  // 使用break跳出switch语句\n  print('\\n使用break跳出switch语句:');\n  String color = 'green';\n  switch (color) {\n    case 'red':\n      print('红色');\n      break;\n    case 'green':\n      print('绿色');\n      break; // 如果没有break，会继续执行下一个case\n    case 'blue':\n      print('蓝色');\n      break;\n    default:\n      print('其他颜色');\n  }\n  \n  // 带标签的break\n  print('\\n带标签的break:');\n  outerLoop: for (int i = 0; i < 3; i++) {\n    print('外层循环 i = $i');\n    for (int j = 0; j < 3; j++) {\n      if (i == 1 && j == 1) {\n        print('在 i=1, j=1 处跳出外层循环');\n        break outerLoop; // 跳出标记为outerLoop的循环\n      }\n      print('  内层循环 j = $j');\n    }\n  }\n  \n  // 带标签的continue\n  print('\\n带标签的continue:');\n  outerLoop: for (int i = 0; i < 3; i++) {\n    print('外层循环 i = $i');\n    for (int j = 0; j < 3; j++) {\n      if (i == 1 && j == 1) {\n        print('在 i=1, j=1 处跳到外层循环的下一次迭代');\n        continue outerLoop; // 继续标记为outerLoop的循环的下一次迭代\n      }\n      print('  内层循环 j = $j');\n    }\n  }\n  \n  // 使用break查找特定元素\n  print('\\n使用break查找特定元素:');\n  List<int> numbers = [1, 3, 5, 7, 9, 2, 4, 6, 8];\n  bool found = false;\n  int target = 7;\n  \n  for (int i = 0; i < numbers.length; i++) {\n    if (numbers[i] == target) {\n      print('找到目标 $target，位于索引 $i');\n      found = true;\n      break; // 找到后立即退出循环\n    }\n  }\n  \n  if (!found) {\n    print('未找到目标 $target');\n  }\n  \n  // 使用continue过滤元素\n  print('\\n使用continue过滤元素:');\n  List<int> filteredNumbers = [];\n  for (int num in numbers) {\n    if (num % 2 == 0) {\n      continue; // 跳过偶数\n    }\n    filteredNumbers.add(num);\n  }\n  print('过滤后的数字（只保留奇数）: $filteredNumbers');\n  \n  // 使用break提前结束复杂计算\n  print('\\n使用break提前结束复杂计算:');\n  int sum = 0;\n  int limit = 100;\n  \n  for (int i = 1; i <= 1000; i++) {\n    sum += i;\n    if (sum > limit) {\n      print('和超过$limit，在第$i次迭代停止');\n      break;\n    }\n  }\n  print('最终和: $sum');\n  \n  // 使用break实现搜索算法\n  print('\\n使用break实现二分查找:');\n  List<int> sortedNumbers = [1, 3, 5, 7, 9, 11, 13, 15, 17, 19];\n  int searchTarget = 11;\n  int left = 0;\n  int right = sortedNumbers.length - 1;\n  int mid;\n  bool searchFound = false;\n  \n  while (left <= right) {\n    mid = left + (right - left) ~/ 2;\n    \n    if (sortedNumbers[mid] == searchTarget) {\n      print('找到目标 $searchTarget，位于索引 $mid');\n      searchFound = true;\n      break;\n    } else if (sortedNumbers[mid] < searchTarget) {\n      left = mid + 1;\n    } else {\n      right = mid - 1;\n    }\n  }\n  \n  if (!searchFound) {\n    print('未找到目标 $searchTarget');\n  }\n  \n  // 嵌套continue使用\n  print('\\n嵌套continue使用:');\n  for (int i = 0; i < 3; i++) {\n    print('i = $i');\n    for (int j = 0; j < 3; j++) {\n      if (j == 1) {\n        print('  跳过 j = 1');\n        continue; // 只跳过内层循环的当前迭代\n      }\n      print('  j = $j');\n    }\n  }\n  \n  // 使用break和return的区别\n  print('\\n使用break和return的区别:');\n  breakExample();\n  returnExample();\n}\n\n// 演示break的函数\nvoid breakExample() {\n  print('breakExample函数:');\n  for (int i = 0; i < 5; i++) {\n    if (i == 3) {\n      print('使用break跳出循环');\n      break; // 只跳出循环，函数继续执行\n    }\n    print('i = $i');\n  }\n  print('循环后的代码仍然执行');\n}\n\n// 演示return的函数\nvoid returnExample() {\n  print('\\nreturnExample函数:');\n  for (int i = 0; i < 5; i++) {\n    if (i == 3) {\n      print('使用return退出函数');\n      return; // 完全退出函数\n    }\n    print('i = $i');\n  }\n  print('这行代码不会执行');\n}", "explanation": "这个示例全面展示了Dart中break和continue语句的多种用法。首先展示了基本的break和continue在各种循环结构（for、while、do-while、for-in）中的使用方式，以及break在switch语句中的应用。然后演示了带标签（labeled）的break和continue，它们可以用于控制嵌套循环中的外层循环。示例还包括break和continue在实际应用场景中的用法，如使用break查找特定元素和提前结束计算，使用continue过滤元素等。最后，示例比较了break和return的区别：break只跳出当前循环，而return会完全退出函数。这些示例说明了break和continue如何有效地控制程序流程，提高代码效率，并处理特殊情况。"}]}}, {"name": "Nested Loops", "trans": ["循环嵌套"], "usage": {"syntax": "// 嵌套for循环\nfor (初始化1; 条件1; 增量1) {\n  for (初始化2; 条件2; 增量2) {\n    // 循环体\n  }\n}\n\n// 嵌套while循环\nwhile (条件1) {\n  while (条件2) {\n    // 循环体\n  }\n}\n\n// 混合嵌套循环\nfor (初始化; 条件; 增量) {\n  while (条件) {\n    // 循环体\n  }\n}", "description": "循环嵌套是指在一个循环内部包含另一个循环，允许处理多维数据结构或执行复杂的迭代操作。在Dart中，任何类型的循环（for、while、do-while、for-in）都可以嵌套在另一个循环中，没有嵌套深度的理论限制，但过深的嵌套会影响代码可读性和性能。外层循环的每次迭代都会完整执行内层循环。循环嵌套常用于处理二维数组、矩阵操作、生成组合和排列、多层数据结构遍历等场景。在嵌套循环中，可以使用带标签的break和continue语句来控制特定层级的循环流程。为了提高代码质量，应当尽量限制嵌套深度，考虑提取复杂逻辑为独立函数，以及使用更高级的集合操作来简化代码。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "void main() {\n  // 基本嵌套for循环\n  print('基本嵌套for循环:');\n  for (int i = 1; i <= 3; i++) {\n    for (int j = 1; j <= 3; j++) {\n      print('i = $i, j = $j');\n    }\n  }\n  \n  // 打印乘法表\n  print('\\n乘法表:');\n  for (int i = 1; i <= 9; i++) {\n    String row = '';\n    for (int j = 1; j <= i; j++) {\n      row += '$j×$i=${j*i} ';\n    }\n    print(row);\n  }\n  \n  // 嵌套while循环\n  print('\\n嵌套while循环:');\n  int a = 1;\n  while (a <= 3) {\n    int b = 1;\n    while (b <= 3) {\n      print('a = $a, b = $b');\n      b++;\n    }\n    a++;\n  }\n  \n  // 混合循环嵌套 (for和while)\n  print('\\n混合循环嵌套:');\n  for (int i = 1; i <= 3; i++) {\n    int j = 1;\n    while (j <= 3) {\n      print('i = $i, j = $j');\n      j++;\n    }\n  }\n  \n  // 嵌套do-while循环\n  print('\\n嵌套do-while循环:');\n  int x = 1;\n  do {\n    int y = 1;\n    do {\n      print('x = $x, y = $y');\n      y++;\n    } while (y <= 3);\n    x++;\n  } while (x <= 2);\n  \n  // 使用嵌套for-in循环\n  print('\\n嵌套for-in循环:');\n  List<List<int>> matrix = [\n    [1, 2, 3],\n    [4, 5, 6],\n    [7, 8, 9]\n  ];\n  \n  for (List<int> row in matrix) {\n    for (int cell in row) {\n      print('元素: $cell');\n    }\n  }\n  \n  // 三层嵌套循环\n  print('\\n三层嵌套循环:');\n  for (int i = 1; i <= 2; i++) {\n    for (int j = 1; j <= 2; j++) {\n      for (int k = 1; k <= 2; k++) {\n        print('i = $i, j = $j, k = $k');\n      }\n    }\n  }\n  \n  // 嵌套循环生成坐标\n  print('\\n使用嵌套循环生成坐标:');\n  List<List<String>> coordinates = [];\n  for (int x = 1; x <= 3; x++) {\n    for (int y = 1; y <= 3; y++) {\n      coordinates.add(['$x', '$y']);\n    }\n  }\n  print('生成的坐标: $coordinates');\n  \n  // 嵌套循环与条件判断\n  print('\\n嵌套循环与条件判断:');\n  for (int i = 1; i <= 5; i++) {\n    for (int j = 1; j <= 5; j++) {\n      if (i == j) {\n        print('对角线元素: ($i, $j)');\n      } else if (i < j) {\n        print('上三角元素: ($i, $j)');\n      } else {\n        print('下三角元素: ($i, $j)');\n      }\n    }\n  }\n  \n  // 使用带标签的break控制嵌套循环\n  print('\\n使用带标签的break控制嵌套循环:');\n  outerLoop: for (int i = 0; i < 5; i++) {\n    for (int j = 0; j < 5; j++) {\n      if (i * j > 6) {\n        print('i * j > 6，找到第一个: i = $i, j = $j');\n        break outerLoop; // 跳出外层循环\n      }\n      print('检查: i = $i, j = $j, i * j = ${i * j}');\n    }\n  }\n  \n  // 使用嵌套循环处理Map结构\n  print('\\n使用嵌套循环处理Map结构:');\n  Map<String, Map<String, int>> nestedMap = {\n    '小明': {'数学': 90, '语文': 85, '英语': 78},\n    '小红': {'数学': 95, '语文': 88, '英语': 92},\n    '小刚': {'数学': 82, '语文': 90, '英语': 85}\n  };\n  \n  for (var studentEntry in nestedMap.entries) {\n    String student = studentEntry.key;\n    Map<String, int> scores = studentEntry.value;\n    \n    print('$student的成绩:');\n    for (var subjectEntry in scores.entries) {\n      String subject = subjectEntry.key;\n      int score = subjectEntry.value;\n      print('  $subject: $score');\n    }\n  }\n  \n  // 使用嵌套循环优化（提取内部循环为函数）\n  print('\\n使用嵌套循环优化:');\n  for (int i = 1; i <= 3; i++) {\n    print('处理外层 i = $i');\n    processInnerLoop(i);\n  }\n  \n  // 使用嵌套循环生成图案\n  print('\\n使用嵌套循环生成图案:');\n  int n = 5;\n  for (int i = 1; i <= n; i++) {\n    String stars = '';\n    for (int j = 1; j <= i; j++) {\n      stars += '* ';\n    }\n    print(stars);\n  }\n}\n\n// 提取内部循环为函数\nvoid processInnerLoop(int outerValue) {\n  for (int j = 1; j <= 3; j++) {\n    print('  内部循环: j = $j, 外部值 * 内部值 = ${outerValue * j}');\n  }\n}", "explanation": "这个示例全面展示了Dart中循环嵌套的多种用法。首先展示了基本的嵌套for循环结构，并用它打印了经典的乘法表。然后演示了各种类型的循环嵌套组合，包括嵌套while循环、混合for和while的嵌套、嵌套do-while循环以及嵌套for-in循环用于遍历二维列表。示例还包括三层嵌套循环、使用嵌套循环生成坐标对和与条件判断结合使用。特别展示了使用带标签的break语句控制嵌套循环的流程，以及如何使用嵌套循环处理复杂的嵌套数据结构如Map。最后，示例展示了通过提取内部循环为独立函数来优化嵌套循环代码，以及使用嵌套循环生成图案的应用。这些示例说明了循环嵌套如何用于处理多维数据和实现复杂的迭代逻辑。"}]}}, {"name": "Assignment", "trans": ["实践作业"], "usage": {"syntax": "// 循环语句实践", "description": "完成以下任务，练习Dart中循环语句的使用：1) 使用for循环计算1到100的和；2) 使用while循环实现一个猜数字游戏，随机生成一个1-100的数字，然后提示用户猜测结果（这里可以模拟用户输入）；3) 使用do-while循环实现一个简单的菜单系统，显示选项并处理用户选择；4) 使用for-in循环和forEach方法分别处理一个学生成绩列表，计算平均分；5) 使用嵌套循环打印一个10x10的乘法表；6) 挑战：实现一个使用循环的简单算法，如查找列表中的最大值和第二大值，或者实现冒泡排序。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import 'dart:math';\n\nvoid main() {\n  // 1. 使用for循环计算1到100的和\n  int sum = 0;\n  for (int i = 1; i <= 100; i++) {\n    sum += i;\n  }\n  print('1到100的和: $sum');\n  \n  // 2. 使用while循环实现猜数字游戏\n  print('\\n猜数字游戏:');\n  int targetNumber = Random().nextInt(100) + 1; // 1-100的随机数\n  int guess;\n  int attempts = 0;\n  bool guessed = false;\n  \n  print('系统已生成一个1-100之间的随机数');\n  \n  while (!guessed) {\n    attempts++;\n    guess = simulateUserGuess(targetNumber, attempts); // 模拟用户猜测\n    print('猜测 #$attempts: $guess');\n    \n    if (guess < targetNumber) {\n      print('太小了，再试一次');\n    } else if (guess > targetNumber) {\n      print('太大了，再试一次');\n    } else {\n      print('恭喜！你猜对了，答案是 $targetNumber');\n      print('你总共猜了 $attempts 次');\n      guessed = true;\n    }\n    \n    // 限制最大尝试次数\n    if (attempts >= 10) {\n      print('已达到最大尝试次数，正确答案是 $targetNumber');\n      break;\n    }\n  }\n  \n  // 3. 使用do-while循环实现菜单系统\n  print('\\n简单菜单系统:');\n  int choice;\n  do {\n    printMenu();\n    choice = simulateMenuChoice();\n    print('用户选择: $choice');\n    \n    switch (choice) {\n      case 1:\n        print('执行操作1: 显示当前时间');\n        print('当前时间: ${DateTime.now()}');\n        break;\n      case 2:\n        print('执行操作2: 生成随机数');\n        print('随机数: ${Random().nextInt(100)}');\n        break;\n      case 3:\n        print('执行操作3: 计算平方');\n        int num = 5;\n        print('$num的平方是: ${num * num}');\n        break;\n      case 0:\n        print('退出菜单');\n        break;\n      default:\n        print('无效选择，请重试');\n    }\n    print(''); // 空行\n  } while (choice != 0);\n  \n  // 4. 使用for-in循环和forEach处理成绩列表\n  print('\\n处理学生成绩:');\n  List<int> scores = [85, 92, 78, 65, 90, 76, 88, 95];\n  \n  // 使用for-in循环计算平均分\n  int totalForIn = 0;\n  for (int score in scores) {\n    totalForIn += score;\n  }\n  double averageForIn = totalForIn / scores.length;\n  print('使用for-in计算的平均分: $averageForIn');\n  \n  // 使用forEach计算平均分\n  int totalForEach = 0;\n  scores.forEach((score) {\n    totalForEach += score;\n  });\n  double averageForEach = totalForEach / scores.length;\n  print('使用forEach计算的平均分: $averageForEach');\n  \n  // 5. 使用嵌套循环打印乘法表\n  print('\\n10x10乘法表:');\n  for (int i = 1; i <= 10; i++) {\n    String row = '';\n    for (int j = 1; j <= 10; j++) {\n      // 格式化输出，确保对齐\n      String product = (i * j).toString().padLeft(3);\n      row += '$product ';\n    }\n    print(row);\n  }\n  \n  // 6. 挑战：查找列表中的最大值和第二大值\n  print('\\n查找最大值和第二大值:');\n  List<int> numbers = [34, 56, 12, 89, 67, 43, 91, 75, 22, 88];\n  print('原始列表: $numbers');\n  \n  int max = numbers[0];\n  int secondMax = numbers[0];\n  \n  for (int num in numbers) {\n    if (num > max) {\n      secondMax = max;\n      max = num;\n    } else if (num > secondMax && num < max) {\n      secondMax = num;\n    }\n  }\n  \n  print('最大值: $max');\n  print('第二大值: $secondMax');\n  \n  // 冒泡排序\n  print('\\n使用冒泡排序:');\n  List<int> unsortedList = [64, 34, 25, 12, 22, 11, 90];\n  print('排序前: $unsortedList');\n  \n  for (int i = 0; i < unsortedList.length - 1; i++) {\n    for (int j = 0; j < unsortedList.length - i - 1; j++) {\n      if (unsortedList[j] > unsortedList[j + 1]) {\n        // 交换元素\n        int temp = unsortedList[j];\n        unsortedList[j] = unsortedList[j + 1];\n        unsortedList[j + 1] = temp;\n      }\n    }\n  }\n  \n  print('排序后: $unsortedList');\n}\n\n// 模拟用户猜测的辅助函数\nint simulateUserGuess(int target, int attempt) {\n  // 简单的猜测策略：首次猜50，然后根据大小调整\n  // 实际应用中，这里会获取用户的真实输入\n  if (attempt == 1) return 50;\n  if (attempt == 2) return target > 50 ? 75 : 25;\n  if (attempt == 3) return target > 75 ? 87 : (target > 25 ? 37 : 12);\n  \n  // 最后一次直接猜对\n  if (attempt >= 7) return target;\n  \n  // 其他情况随机猜测接近目标的数\n  int diff = (target - 50).abs() ~/ attempt;\n  return target + Random().nextInt(diff * 2) - diff;\n}\n\n// 打印菜单选项\nvoid printMenu() {\n  print('===== 菜单 =====');\n  print('1. 显示当前时间');\n  print('2. 生成随机数');\n  print('3. 计算平方');\n  print('0. 退出');\n  print('请选择:');\n}\n\n// 模拟用户菜单选择\nint simulateMenuChoice() {\n  // 模拟用户输入，这里简化为固定序列\n  static int callCount = 0;\n  callCount++;\n  \n  switch (callCount) {\n    case 1: return 1;\n    case 2: return 2;\n    case 3: return 3;\n    case 4: return 99; // 无效选择\n    default: return 0; // 退出\n  }\n}", "explanation": "这个参考实现完成了六个循环语句练习任务。第一个任务使用for循环计算1到100的和，采用了简单的累加方式。第二个任务使用while循环实现了一个猜数字游戏，包括随机数生成、用户猜测模拟和反馈提示。第三个任务使用do-while循环实现了一个简单的菜单系统，展示了菜单选项并处理用户选择。第四个任务分别使用for-in循环和forEach方法处理学生成绩列表，计算平均分。第五个任务使用嵌套循环打印了一个10x10的乘法表，包括格式化输出以确保对齐。最后的挑战任务实现了两个简单算法：一个使用for-in循环查找列表中的最大值和第二大值，另一个使用嵌套for循环实现冒泡排序。这些任务涵盖了Dart中各种循环语句的用法和实际应用场景。"}]}}]}