{"name": "Route Guard", "trans": ["路由守卫"], "methods": [{"name": "Route Interception", "trans": ["路由拦截"], "usage": {"syntax": "function PrivateRoute({ children }) {\n  const isAuthenticated = useAuth();\n  return isAuthenticated ? children : <Navigate to=\"/login\" />;\n}", "description": "通过自定义组件拦截未授权用户访问受保护路由，常用于需要登录后才能访问的页面。", "parameters": [{"name": "children", "description": "需要保护的子组件"}], "returnValue": "根据认证状态返回子组件或重定向组件。", "examples": [{"code": "// 路由拦截示例\nimport { Navigate } from 'react-router-dom';\n\nfunction useAuth() {\n  // 假设有认证逻辑\n  return !!localStorage.getItem('token');\n}\n\nfunction PrivateRoute({ children }) {\n  const isAuthenticated = useAuth();\n  // 未登录则重定向到登录页\n  return isAuthenticated ? children : <Navigate to=\"/login\" replace />;\n}\n\n// 路由配置\n<Routes>\n  <Route path=\"/dashboard\" element={\n    <PrivateRoute>\n      <Dashboard />\n    </PrivateRoute>\n  } />\n</Routes>", "explanation": "通过PrivateRoute组件拦截未登录用户，未认证时自动跳转到登录页。"}]}}, {"name": "Authentication", "trans": ["身份验证"], "usage": {"syntax": "const isAuthenticated = useAuth();\nif (!isAuthenticated) navigate('/login');", "description": "判断用户是否已登录，未登录时跳转到登录页，常与路由拦截结合使用。", "parameters": [{"name": "无", "description": "无参数"}], "returnValue": "无返回值", "examples": [{"code": "// 身份验证示例\nimport { useNavigate } from 'react-router-dom';\n\nfunction useAuth() {\n  return !!localStorage.getItem('token');\n}\n\nfunction Profile() {\n  const navigate = useNavigate();\n  const isAuthenticated = useAuth();\n  React.useEffect(() => {\n    if (!isAuthenticated) {\n      navigate('/login', { replace: true });\n    }\n  }, [isAuthenticated, navigate]);\n  return isAuthenticated ? <div>个人资料</div> : null;\n}", "explanation": "组件挂载时检查登录状态，未登录则自动跳转到登录页。"}]}}, {"name": "Authorization", "trans": ["权限控制"], "usage": {"syntax": "function RoleRoute({ children, role }) {\n  const user = useUser();\n  return user.role === role ? children : <Navigate to=\"/403\" />;\n}", "description": "根据用户角色或权限判断是否允许访问特定路由，未授权时跳转到无权限页面。", "parameters": [{"name": "children", "description": "需要保护的子组件"}, {"name": "role", "description": "允许访问的角色"}], "returnValue": "根据权限返回子组件或重定向组件。", "examples": [{"code": "// 权限控制示例\nimport { Navigate } from 'react-router-dom';\n\nfunction useUser() {\n  // 假设返回当前用户信息\n  return { role: 'admin' };\n}\n\nfunction RoleRoute({ children, role }) {\n  const user = useUser();\n  return user.role === role ? children : <Navigate to=\"/403\" replace />;\n}\n\n// 路由配置\n<Route path=\"/admin\" element={\n  <RoleRoute role=\"admin\">\n    <AdminPanel />\n  </RoleRoute>\n} />", "explanation": "通过RoleRoute组件实现基于角色的权限控制，未授权用户跳转到403页面。"}]}}, {"name": "Redirection", "trans": ["重定向"], "usage": {"syntax": "<Navigate to=\"/target\" replace />", "description": "使用Navigate组件或useNavigate钩子实现页面重定向，常用于未授权、未登录等场景。", "parameters": [{"name": "to", "description": "目标路径"}, {"name": "replace", "description": "是否替换历史记录"}], "returnValue": "无返回值", "examples": [{"code": "// 重定向示例\nimport { Navigate } from 'react-router-dom';\n\nfunction NotFound() {\n  // 访问不存在页面时重定向到首页\n  return <Navigate to=\"/\" replace />;\n}", "explanation": "访问404页面时自动重定向到首页。"}]}}, {"name": "Navigation Prompt", "trans": ["导航确认"], "usage": {"syntax": "usePrompt(message, when)", "description": "通过自定义Hook实现离开当前页面前的导航确认提示，防止用户误操作。React Router v6无内置Prompt，可自定义实现。", "parameters": [{"name": "message", "description": "提示信息"}, {"name": "when", "description": "是否启用提示"}], "returnValue": "无返回值", "examples": [{"code": "// 自定义导航确认Hook\nimport { useCallback, useEffect } from 'react';\nimport { UNSAFE_NavigationContext as NavigationContext } from 'react-router-dom';\n\nfunction usePrompt(message, when) {\n  const { navigator } = React.useContext(NavigationContext);\n  useEffect(() => {\n    if (!when) return;\n    const handler = (tx) => {\n      if (window.confirm(message)) {\n        tx.retry();\n      }\n    };\n    navigator.block(handler);\n    return () => navigator.block(() => {});\n  }, [message, when, navigator]);\n}\n\n// 使用示例\nfunction EditForm() {\n  const [dirty, setDirty] = React.useState(false);\n  usePrompt('确定要离开吗？未保存的更改将丢失。', dirty);\n  // ...表单内容\n}", "explanation": "自定义usePrompt Hook实现导航离开确认，防止表单未保存时误离开页面。"}]}}, {"name": "Route Lifecycle", "trans": ["路由生命周期"], "usage": {"syntax": "useEffect(() => {\n  // 路由进入时执行\n  return () => {\n    // 路由离开时执行\n  };\n}, [location.pathname]);", "description": "通过useEffect监听路由变化，实现路由进入和离开时的副作用处理，如数据加载、清理等。", "parameters": [{"name": "无", "description": "无参数"}], "returnValue": "无返回值", "examples": [{"code": "// 路由生命周期示例\nimport { useEffect } from 'react';\nimport { useLocation } from 'react-router-dom';\n\nfunction Page() {\n  const location = useLocation();\n  useEffect(() => {\n    // 路由进入时\n    console.log('进入页面:', location.pathname);\n    return () => {\n      // 路由离开时\n      console.log('离开页面:', location.pathname);\n    };\n  }, [location.pathname]);\n  return <div>页面内容</div>;\n}", "explanation": "通过useEffect监听路由变化，实现进入和离开页面时的副作用处理。"}]}}, {"name": "作业：实现路由守卫和权限控制", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 实现一个PrivateRoute组件，拦截未登录用户访问受保护页面\n// 2. 实现基于角色的RoleRoute组件，只有特定角色可访问\n// 3. 在表单编辑页实现离开前的导航确认\n// 4. 演示重定向和路由生命周期副作用", "description": "通过实践路由守卫、权限控制、导航确认和生命周期，掌握React Router高级用法。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 创建PrivateRoute和RoleRoute组件\n// 2. 在路由配置中使用\n// 3. 在表单页使用usePrompt实现离开确认\n// 4. 通过useEffect监听路由变化", "explanation": "作业提示，学生需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nimport React from 'react';\nimport { BrowserRouter, Routes, Route, Navigate, useLocation } from 'react-router-dom';\n\nfunction useAuth() {\n  return !!localStorage.getItem('token');\n}\nfunction useUser() {\n  return { role: 'admin' };\n}\nfunction PrivateRoute({ children }) {\n  const isAuthenticated = useAuth();\n  return isAuthenticated ? children : <Navigate to=\"/login\" replace />;\n}\nfunction RoleRoute({ children, role }) {\n  const user = useUser();\n  return user.role === role ? children : <Navigate to=\"/403\" replace />;\n}\nfunction usePrompt(message, when) {\n  React.useEffect(() => {\n    if (!when) return;\n    const handler = (e) => {\n      e.preventDefault();\n      e.returnValue = message;\n      return message;\n    };\n    window.addEventListener('beforeunload', handler);\n    return () => window.removeEventListener('beforeunload', handler);\n  }, [message, when]);\n}\nfunction EditForm() {\n  const [dirty, setDirty] = React.useState(false);\n  usePrompt('确定要离开吗？未保存的更改将丢失。', dirty);\n  return (\n    <form>\n      <input onChange={() => setDirty(true)} />\n      <button type=\"submit\">保存</button>\n    </form>\n  );\n}\nfunction Dashboard() {\n  const location = useLocation();\n  React.useEffect(() => {\n    console.log('进入:', location.pathname);\n    return () => {\n      console.log('离开:', location.pathname);\n    };\n  }, [location.pathname]);\n  return <div>仪表盘</div>;\n}\nfunction App() {\n  return (\n    <BrowserRouter>\n      <Routes>\n        <Route path=\"/dashboard\" element={\n          <PrivateRoute>\n            <Dashboard />\n          </PrivateRoute>\n        } />\n        <Route path=\"/admin\" element={\n          <RoleRoute role=\"admin\">\n            <div>管理员页面</div>\n          </RoleRoute>\n        } />\n        <Route path=\"/edit\" element={<EditForm />} />\n        <Route path=\"/login\" element={<div>登录页</div>} />\n        <Route path=\"/403\" element={<div>无权限</div>} />\n      </Routes>\n    </BrowserRouter>\n  );\n}\nexport default App;", "explanation": "完整实现了路由守卫、权限控制、导航确认和路由生命周期。"}]}}]}