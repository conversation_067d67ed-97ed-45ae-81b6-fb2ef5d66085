{"name": "<PERSON><PERSON><PERSON>", "trans": ["模块化"], "methods": [{"name": "export/import (ESM)", "trans": ["export/import"], "usage": {"syntax": "// 导出\nexport const a = 1;\nexport function foo() {}\nexport default function() {}\n// 导入\nimport { a, foo } from './mod.js';\nimport def from './mod.js';", "description": "ESM模块通过export导出，import导入。支持命名导出和默认导出。", "parameters": [{"name": "export", "description": "导出变量、函数、类等。"}, {"name": "import", "description": "导入其他模块内容。"}, {"name": "default", "description": "默认导出/导入。"}], "returnValue": "无返回值，模块内容被导入。", "examples": [{"code": "// mod.js\nexport const x = 1;\nexport default function() { return 2; }\n// main.js\nimport { x } from './mod.js';\nimport def from './mod.js';", "explanation": "ESM命名导出、默认导出与导入。"}]}}, {"name": "CommonJS & ESM Difference", "trans": ["CommonJS与ESM"], "usage": {"syntax": "// CommonJS\nmodule.exports = value;\nconst mod = require('./mod');\n// ESM\nexport default value;\nimport mod from './mod.js';", "description": "CommonJS用于Node.js，ESM为浏览器和现代Node.js标准。CommonJS同步加载，ESM支持静态分析和异步加载。", "parameters": [{"name": "module.exports", "description": "CommonJS导出。"}, {"name": "require", "description": "CommonJS导入。"}, {"name": "export/import", "description": "ESM导出导入。"}], "returnValue": "无返回值，模块内容被导入。", "examples": [{"code": "// CommonJS\nmodule.exports = { a: 1 };\nconst mod = require('./mod');\n// ESM\nexport const b = 2;\nimport { b } from './mod.js';", "explanation": "CommonJS与ESM的导入导出区别。"}]}}, {"name": "Dynamic import()", "trans": ["动态import"], "usage": {"syntax": "import('./mod.js').then(mod => {\n  // 使用mod\n});", "description": "动态import()按需异步加载模块，返回Promise，适合懒加载和代码分割。", "parameters": [{"name": "import()", "description": "动态导入模块。"}], "returnValue": "返回Promise，resolve为模块内容。", "examples": [{"code": "import('./mod.js').then(mod => {\n  console.log(mod);\n});", "explanation": "动态import异步加载模块。"}]}}, {"name": "作业：模块化实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 用export/import实现模块化\n// 2. 区分CommonJS与ESM\n// 3. 用动态import实现懒加载", "description": "通过实践export/import、CommonJS、动态import，掌握JS模块化。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. ESM/CommonJS导入导出\n// 2. 动态import", "explanation": "作业提示，需结合本节内容完成实现。"}, {"code": "// 正确实现示例\n// mod.js\nexport const a = 1;\nexport default function(){}\n// main.js\nimport { a } from './mod.js';\nimport def from './mod.js';\nimport('./mod.js').then(mod=>console.log(mod.a));", "explanation": "涵盖ESM导入导出、动态import的正确实现。"}]}}]}