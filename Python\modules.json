{"name": "<PERSON><PERSON><PERSON>", "trans": ["模块基础"], "methods": [{"name": "Module Creation and Import", "trans": ["模块的创建与导入"], "usage": {"syntax": "# mymodule.py\ndef foo():\n    pass\nimport mymodule\nmymodule.foo()", "description": "每个.py文件都是一个模块。可通过import语句导入模块，使用模块名.成员访问。", "parameters": [{"name": "模块名", "description": "要导入的模块文件名（不含.py）"}], "returnValue": "模块对象", "examples": [{"code": "# mymodule.py\ndef hello():\n    print('Hello')\n# main.py\nimport mymodule\nmymodule.hello()  # Hello", "explanation": "演示了自定义模块的创建与导入。"}]}}, {"name": "import and from...import", "trans": ["import与from...import"], "usage": {"syntax": "import 模块名\nfrom 模块名 import 成员\nfrom 模块名 import *", "description": "import导入整个模块，from...import导入指定成员。*导入所有成员（不推荐）。", "parameters": [{"name": "模块名", "description": "要导入的模块"}, {"name": "成员", "description": "要导入的函数、类、变量等"}], "returnValue": "导入的模块或成员对象", "examples": [{"code": "import math\nprint(math.pi)\nfrom math import sqrt\nprint(sqrt(16))  # 4.0", "explanation": "演示了import和from...import的用法。"}]}}, {"name": "__name__ and Main Program Check", "trans": ["__name__与主程序判断"], "usage": {"syntax": "if __name__ == '__main__':\n    代码块", "description": "__name__变量用于判断模块是被导入还是直接运行。主程序时__name__为'__main__'。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# mymodule.py\ndef foo():\n    print('foo')\nif __name__ == '__main__':\n    foo()  # 仅直接运行时执行", "explanation": "演示了__name__变量的作用。"}]}}, {"name": "Module Search Path", "trans": ["模块搜索路径"], "usage": {"syntax": "import sys\nprint(sys.path)", "description": "sys.path是模块搜索路径列表。导入模块时会按顺序查找。可动态修改sys.path。", "parameters": [], "returnValue": "模块搜索路径列表", "examples": [{"code": "import sys\nprint(sys.path)", "explanation": "演示了如何查看和理解模块搜索路径。"}]}}, {"name": "Standard Library and Third-Party Modules", "trans": ["标准库与第三方库"], "usage": {"syntax": "import 标准库模块\nimport 第三方库模块", "description": "Python自带大量标准库模块。第三方库需通过pip安装后导入。", "parameters": [{"name": "标准库模块", "description": "如os、sys、math等"}, {"name": "第三方库模块", "description": "如requests、numpy等"}], "returnValue": "模块对象", "examples": [{"code": "import os\nprint(os.getcwd())\nimport requests\nprint(requests.__version__)", "explanation": "演示了标准库和第三方库的导入与使用。"}]}}, {"name": "<PERSON><PERSON>les Assignment", "trans": ["模块基础练习"], "usage": {"syntax": "# 模块基础练习", "description": "完成以下练习，巩固模块基础相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 创建一个自定义模块并导入使用。\n2. 用from...import导入math库的sqrt函数。\n3. 判断当前文件是否为主程序。\n4. 查看并修改sys.path。", "explanation": "练习要求动手实践模块创建、导入、__name__判断、sys.path等。"}]}}]}