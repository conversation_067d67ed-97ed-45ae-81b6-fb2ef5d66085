{"name": "Subqueries", "trans": ["子查询"], "methods": [{"name": "Scalar <PERSON>", "trans": ["标量子查询"], "usage": {"syntax": "SELECT 列名 FROM 表名 WHERE 列名 运算符 (SELECT 单个值 FROM 表名);", "description": "标量子查询返回单个值（一行一列），可以在任何允许表达式的地方使用，如SELECT、WHERE、HAVING等子句。", "parameters": [{"name": "外部查询", "description": "主查询语句"}, {"name": "子查询", "description": "返回单个值的查询"}], "returnValue": "基于子查询结果的查询结果集", "examples": [{"code": "-- 查询比平均年龄大的学生\nSELECT name, age\nFROM students\nWHERE age > (SELECT AVG(age) FROM students);\n\n-- 查询与张三同班的学生\nSELECT name\nFROM students\nWHERE class_id = (SELECT class_id FROM students WHERE name = '张三');", "explanation": "第一个查询找出年龄大于平均年龄的学生；第二个查询找出与张三同班的所有学生。"}]}}, {"name": "Column Subquery", "trans": ["列子查询"], "usage": {"syntax": "SELECT 列名 FROM 表名 WHERE 列名 IN (SELECT 单列多行 FROM 表名);", "description": "列子查询返回单列多行数据，通常与IN、ANY、ALL等运算符一起使用。", "parameters": [{"name": "外部查询", "description": "主查询语句"}, {"name": "子查询", "description": "返回单列多行的查询"}], "returnValue": "基于子查询结果的查询结果集", "examples": [{"code": "-- 查询数学成绩大于90的学生\nSELECT name\nFROM students\nWHERE id IN (SELECT student_id FROM scores WHERE subject = '数学' AND score > 90);\n\n-- 查询所有班长的信息\nSELECT *\nFROM students\nWHERE id IN (SELECT monitor_id FROM classes);", "explanation": "第一个查询找出数学成绩大于90分的所有学生；第二个查询找出所有担任班长的学生信息。"}]}}, {"name": "Row Subquery", "trans": ["行子查询"], "usage": {"syntax": "SELECT 列名 FROM 表名 WHERE (列1, 列2) = (SELECT 值1, 值2 FROM 表名);", "description": "行子查询返回多列数据，可以同时匹配多个列的值。", "parameters": [{"name": "外部查询", "description": "主查询语句"}, {"name": "子查询", "description": "返回多列的查询"}], "returnValue": "基于子查询结果的查询结果集", "examples": [{"code": "-- 查询与张三同年龄同性别的学生\nSELECT name\nFROM students\nWHERE (age, gender) = (SELECT age, gender FROM students WHERE name = '张三');\n\n-- 查询与某个学生成绩完全相同的其他学生\nSELECT s1.name\nFROM students s1\nWHERE (SELECT COUNT(*) FROM scores WHERE student_id = s1.id) = \n      (SELECT COUNT(*) FROM scores WHERE student_id = 1)\nAND (SELECT SUM(score) FROM scores WHERE student_id = s1.id) = \n    (SELECT SUM(score) FROM scores WHERE student_id = 1)\nAND s1.id != 1;", "explanation": "第一个查询找出与张三同年龄同性别的学生；第二个查询找出与ID为1的学生成绩数量和总分都相同的其他学生。"}]}}, {"name": "IN/EXISTS Subquery", "trans": ["IN/EXISTS子查询"], "usage": {"syntax": "SELECT 列名 FROM 表名 WHERE 列名 IN (子查询);\nSELECT 列名 FROM 表名 WHERE EXISTS (子查询);", "description": "IN子查询检查值是否在子查询结果集中；EXISTS子查询检查子查询是否返回任何行。", "parameters": [{"name": "外部查询", "description": "主查询语句"}, {"name": "子查询", "description": "用于IN或EXISTS条件的查询"}], "returnValue": "基于子查询结果的查询结果集", "examples": [{"code": "-- 使用IN子查询查询有成绩记录的学生\nSELECT name\nFROM students\nWHERE id IN (SELECT DISTINCT student_id FROM scores);\n\n-- 使用EXISTS子查询查询有成绩记录的学生\nSELECT name\nFROM students s\nWHERE EXISTS (SELECT 1 FROM scores WHERE student_id = s.id);\n\n-- 使用NOT EXISTS查询没有选修数学的学生\nSELECT name\nFROM students s\nWHERE NOT EXISTS (SELECT 1 FROM scores WHERE student_id = s.id AND subject = '数学');", "explanation": "前两个查询都找出有成绩记录的学生，但使用了不同的方法；第三个查询找出没有数学成绩的学生。"}]}}, {"name": "Correlated Subquery", "trans": ["相关子查询"], "usage": {"syntax": "SELECT 列名 FROM 表名 外部别名 WHERE 列名 运算符 (SELECT 列名 FROM 表名 WHERE 条件包含外部别名);", "description": "相关子查询是引用外部查询列的子查询，子查询的执行依赖于外部查询的当前行。", "parameters": [{"name": "外部查询", "description": "主查询语句，通常带有别名"}, {"name": "子查询", "description": "引用外部查询列的查询"}], "returnValue": "基于相关子查询结果的查询结果集", "examples": [{"code": "-- 查询每个班级中年龄最大的学生\nSELECT *\nFROM students s1\nWHERE age = (SELECT MAX(age) FROM students s2 WHERE s2.class_id = s1.class_id);\n\n-- 查询每个科目成绩高于该科目平均分的学生\nSELECT s.name, sc.subject, sc.score\nFROM students s\nJOIN scores sc ON s.id = sc.student_id\nWHERE sc.score > (SELECT AVG(score) FROM scores WHERE subject = sc.subject);", "explanation": "第一个查询找出每个班级中年龄最大的学生；第二个查询找出每个科目成绩高于该科目平均分的学生。"}]}}, {"name": "Subqueries Assignment", "trans": ["子查询练习"], "usage": {"syntax": "# 子查询练习", "description": "完成以下练习，巩固子查询的使用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n假设有以下表结构：\n\n-- 学生表\nCREATE TABLE students (\n  id INT PRIMARY KEY,\n  name VARCHAR(20),\n  gender CHAR(1),\n  class_id INT,\n  age INT\n);\n\n-- 班级表\nCREATE TABLE classes (\n  id INT PRIMARY KEY,\n  class_name VARCHAR(20),\n  monitor_id INT\n);\n\n-- 成绩表\nCREATE TABLE scores (\n  id INT PRIMARY KEY,\n  student_id INT,\n  subject VARCHAR(20),\n  score DECIMAL(5,2)\n);\n\n请完成以下查询：\n1. 使用标量子查询，查询比平均年龄大的学生。\n2. 使用列子查询，查询数学成绩大于90分的学生姓名。\n3. 使用行子查询，查询与张三同班同性别的学生。\n4. 使用EXISTS子查询，查询没有任何成绩记录的学生。\n5. 使用相关子查询，查询每个班级中年龄最小的学生。\n6. 查询所有科目都及格（分数>=60）的学生。", "explanation": "通过实际操作掌握标量子查询、列子查询、行子查询、IN/EXISTS子查询和相关子查询的使用。"}]}}]}