# Vue学习路线与核心内容

## 1. Vue基础  //已完成

### 模板语法
- 插值表达式
- 指令（v-bind、v-if、v-for等）
- 事件绑定（v-on）
- 属性绑定
- 条件渲染
- 列表渲染
- 过滤器（2.x）
- 模板引用ref

### 组件基础
- 组件定义与注册
- 单文件组件（SFC）
- 父子组件通信（props）
- 事件派发与自定义事件
- 插槽（slot）
- 动态组件
- 组件生命周期
- 组件样式作用域

### 响应式原理
- 响应式数据声明
- 数据代理与劫持
- 计算属性（computed）
- 侦听器（watch）
- 深度监听
- 响应式陷阱与注意事项

### 表单处理
- v-model用法
- 表单元素类型
- 修饰符（.lazy、.number、.trim）
- 表单校验
- 动态表单
- 受控与非受控表单

### 条件与列表渲染
- v-if/v-else/v-else-if
- v-show
- v-for
- key的作用
- 列表项组件提取
- 列表性能优化

### 事件处理
- 事件绑定与修饰符
- 事件对象
- 自定义事件
- 事件冒泡与捕获
- 事件解绑
- 事件委托

## 2. 组合式API //已完成

### setup函数
- setup参数
- 返回值
- 生命周期钩子
- 组合式API与选项式API对比

### ref与reactive
- ref基本用法
- reactive基本用法
- 响应式对象与基本类型
- toRefs与toRef
- shallowRef/shallowReactive
- readonly/markRaw

### 计算属性与侦听器
- computed用法
- watch用法
- watchEffect
- 侦听多个源
- 深度与立即侦听

### 生命周期钩子
- onMounted
- onUpdated
- onUnmounted
- onBeforeMount
- onBeforeUpdate
- onBeforeUnmount
- onErrorCaptured
- 生命周期钩子与选项式对照

### provide/inject
- 跨层级依赖注入
- provide/inject用法
- 响应式注入
- 场景与注意事项

### 自定义组合函数
- 创建自定义hook（composable）
- 逻辑复用
- 状态隔离
- 组合函数参数与返回
- 实际应用示例

## 3. 组件模式  //已完成

### 插槽与作用域插槽
- 默认插槽
- 具名插槽
- 作用域插槽
- 动态插槽
- 插槽内容传递

### 动态组件与异步组件
- is属性动态切换
- keep-alive缓存
- 异步组件加载
- 异步错误处理

### 高阶组件与混入
- 混入（mixin）
- 高阶组件（HOC）
- extends与mixin区别
- 组合式API替代方案

### 依赖注入与插件
- 插件开发
- 全局属性与方法
- 全局组件注册
- 插件生命周期

### 组件通信模式
- props与emit
- provide/inject
- eventBus
- Vuex/Pinia
- $attrs与$listeners
- v-model双向绑定

## 4. 状态管理  //已完成

### Vue内置状态管理
- 组件本地状态
- props向下传递
- 事件向上传递
- 状态提升
- 跨组件通信

### Vuex基础
- Store概念
- State/Getter/Mutation/Action
- 模块化
- 严格模式
- 插件机制
- 异步操作
- 状态持久化

### Pinia
- Store定义
- State/Getter/Action
- 组合式API集成
- 模块拆分
- 插件与中间件
- 状态持久化
- 与Vuex对比

### 其他状态管理方案
- inject/provide
- eventBus
- 组合式API
- 适用场景分析

## 5. 路由      //已完成

### Vue Router基础
- 安装与配置
- 路由模式
- 路由表与嵌套路由
- 动态路由
- 路由参数
- 编程式导航
- 路由守卫
- 路由元信息

### 路由进阶
- 懒加载与代码分割
- 路由懒加载
- 组件缓存与keep-alive
- 滚动行为控制
- 动态添加/移除路由
- 404与重定向

## 6. 性能优化   //已完成

### 响应式优化
- 响应式数据粒度
- computed与watch优化
- 避免不必要的渲染
- v-once/v-memo
- 事件与数据分离

### 虚拟列表与大数据渲染
- 虚拟滚动原理
- 常用虚拟列表库
- 动态高度处理
- 性能测试与调优

### 异步组件与懒加载
- 异步组件加载
- 路由懒加载
- 图片懒加载
- 资源预加载

### 缓存与持久化
- keep-alive
- localStorage/sessionStorage
- IndexedDB
- 数据缓存策略

### 性能分析工具
- Vue Devtools
- Chrome Performance
- Lighthouse
- 性能瓶颈排查

## 7. 测试       //已完成

### 单元测试
- 测试环境搭建
- 测试库选择
- 组件单元测试
- 组合函数测试
- 异步代码测试
- mock依赖

### 组件测试
- 组件渲染测试
- 交互测试
- 快照测试
- 事件测试
- 状态变化测试
- 属性验证

### 集成测试
- 路由测试
- 状态管理测试
- API交互测试
- 用户流程测试
- 测试覆盖率

### Vue Test Utils
- 渲染组件
- 查询元素
- 触发事件
- 断言结果
- 异步测试
- 最佳实践

### Jest与VTU结合
- 测试结构
- mock函数
- 快照测试
- 钩子测试
- 覆盖率报告
- 持续集成

## 8. Vue生态    //已完成

### Nuxt.js
- 服务端渲染(SSR)
- 静态站点生成(SSG)
- 路由自动生成
- API集成
- 布局与页面
- 插件机制
- 性能优化

### TypeScript与Vue
- 类型定义
- 组件类型
- Props类型
- 组合式API类型
- 事件类型
- 通用组件
- 类型断言

### Vue Query/Vue Apollo
- 数据获取
- 缓存管理
- 请求状态
- 乐观更新
- 无限加载
- 轮询和重试
- 预取数据

### CSS-in-JS与样式方案
- scoped样式
- CSS Modules
- 动态样式
- 全局样式
- 样式复用
- CSS-in-JS
- SSR样式处理

### 动画与交互
- Transition组件
- 动画钩子
- 页面转场
- 手势交互
- 微交互
- 性能优化
- 可访问性

### Vue Devtools
- 组件检查
- Props和State查看
- 性能分析
- 调试组合式API
- 组件过滤
- 时间旅行调试
- 网络环境模拟 