{"name": "Dynamic and Async Components", "trans": ["动态组件与异步组件"], "methods": [{"name": "Dynamic Component with is Attribute", "trans": ["is属性动态切换"], "usage": {"syntax": "<component :is=\"componentName\"></component>", "description": "通过is属性动态切换组件，实现组件间的动态切换，常用于标签页、多视图界面等场景。", "parameters": [{"name": ":is", "description": "绑定组件名称或组件对象"}], "returnValue": "渲染指定的组件，无返回值", "examples": [{"code": "// 组件切换\n<template>\n  <component :is=\"currentTab\"></component>\n  <button @click=\"currentTab = 'TabA'\">Tab A</button>\n  <button @click=\"currentTab = 'TabB'\">Tab B</button>\n</template>\n<script setup>\nimport { ref } from 'vue'\nimport TabA from './TabA.vue'\nimport TabB from './TabB.vue'\n\nconst currentTab = ref('TabA')\n</script>", "explanation": "点击按钮动态切换TabA和TabB组件。"}]}}, {"name": "keep-alive Caching", "trans": ["keep-alive缓存"], "usage": {"syntax": "<keep-alive>\n  <component :is=\"currentTab\"></component>\n</keep-alive>", "description": "keep-alive包裹动态组件，缓存非活动组件实例，避免重新渲染，保留组件状态。", "parameters": [{"name": "include", "description": "字符串或正则表达式，只缓存匹配的组件"}, {"name": "exclude", "description": "字符串或正则表达式，不缓存匹配的组件"}, {"name": "max", "description": "数字，最多缓存多少组件实例"}], "returnValue": "缓存的组件实例，无返回值", "examples": [{"code": "<template>\n  <keep-alive :include=\"['TabA', 'TabB']\" :max=\"10\">\n    <component :is=\"currentTab\"></component>\n  </keep-alive>\n</template>", "explanation": "缓存TabA和TabB组件，最多缓存10个组件实例。"}]}}, {"name": "Async Component Loading", "trans": ["异步组件加载"], "usage": {"syntax": "const AsyncComp = defineAsyncComponent(() => import('./AsyncComp.vue'))", "description": "异步组件实现按需加载，减小初始包体积，提升首屏加载速度，适用于大型应用拆分。", "parameters": [{"name": "defineAsyncComponent", "description": "定义异步组件的函数"}, {"name": "import函数", "description": "动态导入组件的函数"}], "returnValue": "返回异步组件，可在模板中使用", "examples": [{"code": "import { defineAsyncComponent } from 'vue'\n\n// 简单用法\nconst AsyncComp = defineAsyncComponent(() => import('./AsyncComp.vue'))\n\n// 完整用法\nconst AsyncCompWithOptions = defineAsyncComponent({\n  loader: () => import('./AsyncComp.vue'),\n  loadingComponent: LoadingComponent,\n  errorComponent: ErrorComponent,\n  delay: 200,\n  timeout: 3000\n})", "explanation": "定义异步组件，支持加载中和错误状态。"}]}}, {"name": "Async Error <PERSON>ling", "trans": ["异步错误处理"], "usage": {"syntax": "defineAsyncComponent({\n  loader: () => import('./AsyncComp.vue'),\n  errorComponent: ErrorComponent,\n  onError(error, retry, fail) {\n    // 处理错误\n  }\n})", "description": "异步组件加载失败时的错误处理，包括显示错误组件、重试加载、超时处理等。", "parameters": [{"name": "errorComponent", "description": "加载失败时显示的组件"}, {"name": "onError", "description": "错误处理回调函数"}, {"name": "timeout", "description": "超时时间，单位毫秒"}], "returnValue": "处理错误的异步组件", "examples": [{"code": "const AsyncComp = defineAsyncComponent({\n  loader: () => import('./AsyncComp.vue'),\n  errorComponent: ErrorComponent,\n  timeout: 3000,\n  onError(error, retry, fail) {\n    if (error.message.includes('网络错误')) {\n      // 网络错误，尝试重试\n      retry()\n    } else {\n      // 其他错误，直接失败\n      fail()\n    }\n  }\n})", "explanation": "处理异步组件加载错误，支持重试和超时。"}]}}, {"name": "Dynamic and Async Components Assignment", "trans": ["动态与异步组件练习"], "usage": {"syntax": "# 动态与异步组件练习", "description": "完成以下练习，巩固动态与异步组件相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 实现标签页切换功能，用is属性动态切换组件。\n2. 给动态组件添加keep-alive缓存，保留组件状态。\n3. 将大型组件改为异步加载，并添加加载中状态。\n4. 处理异步组件加载错误，实现重试功能。", "explanation": "通过这些练习掌握动态组件和异步组件的用法。"}]}}]}