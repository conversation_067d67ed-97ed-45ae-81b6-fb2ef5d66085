{"name": "useEffect", "trans": ["副作用钩子"], "methods": [{"name": "Effect Execution Timing", "trans": ["效果执行时机"], "usage": {"syntax": "useEffect(() => {\n  // 副作用逻辑\n});", "description": "useEffect中的回调函数会在组件渲染后（DOM更新后）执行。默认情况下，每次渲染都会执行副作用。可以用来处理数据获取、事件监听、手动操作DOM等副作用逻辑。", "parameters": [{"name": "effect", "description": "副作用回调函数，在渲染后执行"}], "returnValue": "无返回值，副作用函数可返回清理函数用于卸载时调用", "examples": [{"code": "// useEffect执行时机示例\nimport React, { useState, useEffect } from 'react';\n\nfunction Logger() {\n  const [count, setCount] = useState(0);\n  useEffect(() => {\n    console.log('组件渲染后执行副作用，当前count:', count);\n  });\n  return (\n    <div>\n      <p>count: {count}</p>\n      <button onClick={() => setCount(count + 1)}>加一</button>\n    </div>\n  );\n}", "explanation": "本例展示了useEffect的回调会在每次渲染后执行，适合处理需要响应UI变化的副作用逻辑。"}]}}, {"name": "Dependency Array", "trans": ["依赖数组"], "usage": {"syntax": "useEffect(() => {\n  // 副作用逻辑\n}, [dep1, dep2]);", "description": "依赖数组决定副作用的执行时机。只有当依赖项发生变化时，副作用才会重新执行。依赖数组为空时，副作用只在组件挂载和卸载时执行一次。", "parameters": [{"name": "dep1, dep2", "description": "副作用依赖的state或props变量"}], "returnValue": "无返回值，副作用函数可返回清理函数用于卸载时调用", "examples": [{"code": "// 依赖数组示例\nimport React, { useState, useEffect } from 'react';\n\nfunction Timer({ start }) {\n  const [count, setCount] = useState(start);\n  useEffect(() => {\n    const id = setInterval(() => setCount(c => c + 1), 1000);\n    return () => clearInterval(id); // 清理定时器\n  }, [start]); // 仅当start变化时重新执行\n  return <div>计数：{count}</div>\n}\n\n// 只在挂载和卸载时执行\nfunction OnceEffect() {\n  useEffect(() => {\n    console.log('只执行一次');\n  }, []);\n  return <div>只执行一次的副作用</div>\n}", "explanation": "第一个例子展示了依赖数组的用法，只有start变化时副作用才会重新执行。第二个例子展示了依赖数组为空时，副作用只在挂载和卸载时执行一次。"}]}}, {"name": "Cleanup Side Effects", "trans": ["清除副作用"], "usage": {"syntax": "useEffect(() => {\n  // 副作用逻辑\n  return () => {\n    // 清理逻辑\n  };\n}, [deps]);", "description": "有些副作用需要在组件卸载或依赖变化时进行清理，如定时器、订阅、事件监听等。useEffect回调可以返回一个清理函数，在卸载或依赖变化时自动执行。", "parameters": [{"name": "cleanup", "description": "清理副作用的函数"}], "returnValue": "无返回值，清理函数在卸载或依赖变化时调用", "examples": [{"code": "// 清除副作用示例\nimport React, { useEffect } from 'react';\n\nfunction Timer() {\n  useEffect(() => {\n    const id = setInterval(() => {\n      console.log('tick');\n    }, 1000);\n    return () => {\n      clearInterval(id); // 清理定时器\n      console.log('定时器已清理');\n    };\n  }, []);\n  return <div>定时器已启动</div>;\n}\n\n// 事件监听清理\nfunction WindowResize() {\n  useEffect(() => {\n    function onResize() {\n      console.log('窗口大小变化');\n    }\n    window.addEventListener('resize', onResize);\n    return () => window.removeEventListener('resize', onResize);\n  }, []);\n  return <div>监听窗口大小变化</div>;\n}", "explanation": "第一个例子展示了如何在useEffect中清理定时器。第二个例子展示了如何清理事件监听，防止内存泄漏。"}]}}, {"name": "Conditional Effect Execution", "trans": ["条件执行副作用"], "usage": {"syntax": "useEffect(() => {\n  if (condition) {\n    // 只在满足条件时执行副作用\n  }\n}, [condition]);", "description": "通过在useEffect内部添加条件判断，可以实现副作用只在特定条件下执行。常用于依赖变化后只在部分情况下触发副作用。", "parameters": [{"name": "condition", "description": "控制副作用执行的条件表达式"}], "returnValue": "无返回值，副作用只在条件满足时执行", "examples": [{"code": "// 条件执行副作用示例\nimport React, { useState, useEffect } from 'react';\n\nfunction FetchOnFlag({ shouldFetch }) {\n  const [data, setData] = useState(null);\n  useEffect(() => {\n    if (shouldFetch) {\n      fetch('/api/data').then(res => res.json()).then(setData);\n    }\n  }, [shouldFetch]);\n  return <div>{data ? JSON.stringify(data) : '暂无数据'}</div>;\n}\n\n// 只在count大于0时执行副作用\nfunction PositiveLogger({ count }) {\n  useEffect(() => {\n    if (count > 0) {\n      console.log('count大于0:', count);\n    }\n  }, [count]);\n  return <div>count: {count}</div>;\n}", "explanation": "第一个例子展示了如何根据shouldFetch条件决定是否发起请求。第二个例子展示了只在count大于0时才执行副作用。"}]}}, {"name": "Data Fetching", "trans": ["数据获取"], "usage": {"syntax": "useEffect(() => {\n  fetch(url).then(res => res.json()).then(setData);\n}, [url]);", "description": "useEffect常用于组件挂载或依赖变化时发起数据请求。通过设置依赖数组，可以控制何时重新获取数据。数据获取后通常需要更新state。", "parameters": [{"name": "url", "description": "要请求的数据接口地址"}, {"name": "setData", "description": "用于保存请求结果的state更新函数"}], "returnValue": "无返回值，数据通过state保存并驱动UI更新", "examples": [{"code": "// 数据获取示例\nimport React, { useState, useEffect } from 'react';\n\nfunction UserList() {\n  const [users, setUsers] = useState([]);\n  useEffect(() => {\n    fetch('https://jsonplaceholder.typicode.com/users')\n      .then(res => res.json())\n      .then(setUsers);\n  }, []); // 只在挂载时请求一次\n  return (\n    <ul>\n      {users.map(user => <li key={user.id}>{user.name}</li>)}\n    </ul>\n  );\n}\n\n// 根据参数变化重新获取数据\nfunction RepoList({ org }) {\n  const [repos, setRepos] = useState([]);\n  useEffect(() => {\n    fetch(`https://api.github.com/orgs/${org}/repos`)\n      .then(res => res.json())\n      .then(setRepos);\n  }, [org]);\n  return (\n    <ul>\n      {repos.map(repo => <li key={repo.id}>{repo.name}</li>)}\n    </ul>\n  );\n}", "explanation": "第一个例子展示了如何在组件挂载时获取数据。第二个例子展示了依赖变化时重新获取数据的用法。"}]}}, {"name": "Subscription Management", "trans": ["订阅管理"], "usage": {"syntax": "useEffect(() => {\n  const unsubscribe = subscribe(callback);\n  return () => unsubscribe();\n}, [deps]);", "description": "useEffect常用于管理订阅（如WebSocket、事件总线、外部数据源等）。在副作用中建立订阅，并在清理函数中取消订阅，防止内存泄漏和重复订阅。", "parameters": [{"name": "subscribe", "description": "建立订阅的函数，返回取消订阅的方法"}, {"name": "callback", "description": "订阅时触发的回调函数"}], "returnValue": "无返回值，订阅和取消订阅由副作用和清理函数管理", "examples": [{"code": "// 订阅管理示例\nimport React, { useEffect, useState } from 'react';\n\n// 假设有一个事件总线\nconst bus = {\n  listeners: [],\n  subscribe(fn) { this.listeners.push(fn); return () => { this.listeners = this.listeners.filter(l => l !== fn); }; },\n  emit(data) { this.listeners.forEach(fn => fn(data)); }\n};\n\nfunction BusSubscriber() {\n  const [msg, setMsg] = useState('');\n  useEffect(() => {\n    const unsubscribe = bus.subscribe(setMsg);\n    return () => unsubscribe(); // 清理订阅\n  }, []);\n  return <div>收到消息：{msg}</div>;\n}\n\n// WebSocket订阅管理\nfunction WebSocketDemo() {\n  const [msg, setMsg] = useState('');\n  useEffect(() => {\n    const ws = new WebSocket('wss://echo.websocket.org');\n    ws.onmessage = e => setMsg(e.data);\n    ws.onopen = () => ws.send('hello');\n    return () => ws.close(); // 断开连接\n  }, []);\n  return <div>WebSocket消息：{msg}</div>;\n}", "explanation": "第一个例子展示了如何用useEffect管理事件总线的订阅和取消订阅。第二个例子展示了WebSocket的连接和断开管理。"}]}}, {"name": "DOM Manipulation", "trans": ["DOM操作"], "usage": {"syntax": "useEffect(() => {\n  // 通过ref操作DOM\n  ref.current.focus();\n}, []);", "description": "useEffect可以用于在组件渲染后操作DOM，如设置焦点、滚动、测量等。通常结合useRef获取DOM节点引用。DOM操作应放在副作用中，保证DOM已更新。", "parameters": [{"name": "ref", "description": "通过useRef获取的DOM节点引用"}], "returnValue": "无返回值，DOM操作在副作用中完成", "examples": [{"code": "// DOM操作示例\nimport React, { useRef, useEffect } from 'react';\n\nfunction AutoFocusInput() {\n  const inputRef = useRef();\n  useEffect(() => {\n    inputRef.current.focus(); // 组件挂载后自动聚焦\n  }, []);\n  return <input ref={inputRef} placeholder=\"自动聚焦\" />;\n}\n\n// 滚动到页面底部\nfunction ScrollToBottom({ messages }) {\n  const endRef = useRef();\n  useEffect(() => {\n    endRef.current.scrollIntoView({ behavior: 'smooth' });\n  }, [messages]);\n  return (\n    <div style={{height:200,overflow:'auto'}}>\n      {messages.map((msg,i) => <div key={i}>{msg}</div>)}\n      <div ref={endRef} />\n    </div>\n  );\n}", "explanation": "第一个例子展示了如何用useEffect和ref实现输入框自动聚焦。第二个例子展示了如何在消息更新后自动滚动到底部。"}]}}, {"name": "Common Pitfalls", "trans": ["常见陷阱"], "usage": {"syntax": "// 不要遗漏依赖\nuseEffect(() => {\n  // 使用了外部变量\n}, []); // 错误：依赖未声明\n\n// 正确写法\nuseEffect(() => {\n  // 使用了count\n}, [count]);", "description": "使用useEffect时常见的陷阱包括遗漏依赖、死循环、闭包陷阱等。应确保依赖数组声明所有用到的外部变量，避免副作用重复执行或状态不一致。", "parameters": [{"name": "依赖遗漏", "description": "未在依赖数组声明所有用到的变量，导致副作用不更新或状态错误"}, {"name": "死循环", "description": "副作用中更新依赖变量，导致无限循环"}, {"name": "闭包陷阱", "description": "副作用回调引用了过期的变量值"}], "returnValue": "无返回值，需通过正确依赖声明和写法避免陷阱", "examples": [{"code": "// 依赖遗漏陷阱\nimport React, { useState, useEffect } from 'react';\n\nfunction MissedDeps() {\n  const [count, setCount] = useState(0);\n  useEffect(() => {\n    // 依赖了count但未声明\n    console.log(count);\n  }, []); // 错误：count应在依赖数组中\n  return <button onClick={() => setCount(count + 1)}>加一</button>;\n}\n\n// 死循环陷阱\nfunction InfiniteLoop() {\n  const [num, setNum] = useState(0);\n  useEffect(() => {\n    setNum(num + 1); // 每次渲染都更新num，导致死循环\n  }, [num]);\n  return <div>{num}</div>;\n}\n\n// 闭包陷阱\nfunction ClosureTrap() {\n  const [msg, setMsg] = useState('hello');\n  useEffect(() => {\n    const id = setInterval(() => {\n      // msg值不会随state更新\n      console.log(msg);\n    }, 1000);\n    return () => clearInterval(id);\n  }, []);\n  return <button onClick={() => setMsg('world')}>改消息</button>;\n}", "explanation": "本例展示了useEffect常见的依赖遗漏、死循环和闭包陷阱，并给出正确写法建议。"}]}}, {"name": "Assignment: useEffect练习", "trans": ["作业：useEffect练习"], "usage": {"syntax": "// 作业要求见description", "description": "1. 创建一个定时器组件，挂载时每秒自增，卸载时清理定时器。\n2. 创建一个输入框，输入内容变化时在控制台打印最新值。\n3. 实现一个数据列表组件，挂载时请求远程数据并渲染。\n4. （进阶）实现一个WebSocket消息订阅组件，挂载时连接，卸载时断开。\n5. 总结useEffect常见陷阱并尝试复现。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 示例：useEffect综合练习\nimport React, { useState, useEffect, useRef } from 'react';\n\nfunction Timer() {\n  const [count, setCount] = useState(0);\n  useEffect(() => {\n    const id = setInterval(() => setCount(c => c + 1), 1000);\n    return () => clearInterval(id);\n  }, []);\n  return <div>计数：{count}</div>;\n}\n\nfunction InputLogger() {\n  const [value, setValue] = useState('');\n  useEffect(() => {\n    console.log('输入变化：', value);\n  }, [value]);\n  return <input value={value} onChange={e => setValue(e.target.value)} />;\n}\n\nfunction UserList() {\n  const [users, setUsers] = useState([]);\n  useEffect(() => {\n    fetch('https://jsonplaceholder.typicode.com/users')\n      .then(res => res.json())\n      .then(setUsers);\n  }, []);\n  return <ul>{users.map(u => <li key={u.id}>{u.name}</li>)}</ul>;\n}\n\n// 作业：\n// 1. 尝试实现WebSocket消息订阅组件\n// 2. 尝试复现依赖遗漏、死循环等陷阱并修正", "explanation": "本作业要求你综合运用useEffect的副作用、依赖、清理、数据获取、订阅等知识点。通过动手实践，加深对副作用管理的理解。"}]}}]}