# C++语言学习路线与核心内容

## 1. C++基础

### 语言简介
- C++发展历史
- C++的特点
- C++与C的区别
- C++的应用领域

### 开发环境搭建
- 常用编译器（G++, Clang, MSVC）
- Windows/Linux/Mac环境配置
- 编辑器与IDE选择
- 编译与运行流程
- 第一个C++程序

### 基本语法
- main函数结构
- 语句与分号
- 注释写法
- 标识符与关键字
- 代码风格规范

## 2. 数据类型与运算符

### 基本数据类型
- int、float、double、char、bool
- short、long、unsigned
- 类型转换
- auto与decltype
- sizeof运算符

### 常量与变量
- 变量声明与初始化
- const与constexpr
- 作用域与生命周期

### 运算符
- 算术运算符
- 关系运算符
- 逻辑运算符
- 位运算符
- 赋值运算符
- 其他运算符（条件、逗号、下标、作用域等）
- 运算符重载

## 3. 流程控制

### 条件语句
- if/else语句
- switch语句
- 嵌套条件判断

### 循环语句
- for循环
- while循环
- do...while循环
- range-based for循环
- break与continue
- 循环嵌套

### 跳转语句
- goto语句
- return语句

## 4. 函数

### 函数定义与声明
- 函数的基本结构
- 参数与返回值
- void类型
- 默认参数与函数重载
- 内联函数
- 递归函数
- Lambda表达式

### 作用域与存储类型
- 局部变量与全局变量
- static、extern、register、mutable

### 参数传递方式
- 值传递
- 引用传递
- 指针传递

## 5. 面向对象编程

### 类与对象
- 类的定义与声明
- 对象的创建与使用
- 构造函数与析构函数
- 拷贝构造与赋值运算符
- this指针

### 封装、继承与多态
- 访问控制（public/private/protected）
- 继承与派生类
- 虚函数与多态
- 抽象类与纯虚函数
- 多继承与虚继承

### 运算符重载
- 常见运算符重载
- 输入输出运算符重载
- 友元函数与友元类

## 6. 模板与泛型编程

### 函数模板
- 基本用法
- 模板参数
- 模板特化

### 类模板
- 类模板定义与使用
- 模板成员函数
- 模板继承

### STL标准模板库
- 容器（vector、list、map、set等）
- 迭代器
- 算法（sort、find、for_each等）
- 仿函数与lambda
- 容器适配器（stack、queue、priority_queue）

## 7. 文件与输入输出

### 标准输入输出
- cout/cin
- getline与字符串流
- 格式化输出

### 文件操作
- 文件流ifstream/ofstream/fstream
- 文本文件读写
- 二进制文件读写
- 文件异常处理

## 8. 内存管理

### 动态内存分配
- new/delete
- 智能指针（unique_ptr、shared_ptr、weak_ptr）
- 内存泄漏与检测
- 指针与动态数组

## 9. 异常处理

### 异常机制
- try/catch/throw
- 标准异常类
- 自定义异常
- 异常安全

## 10. 预处理指令

### 常用预处理指令
- #include
- #define
- #ifdef/#ifndef
- #undef
- #error/#pragma

### 宏定义与宏函数
- 宏参数
- 多行宏
- 宏与调试

## 11. 调试与常见错误

### 编译错误与警告
- 语法错误
- 类型不匹配
- 未初始化变量
- 段错误（Segmentation Fault）

### 调试技巧
- gdb调试
- 打印调试
- 断点与单步执行
- 常用调试工具

## 12. 项目实战与练习

### 实战案例
- 简单计算器实现
- 学生成绩管理系统
- 文件批量处理工具
- 字符串查找与替换
- 数据结构（链表、栈、队列、map）实现

### 练习与项目
- 编写九九乘法表
- 实现冒泡排序与二分查找
- 编写自定义字符串函数
- 文件复制工具
- 智能指针应用
- STL容器综合练习 