{"name": "Widget Test", "trans": ["Widget测试"], "methods": [{"name": "Using flutter_test Package", "trans": ["flutter_test包"], "usage": {"syntax": "import 'package:flutter_test/flutter_test.dart';\ntestWidgets('描述', (WidgetTester tester) async { ... });", "description": "flutter_test包是Flutter官方的Widget测试框架，支持组件渲染、交互和断言。", "parameters": [{"name": "testWidgets", "description": "Widget测试用例函数，包含描述和测试逻辑"}, {"name": "WidgetTester", "description": "测试工具对象，用于操作和查找Widget"}], "returnValue": "无返回值，测试通过或失败。", "examples": [{"code": "// flutter_test包用法示例\nimport 'package:flutter/material.dart';\nimport 'package:flutter_test/flutter_test.dart';\n\nvoid main() {\n  testWidgets('Text组件渲染测试', (WidgetTester tester) async {\n    await tester.pumpWidget(MaterialApp(home: Text('Hello')));\n    expect(find.text('Hello'), findsOneWidget); // 断言页面有Hello文本\n  });\n}\n", "explanation": "演示如何用flutter_test包测试Text组件的渲染。"}]}}, {"name": "Widget Rendering and Interaction Test", "trans": ["组件渲染与交互测试"], "usage": {"syntax": "tester.pumpWidget、tester.tap、tester.enterText、expect等", "description": "通过WidgetTester可以渲染组件、模拟点击、输入等交互，并断言UI变化。", "parameters": [{"name": "pumpWidget", "description": "渲染测试用的Widget"}, {"name": "tap", "description": "模拟点击事件"}, {"name": "enterText", "description": "模拟输入文本"}, {"name": "expect", "description": "断言UI状态"}], "returnValue": "无返回值，测试通过或失败。", "examples": [{"code": "// 组件渲染与交互测试示例\nimport 'package:flutter/material.dart';\nimport 'package:flutter_test/flutter_test.dart';\n\nvoid main() {\n  testWidgets('按钮点击计数测试', (WidgetTester tester) async {\n    await tester.pumpWidget(MaterialApp(home: CounterDemo()));\n    expect(find.text('0'), findsOneWidget);\n    await tester.tap(find.byType(ElevatedButton));\n    await tester.pump();\n    expect(find.text('1'), findsOneWidget);\n  });\n}\n\nclass CounterDemo extends StatefulWidget {\n  @override\n  _CounterDemoState createState() => _CounterDemoState();\n}\n\nclass _CounterDemoState extends State<CounterDemo> {\n  int _count = 0;\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      body: Center(child: Text('$_count')),\n      floatingActionButton: ElevatedButton(\n        onPressed: () => setState(() => _count++),\n        child: Text('加1'),\n      ),\n    );\n  }\n}\n", "explanation": "演示如何测试按钮点击后计数器自增的交互效果。"}]}}, {"name": "Assignment", "trans": ["作业：编写一个Widget交互测试用例。"], "usage": {"syntax": "无", "description": "请为一个包含输入框和按钮的页面编写Widget测试，要求输入内容后点击按钮能正确显示输入值。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 作业参考实现\nimport 'package:flutter/material.dart';\nimport 'package:flutter_test/flutter_test.dart';\n\nvoid main() {\n  testWidgets('输入并显示内容测试', (WidgetTester tester) async {\n    await tester.pumpWidget(MaterialApp(home: InputDemo()));\n    await tester.enterText(find.byType(TextField), 'Flutter');\n    await tester.tap(find.byType(ElevatedButton));\n    await tester.pump();\n    expect(find.text('Flutter'), findsOneWidget);\n  });\n}\n\nclass InputDemo extends StatefulWidget {\n  @override\n  _InputDemoState createState() => _InputDemoState();\n}\n\nclass _InputDemoState extends State<InputDemo> {\n  String _text = '';\n  final _controller = TextEditingController();\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      body: Column(\n        children: [\n          Text<PERSON><PERSON>(controller: _controller),\n          ElevatedButton(onPressed: () => setState(() => _text = _controller.text), child: Text('显示')),\n          Text(_text),\n        ],\n      ),\n    );\n  }\n}\n", "explanation": "作业要求测试输入内容后点击按钮能正确显示输入值。"}]}}]}