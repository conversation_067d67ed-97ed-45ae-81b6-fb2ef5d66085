{"name": "Redis Cluster", "trans": ["Redis Cluster集群"], "methods": [{"name": "Sharding Principle", "trans": ["分片原理"], "usage": {"syntax": "Redis Cluster通过哈希槽（hash slot）将数据分布到多个节点，实现分布式存储和线性扩展。", "description": "Redis Cluster采用分片机制，将16384个哈希槽分配给集群中的各个节点。每个key根据CRC16哈希后分配到某个槽，实现数据分布和负载均衡。", "parameters": [{"name": "hash slot", "description": "哈希槽，决定key的分布位置。"}, {"name": "CRC16", "description": "用于计算key对应的哈希槽。"}], "returnValue": "无返回值", "examples": [{"code": "# 查看key属于哪个槽\nCLUSTER KEYSLOT mykey  # 返回mykey对应的槽编号\n", "explanation": "通过CLUSTER KEYSLOT命令查看key的分片槽。"}]}}, {"name": "Node Management and Scaling", "trans": ["节点管理与扩容"], "usage": {"syntax": "通过CLUSTER MEET、CLUSTER FORGET等命令管理节点，支持动态扩容和缩容。", "description": "Redis Cluster支持节点的动态添加、删除和迁移槽位，实现集群的弹性扩展和高可用。", "parameters": [{"name": "CLUSTER MEET", "description": "添加新节点到集群。"}, {"name": "CLUSTER FORGET", "description": "移除节点。"}, {"name": "CLUSTER ADDSLOTS/DELSLOTS", "description": "分配或回收哈希槽。"}], "returnValue": "无返回值", "examples": [{"code": "# 添加新节点\nCLUSTER MEET <ip> <port>\n# 分配槽位\nCLUSTER ADDSLOTS 0 1 2\n# 移除节点\nCLUSTER FORGET <node_id>\n", "explanation": "通过集群命令实现节点的添加、删除和槽位管理。"}]}}, {"name": "Cluster Data Consistency", "trans": ["集群数据一致性"], "usage": {"syntax": "Redis Cluster采用异步复制，存在短暂不一致，需关注故障转移和一致性保障。", "description": "集群内主从节点间采用异步复制，主节点故障时自动故障转移。可通过配置参数和合理架构设计提升一致性和可用性。", "parameters": [{"name": "异步复制", "description": "主从间数据同步为异步，可能短暂不一致。"}, {"name": "故障转移", "description": "主节点故障时自动切换，保障可用性。"}], "returnValue": "无返回值", "examples": [{"code": "# 查看集群状态\nCLUSTER INFO  # 查看集群健康和一致性状态\n", "explanation": "通过CLUSTER INFO命令监控集群一致性和健康。"}]}}, {"name": "Cluster集群作业", "trans": ["作业：Cluster集群"], "usage": {"syntax": "请完成以下任务：\n1. 搭建3主3从的Redis Cluster集群。\n2. 添加新节点并分配槽位。\n3. 模拟主节点故障，观察数据一致性。", "description": "通过实际操作理解Cluster集群的分片、扩容和一致性保障。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 作业示例\n# 1. 使用redis-cli --cluster create搭建集群\n# 2. 使用CLUSTER MEET/ADDSLOTS添加节点和槽位\n# 3. 停止主节点，观察故障转移和一致性\n", "explanation": "通过动手实验掌握Cluster集群的搭建和管理。"}]}}, {"name": "Cluster集群正确实现示例", "trans": ["正确实现卡片"], "usage": {"syntax": "Cluster集群的标准搭建与监控流程。", "description": "展示如何标准搭建Cluster集群，并通过命令监控分片和一致性，确保高可用。", "parameters": [{"name": "redis-cli --cluster create", "description": "一键搭建集群。"}, {"name": "CLUSTER INFO", "description": "监控集群状态和一致性。"}], "returnValue": "无返回值", "examples": [{"code": "# 标准集群搭建\nredis-cli --cluster create 127.0.0.1:7000 127.0.0.1:7001 127.0.0.1:7002 --cluster-replicas 1\n\n# 监控流程\n# 1. 使用CLUSTER INFO命令监控集群状态\n# 2. 使用CLUSTER NODES查看节点分布\n", "explanation": "标准Cluster集群搭建和监控流程，保证分片和一致性。"}]}}]}