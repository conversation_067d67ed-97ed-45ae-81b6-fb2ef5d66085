{"topics": [{"id": "intro-and-framework", "name": "语言与框架简介", "file": "Flutter/flutter-01-intro.json", "description": "Flutter框架的基本介绍、Dart语言、与原生开发的对比及应用场景，适合初学者快速入门。"}, {"id": "setup-env", "name": "开发环境搭建", "file": "Flutter/flutter-02-setup.json", "description": "Flutter SDK安装、IDE配置、模拟器与真机调试、项目结构与第一个应用，帮助学员完成开发环境搭建。"}, {"id": "dart-basic", "name": "基本语法与Dart入门", "file": "Flutter/flutter-03-dart-basic.json", "description": "Dart main函数、变量常量、数据类型、函数方法、类对象、异步编程等基础语法，适合初学者系统入门。"}, {"id": "basic-widgets", "name": "常用基础组件", "file": "Flutter/flutter-04-basic-widgets.json", "description": "Text、Image、Icon、Button、TextField、ListView、GridView、Container等常用Flutter基础组件详解与实用示例。"}, {"id": "layout-widgets", "name": "布局组件", "file": "Flutter/flutter-05-layout-widgets.json", "description": "Row、Column、Stack、Align、Positioned、Expanded、Flexible、Padding、Center、自适应布局等常用布局组件详解与实用示例。"}, {"id": "custom-widgets", "name": "自定义组件", "file": "Flutter/flutter-06-custom-widgets.json", "description": "StatelessWidget、StatefulWidget、组件组合与复用、生命周期等自定义Flutter组件详解与实用示例。"}, {"id": "routing", "name": "路由管理", "file": "Flutter/flutter-07-routing.json", "description": "Navigator基本用法、命名路由、参数传递、返回值、路由守卫等页面跳转与路由管理详解与实用示例。"}, {"id": "route-animation", "name": "页面跳转与动画", "file": "Flutter/flutter-08-route-animation.json", "description": "页面切换动画、自定义路由动画等Flutter页面跳转动画详解与实用示例。"}, {"id": "state-management", "name": "本地状态管理", "file": "Flutter/flutter-09-state-management.json", "description": "setState、InheritedWidget、Provider、Riverpod、Bloc、GetX等主流Flutter状态管理方案详解与实用示例。"}, {"id": "network", "name": "网络请求", "file": "Flutter/flutter-10-network.json", "description": "http、Dio、异步数据获取、JSON解析、异常处理等Flutter网络请求详解与实用示例。"}, {"id": "storage", "name": "本地存储", "file": "Flutter/flutter-11-storage.json", "description": "SharedPreferences、文件读写、sqflite数据库、加密等Flutter本地存储方案详解与实用示例。"}, {"id": "animation-basic", "name": "动画基础", "file": "Flutter/flutter-12-animation-basic.json", "description": "AnimationController、Twe<PERSON>、Curve、隐式动画、Hero动画、手势交互等Flutter动画基础详解与实用示例。"}, {"id": "custom-animation", "name": "自定义动画", "file": "Flutter/flutter-13-custom-animation.json", "description": "自定义动画实现、动画与状态结合等Flutter自定义动画详解与实用示例。"}, {"id": "native-integration", "name": "原生集成", "file": "Flutter/flutter-14-native-integration.json", "description": "插件开发与使用、与原生代码通信、调用相机定位传感器、推送与通知等Flutter原生集成详解与实用示例。"}, {"id": "multi-platform-adapt", "name": "多端适配", "file": "Flutter/flutter-15-multi-platform-adapt.json", "description": "响应式布局、国际化与本地化、主题与样式自定义等Flutter多端适配详解与实用示例。"}, {"id": "unit-test", "name": "单元测试", "file": "Flutter/flutter-16-unit-test.json", "description": "test包用法、断言与Mock等Flutter单元测试详解与实用示例。"}, {"id": "widget-test", "name": "Widget测试", "file": "Flutter/flutter-17-widget-test.json", "description": "flutter_test包、组件渲染与交互测试等Flutter Widget测试详解与实用示例。"}, {"id": "integration-test", "name": "集成测试", "file": "Flutter/flutter-18-integration-test.json", "description": "integration_test包、自动化UI测试等Flutter集成测试详解与实用示例。"}, {"id": "performance-analysis", "name": "性能分析", "file": "Flutter/flutter-19-performance-analysis.json", "description": "Flutter DevTools、帧率与内存分析、性能瓶颈定位等Flutter性能分析详解与实用示例。"}, {"id": "optimization-tips", "name": "优化技巧", "file": "Flutter/flutter-20-optimization-tips.json", "description": "组件重用与懒加载、图片与资源优化、动画性能优化、启动速度优化等Flutter优化技巧详解与实用示例。"}, {"id": "common-error-troubleshooting", "name": "常见错误排查", "file": "Flutter/flutter-common-error-troubleshooting.json", "description": "Flutter开发中常见的编译、渲染、网络、状态同步等错误排查与修复方法。"}, {"id": "debugging-tips", "name": "调试技巧", "file": "Flutter/flutter-debugging-tips.json", "description": "Flutter开发中的日志、断点、热重载、DevTools等调试技巧详解与实用示例。"}, {"id": "practical-cases", "name": "实战案例", "file": "Flutter/flutter-practical-cases.json", "description": "Flutter开发中的实用案例详解与实用示例。"}]}