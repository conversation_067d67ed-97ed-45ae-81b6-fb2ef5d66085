{"name": "Container Networking", "trans": ["容器网络"], "methods": [{"name": "Network Modes", "trans": ["网络模式（bridge、host、none、container）"], "usage": {"syntax": "docker run --network=<模式> <镜像名>", "description": "通过--network参数指定容器的网络模式，常用有bridge（默认）、host、none、container。", "parameters": [{"name": "--network", "description": "指定网络模式，如bridge、host、none、container。"}, {"name": "<镜像名>", "description": "要运行的镜像名称。"}], "returnValue": "无返回值，容器以指定网络模式运行。", "examples": [{"code": "# 使用host网络模式\ndocker run --network=host nginx", "explanation": "nginx容器直接使用主机网络。"}, {"code": "# 使用none网络模式\ndocker run --network=none nginx", "explanation": "nginx容器无网络连接。"}]}}, {"name": "Custom Network Creation", "trans": ["自定义网络创建"], "usage": {"syntax": "docker network create <网络名>", "description": "通过docker network create命令创建自定义网络，便于容器间通信和隔离。", "parameters": [{"name": "<网络名>", "description": "自定义网络的名称。"}], "returnValue": "无返回值，创建后可供容器使用。", "examples": [{"code": "# 创建名为mynet的自定义网络\ndocker network create mynet", "explanation": "新建一个名为mynet的自定义网络。"}]}}, {"name": "Container Communication", "trans": ["容器间通信"], "usage": {"syntax": "docker run --network=<网络名> --name <容器名> <镜像名>", "description": "将多个容器加入同一自定义网络后，可通过容器名互相通信。", "parameters": [{"name": "--network", "description": "指定加入的自定义网络。"}, {"name": "--name", "description": "容器名称，便于通信。"}, {"name": "<镜像名>", "description": "要运行的镜像名称。"}], "returnValue": "无返回值，容器间可通过名称互通。", "examples": [{"code": "# 启动两个容器在同一网络\ndocker run -d --network=mynet --name web nginx\ndocker run -d --network=mynet --name busybox busybox sleep 3600", "explanation": "web和busybox容器可通过名称互相访问。"}]}}, {"name": "Port Mapping & Exposure", "trans": ["端口映射与暴露"], "usage": {"syntax": "docker run -p <主机端口>:<容器端口> <镜像名>", "description": "通过-p参数将主机端口映射到容器端口，实现外部访问。", "parameters": [{"name": "-p", "description": "端口映射格式为主机端口:容器端口。"}, {"name": "<镜像名>", "description": "要运行的镜像名称。"}], "returnValue": "无返回值，主机端口可访问容器服务。", "examples": [{"code": "# 映射本地8080到容器80\ndocker run -d -p 8080:80 nginx", "explanation": "本地8080端口可访问nginx容器的80端口。"}]}}, {"name": "Network Isolation & Security", "trans": ["网络隔离与安全"], "usage": {"syntax": "docker network inspect <网络名>\ndocker network rm <网络名>", "description": "通过自定义网络实现容器间隔离，使用inspect查看网络详情，rm删除网络。", "parameters": [{"name": "docker network inspect", "description": "查看网络详细信息。"}, {"name": "docker network rm", "description": "删除自定义网络。"}, {"name": "<网络名>", "description": "目标网络名称。"}], "returnValue": "无返回值，提升容器间安全隔离。", "examples": [{"code": "# 查看网络详情\ndocker network inspect mynet\n# 删除网络\ndocker network rm mynet", "explanation": "查看自定义网络配置并删除网络，提升隔离性。"}]}}, {"name": "Assignment: 容器网络实践", "trans": ["作业：容器网络实践"], "usage": {"syntax": "请完成以下任务：\n1. 创建自定义网络mynet\n2. 启动nginx和busybox容器并加入mynet\n3. 在busybox容器内ping nginx容器\n4. 将nginx容器80端口映射到主机8080\n5. 删除mynet网络并观察效果", "description": "通过实际操作掌握容器网络模式、自定义网络、通信、端口映射和隔离。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 1. 创建自定义网络\ndocker network create mynet\n# 2. 启动容器并加入网络\ndocker run -d --network=mynet --name web nginx\ndocker run -it --network=mynet --name busybox busybox\n# 3. 在busybox内ping nginx\nping web\n# 4. 端口映射\ndocker run -d -p 8080:80 --name web2 nginx\n# 5. 删除网络\ndocker network rm mynet", "explanation": "依次完成容器网络的核心实践。"}]}}]}