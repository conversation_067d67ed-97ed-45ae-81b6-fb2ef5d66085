[{"id": "language-basics", "name": "Dart语言简介", "file": "Dart/language-introduction.json", "description": "Dart语言的基本介绍、历史、特点和应用场景"}, {"id": "environment-setup", "name": "开发环境搭建", "file": "Dart/environment-setup.json", "description": "Dart SDK安装、编辑器配置和第一个Dart程序"}, {"id": "basic-syntax", "name": "基本语法", "file": "Dart/basic-syntax.json", "description": "Dart的基本语法，包括main函数、变量与常量、数据类型、注释、标识符与关键字等"}, {"id": "data-types", "name": "基本数据类型", "file": "Dart/data-types.json", "description": "Dart的数据类型，包括数字类型、字符串、布尔值、动态类型、类型推断与转换、常量定义等"}, {"id": "operators", "name": "运算符", "file": "Dart/operators.json", "description": "Dart的各种运算符，包括算术运算符、关系运算符、逻辑运算符、位运算符、赋值运算符和其他特殊运算符"}, {"id": "control-flow", "name": "流程控制", "file": "Dart/control-flow.json", "description": "Dart的流程控制语句，包括if/else条件语句、switch/case选择语句和嵌套条件判断"}, {"id": "loops", "name": "循环语句", "file": "Dart/loops.json", "description": "Dart的循环语句，包括for循环、while循环、do...while循环、for-in与forEach、break与continue以及循环嵌套"}, {"id": "jump-statements", "name": "跳转语句", "file": "Dart/jump-statements.json", "description": "Dart的跳转语句，包括return语句和throw语句，用于控制函数返回和异常处理"}, {"id": "functions", "name": "函数定义与调用", "file": "Dart/functions.json", "description": "Dart的函数定义与调用，包括函数结构、参数与返回值、可选参数与默认值、命名参数与位置参数、匿名函数与箭头函数"}, {"id": "classes", "name": "类与对象", "file": "Dart/classes.json", "description": "Dart的类与对象，包括类的定义、构造函数、实例方法、访问修饰符、属性和字段、实例化与使用"}, {"id": "inheritance", "name": "继承与多态", "file": "Dart/inheritance.json", "description": "Dart的继承与多态特性，包括继承与super关键字、抽象类与接口、mixin与with关键字、方法重写与多态"}, {"id": "collections", "name": "集合类型", "file": "Dart/collections.json", "description": "Dart的集合类型，包括List、Set和Map的定义、初始化、常用操作、遍历与转换"}, {"id": "async-programming", "name": "异步编程", "file": "Dart/async-programming.json", "description": "Dart的异步编程，包括Future基本用法、async/await语法、异步异常处理"}, {"id": "stream", "name": "Stream流", "file": "Dart/stream.json", "description": "Dart的Stream流，包括Stream定义与监听、单订阅与广播流、Stream常用操作"}, {"id": "exception-handling", "name": "异常处理", "file": "Dart/exception.json", "description": "Dart的异常处理机制，包括try/catch/finally、抛出异常与自定义异常、异步异常捕获"}, {"id": "file-io", "name": "文件操作", "file": "Dart/file-io.json", "description": "Dart的文件操作，包括文件读写、目录遍历、文件路径处理"}]