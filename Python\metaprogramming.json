{"name": "Metaprogramming", "trans": ["元编程"], "methods": [{"name": "Dynamic Attributes and Methods", "trans": ["动态属性与方法"], "usage": {"syntax": "setattr(obj, name, value)\ngetattr(obj, name[, default])", "description": "可在运行时动态添加、修改对象的属性和方法，实现灵活的对象行为。", "parameters": [{"name": "obj", "description": "目标对象"}, {"name": "name", "description": "属性或方法名"}, {"name": "value", "description": "属性值或方法"}], "returnValue": "属性值、方法或None", "examples": [{"code": "class A: pass\na = A()\nsetattr(a, 'x', 10)\nprint(a.x)  # 10\ndef foo(self): return 123\nsetattr(A, 'foo', foo)\nprint(a.foo())  # 123", "explanation": "演示了动态添加属性和方法。"}]}}, {"name": "getattr and setattr", "trans": ["getattr/setattr"], "usage": {"syntax": "getattr(obj, name[, default])\nsetattr(obj, name, value)", "description": "getattr用于获取对象属性，setattr用于设置对象属性，支持动态操作。", "parameters": [{"name": "obj", "description": "目标对象"}, {"name": "name", "description": "属性名"}, {"name": "value", "description": "属性值（仅setattr）"}, {"name": "default", "description": "默认值（仅getattr）"}], "returnValue": "属性值或None", "examples": [{"code": "class B: pass\nb = B()\nsetattr(b, 'y', 20)\nprint(getattr(b, 'y'))  # 20\nprint(getattr(b, 'z', '默认'))  # 默认", "explanation": "演示了getattr和setattr的用法。"}]}}, {"name": "type and Metaclass", "trans": ["type与元类"], "usage": {"syntax": "type(name, bases, dict)\nclass Meta(type): ...", "description": "type可动态创建类，元类用于控制类的创建过程，实现高级定制。", "parameters": [{"name": "name", "description": "类名"}, {"name": "bases", "description": "基类元组"}, {"name": "dict", "description": "类属性字典"}], "returnValue": "新类或元类对象", "examples": [{"code": "C = type('C', (), {'z': 30})\nc = C()\nprint(c.z)  # 30\nclass Meta(type):\n    def __new__(cls, name, bases, dct):\n        dct['tag'] = 'meta'\n        return super().__new__(cls, name, bases, dct)\nclass D(metaclass=Meta): pass\nd = D()\nprint(d.tag)", "explanation": "演示了type动态创建类和元类的用法。"}]}}, {"name": "Dynamic Import and Reflection", "trans": ["动态导入与反射"], "usage": {"syntax": "__import__('module')\nhasattr(obj, name)", "description": "可通过__import__动态导入模块，hasattr等反射函数可动态检查和操作对象属性。", "parameters": [{"name": "module", "description": "模块名字符串"}, {"name": "obj", "description": "目标对象"}, {"name": "name", "description": "属性名"}], "returnValue": "模块对象、布尔值等", "examples": [{"code": "math = __import__('math')\nprint(math.sqrt(16))\nclass E: x = 1\ne = E()\nprint(hasattr(e, 'x'))  # True", "explanation": "演示了动态导入和反射操作。"}]}}, {"name": "Metaprogramming Assignment", "trans": ["元编程练习"], "usage": {"syntax": "# 元编程练习", "description": "完成以下练习，巩固元编程相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用setattr动态添加对象属性。\n2. 用type动态创建一个类。\n3. 用元类为类自动添加属性。\n4. 用__import__动态导入math模块并调用sqrt。", "explanation": "练习要求动手实践setattr、type、元类、__import__等。"}]}}]}