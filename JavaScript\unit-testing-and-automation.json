{"name": "Unit Testing and Automation", "trans": ["单元测试与自动化"], "methods": [{"name": "Jest/Mocha Basics", "trans": ["Jest/Mocha基础"], "usage": {"syntax": "jest / mocha test.js", "description": "Jest和Mocha是主流的JavaScript测试框架，用于编写和运行单元测试。\n- Jest：集成断言、Mo<PERSON>、覆盖率，适合React项目\n- Mocha：灵活、插件丰富，适合多种场景", "parameters": [{"name": "testFile", "description": "测试文件路径"}], "returnValue": "无返回值", "examples": [{"code": "// 使用Jest运行测试\nnpx jest\n\n// 使用Mocha运行测试\nnpx mocha test.js\n\n// Jest测试示例\n// sum.js\nfunction sum(a, b) { return a + b; }\nmodule.exports = sum;\n// sum.test.js\nconst sum = require('./sum');\ntest('1 + 2 = 3', () => {\n  expect(sum(1, 2)).toBe(3);\n});", "explanation": "示例展示了Jest和Mocha的基本用法，以及Jest的简单单元测试写法。"}]}}, {"name": "Assertion and Mock", "trans": ["断言与Mock"], "usage": {"syntax": "expect(value).toBe(x) / sinon.mock(obj)", "description": "断言用于判断测试结果是否符合预期，Mock用于模拟函数、模块或数据。\n- Jest内置expect断言和Mock功能\n- Mocha常配合Chai断言库和Sinon Mock库", "parameters": [{"name": "value", "description": "被断言的值"}, {"name": "x", "description": "期望值"}], "returnValue": "无返回值", "examples": [{"code": "// Jest断言\nexpect(2 + 2).toBe(4);\nexpect([1, 2]).toContain(2);\n\n// Jest <PERSON>ck函数\nconst fn = jest.fn();\nfn('hello');\nexpect(fn).toHaveBeenCalledWith('hello');\n\n// <PERSON><PERSON> + <PERSON><PERSON>断言\nconst { expect } = require('chai');\nexpect(2 + 2).to.equal(4);\n\n// Sinon Mock\nconst sinon = require('sinon');\nconst obj = { say: () => 'hi' };\nconst mock = sinon.mock(obj);\nmock.expects('say').once().returns('hello');\nobj.say();\nmock.verify();", "explanation": "示例展示了Jest和Mocha+Chai+Sinon的断言与Mock用法。"}]}}, {"name": "Coverage Report", "trans": ["覆盖率报告"], "usage": {"syntax": "jest --coverage / nyc mocha", "description": "覆盖率报告用于统计测试代码对源代码的覆盖程度，包括语句、分支、函数等。\n- Jest内置覆盖率统计\n- NYC可与Mocha等配合生成报告", "parameters": [{"name": "command", "description": "生成覆盖率报告的命令"}], "returnValue": "无返回值", "examples": [{"code": "// Jest生成覆盖率报告\nnpx jest --coverage\n\n// NYC配合Mocha生成覆盖率\nnpx nyc mocha test.js\n\n// 查看覆盖率报告\n// coverage/lcov-report/index.html", "explanation": "示例展示了如何用Jest和NYC生成和查看覆盖率报告。"}]}}, {"name": "Continuous Integration", "trans": ["持续集成"], "usage": {"syntax": ".github/workflows/ci.yml / GitLab CI / Jenkinsfile", "description": "持续集成（CI）通过自动化工具在代码提交后自动运行测试、构建和部署，提高开发效率和代码质量。常用工具：\n- GitHub Actions\n- GitLab CI\n- Jenkins", "parameters": [{"name": "config", "description": "CI配置文件路径或内容"}], "returnValue": "无返回值", "examples": [{"code": "# GitHub Actions CI配置示例\nname: CI\non: [push]\njobs:\n  build:\n    runs-on: ubuntu-latest\n    steps:\n      - uses: actions/checkout@v3\n      - name: 安装依赖\n        run: npm install\n      - name: 运行测试\n        run: npm test", "explanation": "示例展示了GitHub Actions自动化测试的基本配置。"}]}}, {"name": "Unit Testing Assignment", "trans": ["单元测试与自动化练习"], "usage": {"syntax": "// 单元测试与自动化练习", "description": "完成以下练习，巩固单元测试与自动化相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "/*\n练习：\n1. 使用Jest或Mocha为一个加法函数编写单元测试\n2. 使用Mock模拟一个API请求\n3. 生成并查看覆盖率报告\n4. 配置一次自动化CI流程\n*/", "explanation": "练习要求动手实践单元测试、<PERSON><PERSON>、覆盖率和持续集成配置。"}]}}]}