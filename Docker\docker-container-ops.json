{"name": "Container Operations", "trans": ["容器操作"], "methods": [{"name": "docker run", "trans": ["创建与启动容器"], "usage": {"syntax": "docker run [OPTIONS] <镜像名> [命令]", "description": "基于指定镜像创建并启动一个新容器，可附加参数控制网络、端口、挂载等。", "parameters": [{"name": "[OPTIONS]", "description": "可选参数，如-d后台运行，-p端口映射，--name容器命名等。"}, {"name": "<镜像名>", "description": "要运行的镜像名称。"}, {"name": "[命令]", "description": "容器启动时执行的命令，可选。"}], "returnValue": "无返回值，命令执行后新容器启动。", "examples": [{"code": "# 以交互模式启动nginx容器\ndocker run -it --name mynginx nginx", "explanation": "创建并进入名为mynginx的nginx容器。"}, {"code": "# 后台运行并映射端口\ndocker run -d -p 8080:80 --name web nginx", "explanation": "后台运行nginx容器并将本地8080端口映射到容器80端口。"}]}}, {"name": "docker ps", "trans": ["查看容器"], "usage": {"syntax": "docker ps [OPTIONS]", "description": "列出当前正在运行的容器，-a参数可显示所有（包括已停止）容器。", "parameters": [{"name": "[OPTIONS]", "description": "可选参数，如-a显示所有容器，-q只显示容器ID等。"}], "returnValue": "无返回值，命令行输出容器列表。", "examples": [{"code": "# 查看正在运行的容器\ndocker ps", "explanation": "显示当前所有运行中的容器信息。"}, {"code": "# 查看所有容器（含已停止）\ndocker ps -a", "explanation": "显示所有容器，包括已停止的。"}]}}, {"name": "docker stop / docker rm", "trans": ["停止与删除容器"], "usage": {"syntax": "docker stop <容器名或ID>\ndocker rm <容器名或ID>", "description": "docker stop用于停止运行中的容器，docker rm用于删除已停止的容器。", "parameters": [{"name": "<容器名或ID>", "description": "目标容器的名称或ID。"}], "returnValue": "无返回值，命令执行后容器被停止或删除。", "examples": [{"code": "# 停止容器\ndocker stop mynginx\n# 删除容器\ndocker rm mynginx", "explanation": "先停止名为mynginx的容器，再将其删除。"}]}}, {"name": "docker logs", "trans": ["容器日志查看"], "usage": {"syntax": "docker logs [OPTIONS] <容器名或ID>", "description": "查看指定容器的标准输出日志，常用于排查问题。", "parameters": [{"name": "[OPTIONS]", "description": "可选参数，如-f实时输出，--tail显示最后N行等。"}, {"name": "<容器名或ID>", "description": "目标容器的名称或ID。"}], "returnValue": "无返回值，命令行输出容器日志。", "examples": [{"code": "# 查看容器日志\ndocker logs mynginx", "explanation": "输出mynginx容器的全部日志。"}, {"code": "# 实时查看日志\ndocker logs -f mynginx", "explanation": "实时跟踪mynginx容器日志输出。"}]}}, {"name": "docker exec / docker attach", "trans": ["容器进入与交互"], "usage": {"syntax": "docker exec -it <容器名或ID> <命令>\ndocker attach <容器名或ID>", "description": "docker exec用于在运行中的容器内执行命令，常用于进入容器shell。docker attach用于附加到容器主进程。", "parameters": [{"name": "<容器名或ID>", "description": "目标容器的名称或ID。"}, {"name": "<命令>", "description": "要在容器内执行的命令，如/bin/sh。"}], "returnValue": "无返回值，命令行进入容器或执行命令。", "examples": [{"code": "# 进入容器shell\ndocker exec -it mynginx /bin/sh", "explanation": "在mynginx容器内打开交互式shell。"}, {"code": "# 附加到容器主进程\ndocker attach mynginx", "explanation": "附加到mynginx容器的主进程，直接交互。"}]}}, {"name": "Assignment: 容器操作实践", "trans": ["作业：容器操作实践"], "usage": {"syntax": "请完成以下任务：\n1. 使用nginx镜像创建并启动一个名为testnginx的容器\n2. 查看所有容器并找到testnginx的ID\n3. 停止并删除testnginx容器\n4. 查看testnginx容器日志（如有）\n5. 使用exec进入另一个正在运行的容器并执行ls命令", "description": "通过实际操作掌握容器的创建、查看、停止、删除、日志和交互。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 1. 创建并启动容器\ndocker run -d --name testnginx nginx\n# 2. 查看所有容器\ndocker ps -a\n# 3. 停止并删除\ndocker stop testnginx\ndocker rm testnginx\n# 4. 查看日志\ndocker logs testnginx\n# 5. 进入另一个容器\ndocker exec -it <容器名> ls", "explanation": "依次完成容器操作的核心实践。"}]}}]}