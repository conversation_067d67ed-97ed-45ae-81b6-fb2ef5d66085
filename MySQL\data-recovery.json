{"name": "Data Recovery", "trans": ["恢复数据"], "methods": [{"name": "Restore from SQL File", "trans": ["恢复SQL文件"], "usage": {"syntax": "mysql -u 用户名 -p 数据库名 < 备份文件.sql", "description": "通过mysql命令行工具将SQL备份文件导入到指定数据库，实现数据恢复。适用于mysqldump等逻辑备份生成的SQL文件。", "parameters": [{"name": "用户名", "description": "数据库登录用户名"}, {"name": "数据库名", "description": "要恢复的目标数据库"}, {"name": "备份文件.sql", "description": "SQL备份文件名"}], "returnValue": "无返回值", "examples": [{"code": "-- 恢复testdb数据库\nmysql -u root -p testdb < testdb_backup.sql", "explanation": "本例展示了如何通过mysql命令行将SQL备份文件恢复到testdb数据库。"}]}}, {"name": "<PERSON>ore from Physical Backup", "trans": ["恢复物理备份"], "usage": {"syntax": "-- 停止MySQL服务\nservice mysql stop\n\n-- 拷贝物理备份文件到数据目录\ncp -r /path/to/backup/* /var/lib/mysql/\n\n-- 启动MySQL服务\nservice mysql start", "description": "通过拷贝物理备份文件（如整个数据目录）并重启MySQL服务，实现数据库的物理级恢复。适用于大数据量或需要完整恢复的场景。", "parameters": [{"name": "物理备份目录", "description": "包含所有数据库文件的备份目录"}, {"name": "MySQL数据目录", "description": "MySQL实际存储数据的目录"}], "returnValue": "无返回值", "examples": [{"code": "-- 恢复物理备份\nservice mysql stop\ncp -r /backup/mysql/* /var/lib/mysql/\nservice mysql start", "explanation": "本例展示了如何通过物理文件拷贝和服务重启恢复MySQL数据库。"}]}}, {"name": "Data Recovery Assignment", "trans": ["恢复数据练习"], "usage": {"syntax": "# 恢复数据练习", "description": "完成以下练习，巩固MySQL数据恢复的基本技能。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 使用mysql命令将testdb_backup.sql恢复到testdb数据库。\n2. 假设有/backup/mysql/目录的物理备份，恢复到MySQL数据目录并重启服务。\n3. 总结逻辑备份和物理备份恢复的优缺点和适用场景。", "explanation": "通过这些练习，你将掌握MySQL逻辑和物理备份的恢复方法及其适用场景。"}]}}]}