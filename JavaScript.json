{"topics": [{"id": "variables-and-constants", "name": "变量与常量", "file": "JavaScript/variables-and-constants.json", "description": "详细介绍var/let/const声明、变量作用域、变量提升和常量特性，帮助掌握JavaScript变量基础。"}, {"id": "data-types", "name": "数据类型", "file": "JavaScript/data-types.json", "description": "系统讲解JS的基本数据类型、引用类型、类型判断与转换、浅拷贝与深拷贝，帮助理解和应用数据类型。"}, {"id": "operators-and-expressions", "name": "运算符与表达式", "file": "JavaScript/operators-and-expressions.json", "description": "系统讲解JS的算术、赋值、比较、逻辑、位、三元等运算符及表达式，帮助掌握表达式计算和条件判断。"}, {"id": "control-flow", "name": "流程控制", "file": "JavaScript/control-flow.json", "description": "系统讲解if/else、switch、循环、break/continue、return等流程控制语句，帮助掌握程序流程。"}, {"id": "functions", "name": "函数基础", "file": "JavaScript/functions.json", "description": "系统讲解函数声明、参数、箭头函数、IIFE等函数基础知识，帮助掌握函数用法。"}, {"id": "scope-and-closure", "name": "作用域与闭包", "file": "JavaScript/scope-and-closure.json", "description": "系统讲解作用域链、闭包、this、call/apply/bind、内存管理等，帮助理解JS执行上下文和内存机制。"}, {"id": "objects", "name": "对象基础", "file": "JavaScript/objects.json", "description": "系统讲解对象字面量、属性操作、描述符、方法、原型与继承等，帮助掌握JS对象机制。"}, {"id": "classes-and-oop", "name": "类与ES6面向对象", "file": "JavaScript/classes-and-oop.json", "description": "系统讲解class声明、构造函数、静态属性、继承、多态、getter/setter等ES6面向对象知识。"}, {"id": "arrays", "name": "数组基础", "file": "JavaScript/arrays.json", "description": "系统讲解数组声明、遍历、常用方法、多维与稀疏数组等，帮助掌握JS数组操作。"}, {"id": "collections", "name": "ES6集合类型", "file": "JavaScript/collections.json", "description": "系统讲解Set、Map、WeakSet、WeakMap等ES6集合类型及常用操作。"}, {"id": "strings", "name": "字符串操作", "file": "JavaScript/strings.json", "description": "系统讲解字符串声明、模板字符串、常用方法、遍历与处理等，帮助掌握JS字符串操作。"}, {"id": "regex", "name": "正则表达式", "file": "JavaScript/regex.json", "description": "系统讲解正则基础语法、常用方法、应用场景等，帮助掌握JS正则表达式。"}, {"id": "destructuring-and-spread", "name": "变量与解构", "file": "JavaScript/destructuring-and-spread.json", "description": "系统讲解let/const、解构赋值、扩展运算符等ES6变量与解构用法。"}, {"id": "template-literals-and-shorthand", "name": "模板字符串与简写", "file": "JavaScript/template-literals-and-shorthand.json", "description": "系统讲解模板字符串、属性简写、方法简写等ES6简洁语法。"}, {"id": "promise-and-async", "name": "Promise与异步", "file": "JavaScript/promise-and-async.json", "description": "系统讲解Promise、async/await、fetch、Generator、异步流程控制等ES6异步新特性。"}, {"id": "modules", "name": "模块化", "file": "JavaScript/modules.json", "description": "系统讲解export/import、CommonJS与ESM、动态import等模块化新特性。"}, {"id": "es6plus-features", "name": "其他ES6+特性", "file": "JavaScript/es6plus-features.json", "description": "系统讲解Symbol、Proxy、可选链、Array新方法等ES6+常用新特性。"}, {"id": "callback-and-promise", "name": "回调与Promise", "file": "JavaScript/callback-and-promise.json", "description": "系统讲解回调函数、回调地狱、Promise链式调用、错误处理等异步编程基础。"}, {"id": "async-await", "name": "Async/Await", "file": "JavaScript/async-await.json", "description": "系统讲解async/await基本用法、错误捕获与并发处理，帮助掌握现代JavaScript异步编程。"}, {"id": "event-loop", "name": "事件循环与微任务", "file": "JavaScript/event-loop.json", "description": "系统讲解事件循环机制、宏任务与微任务、定时器、requestAnimationFrame等JavaScript执行机制。"}, {"id": "dom-operations", "name": "DOM操作", "file": "JavaScript/dom-operations.json", "description": "系统讲解节点获取与遍历、节点增删改查、属性与样式操作、事件绑定与委托、表单处理等DOM操作技术。"}, {"id": "bom-operations", "name": "BOM操作", "file": "JavaScript/bom-operations.json", "description": "系统讲解window对象、location/history/navigator、本地存储、弹窗与定时器等浏览器对象模型操作技术。"}, {"id": "event-model", "name": "事件模型", "file": "JavaScript/event-model.json", "description": "系统讲解事件捕获与冒泡、事件对象与属性、自定义事件、事件委托等JavaScript事件处理机制。"}, {"id": "javascript-events", "name": "JavaScript事件", "file": "JavaScript/event-types.json", "description": "JavaScript中常见的事件类型，包括鼠标事件、键盘事件、表单事件以及触摸与手势事件"}, {"id": "utility-functions", "name": "常用工具函数", "file": "JavaScript/utility-functions.json", "description": "系统讲解防抖与节流、深拷贝与浅拷贝、对象合并与克隆、数组去重与分组、函数柯里化与组合等JavaScript实用工具函数。"}, {"id": "coding-style-and-best-practices", "name": "编码规范与最佳实践", "file": "JavaScript/coding-style-and-best-practices.json", "description": "系统讲解命名规范、代码风格、注释与文档、代码可读性与可维护性等最佳实践，帮助提升代码质量和团队协作。"}, {"id": "debugging-skills", "name": "调试技巧", "file": "JavaScript/debugging-skills.json", "description": "系统讲解console.log与断点调试、浏览器调试工具、SourceMap、错误捕获与处理等调试技巧，帮助快速定位和解决问题。"}, {"id": "performance-optimization", "name": "性能优化", "file": "JavaScript/performance-optimization.json", "description": "系统讲解内存泄漏排查、代码分割与懒加载、资源压缩与缓存、DOM优化、事件优化、异步优化等前端性能优化技巧。"}, {"id": "package-management-and-build", "name": "包管理与构建", "file": "JavaScript/package-management-and-build.json", "description": "系统讲解npm/yarn/pnpm、package.json配置、模块打包（Webpack、Vite、Rollup）、Babel与转译、Lint与格式化等前端工程化与构建流程。"}, {"id": "unit-testing-and-automation", "name": "单元测试与自动化", "file": "JavaScript/unit-testing-and-automation.json", "description": "系统讲解Jest/Mocha基础、断言与Mock、覆盖率报告、持续集成等前端单元测试与自动化流程。"}, {"id": "modern-frontend-ecosystem", "name": "现代前端生态", "file": "JavaScript/modern-frontend-ecosystem.json", "description": "系统讲解TypeScript集成、主流前端框架、状态管理、路由与数据流、UI组件库、移动端与PWA等现代前端生态内容。"}]}