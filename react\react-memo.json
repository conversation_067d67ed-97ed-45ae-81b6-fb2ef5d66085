{"name": "React.memo", "trans": ["组件记忆化"], "methods": [{"name": "Component Memoization", "trans": ["组件记忆化"], "usage": {"syntax": "const MemoComponent = React.memo(Component);", "description": "使用React.memo高阶组件对函数组件进行记忆化，只有props变化时才重新渲染，提升性能。", "parameters": [{"name": "Component", "description": "需要记忆化的函数组件"}], "returnValue": "返回一个经过记忆化处理的组件。", "examples": [{"code": "// 组件记忆化示例\nimport React from 'react';\n\nfunction ListItem({ value }) {\n  console.log('渲染:', value);\n  return <li>{value}</li>;\n}\n// 使用React.memo包裹\nconst MemoListItem = React.memo(ListItem);\n\nfunction List({ items }) {\n  return (\n    <ul>\n      {items.map(item => <MemoListItem key={item} value={item} />)}\n    </ul>\n  );\n}", "explanation": "MemoListItem只有在props.value变化时才会重新渲染，避免不必要的渲染。"}]}}, {"name": "Custom Comparison Function", "trans": ["自定义比较函数"], "usage": {"syntax": "const MemoComponent = React.memo(Component, areEqual);", "description": "通过传入自定义比较函数areEqual，决定props变化时是否需要重新渲染，适用于复杂props。", "parameters": [{"name": "Component", "description": "需要记忆化的函数组件"}, {"name": "areEqual", "description": "自定义比较函数，返回true则跳过渲染，false则重新渲染"}], "returnValue": "返回一个经过自定义比较的记忆化组件。", "examples": [{"code": "// 自定义比较函数示例\nimport React from 'react';\n\nfunction UserCard({ user }) {\n  console.log('渲染:', user.name);\n  return <div>{user.name}</div>;\n}\n// 只在user.id变化时才重新渲染\nconst areEqual = (prevProps, nextProps) => prevProps.user.id === nextProps.user.id;\nconst MemoUserCard = React.memo(UserCard, areEqual);\n\nfunction UserList({ users }) {\n  return users.map(u => <MemoUserCard key={u.id} user={u} />);\n}", "explanation": "通过自定义areEqual函数，只有user.id变化时才会重新渲染MemoUserCard。"}]}}, {"name": "Applicable Scenarios", "trans": ["适用场景"], "usage": {"syntax": "// 适用场景：\n// 1. 组件渲染开销大\n// 2. 组件接收的props不经常变化\n// 3. 父组件频繁渲染但子组件数据稳定", "description": "React.memo适用于渲染开销较大、props变化不频繁、父组件频繁渲染但子组件数据稳定的场景。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 适用场景示例\n// 1. 大型表格、列表项组件\n// 2. 复杂UI子组件\n// 3. 受控表单项\n// 4. 图表、地图等高渲染成本组件", "explanation": "这些场景下使用React.memo可显著减少不必要的渲染，提高性能。"}]}}, {"name": "Comparison with shouldComponentUpdate", "trans": ["与shouldComponentUpdate对比"], "usage": {"syntax": "// 类组件：shouldComponentUpdate\nclass MyComponent extends React.PureComponent {\n  shouldComponentUpdate(nextProps, nextState) {\n    // 自定义比较逻辑\n  }\n}\n// 函数组件：React.memo\nconst MemoComponent = React.memo(Component, areEqual);", "description": "类组件通过shouldComponentUpdate或PureComponent实现渲染优化，函数组件通过React.memo和自定义比较函数实现。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 对比示例\n// 类组件优化\nclass UserCard extends React.PureComponent {\n  shouldComponentUpdate(nextProps) {\n    return nextProps.user.id !== this.props.user.id;\n  }\n  render() {\n    return <div>{this.props.user.name}</div>;\n  }\n}\n// 函数组件优化\nconst MemoUserCard = React.memo(\n  function UserCard({ user }) {\n    return <div>{user.name}</div>;\n  },\n  (prev, next) => prev.user.id === next.user.id\n);", "explanation": "类组件用shouldComponentUpdate，函数组件用React.memo和自定义比较函数实现渲染优化。"}]}}, {"name": "Optimization Strategies", "trans": ["优化策略"], "usage": {"syntax": "// 优化策略：\n// 1. 只对渲染开销大的组件使用React.memo\n// 2. 配合useCallback、useMemo优化props\n// 3. 合理设计props结构，避免频繁变化", "description": "合理使用React.memo、useCallback、useMemo等手段，结合props结构优化，避免无效渲染。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 优化策略示例\n// 1. 用useCallback包裹事件处理函数\nconst handleClick = useCallback(() => { /* ... */ }, []);\n// 2. 用useMemo缓存复杂数据\nconst data = useMemo(() => computeData(), [deps]);\n// 3. 只对高渲染成本组件使用React.memo", "explanation": "配合useCallback、useMemo和合理props设计，最大化React.memo的优化效果。"}]}}, {"name": "Over-optimization Issues", "trans": ["过度优化问题"], "usage": {"syntax": "// 过度优化问题：\n// 1. 频繁包裹无意义的小组件\n// 2. props频繁变化导致无效记忆化\n// 3. 维护成本增加", "description": "过度使用React.memo可能导致维护复杂度提升，props频繁变化时无优化效果，需权衡使用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 过度优化示例\n// 1. 对所有小组件都用React.memo，实际无性能提升\n// 2. 父组件每次渲染都生成新props，导致子组件频繁重渲染", "explanation": "不合理使用React.memo会增加代码复杂度，未必带来性能提升。"}]}}, {"name": "作业：理解与应用React.memo", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 用React.memo优化一个列表渲染组件\n// 2. 实现自定义比较函数\n// 3. 对比优化前后的渲染次数\n// 4. 分析过度优化的风险", "description": "通过实践React.memo的使用、比较函数实现和优化效果分析，掌握组件记忆化的原理和边界。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 创建普通列表组件和React.memo优化版\n// 2. 实现自定义areEqual函数\n// 3. 用console.log对比渲染次数\n// 4. 总结过度优化的风险", "explanation": "作业提示，学生需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nimport React, { useState } from 'react';\nfunction ListItem({ value }) {\n  console.log('渲染:', value);\n  return <li>{value}</li>;\n}\nconst MemoListItem = React.memo(ListItem);\nfunction App() {\n  const [count, setCount] = useState(0);\n  const items = ['A', 'B', 'C'];\n  return (\n    <div>\n      <button onClick={() => setCount(c => c + 1)}>增加</button>\n      <ul>\n        {items.map(item => <MemoListItem key={item} value={item} />)}\n      </ul>\n      <p>点击按钮只会重新渲染App，不会重新渲染MemoListItem</p>\n    </div>\n  );\n}\nexport default App;", "explanation": "完整实现了React.memo优化列表渲染、对比渲染次数和分析优化边界。"}]}}]}