{"name": "Controlled vs Uncontrolled Components", "trans": ["受控与非受控组件"], "methods": [{"name": "Controlled Component Principle", "trans": ["受控组件原则"], "usage": {"syntax": "<input value={value} onChange={e => setValue(e.target.value)} />", "description": "受控组件是指其值由React状态（state）控制，所有数据流和变更都通过props和事件处理函数传递，保证数据源唯一。", "parameters": [{"name": "value", "description": "输入框的值，由state控制"}, {"name": "onChange", "description": "值变化时的回调，更新state"}], "returnValue": "渲染受控的表单元素", "examples": [{"code": "function ControlledInput() {\n  const [value, setValue] = React.useState('');\n  return (\n    <input\n      value={value}\n      onChange={e => setValue(e.target.value)}\n      placeholder=\"请输入内容\"\n    />\n  );\n}", "explanation": "输入框的值完全由React状态控制，输入内容实时同步到state。"}]}}, {"name": "Uncontrolled Component Scenarios", "trans": ["非受控组件应用场景"], "usage": {"syntax": "<input defaultValue=\"初始值\" ref={inputRef} />", "description": "非受控组件由DOM自身管理其状态，React通过ref访问其值。适用于无需频繁同步、性能敏感或与第三方库集成的场景。", "parameters": [{"name": "defaultValue", "description": "初始值，仅用于首次渲染"}, {"name": "ref", "description": "用于访问DOM节点的引用"}], "returnValue": "渲染非受控的表单元素", "examples": [{"code": "function UncontrolledInput() {\n  const inputRef = React.useRef();\n  function handleClick() {\n    alert('当前值: ' + inputRef.current.value);\n  }\n  return (\n    <div>\n      <input defaultValue=\"默认值\" ref={inputRef} />\n      <button onClick={handleClick}>获取值</button>\n    </div>\n  );\n}", "explanation": "通过ref访问非受控输入框的当前值，适合简单或性能敏感场景。"}]}}, {"name": "Form Handling Patterns", "trans": ["表单处理模式"], "usage": {"syntax": "// 受控：value+onChange\n// 非受控：defaultValue+ref", "description": "表单处理可采用受控或非受控模式。受控模式适合需要校验、联动、受state驱动的场景。非受控模式适合简单、无需频繁同步的场景。", "parameters": [], "returnValue": "表单数据的管理方式", "examples": [{"code": "// 受控表单\nfunction ControlledForm() {\n  const [name, setName] = React.useState('');\n  return (\n    <form>\n      <input value={name} onChange={e => setName(e.target.value)} />\n    </form>\n  );\n}\n// 非受控表单\nfunction UncontrolledForm() {\n  const inputRef = React.useRef();\n  function handleSubmit(e) {\n    e.preventDefault();\n    alert('姓名: ' + inputRef.current.value);\n  }\n  return (\n    <form onSubmit={handleSubmit}>\n      <input ref={inputRef} defaultValue=\"张三\" />\n      <button type=\"submit\">提交</button>\n    </form>\n  );\n}", "explanation": "对比受控和非受控表单的实现方式。"}]}}, {"name": "Implementing Controlled Components", "trans": ["实现受控组件"], "usage": {"syntax": "<input value={value} onChange={handleChange} />", "description": "实现受控组件时，组件的值和变更完全由父组件或自身state管理，所有变更通过onChange事件驱动。", "parameters": [{"name": "value", "description": "受控值"}, {"name": "onChange", "description": "变更事件回调"}], "returnValue": "受控的表单组件", "examples": [{"code": "function MyInput({ value, onChange }) {\n  return <input value={value} onChange={onChange} />;\n}\n// 用法\nfunction Demo() {\n  const [val, setVal] = React.useState('');\n  return <MyInput value={val} onChange={e => setVal(e.target.value)} />;\n}", "explanation": "自定义受控输入组件，父组件完全控制其值。"}]}}, {"name": "Implementing Uncontrolled Components", "trans": ["实现非受控组件"], "usage": {"syntax": "<input defaultValue={defaultValue} ref={inputRef} />", "description": "实现非受控组件时，组件自身管理值，父组件通过ref访问其当前值。适合无需频繁同步的场景。", "parameters": [{"name": "defaultValue", "description": "初始值"}, {"name": "ref", "description": "访问DOM节点的引用"}], "returnValue": "非受控的表单组件", "examples": [{"code": "function MyUncontrolledInput({ defaultValue, inputRef }) {\n  return <input defaultValue={defaultValue} ref={inputRef} />;\n}\n// 用法\nfunction Demo() {\n  const ref = React.useRef();\n  function showValue() {\n    alert(ref.current.value);\n  }\n  return (\n    <div>\n      <MyUncontrolledInput defaultValue=\"hello\" inputRef={ref} />\n      <button onClick={showValue}>显示值</button>\n    </div>\n  );\n}", "explanation": "自定义非受控输入组件，通过ref访问其值。"}]}}, {"name": "Choosing the Right Pattern", "trans": ["选择合适的模式"], "usage": {"syntax": "// 依据需求选择受控或非受控模式", "description": "选择受控或非受控模式需根据业务需求、性能、可维护性等因素权衡。受控适合复杂交互和数据同步，非受控适合简单、性能敏感场景。", "parameters": [], "returnValue": "最佳实践建议", "examples": [{"code": "// 受控适合：需要校验、联动、受state驱动\n// 非受控适合：性能敏感、与第三方库集成、简单场景", "explanation": "总结两种模式的适用场景和选择建议。"}]}}, {"name": "作业：实现一个受控表单和一个非受控表单", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 实现一个受控表单，输入内容实时同步到state并显示。\n// 2. 实现一个非受控表单，通过ref获取输入值。\n// 3. 代码结构清晰，注释完整。", "description": "请实现上述两个表单，提交时请附上完整代码和注释。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 示例代码略，请学生自行实现\n// 提示：分别用useState和useRef实现。", "explanation": "作业要求学生掌握两种表单模式的实现。"}, {"code": "import React, { useState, useRef } from 'react';\n// 受控表单\nfunction ControlledForm() {\n  const [value, setValue] = useState('');\n  return (\n    <div>\n      <input value={value} onChange={e => setValue(e.target.value)} />\n      <div>当前值: {value}</div>\n    </div>\n  );\n}\n// 非受控表单\nfunction UncontrolledForm() {\n  const inputRef = useRef();\n  function handleShow() {\n    alert('当前值: ' + inputRef.current.value);\n  }\n  return (\n    <div>\n      <input defaultValue=\"默认值\" ref={inputRef} />\n      <button onClick={handleShow}>显示值</button>\n    </div>\n  );\n}", "explanation": "完整实现受控和非受控表单，分别用useState和useRef管理输入值。"}]}}]}