{"name": "Conditional Rendering", "trans": ["条件渲染"], "methods": [{"name": "If Statement Conditional Rendering", "trans": ["if语句条件渲染"], "usage": {"syntax": "function Component() {\n  if (condition) {\n    return <ComponentA />;\n  }\n  return <ComponentB />;\n}", "description": "使用JavaScript的if语句根据条件渲染不同的组件或元素。这是最直接的条件渲染方式，适用于条件逻辑较复杂的情况。", "parameters": [{"name": "condition", "description": "决定渲染内容的条件表达式，计算结果为布尔值"}, {"name": "ComponentA", "description": "当condition为真时渲染的组件或元素"}, {"name": "ComponentB", "description": "当condition为假时渲染的组件或元素"}], "returnValue": "根据条件返回不同的React元素或组件。如果条件为真，返回第一个组件；否则返回第二个组件。", "examples": [{"code": "// 基本if语句条件渲染\nimport React from 'react';\n\nfunction UserGreeting(props) {\n  const { isLoggedIn } = props;\n  \n  if (isLoggedIn) {\n    return <h1>Welcome back!</h1>;\n  }\n  return <h1>Please sign in.</h1>;\n}\n\n// 在函数组件中使用if语句\nfunction LoginControl() {\n  const [isLoggedIn, setIsLoggedIn] = React.useState(false);\n  \n  function handleLoginClick() {\n    setIsLoggedIn(true);\n  }\n  \n  function handleLogoutClick() {\n    setIsLoggedIn(false);\n  }\n  \n  let button;\n  if (isLoggedIn) {\n    button = <button onClick={handleLogoutClick}>Logout</button>;\n  } else {\n    button = <button onClick={handleLoginClick}>Login</button>;\n  }\n  \n  return (\n    <div>\n      <UserGreeting isLoggedIn={isLoggedIn} />\n      {button}\n    </div>\n  );\n}\n\n// 在类组件中使用if语句\nclass ConditionalRenderingExample extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      isDataLoaded: false,\n      data: null,\n      error: null\n    };\n  }\n  \n  componentDidMount() {\n    // 模拟数据加载\n    setTimeout(() => {\n      this.setState({\n        isDataLoaded: true,\n        data: ['Item 1', 'Item 2', 'Item 3']\n      });\n    }, 2000);\n  }\n  \n  render() {\n    const { isDataLoaded, data, error } = this.state;\n    \n    // 处理错误状态\n    if (error) {\n      return <div>Error: {error.message}</div>;\n    }\n    \n    // 处理加载状态\n    if (!isDataLoaded) {\n      return <div>Loading...</div>;\n    }\n    \n    // 处理空数据状态\n    if (data.length === 0) {\n      return <div>No items found.</div>;\n    }\n    \n    // 渲染数据\n    return (\n      <ul>\n        {data.map((item, index) => (\n          <li key={index}>{item}</li>\n        ))}\n      </ul>\n    );\n  }\n}\n\n// 使用if语句处理权限控制\nfunction AdminPanel() {\n  return <h2>Admin Panel</h2>;\n}\n\nfunction RegularUserPanel() {\n  return <h2>User Dashboard</h2>;\n}\n\nfunction Dashboard({ user }) {\n  if (user.role === 'admin') {\n    return <AdminPanel />;\n  }\n  if (user.role === 'moderator') {\n    return <ModeratorPanel />;\n  }\n  return <RegularUserPanel />;\n}", "explanation": "这些例子展示了如何使用if语句进行条件渲染。if语句允许基于条件返回完全不同的组件或元素。这种方法非常灵活，适用于复杂的条件逻辑，如处理多种状态（加载中、错误、空数据、正常数据）或基于用户角色显示不同内容等场景。if语句通常用在组件的顶层，直接控制整个组件的渲染输出。"}]}}, {"name": "Ternary Operator Conditional Rendering", "trans": ["三元表达式条件渲染"], "usage": {"syntax": "condition ? <ComponentA /> : <ComponentB />", "description": "使用JavaScript的三元运算符（?:）在JSX中内联条件渲染。这种方法简洁，适合在JSX中嵌入简单的条件逻辑。", "parameters": [{"name": "condition", "description": "决定渲染内容的条件表达式，计算结果为布尔值"}, {"name": "ComponentA", "description": "当condition为真时渲染的组件或元素（trueExpression）"}, {"name": "ComponentB", "description": "当condition为假时渲染的组件或元素（falseExpression）"}], "returnValue": "根据条件返回不同的React元素或组件。如果条件为真，返回第一个表达式的结果；否则返回第二个表达式的结果。", "examples": [{"code": "// 基本三元表达式条件渲染\nimport React from 'react';\n\nfunction Greeting({ isLoggedIn }) {\n  return (\n    <h1>\n      {isLoggedIn ? 'Welcome back!' : 'Please sign in'}\n    </h1>\n  );\n}\n\n// 在JSX中使用三元表达式渲染不同组件\nfunction LoginControl({ isLoggedIn, onLogin, onLogout }) {\n  return (\n    <div>\n      <Greeting isLoggedIn={isLoggedIn} />\n      {isLoggedIn\n        ? <button onClick={onLogout}>Logout</button>\n        : <button onClick={onLogin}>Login</button>\n      }\n    </div>\n  );\n}\n\n// 在JSX属性中使用三元表达式\nfunction Button({ isActive, onClick }) {\n  return (\n    <button\n      className={isActive ? 'active' : 'inactive'}\n      onClick={onClick}\n    >\n      {isActive ? 'Active' : 'Inactive'}\n    </button>\n  );\n}\n\n// 嵌套三元表达式（虽然可行，但可能降低可读性）\nfunction StatusIndicator({ status }) {\n  return (\n    <div className=\"status-indicator\">\n      {status === 'success'\n        ? <span className=\"success\">✓ Success</span>\n        : status === 'error'\n          ? <span className=\"error\">✗ Error</span>\n          : status === 'warning'\n            ? <span className=\"warning\">⚠ Warning</span>\n            : <span className=\"info\">ℹ Info</span>\n      }\n    </div>\n  );\n}\n\n// 三元表达式与逻辑运算符结合\nfunction UserProfile({ user, isEditing }) {\n  return (\n    <div>\n      <h2>{user.name}</h2>\n      {user.bio\n        ? <p>{user.bio}</p>\n        : <p>No bio available</p>\n      }\n      {isEditing && (\n        <button>Edit Profile</button>\n      )}\n    </div>\n  );\n}", "explanation": "这些例子展示了如何使用三元表达式（?:）进行条件渲染。三元表达式是一种简洁的内联条件语法，非常适合在JSX中嵌入简单的条件逻辑。它可以用于条件性地渲染文本、元素、组件，甚至可以用在JSX属性中。三元表达式的优点是简洁和内联，但对于复杂的条件逻辑，嵌套的三元表达式可能会降低代码可读性，这种情况下应考虑使用if语句或提取为单独的变量。"}]}}, {"name": "Logical AND (&&) Conditional Rendering", "trans": ["逻辑与(&&)条件渲染"], "usage": {"syntax": "condition && <Component />", "description": "使用JavaScript的逻辑与运算符（&&）进行条件渲染。当条件为真时渲染组件，为假时不渲染任何内容（或渲染为false值）。这种方法适合只需要在条件为真时渲染内容的情况。", "parameters": [{"name": "condition", "description": "决定是否渲染内容的条件表达式，计算结果为布尔值"}, {"name": "Component", "description": "当condition为真时渲染的组件或元素"}], "returnValue": "如果条件为真，返回指定的React元素或组件；如果条件为假，返回false（在React中不会渲染）。注意：当条件为0、空字符串等假值时，这些值会被返回而不是false。", "examples": [{"code": "// 基本逻辑与条件渲染\nimport React, { useState } from 'react';\n\nfunction Notifications({ messages }) {\n  return (\n    <div>\n      <h2>Notifications</h2>\n      {messages.length > 0 && (\n        <p>You have {messages.length} unread messages.</p>\n      )}\n    </div>\n  );\n}\n\n// 使用&&渲染可选元素\nfunction UserProfile({ user }) {\n  return (\n    <div>\n      <h2>{user.name}</h2>\n      {user.bio && <p>{user.bio}</p>}\n      {user.location && <p>Location: {user.location}</p>}\n      {user.website && (\n        <p>\n          Website: <a href={user.website}>{user.website}</a>\n        </p>\n      )}\n    </div>\n  );\n}\n\n// 在列表渲染中使用&&\nfunction TodoList({ todos, showCompleted }) {\n  return (\n    <ul>\n      {todos.map(todo => (\n        <li key={todo.id}>\n          {todo.text}\n          {todo.completed && showCompleted && (\n            <span className=\"completed-badge\"> ✓</span>\n          )}\n        </li>\n      ))}\n    </ul>\n  );\n}\n\n// 使用&&处理权限控制\nfunction AdminControls({ user }) {\n  return (\n    <div>\n      <h2>Dashboard</h2>\n      {user.isAdmin && (\n        <div className=\"admin-panel\">\n          <h3>Admin Controls</h3>\n          <button>Manage Users</button>\n          <button>System Settings</button>\n        </div>\n      )}\n      {/* 所有用户都能看到的内容 */}\n      <div className=\"user-content\">\n        <h3>Your Account</h3>\n        <p>Welcome, {user.name}</p>\n      </div>\n    </div>\n  );\n}\n\n// 注意：避免使用非布尔值作为&&左侧的操作数\nfunction BadExample({ count }) {\n  return (\n    <div>\n      {/* 当count为0时，会渲染0而不是什么都不渲染 */}\n      {count && <h1>Messages: {count}</h1>}\n    </div>\n  );\n}\n\n// 修正的版本\nfunction GoodExample({ count }) {\n  return (\n    <div>\n      {/* 使用count > 0确保条件是布尔值 */}\n      {count > 0 && <h1>Messages: {count}</h1>}\n    </div>\n  );\n}", "explanation": "这些例子展示了如何使用逻辑与运算符（&&）进行条件渲染。当左侧的条件为真时，&&运算符会返回右侧的表达式；当条件为假时，返回条件本身（通常是false，React不会渲染）。这种方法特别适合「条件为真时渲染内容，为假时不渲染任何内容」的场景。需要注意的是，当条件是非布尔值（如0、空字符串）时，这些值会被返回，可能导致意外渲染，因此最好确保&&左侧的条件是布尔表达式。"}]}}, {"name": "Variable-Based Conditional Rendering", "trans": ["变量控制条件渲染"], "usage": {"syntax": "let element;\nif (condition) {\n  element = <ComponentA />;\n} else {\n  element = <ComponentB />;\n}\nreturn element;", "description": "使用JavaScript变量存储条件渲染的结果，然后在JSX中引用该变量。这种方法将条件逻辑与渲染逻辑分离，使代码更清晰，特别适合复杂的条件渲染场景。", "parameters": [{"name": "variable", "description": "存储条件渲染结果的JavaScript变量"}, {"name": "<PERSON>L<PERSON><PERSON>", "description": "决定变量值的条件语句或表达式"}], "returnValue": "返回存储在变量中的React元素或组件。通过在组件函数的返回语句之前使用变量和条件逻辑，可以实现更复杂的条件渲染。", "examples": [{"code": "// 基本变量控制条件渲染\nimport React, { useState } from 'react';\n\nfunction LoginControl() {\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  \n  function handleLoginClick() {\n    setIsLoggedIn(true);\n  }\n  \n  function handleLogoutClick() {\n    setIsLoggedIn(false);\n  }\n  \n  // 使用变量存储要渲染的按钮\n  let button;\n  if (isLoggedIn) {\n    button = <button onClick={handleLogoutClick}>Logout</button>;\n  } else {\n    button = <button onClick={handleLoginClick}>Login</button>;\n  }\n  \n  return (\n    <div>\n      <h1>{isLoggedIn ? 'Welcome back!' : 'Please sign in'}</h1>\n      {button}\n    </div>\n  );\n}\n\n// 使用变量处理多个条件渲染元素\nfunction WeatherDisplay({ temperature, conditions }) {\n  let display;\n  let alertMessage;\n  \n  // 根据温度决定显示内容\n  if (temperature > 30) {\n    display = <div className=\"hot\">🔥 It's hot! ({temperature}°C)</div>;\n    alertMessage = <p className=\"warning\">Heat advisory: Stay hydrated!</p>;\n  } else if (temperature < 0) {\n    display = <div className=\"cold\">❄️ It's freezing! ({temperature}°C)</div>;\n    alertMessage = <p className=\"warning\">Frost advisory: Bundle up!</p>;\n  } else {\n    display = <div className=\"moderate\">🌤️ Weather is moderate ({temperature}°C)</div>;\n    alertMessage = null;\n  }\n  \n  // 根据天气状况添加额外信息\n  let conditionInfo;\n  switch (conditions) {\n    case 'rainy':\n      conditionInfo = <p>☔ Don't forget your umbrella!</p>;\n      break;\n    case 'windy':\n      conditionInfo = <p>💨 It's windy outside!</p>;\n      break;\n    case 'snowy':\n      conditionInfo = <p>❄️ Snow is falling!</p>;\n      break;\n    default:\n      conditionInfo = null;\n  }\n  \n  return (\n    <div className=\"weather-display\">\n      {display}\n      {alertMessage}\n      {conditionInfo}\n    </div>\n  );\n}\n\n// 在类组件中使用变量控制复杂UI渲染\nclass Dashboard extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      isLoading: true,\n      hasError: false,\n      user: null,\n      activeTab: 'overview'\n    };\n  }\n  \n  componentDidMount() {\n    // 模拟数据加载\n    setTimeout(() => {\n      this.setState({\n        isLoading: false,\n        user: { name: 'John Doe', role: 'admin' }\n      });\n    }, 1500);\n  }\n  \n  setActiveTab = (tab) => {\n    this.setState({ activeTab: tab });\n  }\n  \n  render() {\n    const { isLoading, hasError, user, activeTab } = this.state;\n    \n    // 处理加载和错误状态\n    let content;\n    if (isLoading) {\n      content = <div className=\"loader\">Loading...</div>;\n    } else if (hasError) {\n      content = <div className=\"error\">An error occurred. Please try again.</div>;\n    } else if (!user) {\n      content = <div className=\"no-user\">No user data available.</div>;\n    } else {\n      // 根据活动标签渲染不同内容\n      let tabContent;\n      switch (activeTab) {\n        case 'overview':\n          tabContent = <div className=\"tab-content\">Overview content here</div>;\n          break;\n        case 'profile':\n          tabContent = <div className=\"tab-content\">Profile content for {user.name}</div>;\n          break;\n        case 'settings':\n          tabContent = <div className=\"tab-content\">Settings panel</div>;\n          break;\n        default:\n          tabContent = <div className=\"tab-content\">Select a tab</div>;\n      }\n      \n      // 组合用户信息和标签内容\n      content = (\n        <div className=\"dashboard-content\">\n          <div className=\"user-info\">\n            <h2>Welcome, {user.name}</h2>\n            {user.role === 'admin' && <span className=\"badge\">Admin</span>}\n          </div>\n          \n          <div className=\"tabs\">\n            <button \n              className={activeTab === 'overview' ? 'active' : ''}\n              onClick={() => this.setActiveTab('overview')}\n            >\n              Overview\n            </button>\n            <button \n              className={activeTab === 'profile' ? 'active' : ''}\n              onClick={() => this.setActiveTab('profile')}\n            >\n              Profile\n            </button>\n            <button \n              className={activeTab === 'settings' ? 'active' : ''}\n              onClick={() => this.setActiveTab('settings')}\n            >\n              Settings\n            </button>\n          </div>\n          \n          {tabContent}\n        </div>\n      );\n    }\n    \n    return (\n      <div className=\"dashboard\">\n        <header>\n          <h1>Application Dashboard</h1>\n        </header>\n        {content}\n      </div>\n    );\n  }\n}", "explanation": "这些例子展示了如何使用变量控制条件渲染。通过将条件逻辑的结果存储在变量中，然后在JSX中引用这些变量，可以使代码更有组织性和可读性。这种方法特别适合处理复杂的条件渲染场景，如多个条件分支、嵌套条件、复杂UI组件等。变量控制条件渲染的主要优点是将条件逻辑与渲染逻辑分离，使代码结构更清晰，同时避免了在JSX中使用复杂的内联条件表达式。"}]}}, {"name": "Preventing Component Rendering", "trans": ["阻止组件渲染"], "usage": {"syntax": "function Component() {\n  if (condition) {\n    return null;  // 或 return false;\n  }\n  return <div>Content</div>;\n}", "description": "通过在组件的render方法中返回null或false来阻止组件渲染。这种技术允许组件决定是否渲染自身，即使它已经被父组件包含在JSX中。", "parameters": [{"name": "condition", "description": "决定是否阻止渲染的条件表达式"}, {"name": "returnValue", "description": "当不希望渲染任何内容时返回的值，可以是null或false"}], "returnValue": "当条件满足时返回null或false，React将不会渲染任何内容；否则返回正常的JSX内容。返回null和返回false在React中的效果相同，都不会渲染任何内容。", "examples": [{"code": "// 基本阻止组件渲染示例\nimport React from 'react';\n\nfunction WarningBanner({ warn }) {\n  if (!warn) {\n    return null;  // 当warn为false时不渲染任何内容\n  }\n  \n  return (\n    <div className=\"warning\">\n      Warning!\n    </div>\n  );\n}\n\nfunction Page() {\n  const [showWarning, setShowWarning] = React.useState(true);\n  \n  return (\n    <div>\n      <WarningBanner warn={showWarning} />\n      <button onClick={() => setShowWarning(!showWarning)}>\n        {showWarning ? 'Hide' : 'Show'} Warning\n      </button>\n    </div>\n  );\n}\n\n// 在类组件中阻止渲染\nclass ConditionalComponent extends React.Component {\n  render() {\n    if (this.props.isHidden) {\n      return null;  // 当isHidden为true时不渲染任何内容\n    }\n    \n    return (\n      <div>\n        <h2>{this.props.title}</h2>\n        <p>{this.props.content}</p>\n      </div>\n    );\n  }\n}\n\n// 使用阻止渲染实现权限控制\nfunction SecureContent({ user, requiredRole, children }) {\n  // 如果用户没有所需角色，不渲染内容\n  if (!user || user.role !== requiredRole) {\n    return null;\n  }\n  \n  return <div className=\"secure-content\">{children}</div>;\n}\n\nfunction AdminDashboard({ user }) {\n  return (\n    <div>\n      <h1>Dashboard</h1>\n      \n      {/* 只有管理员可以看到这部分内容 */}\n      <SecureContent user={user} requiredRole=\"admin\">\n        <div className=\"admin-controls\">\n          <h2>Admin Controls</h2>\n          <button>Manage Users</button>\n          <button>System Settings</button>\n        </div>\n      </SecureContent>\n      \n      {/* 所有用户都可以看到这部分内容 */}\n      <div className=\"user-content\">\n        <h2>User Content</h2>\n        <p>Welcome, {user.name}</p>\n      </div>\n    </div>\n  );\n}\n\n// 阻止渲染与生命周期方法\nclass FeatureToggle extends React.Component {\n  componentDidMount() {\n    console.log('FeatureToggle mounted');\n    // 即使组件不渲染内容，componentDidMount仍会被调用\n  }\n  \n  componentWillUnmount() {\n    console.log('FeatureToggle will unmount');\n    // 当父组件重新渲染且不再包含此组件时调用\n  }\n  \n  render() {\n    if (!this.props.isEnabled) {\n      return null;  // 功能被禁用时不渲染\n    }\n    \n    return (\n      <div className=\"feature\">\n        <h3>New Feature</h3>\n        <p>This is a new feature that is currently enabled.</p>\n      </div>\n    );\n  }\n}\n\n// 注意：返回null与不渲染组件的区别\nfunction ParentComponent({ showChild }) {\n  return (\n    <div>\n      <h1>Parent Component</h1>\n      \n      {/* 方法1：条件性地包含子组件 */}\n      {showChild && <ChildComponent />}\n      \n      {/* 方法2：始终包含子组件，但子组件可能返回null */}\n      <ConditionalChildComponent isVisible={showChild} />\n    </div>\n  );\n}\n\nfunction ChildComponent() {\n  console.log('ChildComponent rendered');\n  return <div>Child Component</div>;\n}\n\nfunction ConditionalChildComponent({ isVisible }) {\n  console.log('ConditionalChildComponent executed');  // 这行总是会执行\n  \n  if (!isVisible) {\n    return null;  // 不渲染任何内容，但组件函数仍被调用\n  }\n  \n  return <div>Conditional Child Component</div>;\n}", "explanation": "这些例子展示了如何通过返回null或false来阻止组件渲染。当组件的render方法返回null或false时，React不会渲染任何内容，但组件的生命周期方法仍然会被调用。这种技术在以下场景特别有用：\n\n1. 基于条件显示或隐藏警告、通知等元素\n2. 实现基于权限的内容访问控制\n3. 实现功能开关或A/B测试\n\n需要注意的是，返回null的组件仍然会被实例化和执行，这与条件性地不包含组件（如使用&&运算符）不同。如果性能是关键考虑因素，且组件可能包含复杂逻辑，可能更适合使用条件性地包含组件的方法。"}]}}, {"name": "Conditional Rendering Best Practices", "trans": ["条件渲染最佳实践"], "usage": {"syntax": "// 根据具体场景选择最合适的条件渲染方法", "description": "React条件渲染的最佳实践包括选择合适的条件渲染方法、保持代码可读性、避免常见陷阱，以及优化性能。根据具体场景和复杂度选择最合适的条件渲染技术。", "parameters": [{"name": "readability", "description": "选择能够保持代码清晰易读的条件渲染方法"}, {"name": "complexity", "description": "根据条件逻辑的复杂程度选择适当的渲染方法"}, {"name": "performance", "description": "考虑条件渲染方法对性能的影响"}], "returnValue": "使用最佳实践的条件渲染代码应该是清晰、高效且易于维护的。不同的条件渲染方法适用于不同的场景，没有一种方法适用于所有情况。", "examples": [{"code": "// 条件渲染最佳实践示例\nimport React, { useState } from 'react';\n\n// 1. 为简单条件选择合适的方法\nfunction SimpleConditionExample({ isLoggedIn, count, hasPermission }) {\n  return (\n    <div>\n      {/* 对于二选一渲染，使用三元表达式 */}\n      <div>{isLoggedIn ? <UserGreeting /> : <GuestGreeting />}</div>\n      \n      {/* 对于条件性显示元素，使用&& */}\n      <div>{count > 0 && <NotificationBadge count={count} />}</div>\n      \n      {/* 对于否定条件，使用! */}\n      <div>{!hasPermission && <RestrictedBanner />}</div>\n    </div>\n  );\n}\n\n// 2. 对于复杂条件，使用变量或提取组件\nfunction ComplexConditionExample({ status, user, data }) {\n  // 使用变量存储复杂条件的结果\n  let statusDisplay;\n  if (status === 'loading') {\n    statusDisplay = <LoadingSpinner />;\n  } else if (status === 'error') {\n    statusDisplay = <ErrorMessage />;\n  } else if (status === 'empty') {\n    statusDisplay = <EmptyState />;\n  } else {\n    statusDisplay = <DataDisplay data={data} />;\n  }\n  \n  // 提取复杂条件逻辑到专用组件\n  return (\n    <div>\n      {statusDisplay}\n      <UserPermissionContent user={user} />\n    </div>\n  );\n}\n\n// 提取的条件渲染组件\nfunction UserPermissionContent({ user }) {\n  if (!user) {\n    return <LoginPrompt />;\n  }\n  \n  if (user.role === 'admin') {\n    return <AdminPanel />;\n  } else if (user.role === 'moderator') {\n    return <ModeratorPanel />;\n  } else {\n    return <UserPanel />;\n  }\n}\n\n// 3. 避免常见陷阱\nfunction AvoidPitfallsExample({ count, user, items }) {\n  return (\n    <div>\n      {/* 陷阱1: 使用非布尔值作为&&左侧的操作数 */}\n      {/* 错误方式：当count为0时会渲染0 */}\n      <div>{count && <span>Count: {count}</span>}</div>\n      \n      {/* 正确方式：确保左侧是布尔表达式 */}\n      <div>{count > 0 && <span>Count: {count}</span>}</div>\n      \n      {/* 陷阱2: 过度嵌套的三元表达式 */}\n      {/* 难以阅读的嵌套三元表达式 */}\n      <div>\n        {user ? (user.isVerified ? <VerifiedUser /> : <UnverifiedUser />) : <GuestUser />}\n      </div>\n      \n      {/* 更易读的变量方式 */}\n      {(() => {\n        if (!user) return <GuestUser />;\n        return user.isVerified ? <VerifiedUser /> : <UnverifiedUser />;\n      })()}\n      \n      {/* 陷阱3: 在循环中的条件渲染 */}\n      {/* 不推荐：在map中使用条件会导致索引不匹配问题 */}\n      <ul>\n        {items.map((item, index) => (\n          item.isVisible && <li key={index}>{item.name}</li>\n        ))}\n      </ul>\n      \n      {/* 推荐：先过滤，再渲染 */}\n      <ul>\n        {items\n          .filter(item => item.isVisible)\n          .map((item, index) => (\n            <li key={item.id || index}>{item.name}</li>\n          ))\n        }\n      </ul>\n    </div>\n  );\n}\n\n// 4. 性能优化\nfunction PerformanceOptimizationExample({ showDetails, expensiveData }) {\n  // 使用React.memo避免不必要的重新渲染\n  const MemoizedDetails = React.memo(ExpensiveDetails);\n  \n  return (\n    <div>\n      <h1>Product Information</h1>\n      \n      {/* 方法1：条件性地不包含组件（更好的性能） */}\n      {showDetails && <MemoizedDetails data={expensiveData} />}\n      \n      {/* 方法2：组件始终被实例化，但可能返回null */}\n      <AlwaysInstantiatedComponent isVisible={showDetails} data={expensiveData} />\n    </div>\n  );\n}\n\n// 这个组件即使不可见也会被实例化和执行\nfunction AlwaysInstantiatedComponent({ isVisible, data }) {\n  // 即使isVisible为false，这里的代码也会执行\n  const processedData = processExpensiveData(data);  // 假设这是昂贵的计算\n  \n  if (!isVisible) {\n    return null;\n  }\n  \n  return <div>{/* 使用processedData渲染内容 */}</div>;\n}\n\n// 5. 使用React.lazy和Suspense进行条件加载\nconst LazyLoadedComponent = React.lazy(() => import('./LazyLoadedComponent'));\n\nfunction LazyLoadingExample({ shouldLoadComponent }) {\n  return (\n    <div>\n      <h1>Main Content</h1>\n      \n      {shouldLoadComponent && (\n        <React.Suspense fallback={<div>Loading...</div>}>\n          <LazyLoadedComponent />\n        </React.Suspense>\n      )}\n    </div>\n  );\n}", "explanation": "这些例子展示了React条件渲染的最佳实践：\n\n1. **选择合适的条件渲染方法**：\n   - 对于简单的二选一渲染，使用三元表达式\n   - 对于条件性显示元素，使用&&运算符\n   - 对于复杂条件，使用变量或提取专用组件\n\n2. **保持代码可读性**：\n   - 避免过度嵌套的条件表达式\n   - 为复杂条件逻辑提取变量或组件\n   - 使用有意义的命名使条件逻辑更清晰\n\n3. **避免常见陷阱**：\n   - 确保&&左侧是布尔表达式，避免使用可能为0或空字符串的值\n   - 在列表渲染中，先过滤再渲染，而不是在map中使用条件\n   - 注意条件渲染对组件生命周期的影响\n\n4. **性能优化**：\n   - 对于昂贵的组件，优先使用条件性不包含组件的方式\n   - 使用React.memo避免不必要的重新渲染\n   - 考虑使用React.lazy和Suspense进行条件加载\n\n选择最佳的条件渲染方法应该基于具体场景、代码复杂度和性能要求。没有一种方法适用于所有情况，关键是保持代码清晰、高效且易于维护。"}]}}]}