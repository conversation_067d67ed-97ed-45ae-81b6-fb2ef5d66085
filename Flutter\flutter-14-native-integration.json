{"name": "Native Integration", "trans": ["原生集成"], "methods": [{"name": "Plugin Development and Usage", "trans": ["插件开发与使用"], "usage": {"syntax": "flutter pub add <plugin>\nimport 'package:plugin_name/plugin_name.dart'", "description": "通过pub添加第三方插件，或自定义开发插件，扩展Flutter的原生能力。", "parameters": [{"name": "plugin", "description": "插件包名，如camera、shared_preferences等"}, {"name": "import", "description": "导入插件包的Dart语句"}], "returnValue": "插件功能的Dart API接口。", "examples": [{"code": "// 使用camera插件拍照示例\n// 1. 在pubspec.yaml添加依赖：camera\n// 2. 导入包\nimport 'package:camera/camera.dart';\n\n// 3. 获取可用摄像头并初始化\nList<CameraDescription> cameras = await availableCameras();\nCameraController controller = CameraController(cameras[0], ResolutionPreset.high);\nawait controller.initialize();\n// 4. 拍照\nXFile file = await controller.takePicture();\n", "explanation": "演示如何添加并使用camera插件实现拍照功能。"}]}}, {"name": "Platform Channel Communication", "trans": ["与原生代码通信（Platform Channel）"], "usage": {"syntax": "MethodChannel('channel_name').invokeMethod('method', args)", "description": "通过MethodChannel实现Flutter与原生(Android/iOS)代码的双向通信，调用原生功能或响应原生事件。", "parameters": [{"name": "channel_name", "description": "通道名称，需与原生端一致"}, {"name": "method", "description": "调用的方法名"}, {"name": "args", "description": "传递的参数，可选"}], "returnValue": "Future，返回原生方法的执行结果。", "examples": [{"code": "// Flutter端调用原生方法示例\nimport 'package:flutter/services.dart';\n\nconst platform = MethodChannel('samples.flutter.dev/battery');\nFuture<void> getBatteryLevel() async {\n  try {\n    final int result = await platform.invokeMethod('getBatteryLevel');\n    print('电量: $result%');\n  } on PlatformException catch (e) {\n    print('获取失败: \\${e.message}');\n  }\n}\n", "explanation": "演示Flutter端通过MethodChannel调用原生电量获取方法。"}]}}, {"name": "Device Features Access", "trans": ["调用相机、定位、传感器等"], "usage": {"syntax": "使用相关插件如camera、geolocator、sensors等", "description": "通过插件调用设备硬件能力，如相机拍照、定位获取、传感器数据读取等。", "parameters": [{"name": "camera/geolocator/sensors", "description": "对应功能的插件包名"}], "returnValue": "插件API返回的功能数据。", "examples": [{"code": "// 获取定位示例\nimport 'package:geolocator/geolocator.dart';\n\nFuture<Position> getPosition() async {\n  bool serviceEnabled = await Geolocator.isLocationServiceEnabled();\n  if (!serviceEnabled) {\n    return Future.error('定位服务未开启');\n  }\n  LocationPermission permission = await Geolocator.checkPermission();\n  if (permission == LocationPermission.denied) {\n    permission = await Geolocator.requestPermission();\n    if (permission == LocationPermission.denied) {\n      return Future.error('定位权限被拒绝');\n    }\n  }\n  return await Geolocator.getCurrentPosition();\n}\n", "explanation": "演示如何通过geolocator插件获取当前定位。"}]}}, {"name": "Push Notification", "trans": ["推送与通知"], "usage": {"syntax": "使用firebase_messaging、flutter_local_notifications等插件", "description": "通过推送插件实现消息推送与本地通知功能。", "parameters": [{"name": "firebase_messaging/flutter_local_notifications", "description": "推送与通知相关插件"}], "returnValue": "推送消息或本地通知的回调。", "examples": [{"code": "// 本地通知示例\nimport 'package:flutter_local_notifications/flutter_local_notifications.dart';\n\nfinal FlutterLocalNotificationsPlugin notificationsPlugin = FlutterLocalNotificationsPlugin();\n\nFuture<void> showNotification() async {\n  const AndroidNotificationDetails androidDetails = AndroidNotificationDetails('channelId', 'channelName', importance: Importance.max, priority: Priority.high);\n  const NotificationDetails details = NotificationDetails(android: androidDetails);\n  await notificationsPlugin.show(0, '标题', '内容', details);\n}\n", "explanation": "演示如何通过flutter_local_notifications插件发送本地通知。"}]}}, {"name": "Assignment", "trans": ["作业：实现一个调用原生能力的Flutter页面。"], "usage": {"syntax": "无", "description": "请实现一个页面，要求调用相机拍照或获取定位，并通过本地通知展示结果。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 作业参考实现（伪代码，需配置权限和插件）\nimport 'package:camera/camera.dart';\nimport 'package:geolocator/geolocator.dart';\nimport 'package:flutter_local_notifications/flutter_local_notifications.dart';\n\n// 省略初始化和权限配置\nFuture<void> takePhotoAndNotify() async {\n  // 拍照\n  List<CameraDescription> cameras = await availableCameras();\n  CameraController controller = CameraController(cameras[0], ResolutionPreset.high);\n  await controller.initialize();\n  XFile file = await controller.takePicture();\n  // 获取定位\n  Position pos = await Geolocator.getCurrentPosition();\n  // 通知\n  final plugin = FlutterLocalNotificationsPlugin();\n  await plugin.show(0, '拍照+定位', '图片路径: \\${file.path}, 坐标: \\${pos.latitude},\\${pos.longitude}', NotificationDetails(android: AndroidNotificationDetails('id','name')));\n}\n", "explanation": "作业要求调用相机和定位，并通过本地通知展示结果。"}]}}]}