{"name": "useContext", "trans": ["上下文钩子"], "methods": [{"name": "Context Creation", "trans": ["Context创建"], "usage": {"syntax": "const MyContext = React.createContext(defaultValue);", "description": "使用React.createContext可以创建一个上下文对象。上下文用于在组件树中传递数据，无需逐层传递props。defaultValue为默认值，仅在组件未被Provider包裹时生效。", "parameters": [{"name": "defaultValue", "description": "上下文的默认值"}], "returnValue": "返回一个包含Provider和Consumer的Context对象", "examples": [{"code": "// Context创建示例\nimport React from 'react';\n\n// 创建一个主题上下文，默认值为'light'\nconst ThemeContext = React.createContext('light');\n\nfunction App() {\n  return (\n    <ThemeContext.Provider value=\"dark\">\n      <Toolbar />\n    </ThemeContext.Provider>\n  );\n}\n\nfunction Toolbar() {\n  return <ThemedButton />;\n}\n\nfunction ThemedButton() {\n  return (\n    <ThemeContext.Consumer>\n      {theme => <button className={theme}>按钮</button>}\n    </ThemeContext.Consumer>\n  );\n}", "explanation": "本例展示了如何创建Context对象，并通过Provider和Consumer在组件树中传递和消费上下文数据。"}]}}, {"name": "Provider", "trans": ["提供者(Provider)"], "usage": {"syntax": "<MyContext.Provider value={contextValue}>\n  <Child />\n</MyContext.Provider>", "description": "Provider组件用于为其子组件树提供上下文值。所有被Provider包裹的后代组件都可以访问到value属性传递的上下文数据。可以嵌套多个Provider实现多层级或动态切换上下文。", "parameters": [{"name": "value", "description": "要传递给子组件的上下文数据"}], "returnValue": "无返回值，Provider通过React上下文机制传递数据", "examples": [{"code": "// Provider用法示例\nimport React from 'react';\nconst UserContext = React.createContext(null);\n\nfunction App() {\n  const user = { name: 'Alice', role: 'admin' };\n  return (\n    <UserContext.Provider value={user}>\n      <UserProfile />\n    </UserContext.Provider>\n  );\n}\n\nfunction UserProfile() {\n  return (\n    <UserContext.Consumer>\n      {user => user ? <div>用户：{user.name}</div> : <div>未登录</div>}\n    </UserContext.Consumer>\n  );\n}", "explanation": "本例展示了如何用Provider为组件树提供上下文数据，所有后代组件都可以访问到value属性传递的user对象。"}]}}, {"name": "Consumer", "trans": ["消费者(Consumer)"], "usage": {"syntax": "<MyContext.Consumer>\n  {value => /* 渲染内容 */}\n</MyContext.Consumer>", "description": "Consumer组件用于在类组件或不方便使用Hook的地方消费上下文数据。通过函数作为子元素（render props）获取当前上下文值。", "parameters": [{"name": "value", "description": "Provider传递的上下文数据"}], "returnValue": "无返回值，Consumer通过函数子元素传递上下文值", "examples": [{"code": "// Consumer用法示例\nimport React from 'react';\nconst ThemeContext = React.createContext('light');\n\nfunction ThemedButton() {\n  return (\n    <ThemeContext.Consumer>\n      {theme => <button className={theme}>按钮</button>}\n    </ThemeContext.Consumer>\n  );\n}\n\n// 在类组件中使用Consumer\nclass UserProfile extends React.Component {\n  render() {\n    return (\n      <UserContext.Consumer>\n        {user => user ? <div>用户：{user.name}</div> : <div>未登录</div>}\n      </UserContext.Consumer>\n    );\n  }\n}", "explanation": "本例展示了如何在函数组件和类组件中使用Consumer消费上下文数据。"}]}}, {"name": "Cross-Component State Sharing", "trans": ["跨组件状态共享"], "usage": {"syntax": "// 在顶层组件创建Context和状态\nconst MyContext = React.createContext();\nfunction App() {\n  const [value, setValue] = useState('A');\n  return (\n    <MyContext.Provider value={{ value, setValue }}>\n      <Child1 />\n      <Child2 />\n    </MyContext.Provider>\n  );\n}\n// 在任意子组件中消费Context\nconst { value, setValue } = useContext(MyContext);", "description": "通过Context可以在多个不直接相关的组件间共享状态和方法，避免繁琐的props传递。常用于全局主题、用户信息、语言切换等场景。", "parameters": [{"name": "value", "description": "要共享的状态数据"}, {"name": "setValue", "description": "用于更新共享状态的方法"}], "returnValue": "无返回值，状态和方法通过Context传递给所有后代组件", "examples": [{"code": "// 跨组件状态共享示例\nimport React, { useState, useContext } from 'react';\nconst LangContext = React.createContext();\n\nfunction App() {\n  const [lang, setLang] = useState('zh');\n  return (\n    <LangContext.Provider value={{ lang, setLang }}>\n      <Header />\n      <Content />\n    </LangContext.Provider>\n  );\n}\nfunction Header() {\n  const { lang, setLang } = useContext(LangContext);\n  return (\n    <div>\n      当前语言：{lang}\n      <button onClick={() => setLang('en')}>切换英文</button>\n      <button onClick={() => setLang('zh')}>切换中文</button>\n    </div>\n  );\n}\nfunction Content() {\n  const { lang } = useContext(LangContext);\n  return <div>{lang === 'zh' ? '你好' : 'Hello'}</div>;\n}", "explanation": "本例展示了如何用Context实现跨组件的状态和方法共享，Header和Content组件都能访问和修改全局语言状态。"}]}}, {"name": "Performance Considerations", "trans": ["性能考虑"], "usage": {"syntax": "<MyContext.Provider value={value}>...</MyContext.Provider>", "description": "Context更新会导致所有消费该Context的组件重新渲染。为避免不必要的渲染，应避免在Provider的value属性中直接传递每次渲染都会变化的新对象或函数。可以用useMemo、useCallback优化，或拆分Context粒度。", "parameters": [{"name": "value", "description": "Provider传递的上下文值，建议用useMemo缓存"}], "returnValue": "无返回值，优化后可减少不必要的组件渲染", "examples": [{"code": "// 性能优化示例\nimport React, { useState, useMemo, useContext } from 'react';\nconst CountContext = React.createContext();\n\nfunction App() {\n  const [count, setCount] = useState(0);\n  // 用useMemo缓存value，避免每次渲染都创建新对象\n  const value = useMemo(() => ({ count, setCount }), [count]);\n  return (\n    <CountContext.Provider value={value}>\n      <Counter />\n    </CountContext.Provider>\n  );\n}\nfunction Counter() {\n  const { count, setCount } = useContext(CountContext);\n  return <button onClick={() => setCount(count + 1)}>计数：{count}</button>;\n}", "explanation": "本例展示了如何用useMemo优化Provider的value，避免Context更新导致所有消费组件不必要的重新渲染。"}]}}, {"name": "Combining with Other Hooks", "trans": ["与其他Hooks组合"], "usage": {"syntax": "const value = useMemo(() => ..., [deps]);\nconst context = useContext(MyContext);", "description": "useContext常与useState、useReducer、useMemo等Hooks结合，实现全局状态管理、性能优化和复杂逻辑封装。可以用useReducer管理复杂状态，再通过Context分发到各组件。", "parameters": [{"name": "useContext", "description": "消费上下文数据"}, {"name": "useState/useReducer", "description": "管理全局或共享状态"}, {"name": "useMemo/useCallback", "description": "优化Provider的value或回调函数，减少不必要渲染"}], "returnValue": "无返回值，Hooks组合提升状态管理能力和性能", "examples": [{"code": "// useContext与useReducer结合\nimport React, { useReducer, useContext, createContext } from 'react';\nconst CountContext = createContext();\nfunction reducer(state, action) {\n  switch(action.type) {\n    case 'inc': return state + 1;\n    case 'dec': return state - 1;\n    default: return state;\n  }\n}\nfunction App() {\n  const [count, dispatch] = useReducer(reducer, 0);\n  return (\n    <CountContext.Provider value={{ count, dispatch }}>\n      <Counter />\n    </CountContext.Provider>\n  );\n}\nfunction Counter() {\n  const { count, dispatch } = useContext(CountContext);\n  return (\n    <div>\n      <button onClick={() => dispatch({type:'dec'})}>-</button>\n      <span>{count}</span>\n      <button onClick={() => dispatch({type:'inc'})}>+</button>\n    </div>\n  );\n}", "explanation": "本例展示了如何用useReducer+useContext实现全局计数器，Hooks组合让状态管理更灵活高效。"}]}}, {"name": "Assignment: useContext练习", "trans": ["作业：useContext练习"], "usage": {"syntax": "// 作业要求见description", "description": "1. 创建一个ThemeContext，实现全局主题切换（如light/dark）。\n2. 创建一个UserContext，实现用户登录信息的全局共享。\n3. 用useContext和useReducer实现一个全局计数器。\n4. （进阶）优化Provider的value，避免不必要的渲染。\n5. 总结Context的性能陷阱和最佳实践。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 示例：useContext综合练习\nimport React, { useState, useContext, useMemo, createContext } from 'react';\nconst ThemeContext = createContext('light');\nfunction ThemeSwitcher() {\n  const [theme, setTheme] = useState('light');\n  const value = useMemo(() => theme, [theme]);\n  return (\n    <ThemeContext.Provider value={value}>\n      <Toolbar />\n      <button onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}>切换主题</button>\n    </ThemeContext.Provider>\n  );\n}\nfunction Toolbar() {\n  const theme = useContext(ThemeContext);\n  return <div>当前主题：{theme}</div>;\n}\n// 作业：\n// 1. 尝试实现UserContext和全局计数器\n// 2. 尝试复现性能陷阱并用useMemo优化", "explanation": "本作业要求你综合运用Context的创建、Provider、useContext、性能优化等知识点。通过动手实践，加深对全局状态管理和Context机制的理解。"}]}}]}