{"name": "Package Management", "trans": ["包管理"], "methods": [{"name": "Package Structure and __init__.py", "trans": ["包的结构与__init__.py"], "usage": {"syntax": "mypkg/\n    __init__.py\n    module1.py\n    module2.py", "description": "包是包含__init__.py文件的文件夹，可包含多个模块。__init__.py用于包初始化。", "parameters": [{"name": "__init__.py", "description": "包初始化文件，必须存在"}], "returnValue": "包对象", "examples": [{"code": "# 目录结构\nmypkg/\n    __init__.py\n    mod.py\n# main.py\nimport mypkg.mod", "explanation": "演示了包的结构和导入。"}]}}, {"name": "Relative and Absolute Import", "trans": ["相对与绝对导入"], "usage": {"syntax": "from . import module\nfrom ..subpackage import module\nimport 包名.模块名", "description": "绝对导入从项目根目录开始，推荐使用。相对导入以.或..开头，仅限包内模块间。", "parameters": [{"name": "模块名", "description": "要导入的模块"}], "returnValue": "模块对象", "examples": [{"code": "# 在mypkg/sub.py中\nfrom . import mod\nfrom .. import othermod", "explanation": "演示了相对导入和绝对导入的用法。"}]}}, {"name": "Virtual Environment (venv)", "trans": ["虚拟环境venv"], "usage": {"syntax": "python -m venv venv\nsource venv/bin/activate # Linux/macOS\nvenv\\Scripts\\activate # Windows\ndeactivate", "description": "venv用于创建独立的Python虚拟环境，隔离依赖。激活后pip操作只影响当前环境。", "parameters": [{"name": "venv", "description": "虚拟环境目录名"}], "returnValue": "无返回值", "examples": [{"code": "python -m venv venv\nsource venv/bin/activate\npip install requests\ndeactivate", "explanation": "演示了虚拟环境的创建、激活、使用和退出。"}]}}, {"name": "pip Package Manager", "trans": ["pip包管理工具"], "usage": {"syntax": "pip install 包名\npip uninstall 包名\npip list\npip freeze > requirements.txt", "description": "pip是Python官方包管理工具，用于安装、卸载、列出和导出依赖。", "parameters": [{"name": "包名", "description": "要安装或卸载的第三方库名"}], "returnValue": "无返回值", "examples": [{"code": "pip install numpy\npip uninstall requests\npip list\npip freeze > requirements.txt", "explanation": "演示了pip的常用命令。"}]}}, {"name": "requirements.txt", "trans": ["requirements.txt"], "usage": {"syntax": "pip install -r requirements.txt", "description": "requirements.txt用于记录项目依赖，便于批量安装和环境复现。", "parameters": [{"name": "requirements.txt", "description": "依赖文件名"}], "returnValue": "无返回值", "examples": [{"code": "# requirements.txt\nrequests==2.25.1\nnumpy>=1.19.0\n# 安装依赖\npip install -r requirements.txt", "explanation": "演示了依赖文件的编写和批量安装。"}]}}, {"name": "Publish and Install Custom Package", "trans": ["发布与安装自定义包"], "usage": {"syntax": "python setup.py sdist bdist_wheel\ntwine upload dist/*\npip install 包名", "description": "可通过setup.py和twine将自定义包发布到PyPI，其他用户可用pip安装。", "parameters": [{"name": "setup.py", "description": "包的构建配置文件"}], "returnValue": "无返回值", "examples": [{"code": "python setup.py sdist bdist_wheel\ntwine upload dist/*\npip install yourpkg", "explanation": "演示了自定义包的发布和安装流程。"}]}}, {"name": "Package Management Assignment", "trans": ["包管理练习"], "usage": {"syntax": "# 包管理练习", "description": "完成以下练习，巩固包管理相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 创建一个带__init__.py的包并导入模块。\n2. 用pip安装并卸载一个第三方库。\n3. 用venv创建虚拟环境并安装依赖。\n4. 编写requirements.txt并批量安装。", "explanation": "练习要求动手实践包结构、pip、venv、requirements.txt等。"}]}}]}