{"name": "Next.js", "trans": ["Next.js"], "methods": [{"name": "Server-Side Rendering (SSR)", "trans": ["服务端渲染(SSR)"], "usage": {"syntax": "export async function getServerSideProps(context) {\n  return { props: { data: ... } };\n}", "description": "SSR通过getServerSideProps在每次请求时服务端获取数据并渲染页面，适合需要实时数据的场景。", "parameters": [{"name": "context", "description": "请求上下文对象，包含params、query、req、res等。"}], "returnValue": "返回包含props的对象，传递给页面组件。", "examples": [{"code": "export async function getServerSideProps() {\n  const res = await fetch('https://api.example.com/data');\n  const data = await res.json();\n  return { props: { data } };\n}\nfunction Page({ data }) {\n  return <div>{data.title}</div>;\n}\nexport default Page;", "explanation": "通过SSR获取数据并渲染页面。"}]}}, {"name": "Static Site Generation (SSG)", "trans": ["静态站点生成(SSG)"], "usage": {"syntax": "export async function getStaticProps() {\n  return { props: { data: ... } };\n}", "description": "SSG通过getStaticProps在构建时生成静态页面，适合内容不频繁变更的场景，提升性能和SEO。", "parameters": [], "returnValue": "返回包含props的对象，传递给页面组件。", "examples": [{"code": "export async function getStaticProps() {\n  const res = await fetch('https://api.example.com/data');\n  const data = await res.json();\n  return { props: { data } };\n}\nfunction Page({ data }) {\n  return <div>{data.title}</div>;\n}\nexport default Page;", "explanation": "通过SSG在构建时获取数据并生成静态页面。"}]}}, {"name": "Incremental Static Regeneration (ISR)", "trans": ["增量静态再生成(ISR)"], "usage": {"syntax": "export async function getStaticProps() {\n  return { props: { data: ... }, revalidate: 10 };\n}", "description": "ISR允许静态页面在后台按需再生成，通过revalidate参数设置重新生成的时间间隔，兼顾性能和实时性。", "parameters": [{"name": "revalidate", "description": "页面重新生成的秒数间隔。"}], "returnValue": "返回包含props和revalidate的对象。", "examples": [{"code": "export async function getStaticProps() {\n  const res = await fetch('https://api.example.com/data');\n  const data = await res.json();\n  return { props: { data }, revalidate: 60 };\n}\nfunction Page({ data }) {\n  return <div>{data.title}</div>;\n}\nexport default Page;", "explanation": "通过ISR实现静态页面的定时再生成。"}]}}, {"name": "API Routes", "trans": ["API路由"], "usage": {"syntax": "// pages/api/hello.js\nexport default function handler(req, res) {\n  res.status(200).json({ name: '<PERSON>' });\n}", "description": "API路由允许在Next.js中直接创建后端接口，文件位于pages/api目录下。", "parameters": [{"name": "req", "description": "请求对象。"}, {"name": "res", "description": "响应对象。"}], "returnValue": "无返回值，通过res发送响应。", "examples": [{"code": "// pages/api/hello.js\nexport default function handler(req, res) {\n  res.status(200).json({ name: '<PERSON>' });\n}", "explanation": "创建一个简单的API接口。"}]}}, {"name": "File System Routing", "trans": ["文件系统路由"], "usage": {"syntax": "// pages/about.js\nexport default function About() { return <div>关于</div>; }", "description": "Next.js根据pages目录下的文件结构自动生成路由，无需手动配置。", "parameters": [], "returnValue": "无返回值，页面自动路由。", "examples": [{"code": "// pages/about.js\nexport default function About() { return <div>关于</div>; }", "explanation": "通过文件结构自动生成/about路由。"}]}}, {"name": "Image Optimization", "trans": ["图像优化"], "usage": {"syntax": "import Image from 'next/image';\n<Image src=\"/logo.png\" width={100} height={100} alt=\"logo\" />", "description": "Next.js内置Image组件支持自动图片优化、懒加载和响应式，提升性能和体验。", "parameters": [{"name": "src", "description": "图片路径。"}, {"name": "width", "description": "图片宽度。"}, {"name": "height", "description": "图片高度。"}, {"name": "alt", "description": "图片描述。"}], "returnValue": "渲染优化后的图片组件。", "examples": [{"code": "import Image from 'next/image';\nexport default function Logo() {\n  return <Image src=\"/logo.png\" width={100} height={100} alt=\"logo\" />;\n}", "explanation": "使用Image组件实现图片优化。"}]}}, {"name": "Layouts and Pages", "trans": ["布局和页面"], "usage": {"syntax": "// pages/_app.js\nexport default function App({ Component, pageProps }) {\n  return <Component {...pageProps} />;\n}", "description": "Next.js通过_app.js自定义全局布局，通过pages目录组织页面，支持嵌套路由和多级布局。", "parameters": [{"name": "Component", "description": "当前页面组件。"}, {"name": "pageProps", "description": "传递给页面的props。"}], "returnValue": "渲染带有全局布局的页面。", "examples": [{"code": "// pages/_app.js\nexport default function App({ Component, pageProps }) {\n  return <Component {...pageProps} />;\n}", "explanation": "自定义全局布局和页面渲染。"}]}}, {"name": "作业：Next.js实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 实现SSR、SSG、ISR页面\n// 2. 创建API路由和文件系统路由\n// 3. 使用Image组件和自定义布局", "description": "通过实践Next.js的SSR、SSG、ISR、API路由、文件系统路由、图片优化和布局，掌握高效开发方法。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 实现不同数据获取方式页面\n// 2. 创建API和页面路由\n// 3. 使用Image和全局布局", "explanation": "作业提示，学生需结合本节内容完成实现。"}, {"code": "// 正确实现示例\n// pages/index.js\nexport default function Home() { return <div>首页</div>; }\n// pages/api/hello.js\nexport default function handler(req, res) { res.status(200).json({ msg: 'ok' }); }", "explanation": "Next.js页面和API路由的正确实现示例。"}]}}]}