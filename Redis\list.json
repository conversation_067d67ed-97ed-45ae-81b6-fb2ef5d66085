{"name": "List", "trans": ["列表类型"], "methods": [{"name": "List Commands", "trans": ["列表操作命令"], "usage": {"syntax": "LPUSH key value [value ...]\nRPUSH key value [value ...]\nLPOP key\nRPOP key\nLRANGE key start stop", "description": "LPUSH/RPUSH向列表左/右插入元素，LPOP/RPOP弹出元素，LRANGE获取指定区间的元素。适合实现队列、栈等结构。", "parameters": [{"name": "key", "description": "列表键名"}, {"name": "value", "description": "插入的元素值"}, {"name": "start/stop", "description": "区间起止下标"}], "returnValue": "插入后列表长度、弹出元素值或区间元素列表", "examples": [{"code": "$ redis-cli lpush mylist a b c\n$ redis-cli rpush mylist d\n$ redis-cli lpop mylist\n$ redis-cli rpop mylist\n$ redis-cli lrange mylist 0 -1", "explanation": "演示了列表的插入、弹出和区间查询。"}]}}, {"name": "Queue and Message Queue Implementation", "trans": ["队列与消息队列实现"], "usage": {"syntax": "LPUSH/RPUSH + BRPOP/BLPOP\nRPOPLPUSH/BRPOPLPUSH", "description": "通过LPUSH+RPOP实现队列，LPOP+LPUSH实现栈。BRPOP/BLPOP为阻塞弹出，适合消息队列。RPOPLPUSH可实现任务转移。", "parameters": [{"name": "key", "description": "队列或消息队列键名"}], "returnValue": "弹出元素或阻塞等待的元素", "examples": [{"code": "$ redis-cli lpush queue task1 task2\n$ redis-cli brpop queue 0 # 阻塞直到有元素弹出\n$ redis-cli rpoplpush queue backup", "explanation": "演示了队列和阻塞消息队列的用法。"}]}}, {"name": "Practice: List类型练习", "trans": ["列表类型练习"], "usage": {"syntax": "# Redis列表类型练习", "description": "完成以下练习，掌握列表类型的常用操作和队列实现。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 使用LPUSH和RPUSH向列表插入元素。\n2. 用LPOP和RPOP弹出元素。\n3. 用LRANGE获取所有元素。\n4. 用BRPOP实现阻塞消息队列。", "explanation": "通过这些练习掌握列表类型的常用命令和队列应用。"}, {"code": "# 正确实现示例\n$ redis-cli lpush jobs job1 job2\n$ redis-cli rpush jobs job3\n$ redis-cli lpop jobs\n$ redis-cli rpop jobs\n$ redis-cli lrange jobs 0 -1\n$ redis-cli brpop jobs 0", "explanation": "展示了练习的标准实现过程。"}]}}]}