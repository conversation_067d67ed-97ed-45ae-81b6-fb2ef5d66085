{"name": "File Operations", "trans": ["文件操作"], "methods": [{"name": "Open and Close File", "trans": ["打开与关闭文件"], "usage": {"syntax": "f = open('文件名', '模式', encoding='编码')\nf.close()", "description": "open()用于打开文件，返回文件对象。操作完成后应调用close()关闭文件，释放资源。", "parameters": [{"name": "文件名", "description": "要打开的文件路径"}, {"name": "模式", "description": "打开模式，如'r'读取，'w'写入，'a'追加，'b'二进制"}, {"name": "encoding", "description": "文本文件编码，常用'utf-8'"}], "returnValue": "文件对象", "examples": [{"code": "f = open('test.txt', 'w', encoding='utf-8')\nf.write('hello')\nf.close()", "explanation": "演示了文件的打开、写入和关闭。"}]}}, {"name": "Read and Write Text File", "trans": ["读写文本文件"], "usage": {"syntax": "f.read()\nf.readline()\nf.readlines()\nf.write(内容)", "description": "read()读取全部内容，readline()读取一行，readlines()读取所有行。write()写入内容。", "parameters": [{"name": "内容", "description": "要写入的字符串"}], "returnValue": "读取到的字符串或写入的字符数", "examples": [{"code": "f = open('test.txt', 'r', encoding='utf-8')\ncontent = f.read()\nprint(content)\nf.close()", "explanation": "演示了文本文件的读取。"}]}}, {"name": "Read and Write Binary File", "trans": ["读写二进制文件"], "usage": {"syntax": "f = open('文件名', 'rb')\ndata = f.read()\nf.close()", "description": "以'b'模式打开文件可进行二进制读写，常用于图片、音频等非文本文件。", "parameters": [{"name": "文件名", "description": "要操作的二进制文件路径"}], "returnValue": "读取到的字节数据或写入的字节数", "examples": [{"code": "f = open('logo.png', 'rb')\ndata = f.read()\nf.close()\nprint(type(data))  # <class 'bytes'>", "explanation": "演示了二进制文件的读取。"}]}}, {"name": "with Statement and Context Manager", "trans": ["with语句与上下文管理器"], "usage": {"syntax": "with open('文件名', 'r', encoding='utf-8') as f:\n    代码块", "description": "with语句可自动管理资源，代码块结束后自动关闭文件。推荐文件操作使用。", "parameters": [{"name": "文件名", "description": "要打开的文件路径"}], "returnValue": "文件对象", "examples": [{"code": "with open('test.txt', 'r', encoding='utf-8') as f:\n    for line in f:\n        print(line.strip())", "explanation": "演示了with语句自动关闭文件。"}]}}, {"name": "File Pointer and seek", "trans": ["文件指针与seek"], "usage": {"syntax": "f.seek(offset, whence)\nf.tell()", "description": "seek()移动文件指针，tell()获取当前位置。常用于二进制或大文件操作。", "parameters": [{"name": "offset", "description": "偏移量，单位字节"}, {"name": "whence", "description": "起始位置，0文件头，1当前位置，2文件尾"}], "returnValue": "新指针位置或当前位置", "examples": [{"code": "f = open('test.txt', 'rb')\nf.seek(5)\nprint(f.tell())  # 5\nf.close()", "explanation": "演示了seek和tell的用法。"}]}}, {"name": "File Encoding", "trans": ["文件编码"], "usage": {"syntax": "open('文件名', 'r', encoding='utf-8')", "description": "encoding参数指定文件编码，常用utf-8。编码不符会导致读取错误。", "parameters": [{"name": "encoding", "description": "文件编码格式"}], "returnValue": "文件对象", "examples": [{"code": "f = open('test.txt', 'r', encoding='utf-8')\nprint(f.read())\nf.close()", "explanation": "演示了指定编码读取文件。"}]}}, {"name": "File Operations Assignment", "trans": ["文件操作练习"], "usage": {"syntax": "# 文件操作练习", "description": "完成以下练习，巩固文件操作相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 写一个文本文件并读取内容。\n2. 用with语句读取文件每一行。\n3. 读取图片文件的字节数。\n4. 用seek定位到文件中间并读取内容。", "explanation": "练习要求动手实践文件读写、with、seek、编码等。"}]}}]}