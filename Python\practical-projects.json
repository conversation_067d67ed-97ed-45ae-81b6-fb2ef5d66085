{"name": "Practical Projects", "trans": ["综合实战"], "methods": [{"name": "Command-line Tool: <PERSON><PERSON>", "trans": ["命令行工具：批量重命名文件"], "usage": {"syntax": "python rename.py 目录 前缀", "description": "编写命令行脚本，批量为指定目录下的文件重命名，添加统一前缀。", "parameters": [{"name": "目录", "description": "要重命名的文件夹路径"}, {"name": "前缀", "description": "新文件名前缀"}], "returnValue": "无返回值，文件被重命名", "examples": [{"code": "import os, sys\ndir = sys.argv[1]\nprefix = sys.argv[2]\nfor i, name in enumerate(os.listdir(dir)):\n    old = os.path.join(dir, name)\n    new = os.path.join(dir, f'{prefix}_{i}{os.path.splitext(name)[1]}')\n    os.rename(old, new)", "explanation": "命令行批量重命名文件，适合自动化运维。"}]}}, {"name": "Data Analysis: CSV Data Statistics", "trans": ["数据分析：CSV数据统计"], "usage": {"syntax": "python analyze.py data.csv", "description": "读取CSV文件，统计某列的均值、最大值、最小值等。", "parameters": [{"name": "data.csv", "description": "待分析的CSV文件"}], "returnValue": "统计结果输出到终端", "examples": [{"code": "import pandas as pd, sys\ndf = pd.read_csv(sys.argv[1])\nprint('均值:', df['value'].mean())\nprint('最大:', df['value'].max())\nprint('最小:', df['value'].min())", "explanation": "用Pandas分析CSV数据，适合数据分析入门。"}]}}, {"name": "Web Application: Simple Blog", "trans": ["Web应用：简易博客"], "usage": {"syntax": "python app.py", "description": "用Flask实现一个简易博客系统，支持文章发布和浏览。", "parameters": [], "returnValue": "Web页面", "examples": [{"code": "from flask import Flask, request\napp = Flask(__name__)\nposts = []\***********('/', methods=['GET', 'POST'])\ndef index():\n    if request.method == 'POST':\n        posts.append(request.form['content'])\n    return '<br>'.join(posts) + '<form method=post><input name=content><input type=submit></form>'\napp.run()", "explanation": "用Flask实现的极简博客，演示Web开发流程。"}]}}, {"name": "Web Crawler: Fetch Titles", "trans": ["爬虫：抓取网页标题"], "usage": {"syntax": "python crawler.py url", "description": "用requests和BeautifulSoup抓取指定网页的标题。", "parameters": [{"name": "url", "description": "目标网页地址"}], "returnValue": "网页标题输出到终端", "examples": [{"code": "import requests, sys\nfrom bs4 import BeautifulSoup\nurl = sys.argv[1]\nresp = requests.get(url)\nsoup = BeautifulSoup(resp.text, 'html.parser')\nprint(soup.title.string)", "explanation": "用requests和BeautifulSoup抓取网页标题，适合爬虫入门。"}]}}, {"name": "Automation Script: Batch Image Compression", "trans": ["自动化脚本：批量图片压缩"], "usage": {"syntax": "python compress.py 目录", "description": "用Pillow库批量压缩指定目录下的图片文件，节省存储空间。", "parameters": [{"name": "目录", "description": "图片所在文件夹路径"}], "returnValue": "压缩后的图片文件", "examples": [{"code": "import os, sys\nfrom PIL import Image\ndir = sys.argv[1]\nfor name in os.listdir(dir):\n    if name.lower().endswith(('.jpg', '.png')):\n        img = Image.open(os.path.join(dir, name))\n        img.save(os.path.join(dir, name), quality=60)", "explanation": "用Pillow批量压缩图片，适合自动化运维和数据处理。"}]}}, {"name": "Practical Projects Assignment", "trans": ["综合实战练习"], "usage": {"syntax": "# 综合实战练习", "description": "完成以下练习，巩固综合实战能力。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 编写命令行工具批量处理文件。\n2. 用Pandas分析数据并输出统计结果。\n3. 用Flask实现一个留言板。\n4. 用requests和BeautifulSoup抓取网页内容。\n5. 用Pillow批量处理图片。", "explanation": "练习要求动手实践命令行、数据分析、Web、爬虫、自动化等综合能力。"}]}}]}