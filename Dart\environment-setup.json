{"name": "Development Environment Setup", "trans": ["开发环境搭建"], "methods": [{"name": "Dart SDK Installation", "trans": ["Dart SDK安装"], "usage": {"syntax": "// 安装命令因操作系统而异", "description": "Dart SDK是开发Dart应用程序的基础工具集，包含了Dart编译器、运行时、核心库和开发工具。安装方式有多种：1) 通过官方安装包：访问dart.dev下载对应操作系统的安装包；2) 通过包管理器：在macOS使用Homebrew，在Windows使用Chocolatey，在Linux使用apt-get等；3) 通过Flutter SDK：如果已安装Flutter，则Dart SDK已包含在内。安装完成后，可以通过命令行运行`dart --version`验证安装是否成功。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// Windows系统安装Dart SDK的方法\n\n// 方法1：使用Chocolatey包管理器\n// 在PowerShell或命令提示符中运行\n// choco install dart-sdk\n\n// 方法2：手动下载安装包\n// 1. 访问 https://dart.dev/get-dart\n// 2. 下载Windows安装程序\n// 3. 运行安装程序，按提示完成安装\n\n// 验证安装\n// 在命令行中运行\n// dart --version\n// 输出类似：Dart SDK version: 2.18.0\n\n// macOS系统安装Dart SDK的方法\n// 使用Homebrew包管理器\n// brew tap dart-lang/dart\n// brew install dart\n\n// Linux系统安装Dart SDK的方法（Ubuntu/Debian）\n// sudo apt-get update\n// sudo apt-get install apt-transport-https\n// sudo sh -c 'wget -qO- https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add -'\n// sudo sh -c 'wget -qO- https://storage.googleapis.com/download.dartlang.org/linux/debian/dart_stable.list > /etc/apt/sources.list.d/dart_stable.list'\n// sudo apt-get update\n// sudo apt-get install dart", "explanation": "这个示例展示了在不同操作系统上安装Dart SDK的方法，包括使用包管理器和手动下载安装。在安装完成后，可以通过运行`dart --version`命令来验证安装是否成功。"}]}}, {"name": "Editor and IDE Selection", "trans": ["编辑器与IDE选择"], "usage": {"syntax": "// 编辑器选择不需要具体语法", "description": "开发Dart程序可以使用多种编辑器和IDE，最常用的有：1) Visual Studio Code：轻量级编辑器，安装Dart插件后提供代码补全、语法高亮、调试等功能；2) IntelliJ IDEA/WebStorm：JetBrains出品的IDE，提供强大的Dart和Flutter支持，包括代码分析、重构、调试等高级功能；3) Android Studio：基于IntelliJ的IDE，主要用于Flutter和Android开发，内置Dart支持；4) DartPad：在线编辑器，无需安装即可编写和运行Dart代码，适合学习和简单测试。选择时应考虑个人习惯、项目需求和硬件配置等因素。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 在Visual Studio Code中配置Dart环境\n\n// 1. 安装VS Code\n// 从 https://code.visualstudio.com/ 下载并安装VS Code\n\n// 2. 安装Dart插件\n// - 打开VS Code\n// - 点击扩展图标或按Ctrl+Shift+X(Windows/Linux)或Cmd+Shift+X(macOS)\n// - 搜索\"Dart\"并安装官方Dart插件\n\n// 3. 配置Dart SDK路径（如果需要）\n// - 打开设置 (Ctrl+,)\n// - 搜索\"Dart: Sdk Path\"\n// - 设置为Dart SDK的安装路径，例如：\n//   Windows: C:\\Program Files\\Dart\\dart-sdk\n//   macOS: /usr/local/opt/dart/libexec\n//   Linux: /usr/lib/dart\n\n// 4. 创建启动配置（用于调试）\n// 在项目根目录创建或编辑.vscode/launch.json文件：\n/*\n{\n  \"version\": \"0.2.0\",\n  \"configurations\": [\n    {\n      \"name\": \"Dart\",\n      \"program\": \"bin/main.dart\",  // 指向你的主Dart文件\n      \"request\": \"launch\",\n      \"type\": \"dart\"\n    }\n  ]\n}\n*/\n\n// 在IntelliJ IDEA或WebStorm中配置Dart环境\n// 1. 安装Dart插件\n// - 打开IDE\n// - 转到Settings/Preferences -> Plugins\n// - 搜索并安装Dart插件\n\n// 2. 配置Dart SDK\n// - 转到Settings/Preferences -> Languages & Frameworks -> Dart\n// - 设置Dart SDK路径\n// - 启用Dart支持", "explanation": "这个示例展示了如何在两个流行的开发环境中配置Dart：Visual Studio Code和JetBrains IDE（如IntelliJ IDEA或WebStorm）。示例包括安装必要的插件、配置SDK路径和设置调试环境。"}]}}, {"name": "First Dart Program", "trans": ["第一个Dart程序"], "usage": {"syntax": "void main() {\n  // 程序代码\n}", "description": "创建第一个Dart程序是学习Dart的起点。Dart程序的入口是main()函数，类似于C、Java等语言。一个基本的Dart程序通常包含以下步骤：1) 创建一个.dart文件；2) 编写main()函数作为程序入口；3) 在main()函数中添加代码，如打印输出；4) 使用命令行工具或IDE运行程序。Dart支持控制台应用、Web应用和Flutter应用等多种类型的程序，但它们都从一个简单的入口点开始。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 创建一个名为hello.dart的文件，内容如下：\n\n// 程序入口点\nvoid main() {\n  // 打印欢迎消息\n  print('Hello, Dart!'); // 输出: Hello, Dart!\n  \n  // 声明变量\n  String name = 'World';\n  int year = DateTime.now().year;\n  \n  // 字符串插值\n  print('Hello, $name! Welcome to Dart in $year.'); // 使用变量值\n  \n  // 简单计算\n  int a = 10;\n  int b = 20;\n  print('$a + $b = ${a + b}'); // 输出: 10 + 20 = 30\n  \n  // 调用函数\n  greet('学员');\n}\n\n// 自定义函数\nvoid greet(String person) {\n  print('欢迎您学习Dart，亲爱的$person！');\n}\n\n// 运行方法：\n// 在命令行中，导航到文件所在目录，然后运行：\n// dart hello.dart\n\n// 在VS Code中：\n// 打开文件，点击右上角的运行按钮或按F5\n\n// 在IntelliJ/WebStorm中：\n// 右键点击文件，选择'Run'或使用快捷键Shift+F10", "explanation": "这个示例创建了一个简单的Dart程序，演示了基本语法、变量声明、字符串操作、简单计算和函数调用。示例还包括如何在命令行和不同IDE中运行该程序的说明。"}]}}, {"name": "Project Structure and Execution", "trans": ["项目结构与运行"], "usage": {"syntax": "// 创建项目结构的命令\n// dart create -t console my_project", "description": "一个标准的Dart项目通常遵循特定的目录结构，这有助于组织代码和依赖项。Dart生态系统使用包管理工具pub来管理项目。典型的Dart项目结构包括：1) pubspec.yaml：项目配置文件，定义名称、版本、依赖等；2) lib/：存放库代码，可被其他项目导入；3) bin/：存放可执行程序；4) test/：存放测试代码；5) example/：存放示例代码；6) .dart_tool/：Dart工具使用的缓存目录。创建项目可以手动完成，也可以使用`dart create`命令自动生成模板。运行项目可以使用`dart run`命令或通过IDE的运行功能。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 使用命令行创建新的Dart项目\n\n// 创建一个控制台应用项目\n// dart create -t console my_first_project\n\n// 创建一个库项目\n// dart create -t package my_library\n\n// 创建一个Web应用项目\n// dart create -t web my_web_app\n\n// 项目创建后的典型目录结构\n/*\nmy_first_project/\n  ├── .dart_tool/          # Dart工具使用的缓存目录\n  ├── .gitignore           # Git忽略文件\n  ├── CHANGELOG.md         # 变更日志\n  ├── README.md            # 项目说明文档\n  ├── analysis_options.yaml # 静态分析配置\n  ├── bin/                 # 可执行文件目录\n  │   └── my_first_project.dart # 主程序入口\n  ├── lib/                 # 库代码目录\n  │   └── my_first_project.dart # 项目库代码\n  ├── pubspec.lock         # 依赖锁定文件（自动生成）\n  ├── pubspec.yaml         # 项目配置和依赖声明\n  └── test/                # 测试代码目录\n      └── my_first_project_test.dart # 测试文件\n*/\n\n// pubspec.yaml示例\n/*\nname: my_first_project     # 项目名称\ndescription: A sample command-line application.  # 项目描述\nversion: 1.0.0             # 项目版本\n\nenvironment:               # Dart SDK版本约束\n  sdk: '>=2.18.0 <3.0.0'\n\ndependencies:              # 项目依赖\n  path: ^1.8.0             # 路径操作库\n\ndev_dependencies:          # 开发依赖（测试等）\n  lints: ^2.0.0            # 代码风格检查\n  test: ^1.16.0            # 测试框架\n*/\n\n// 项目运行方法\n\n// 方法1: 使用dart run命令（从项目根目录）\n// dart run bin/my_first_project.dart\n\n// 方法2: 直接运行.dart文件\n// dart bin/my_first_project.dart\n\n// 方法3: 使用pub global激活项目（发布可执行包）\n// dart pub global activate --source path .\n// my_first_project  # 现在可以直接使用项目名执行", "explanation": "这个示例展示了如何创建一个标准的Dart项目、项目的典型目录结构、pubspec.yaml配置文件的内容以及不同的项目运行方法。通过这些标准实践，可以更好地组织代码并与Dart生态系统集成。"}]}}, {"name": "Assignment", "trans": ["实践作业"], "usage": {"syntax": "// 设置Dart开发环境并创建第一个项目", "description": "完成以下任务，搭建自己的Dart开发环境并创建第一个项目：1) 安装Dart SDK，并验证安装是否成功；2) 安装并配置一个IDE或编辑器（推荐VS Code或IntelliJ IDEA）；3) 创建一个简单的Dart控制台应用，能够打印个人信息（如姓名、年龄、爱好）；4) 使用命令行工具创建一个标准Dart项目结构，添加至少一个外部依赖，并在程序中使用这个依赖；5) 成功运行项目并截图保存结果。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 参考解决方案\n\n// 1. 验证Dart SDK安装\nvoid checkDartInstallation() {\n  // 在命令行运行\n  // dart --version\n  // 确认输出包含Dart版本信息\n}\n\n// 2. 在VS Code中配置Dart扩展\nvoid configureVSCode() {\n  // 安装Dart扩展\n  // 配置SDK路径\n  // 创建launch.json调试配置\n}\n\n// 3. 创建简单控制台应用\n// 文件: personal_info.dart\nvoid main() {\n  // 声明个人信息变量\n  String name = '张三';\n  int age = 25;\n  List<String> hobbies = ['编程', '阅读', '跑步'];\n  \n  // 打印个人信息\n  print('===== 个人信息 =====');\n  print('姓名: $name');\n  print('年龄: $age');\n  print('爱好: ${hobbies.join(', ')}');\n  \n  // 使用字符串模板打印摘要\n  print('\\n我是$name，今年$age岁，喜欢${hobbies.join('和')}。');\n}\n\n// 4. 创建标准项目结构并添加依赖\n// 在命令行运行:\n// dart create -t console my_dart_project\n// cd my_dart_project\n\n// 编辑pubspec.yaml添加依赖:\n/*\ndependencies:\n  intl: ^0.18.0  # 国际化和格式化库\n*/\n\n// 运行pub get安装依赖:\n// dart pub get\n\n// 修改bin/my_dart_project.dart使用依赖:\nvoid main() {\n  // 导入外部依赖\n  import 'package:intl/intl.dart';\n  \n  // 使用依赖格式化日期\n  var now = DateTime.now();\n  var formatter = DateFormat('yyyy-MM-dd HH:mm:ss');\n  String formattedDate = formatter.format(now);\n  \n  // 打印当前日期时间\n  print('当前时间: $formattedDate');\n  \n  // 打印个人信息\n  String name = '李四';\n  int age = 30;\n  \n  print('我是$name，今年$age岁。');\n  print('这是我的第一个Dart项目，创建于$formattedDate');\n}\n\n// 5. 运行项目\n// dart run", "explanation": "这个解决方案展示了完整的Dart开发环境搭建过程，包括SDK安装验证、IDE配置、创建简单程序、设置标准项目结构、添加外部依赖并使用它。通过这个实践，学习者可以熟悉Dart的基本开发流程。"}]}}]}