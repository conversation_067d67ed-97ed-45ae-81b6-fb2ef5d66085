{"name": "Component Testing", "trans": ["组件测试"], "methods": [{"name": "Component Render Testing", "trans": ["组件渲染测试"], "usage": {"syntax": "const wrapper = mount(MyComponent)\nexpect(wrapper.html()).toContain('<div')", "description": "通过mount挂载组件，断言渲染的HTML结构，确保组件正确渲染。", "parameters": [{"name": "mount", "description": "挂载组件进行测试"}, {"name": "html", "description": "获取渲染后的HTML"}], "returnValue": "渲染的HTML结构断言结果", "examples": [{"code": "const wrapper = mount(MyComponent)\nexpect(wrapper.html()).toContain('<div')", "explanation": "断言组件渲染包含div标签。"}]}}, {"name": "Interaction Testing", "trans": ["交互测试"], "usage": {"syntax": "await wrapper.find('button').trigger('click')\nexpect(wrapper.emitted('click')).toBeTruthy()", "description": "通过触发用户交互事件，断言组件行为和事件是否正确响应。", "parameters": [{"name": "find", "description": "查找组件内元素"}, {"name": "trigger", "description": "触发事件"}, {"name": "emitted", "description": "获取组件触发的事件"}], "returnValue": "交互事件的断言结果", "examples": [{"code": "await wrapper.find('button').trigger('click')\nexpect(wrapper.emitted('click')).toBeTruthy()", "explanation": "断言点击按钮后组件触发click事件。"}]}}, {"name": "Snapshot Testing", "trans": ["快照测试"], "usage": {"syntax": "expect(wrapper.html()).toMatchSnapshot()", "description": "通过快照测试，自动对比组件渲染输出，检测UI变更。", "parameters": [{"name": "toMatchSnapshot", "description": "生成并对比快照"}], "returnValue": "快照对比结果，检测UI变更", "examples": [{"code": "expect(wrapper.html()).toMatchSnapshot()", "explanation": "断言当前渲染输出与快照一致。"}]}}, {"name": "Event Testing", "trans": ["事件测试"], "usage": {"syntax": "await wrapper.find('input').setValue('abc')\nexpect(wrapper.emitted('update:modelValue')).toBeTruthy()", "description": "测试组件内事件的触发与响应，确保事件机制正确。", "parameters": [{"name": "setValue", "description": "设置输入框等控件的值"}, {"name": "emitted", "description": "获取组件触发的事件"}], "returnValue": "事件触发的断言结果", "examples": [{"code": "await wrapper.find('input').setValue('abc')\nexpect(wrapper.emitted('update:modelValue')).toBeTruthy()", "explanation": "断言输入后触发update:modelValue事件。"}]}}, {"name": "State Change Testing", "trans": ["状态变化测试"], "usage": {"syntax": "wrapper.setData({ count: 2 })\nexpect(wrapper.vm.count).toBe(2)", "description": "通过修改组件数据，断言状态变化后的结果，确保响应式数据正确。", "parameters": [{"name": "setData", "description": "设置组件数据"}, {"name": "vm", "description": "组件实例对象"}], "returnValue": "状态变化的断言结果", "examples": [{"code": "wrapper.setData({ count: 2 })\nexpect(wrapper.vm.count).toBe(2)", "explanation": "断言count状态变化为2。"}]}}, {"name": "Props Validation", "trans": ["属性验证"], "usage": {"syntax": "const wrapper = mount(MyComponent, { props: { msg: 'hi' } })\nexpect(wrapper.props('msg')).toBe('hi')", "description": "测试组件props的传递与验证，确保属性类型和值正确。", "parameters": [{"name": "props", "description": "组件属性"}], "returnValue": "属性验证的断言结果", "examples": [{"code": "const wrapper = mount(MyComponent, { props: { msg: 'hi' } })\nexpect(wrapper.props('msg')).toBe('hi')", "explanation": "断言msg属性为hi。"}]}}, {"name": "Component Testing Assignment", "trans": ["组件测试练习"], "usage": {"syntax": "# 组件测试练习", "description": "完成以下练习，巩固组件测试相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 编写组件渲染测试。\n2. 编写交互和事件测试。\n3. 编写快照测试。\n4. 验证组件状态变化和属性。", "explanation": "通过这些练习掌握Vue组件测试方法。"}]}}]}