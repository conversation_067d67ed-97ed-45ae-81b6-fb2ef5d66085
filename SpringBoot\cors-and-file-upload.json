{"name": "CORS and File Upload", "trans": ["跨域与文件上传"], "methods": [{"name": "CORS Configuration", "trans": ["CORS配置"], "usage": {"syntax": "@CrossOrigin 或 WebMvcConfigurer实现全局CORS", "description": "Spring Boot支持通过@CrossOrigin注解或实现WebMvcConfigurer接口配置全局跨域，允许前端跨域访问API。", "parameters": [{"name": "@CrossOrigin", "description": "注解方式配置跨域"}, {"name": "WebMvcConfigurer", "description": "全局配置跨域策略"}], "returnValue": "支持跨域访问的REST接口", "examples": [{"code": "@RestController\n@CrossOrigin(origins = \"http://localhost:3000\")\npublic class UserController {\n    @GetMapping(\"/user\")\n    public User getUser() {\n        return new User(1, \"张三\");\n    }\n}\n\n// 全局CORS配置\n@Configuration\npublic class WebConfig implements WebMvcConfigurer {\n    @Override\n    public void addCorsMappings(CorsRegistry registry) {\n        registry.addMapping(\"/**\").allowedOrigins(\"*\");\n    }\n}", "explanation": "@CrossOrigin适合单接口，WebMvcConfigurer适合全局配置。"}]}}, {"name": "File Upload and Download", "trans": ["文件上传与下载"], "usage": {"syntax": "@PostMapping + MultipartFile 实现文件上传，@GetMapping实现文件下载", "description": "Spring Boot通过MultipartFile参数接收上传文件，通过响应流实现文件下载，支持多种文件类型。", "parameters": [{"name": "MultipartFile", "description": "接收上传文件的参数类型"}, {"name": "ResponseEntity", "description": "用于文件下载的响应类型"}], "returnValue": "上传成功提示或下载的文件流", "examples": [{"code": "@RestController\npublic class FileController {\n    @PostMapping(\"/upload\")\n    public String upload(@RequestParam MultipartFile file) throws IOException {\n        file.transferTo(new File(\"/tmp/\" + file.getOriginalFilename()));\n        return \"上传成功\";\n    }\n    @GetMapping(\"/download\")\n    public ResponseEntity<Resource> download() throws IOException {\n        FileSystemResource resource = new FileSystemResource(\"/tmp/test.txt\");\n        return ResponseEntity.ok()\n            .header(HttpHeaders.CONTENT_DISPOSITION, \"attachment; filename=test.txt\")\n            .body(resource);\n    }\n}", "explanation": "MultipartFile用于上传，ResponseEntity<Resource>用于下载。"}]}}, {"name": "Multipart Configuration", "trans": ["Multipart配置"], "usage": {"syntax": "application.properties中配置文件上传参数", "description": "可通过application.properties配置文件上传的大小限制、临时目录等参数，保障上传安全和性能。", "parameters": [{"name": "spring.servlet.multipart.max-file-size", "description": "单个文件最大上传大小"}, {"name": "spring.servlet.multipart.max-request-size", "description": "单次请求最大上传大小"}], "returnValue": "受限的文件上传能力", "examples": [{"code": "# application.properties\nspring.servlet.multipart.max-file-size=10MB\nspring.servlet.multipart.max-request-size=20MB", "explanation": "可灵活限制上传文件大小，防止恶意上传。"}]}}, {"name": "CORS and File Upload Assignment", "trans": ["跨域与文件上传练习"], "usage": {"syntax": "# 跨域与文件上传练习", "description": "完成以下练习，巩固Spring Boot跨域与文件上传相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 配置@CrossOrigin实现接口跨域。\n2. 实现单文件和多文件上传接口。\n3. 实现文件下载接口。\n4. 配置文件上传大小限制。", "explanation": "通过这些练习掌握Spring Boot跨域与文件上传的核心用法。"}]}}]}