{"name": "Image Management", "trans": ["镜像获取与管理"], "methods": [{"name": "docker pull", "trans": ["拉取镜像"], "usage": {"syntax": "docker pull <镜像名>:<标签>", "description": "从远程仓库拉取指定名称和标签的镜像到本地。若不指定标签，默认拉取latest版本。", "parameters": [{"name": "<镜像名>", "description": "要拉取的镜像名称，如nginx、redis等。"}, {"name": "<标签>", "description": "镜像的版本标签，如latest、1.20等，可选。"}], "returnValue": "无返回值，命令执行后镜像被下载到本地。", "examples": [{"code": "# 拉取nginx的最新镜像\ndocker pull nginx:latest", "explanation": "将nginx的latest版本镜像下载到本地。"}, {"code": "# 拉取redis的6.2版本镜像\ndocker pull redis:6.2", "explanation": "将redis 6.2版本镜像下载到本地。"}]}}, {"name": "docker images", "trans": ["查看镜像"], "usage": {"syntax": "docker images [OPTIONS]", "description": "列出本地所有镜像及其相关信息。", "parameters": [{"name": "[OPTIONS]", "description": "可选参数，如-a显示所有镜像，-q只显示镜像ID等。"}], "returnValue": "无返回值，命令行输出镜像列表。", "examples": [{"code": "# 查看本地所有镜像\ndocker images", "explanation": "显示本地所有镜像的名称、标签、ID、创建时间和大小。"}, {"code": "# 只显示镜像ID\ndocker images -q", "explanation": "只输出本地镜像的ID列表。"}]}}, {"name": "docker rmi", "trans": ["删除镜像"], "usage": {"syntax": "docker rmi <镜像名或ID>", "description": "删除本地的一个或多个镜像。", "parameters": [{"name": "<镜像名或ID>", "description": "要删除的镜像名称或镜像ID。"}], "returnValue": "无返回值，命令执行后镜像被移除。", "examples": [{"code": "# 删除nginx镜像\ndocker rmi nginx:latest", "explanation": "删除本地的nginx:latest镜像。"}, {"code": "# 根据ID删除镜像\ndocker rmi 123456789abc", "explanation": "通过镜像ID删除指定镜像。"}]}}, {"name": "Image Tag & Version", "trans": ["镜像标签与版本"], "usage": {"syntax": "docker tag <源镜像>:<标签> <目标镜像>:<新标签>", "description": "为本地镜像添加新的标签，便于版本管理和推送到仓库。", "parameters": [{"name": "<源镜像>", "description": "已有的本地镜像名称。"}, {"name": "<标签>", "description": "已有镜像的标签。"}, {"name": "<目标镜像>", "description": "目标镜像名称。"}, {"name": "<新标签>", "description": "新标签名称。"}], "returnValue": "无返回值，命令执行后镜像拥有新标签。", "examples": [{"code": "# 给nginx:latest打上v1标签\ndocker tag nginx:latest nginx:v1", "explanation": "为本地nginx:latest镜像添加v1标签。"}]}}, {"name": "Image Search & Registry", "trans": ["镜像搜索与仓库"], "usage": {"syntax": "docker search <关键字>", "description": "在Docker Hub等远程仓库中搜索公开镜像。", "parameters": [{"name": "<关键字>", "description": "要搜索的镜像名称或描述关键字。"}], "returnValue": "无返回值，命令行输出搜索结果列表。", "examples": [{"code": "# 搜索nginx相关镜像\ndocker search nginx", "explanation": "在远程仓库中查找与nginx相关的镜像。"}]}}, {"name": "Assignment: 镜像管理实践", "trans": ["作业：镜像管理实践"], "usage": {"syntax": "请完成以下任务：\n1. 拉取nginx:alpine镜像\n2. 查看本地镜像列表\n3. 给nginx:alpine打上mynginx:v1标签\n4. 删除nginx:alpine镜像\n5. 搜索redis相关镜像", "description": "通过实际操作掌握镜像的拉取、查看、打标签、删除和搜索。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 1. 拉取nginx:alpine镜像\ndocker pull nginx:alpine\n# 2. 查看本地镜像\ndocker images\n# 3. 打标签\ndocker tag nginx:alpine mynginx:v1\n# 4. 删除镜像\ndocker rmi nginx:alpine\n# 5. 搜索redis镜像\ndocker search redis", "explanation": "依次完成镜像管理的核心操作。"}]}}]}