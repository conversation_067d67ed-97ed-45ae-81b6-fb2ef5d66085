{"name": "Basic Animation", "trans": ["动画基础"], "methods": [{"name": "AnimationController", "trans": ["AnimationController"], "usage": {"syntax": "AnimationController(vsync: this, duration: ...)", "description": "AnimationController用于控制动画的启动、停止、反向等，需与TickerProvider配合使用。", "parameters": [{"name": "vsync", "description": "TickerProvider对象，通常为State的this"}, {"name": "duration", "description": "动画时长"}], "returnValue": "AnimationController对象，可控制动画。", "examples": [{"code": "// AnimationController基础动画示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(home: AnimationDemo());\n  }\n}\n\nclass AnimationDemo extends StatefulWidget {\n  @override\n  _AnimationDemoState createState() => _AnimationDemoState();\n}\n\nclass _AnimationDemoState extends State<AnimationDemo> with SingleTickerProviderStateMixin {\n  late AnimationController _controller;\n  @override\n  void initState() {\n    super.initState();\n    _controller = AnimationController(vsync: this, duration: Duration(seconds: 1));\n  }\n  @override\n  void dispose() {\n    _controller.dispose();\n    super.dispose();\n  }\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('AnimationController示例')),\n      body: Center(\n        child: Column(\n          mainAxisAlignment: MainAxisAlignment.center,\n          children: [\n            ScaleTransition(\n              scale: _controller,\n              child: Icon(Icons.star, size: 80, color: Colors.orange),\n            ),\n            ElevatedButton(onPressed: () => _controller.forward(from: 0), child: Text('播放动画')),\n          ],\n        ),\n      ),\n    );\n  }\n}\n", "explanation": "演示了AnimationController的基本用法，控制动画播放。"}]}}, {"name": "Tween and Curve", "trans": ["Tween与Curve"], "usage": {"syntax": "Tween(begin: ..., end: ...).animate(controller), CurvedAnimation(parent: ..., curve: ...) ", "description": "Tween用于定义动画的起止值，Curve用于定义动画的速度曲线。", "parameters": [{"name": "begin/end", "description": "动画起始和结束值"}, {"name": "curve", "description": "动画曲线类型"}], "returnValue": "Animation对象，表示动画值。", "examples": [{"code": "// Tween与Curve动画示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(home: AnimationDemo());\n  }\n}\n\nclass AnimationDemo extends StatefulWidget {\n  @override\n  _AnimationDemoState createState() => _AnimationDemoState();\n}\n\nclass _AnimationDemoState extends State<AnimationDemo> with SingleTickerProviderStateMixin {\n  late AnimationController _controller;\n  late Animation<double> _animation;\n  @override\n  void initState() {\n    super.initState();\n    _controller = AnimationController(vsync: this, duration: Duration(seconds: 1));\n    _animation = Tween(begin: 0.0, end: 1.0).animate(CurvedAnimation(parent: _controller, curve: Curves.easeIn));\n  }\n  @override\n  void dispose() {\n    _controller.dispose();\n    super.dispose();\n  }\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('Tween与Curve示例')),\n      body: Center(\n        child: FadeTransition(\n          opacity: _animation,\n          child: Icon(Icons.star, size: 80, color: Colors.orange),\n        ),\n      ),\n      floatingActionButton: FloatingActionButton(\n        onPressed: () => _controller.forward(from: 0),\n        child: Icon(Icons.play_arrow),\n      ),\n    );\n  }\n}\n", "explanation": "演示了Tween和Curve结合实现渐变动画。"}]}}, {"name": "Implicit Animation Widgets", "trans": ["隐式动画组件"], "usage": {"syntax": "AnimatedContainer、AnimatedOpacity等，自动处理动画过渡。", "description": "隐式动画组件可在属性变化时自动平滑过渡，无需手动控制动画。", "parameters": [{"name": "duration", "description": "动画时长"}, {"name": "curve", "description": "动画曲线"}], "returnValue": "Widget，自动处理动画。", "examples": [{"code": "// 隐式动画组件示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(home: AnimationDemo());\n  }\n}\n\nclass AnimationDemo extends StatefulWidget {\n  @override\n  _AnimationDemoState createState() => _AnimationDemoState();\n}\n\nclass _AnimationDemoState extends State<AnimationDemo> {\n  double _opacity = 1.0;\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('隐式动画组件示例')),\n      body: Center(\n        child: AnimatedOpacity(\n          opacity: _opacity,\n          duration: Duration(seconds: 1),\n          child: Icon(Icons.star, size: 80, color: Colors.orange),\n        ),\n      ),\n      floatingActionButton: FloatingActionButton(\n        onPressed: () {\n          setState(() { _opacity = _opacity == 1.0 ? 0.2 : 1.0; });\n        },\n        child: Icon(Icons.play_arrow),\n      ),\n    );\n  }\n}\n", "explanation": "演示了AnimatedOpacity隐式动画组件的用法。"}]}}, {"name": "Hero Animation", "trans": ["Hero动画"], "usage": {"syntax": "Hero(tag: 'tag', child: ...)，用于页面间共享元素动画。", "description": "Hero动画用于页面跳转时实现元素的平滑过渡，需保证tag一致。", "parameters": [{"name": "tag", "description": "唯一标识，前后页面一致"}, {"name": "child", "description": "参与动画的组件"}], "returnValue": "Widget，实现共享元素动画。", "examples": [{"code": "// Hero动画示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(home: FirstPage());\n  }\n}\n\nclass FirstPage extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('第一页')),\n      body: Center(\n        child: GestureDetector(\n          onTap: () {\n            Navigator.push(context, MaterialPageRoute(builder: (_) => SecondPage()));\n          },\n          child: Hero(\n            tag: 'star',\n            child: Icon(Icons.star, size: 80, color: Colors.orange),\n          ),\n        ),\n      ),\n    );\n  }\n}\n\nclass SecondPage extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('第二页')),\n      body: Center(\n        child: Hero(\n          tag: 'star',\n          child: Icon(Icons.star, size: 160, color: Colors.orange),\n        ),\n      ),\n    );\n  }\n}\n", "explanation": "演示了Hero动画在页面跳转时的共享元素过渡效果。"}]}}, {"name": "Interactive Feedback (<PERSON><PERSON><PERSON>, Tap, Slide)", "trans": ["交互反馈（手势、点击、滑动）"], "usage": {"syntax": "GestureDetector、InkWell等，监听手势事件。", "description": "通过GestureDetector、InkWell等组件可实现点击、长按、滑动等手势交互反馈。", "parameters": [{"name": "onTap", "description": "点击事件回调"}, {"name": "onPanUpdate", "description": "滑动事件回调"}], "returnValue": "无返回值，触发交互反馈。", "examples": [{"code": "// 交互反馈（手势、点击、滑动）示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(home: GestureDemo());\n  }\n}\n\nclass GestureDemo extends StatefulWidget {\n  @override\n  _GestureDemoState createState() => _GestureDemoState();\n}\n\nclass _GestureDemoState extends State<GestureDemo> {\n  double _x = 0;\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('手势交互示例')),\n      body: Center(\n        child: GestureDetector(\n          onTap: () {\n            ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('点击事件')));\n          },\n          onPanUpdate: (details) {\n            setState(() { _x += details.delta.dx; });\n          },\n          child: Transform.translate(\n            offset: Offset(_x, 0),\n            child: Container(width: 80, height: 80, color: Colors.blue),\n          ),\n        ),\n      ),\n    );\n  }\n}\n", "explanation": "演示了GestureDetector实现点击和滑动交互反馈。"}]}}, {"name": "Assignment", "trans": ["作业：实现一个带动画和手势交互的页面。"], "usage": {"syntax": "无", "description": "请实现一个页面，包含动画控制、隐式动画、Hero动画和手势交互等内容。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 作业参考实现（简化版）\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(home: AnimationDemo());\n  }\n}\n\nclass AnimationDemo extends StatefulWidget {\n  @override\n  _AnimationDemoState createState() => _AnimationDemoState();\n}\n\nclass _AnimationDemoState extends State<AnimationDemo> with SingleTickerProviderStateMixin {\n  late AnimationController _controller;\n  @override\n  void initState() {\n    super.initState();\n    _controller = AnimationController(vsync: this, duration: Duration(seconds: 1));\n  }\n  @override\n  void dispose() {\n    _controller.dispose();\n    super.dispose();\n  }\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('作业实现')),\n      body: Center(\n        child: GestureDetector(\n          onTap: () => _controller.forward(from: 0),\n          child: ScaleTransition(\n            scale: _controller,\n            child: Hero(\n              tag: 'star',\n              child: Icon(Icons.star, size: 80, color: Colors.orange),\n            ),\n          ),\n        ),\n      ),\n    );\n  }\n}\n", "explanation": "作业要求实现动画控制、Hero动画和手势交互。"}]}}]}