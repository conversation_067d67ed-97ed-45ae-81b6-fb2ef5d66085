{"name": "Lists and Keys", "trans": ["列表与Keys"], "methods": [{"name": "Rendering Lists with map()", "trans": ["使用map()渲染列表"], "usage": {"syntax": "array.map((item, index) => <Element key={uniqueId} ... />)", "description": "在React中，使用JavaScript的map()方法将数组转换为元素列表是最常见的列表渲染方式。它允许我们遍历数组并为每个项目返回一个React元素。", "parameters": [{"name": "array", "description": "要渲染的数据数组"}, {"name": "item", "description": "数组中的当前项目"}, {"name": "index", "description": "当前项目在数组中的索引"}, {"name": "Element", "description": "要为每个项目渲染的React元素或组件"}], "returnValue": "React元素数组，每个元素对应输入数组中的一项", "examples": [{"code": "// 基本列表渲染示例\nimport React from 'react';\n\nfunction NumberList() {\n  const numbers = [1, 2, 3, 4, 5];\n  \n  // 使用map()将数字数组转换为列表项元素\n  const listItems = numbers.map((number) =>\n    <li key={number.toString()}>\n      {number}\n    </li>\n  );\n  \n  return (\n    <ul>{listItems}</ul>\n  );\n}\n\n// 内联map()渲染\nfunction FruitList() {\n  const fruits = ['苹果', '香蕉', '橙子', '葡萄'];\n  \n  return (\n    <div>\n      <h2>水果列表</h2>\n      <ul>\n        {fruits.map((fruit, index) => (\n          <li key={index}>{fruit}</li>\n        ))}\n      </ul>\n    </div>\n  );\n}\n\n// 渲染对象数组\nfunction UserList() {\n  const users = [\n    { id: 1, name: '张三', age: 25 },\n    { id: 2, name: '李四', age: 30 },\n    { id: 3, name: '王五', age: 28 }\n  ];\n  \n  return (\n    <div>\n      <h2>用户列表</h2>\n      <table>\n        <thead>\n          <tr>\n            <th>ID</th>\n            <th>姓名</th>\n            <th>年龄</th>\n          </tr>\n        </thead>\n        <tbody>\n          {users.map(user => (\n            <tr key={user.id}>\n              <td>{user.id}</td>\n              <td>{user.name}</td>\n              <td>{user.age}</td>\n            </tr>\n          ))}\n        </tbody>\n      </table>\n    </div>\n  );\n}\n\n// 使用map()和条件渲染\nfunction FilteredList({ items, showCompleted }) {\n  return (\n    <ul>\n      {items\n        .filter(item => showCompleted || !item.completed)\n        .map(item => (\n          <li key={item.id}>\n            {item.text}\n            {item.completed && ' ✓'}\n          </li>\n        ))\n      }\n    </ul>\n  );\n}\n\n// 在组件中使用map()和props\nfunction ListItem({ value }) {\n  return <li>{value}</li>;\n}\n\nfunction GenericList({ items }) {\n  return (\n    <ul>\n      {items.map((item) => (\n        <ListItem key={item.id} value={item.text} />\n      ))}\n    </ul>\n  );\n}", "explanation": "这些例子展示了在React中使用map()渲染列表的不同方式。map()方法接收一个回调函数，该函数为数组中的每一项返回一个React元素。我们可以在JSX中直接使用map()，也可以先生成元素数组再在JSX中使用。map()可以与filter()等数组方法组合使用，实现更复杂的列表渲染逻辑。注意每个列表项都应该有一个唯一的'key'属性，这将在下一个主题中详细讨论。"}]}}, {"name": "Keys Purpose and Importance", "trans": ["key的作用和重要性"], "usage": {"syntax": "<Element key={uniqueId} ... />", "description": "在React中，key是一个特殊的字符串属性，当创建元素列表时需要包含它。key帮助React识别哪些元素发生了变化、添加或删除。数组中的每个元素都应该有一个唯一的key标识。", "parameters": [{"name": "uniqueId", "description": "列表项的唯一标识符，通常来自数据，如数据库ID"}], "returnValue": "无返回值，key是一个特殊属性，不会传递给组件", "examples": [{"code": "// key的基本使用\nimport React from 'react';\n\nfunction TodoList() {\n  const todos = [\n    { id: 1, text: '学习React' },\n    { id: 2, text: '掌握Redux' },\n    { id: 3, text: '探索Next.js' }\n  ];\n  \n  return (\n    <ul>\n      {todos.map(todo => (\n        // 使用数据中的唯一ID作为key\n        <li key={todo.id}>\n          {todo.text}\n        </li>\n      ))}\n    </ul>\n  );\n}\n\n// key帮助React优化重新渲染性能\nfunction DynamicList({ items }) {\n  return (\n    <ul>\n      {items.map(item => (\n        <li key={item.id}>\n          {item.text}\n          <button>编辑</button>\n          <button>删除</button>\n        </li>\n      ))}\n    </ul>\n  );\n  // 当items数组改变时，React使用key快速确定：\n  // - 哪些元素是新的（需要创建）\n  // - 哪些元素被移除（需要删除）\n  // - 哪些元素改变了位置（需要移动）\n}\n\n// 不正确的key使用可能导致组件状态问题\nfunction StatefulList({ items }) {\n  return (\n    <ul>\n      {items.map(item => (\n        // 如果不使用唯一的key，当列表重新排序时，\n        // 组件的状态可能会出错\n        <ListItemWithState \n          key={item.id} // 正确：使用唯一ID\n          item={item} \n        />\n      ))}\n    </ul>\n  );\n}\n\n// 当没有唯一ID时的替代方案\nfunction SimpleList() {\n  const items = ['学习', '复习', '练习'];\n  \n  return (\n    <ul>\n      {items.map((item, index) => (\n        // 当列表是静态的（不会重新排序、过滤或插入项目）时，\n        // 使用索引作为key是可以接受的\n        <li key={index}>{item}</li>\n      ))}\n    </ul>\n  );\n}\n\n// key必须在上下文中唯一，而不是全局唯一\nfunction NestedLists() {\n  const categories = [\n    { id: 'fruits', name: '水果', items: ['苹果', '香蕉', '橙子'] },\n    { id: 'vegetables', name: '蔬菜', items: ['胡萝卜', '西红柿', '黄瓜'] }\n  ];\n  \n  return (\n    <div>\n      {categories.map(category => (\n        // 外部列表项使用category.id作为key\n        <div key={category.id}>\n          <h3>{category.name}</h3>\n          <ul>\n            {category.items.map((item, index) => (\n              // 内部列表项可以使用索引作为key，\n              // 因为它们只需要在各自的列表中唯一\n              <li key={index}>{item}</li>\n            ))}\n          </ul>\n        </div>\n      ))}\n    </div>\n  );\n}\n\n// key不会传递给组件\nfunction Item({ name, isSelected }) {\n  // 这里无法访问key属性\n  return (\n    <li className={isSelected ? 'selected' : ''}>\n      {name}\n    </li>\n  );\n}\n\nfunction ItemList({ items, selectedId }) {\n  return (\n    <ul>\n      {items.map(item => (\n        <Item\n          key={item.id} // 这个key不会作为prop传递给Item组件\n          name={item.name}\n          isSelected={item.id === selectedId}\n        />\n      ))}\n    </ul>\n  );\n}", "explanation": "这些例子展示了React中key的作用和重要性。key是React用于跟踪列表项的特殊属性，它帮助React高效更新DOM。当列表项顺序变化、添加或删除时，key使React能够准确识别每个元素，而不是重新创建所有元素。使用唯一且稳定的标识符（如数据库ID）作为key是最佳实践。key必须在兄弟元素之间唯一，但不需要全局唯一。key不会作为prop传递给组件，它纯粹是给React内部使用的。正确使用key对于列表渲染性能和避免状态问题至关重要。"}]}}, {"name": "Proper Key Usage", "trans": ["正确的key使用方式"], "usage": {"syntax": "<Element key={properUniqueIdentifier} ... />", "description": "在React中正确使用key是保证应用性能和避免潜在bug的重要因素。本节介绍如何选择和应用合适的key，以及使用key时的最佳实践。", "parameters": [{"name": "properUniqueIdentifier", "description": "适当的唯一标识符，遵循React key的最佳实践"}], "returnValue": "无返回值，key是React内部使用的特殊属性", "examples": [{"code": "// 最佳实践1: 使用唯一且稳定的标识符作为key\nimport React from 'react';\n\nfunction BestPracticeList() {\n  const users = [\n    { id: 'a42b', name: '张三', role: '开发者' },\n    { id: 'c7d9', name: '李四', role: '设计师' },\n    { id: 'e63f', name: '王五', role: '产品经理' }\n  ];\n  \n  return (\n    <ul>\n      {users.map(user => (\n        // 最佳实践: 使用来自数据的唯一ID\n        <li key={user.id}>\n          {user.name} - {user.role}\n        </li>\n      ))}\n    </ul>\n  );\n}\n\n// 最佳实践2: 当没有唯一ID时，创建稳定的标识符\nfunction GenerateKeyExample() {\n  const items = ['React', 'Vue', 'Angular'];\n  \n  return (\n    <ul>\n      {items.map(item => {\n        // 为没有ID的项目创建一个稳定的key\n        // 这比使用索引更好，因为它基于内容\n        const stableKey = `item-${item.toLowerCase().replace(/\\s+/g, '-')}`;\n        return <li key={stableKey}>{item}</li>;\n      })}\n    </ul>\n  );\n}\n\n// 最佳实践3: key应该在兄弟元素间唯一\nfunction SiblingKeysExample() {\n  const categories = [\n    { id: 'frontend', name: '前端技术', tools: ['React', 'Vue', 'Angular'] },\n    { id: 'backend', name: '后端技术', tools: ['Node.js', 'Django', 'Spring'] }\n  ];\n  \n  return (\n    <div>\n      {categories.map(category => (\n        // 每个category的key在其兄弟元素间唯一\n        <section key={category.id}>\n          <h2>{category.name}</h2>\n          <ul>\n            {category.tools.map(tool => {\n              // 对于每个工具列表，key只需要在当前工具列表中唯一\n              const toolKey = `${category.id}-${tool.toLowerCase()}`;\n              return <li key={toolKey}>{tool}</li>;\n            })}\n          </ul>\n        </section>\n      ))}\n    </div>\n  );\n}\n\n// 最佳实践4: 不要在运行时生成key\nfunction BadPracticeExample() {\n  const items = ['苹果', '香蕉', '橙子'];\n  let counter = 0;\n  \n  return (\n    <ul>\n      {items.map(item => (\n        // 错误: 使用随机数或递增计数器作为key\n        // 这会导致不必要的重新渲染\n        <li key={Math.random()}>{item}</li> // 不要这样做!\n        // 或\n        // <li key={counter++}>{item}</li> // 也不要这样做!\n      ))}\n    </ul>\n  );\n}\n\n// 最佳实践5: 在组件之间移动列表项时保持key的一致性\nfunction ListWithMovableItems() {\n  const [activeItems, setActiveItems] = React.useState([\n    { id: 1, text: '任务1' },\n    { id: 2, text: '任务2' }\n  ]);\n  const [completedItems, setCompletedItems] = React.useState([\n    { id: 3, text: '任务3' }\n  ]);\n  \n  function completeItem(id) {\n    const item = activeItems.find(item => item.id === id);\n    setActiveItems(activeItems.filter(item => item.id !== id));\n    setCompletedItems([...completedItems, item]);\n  }\n  \n  return (\n    <div>\n      <h2>进行中</h2>\n      <ul>\n        {activeItems.map(item => (\n          // 当项目从一个列表移动到另一个列表时，保持相同的key\n          <li key={item.id}>\n            {item.text}\n            <button onClick={() => completeItem(item.id)}>完成</button>\n          </li>\n        ))}\n      </ul>\n      \n      <h2>已完成</h2>\n      <ul>\n        {completedItems.map(item => (\n          // 相同的项目在不同列表中使用相同的key\n          <li key={item.id}>\n            {item.text} ✓\n          </li>\n        ))}\n      </ul>\n    </div>\n  );\n}\n\n// 最佳实践6: 在表单中的列表项中正确处理key\nfunction DynamicForm() {\n  const [inputs, setInputs] = React.useState([\n    { id: 'field-1', value: '' },\n    { id: 'field-2', value: '' }\n  ]);\n  \n  function handleChange(id, newValue) {\n    setInputs(inputs.map(input => \n      input.id === id ? { ...input, value: newValue } : input\n    ));\n  }\n  \n  function addInput() {\n    // 为新字段创建唯一ID\n    const newId = `field-${inputs.length + 1}`;\n    setInputs([...inputs, { id: newId, value: '' }]);\n  }\n  \n  return (\n    <form>\n      {inputs.map(input => (\n        // 表单字段使用稳定的ID作为key\n        <div key={input.id}>\n          <label htmlFor={input.id}>字段 {input.id}:</label>\n          <input\n            id={input.id}\n            value={input.value}\n            onChange={(e) => handleChange(input.id, e.target.value)}\n          />\n        </div>\n      ))}\n      <button type=\"button\" onClick={addInput}>添加字段</button>\n    </form>\n  );\n}", "explanation": "这些例子展示了React中正确使用key的最佳实践：\n\n1. 使用来自数据的唯一且稳定的标识符（如ID）作为key\n2. 当数据没有ID时，可以基于内容创建稳定的唯一标识符\n3. key只需要在兄弟元素之间唯一，不需要全局唯一\n4. 避免在渲染过程中生成key，如使用Math.random()或递增计数器\n5. 当列表项在不同组件或列表之间移动时，应保持key的一致性\n6. 在动态表单中，每个字段应有稳定的唯一key\n\n正确使用key可以帮助React高效更新DOM，避免不必要的重新渲染，并防止与组件状态相关的bug。在处理列表渲染时，选择适当的key是一个关键决策。"}]}}, {"name": "Index as Key Problem", "trans": ["索引作为key的问题"], "usage": {"syntax": "array.map((item, index) => <Element key={index} ... />)", "description": "在React中，使用数组索引作为key虽然简单，但通常不是最佳选择。只有在列表项是静态的、不会被重新排序、插入或删除时，才可以使用索引作为key。否则，使用索引会导致组件状态错乱、性能下降等问题。", "parameters": [{"name": "item", "description": "当前数组项"}, {"name": "index", "description": "当前项的索引，作为key使用"}, {"name": "Element", "description": "渲染的React元素或组件"}], "returnValue": "React元素数组，每个元素的key为其索引值", "examples": [{"code": "// 错误示例：动态列表使用索引作为key\nimport React, { useState } from 'react';\n\nfunction BadKeyList() {\n  const [items, setItems] = useState(['A', 'B', 'C']);\n\n  function removeFirst() {\n    setItems(items.slice(1));\n  }\n\n  return (\n    <div>\n      <button onClick={removeFirst}>移除第一个</button>\n      <ul>\n        {items.map((item, index) => (\n          // 不推荐：使用索引作为key\n          <li key={index}>{item}</li>\n        ))}\n      </ul>\n    </div>\n  );\n}\n\n// 正确示例：使用唯一ID作为key\nfunction GoodKeyList() {\n  const [items, setItems] = useState([\n    { id: 1, text: 'A' },\n    { id: 2, text: 'B' },\n    { id: 3, text: 'C' }\n  ]);\n\n  function removeFirst() {\n    setItems(items.slice(1));\n  }\n\n  return (\n    <div>\n      <button onClick={removeFirst}>移除第一个</button>\n      <ul>\n        {items.map(item => (\n          // 推荐：使用唯一ID作为key\n          <li key={item.id}>{item.text}</li>\n        ))}\n      </ul>\n    </div>\n  );\n}", "explanation": "第一个例子展示了使用索引作为key时，移除第一个元素会导致剩余元素的key发生变化，可能引发组件状态错乱。第二个例子使用唯一ID作为key，能保证每个元素的身份始终一致，避免此类问题。"}]}}, {"name": "Extracting List Item Components", "trans": ["列表项组件提取"], "usage": {"syntax": "function ListItem(props) {\n  return <li>{props.value}</li>;\n}\n\narray.map(item => <ListItem key={item.id} value={item.text} />)", "description": "将列表项提取为独立的组件有助于代码复用、结构清晰和易于维护。每个列表项组件应接收必要的props，并在父组件中通过map()渲染。这样可以让每个列表项拥有自己的逻辑和样式。", "parameters": [{"name": "props", "description": "传递给列表项组件的数据对象"}, {"name": "item", "description": "当前数组项"}], "returnValue": "React元素数组，每个元素为独立的列表项组件", "examples": [{"code": "// 提取列表项为独立组件\nimport React from 'react';\n\nfunction ListItem({ value }) {\n  // 可以在这里添加更多逻辑或样式\n  return <li>{value}</li>;\n}\n\nfunction ItemList({ items }) {\n  return (\n    <ul>\n      {items.map(item => (\n        <ListItem key={item.id} value={item.text} />\n      ))}\n    </ul>\n  );\n}\n\n// 支持复杂内容的列表项组件\nfunction UserItem({ user }) {\n  return (\n    <li>\n      <strong>{user.name}</strong> - {user.role}\n    </li>\n  );\n}\n\nfunction UserList({ users }) {\n  return (\n    <ul>\n      {users.map(user => (\n        <UserItem key={user.id} user={user} />\n      ))}\n    </ul>\n  );\n}", "explanation": "第一个例子将简单的<li>提取为ListItem组件，便于复用和扩展。第二个例子展示了更复杂的UserItem组件，可以处理更丰富的数据和样式。通过组件化，列表渲染更灵活、可维护性更高。"}]}}, {"name": "List Performance Optimization", "trans": ["列表性能优化"], "usage": {"syntax": "// 使用React.memo包裹列表项组件\nconst MemoizedItem = React.memo(ListItem);\n\n// 在map中渲染\nitems.map(item => <MemoizedItem key={item.id} value={item.text} />)", "description": "在渲染大型或频繁更新的列表时，性能优化非常重要。可以通过React.memo避免不必要的子组件重新渲染，或使用虚拟化技术（如react-window、react-virtualized）只渲染可见区域的列表项，从而提升性能。", "parameters": [{"name": "ListItem", "description": "要优化的列表项组件"}, {"name": "items", "description": "要渲染的数据数组"}], "returnValue": "优化后的React元素数组，减少不必要的渲染", "examples": [{"code": "// 使用React.memo优化列表项\nimport React from 'react';\n\nconst ListItem = React.memo(function ListItem({ value }) {\n  // 只有当props.value变化时才会重新渲染\n  return <li>{value}</li>;\n});\n\nfunction OptimizedList({ items }) {\n  return (\n    <ul>\n      {items.map(item => (\n        <ListItem key={item.id} value={item.text} />\n      ))}\n    </ul>\n  );\n}\n\n// 使用react-window进行虚拟化渲染\nimport { FixedSizeList as VirtualList } from 'react-window';\n\nfunction Row({ index, style, data }) {\n  const item = data[index];\n  return (\n    <div style={style}>\n      {item.text}\n    </div>\n  );\n}\n\nfunction VirtualizedList({ items }) {\n  return (\n    <VirtualList\n      height={300}\n      itemCount={items.length}\n      itemSize={35}\n      width={400}\n      itemData={items}\n    >\n      {Row}\n    </VirtualList>\n  );\n}", "explanation": "第一个例子使用React.memo包裹列表项组件，只有当props变化时才会重新渲染，适合数据量大或频繁更新的场景。第二个例子使用react-window库实现虚拟化渲染，只渲染可见区域的列表项，大幅提升超长列表的性能。"}]}}, {"name": "Assignment: 列表与Keys练习", "trans": ["作业：列表与Keys练习"], "usage": {"syntax": "// 作业要求见description", "description": "1. 创建一个React组件，接收一个对象数组（如用户列表）作为props，使用map()渲染为表格或列表。\n2. 正确为每个列表项分配唯一的key。\n3. 尝试将列表项提取为独立组件。\n4. 修改数据顺序，观察key的作用。\n5. （进阶）尝试用React.memo或react-window优化性能。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 示例：用户列表作业\nimport React from 'react';\n\nfunction UserItem({ user }) {\n  return (\n    <li>\n      {user.name} - {user.email}\n    </li>\n  );\n}\n\nfunction UserList({ users }) {\n  return (\n    <ul>\n      {users.map(user => (\n        <UserItem key={user.id} user={user} />\n      ))}\n    </ul>\n  );\n}\n\n// 作业：\n// 1. 尝试将UserList改为表格形式\n// 2. 尝试打乱users顺序，观察渲染效果\n// 3. 用React.memo包裹UserItem，体会性能变化", "explanation": "本作业要求你综合运用本节知识，练习列表渲染、key分配、组件提取和性能优化。通过动手实践，加深对React列表与Keys机制的理解。"}]}}]}