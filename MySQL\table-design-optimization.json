{"name": "Table Design Optimization", "trans": ["表设计优化"], "methods": [{"name": "Normalization and Denormalization", "trans": ["规范化与反规范化"], "usage": {"syntax": "-- 规范化示例\nCREATE TABLE 表名1 (...);\nCREATE TABLE 表名2 (...);", "description": "规范化是将数据分解到多个表中以减少冗余和提高数据一致性的过程，而反规范化则是有意引入冗余以提高查询性能。设计数据库时需要在两者之间取得平衡。", "parameters": [{"name": "表名1, 表名2, ...", "description": "规范化或反规范化设计的表名"}], "returnValue": "无返回值，仅为设计示例", "examples": [{"code": "-- 规范化设计（第三范式）\n-- 客户表\nCREATE TABLE customers (\n  customer_id INT PRIMARY KEY,\n  name VARCHAR(100),\n  email VARCHAR(100),\n  phone VARCHAR(20)\n);\n\n-- 地址表\nCREATE TABLE addresses (\n  address_id INT PRIMARY KEY,\n  customer_id INT,\n  address_type VARCHAR(20), -- 'shipping' 或 'billing'\n  street VARCHAR(200),\n  city VARCHAR(100),\n  state VARCHAR(50),\n  postal_code VARCHAR(20),\n  country VARCHAR(50),\n  FOREIGN KEY (customer_id) REFERENCES customers(customer_id)\n);\n\n-- 反规范化设计（为提高查询性能）\nCREATE TABLE customers_denormalized (\n  customer_id INT PRIMARY KEY,\n  name VARCHAR(100),\n  email VARCHAR(100),\n  phone VARCHAR(20),\n  shipping_street VARCHAR(200),\n  shipping_city VARCHAR(100),\n  shipping_state VARCHAR(50),\n  shipping_postal_code VARCHAR(20),\n  shipping_country VARCHAR(50),\n  billing_street VARCHAR(200),\n  billing_city VARCHAR(100),\n  billing_state VARCHAR(50),\n  billing_postal_code VARCHAR(20),\n  billing_country VARCHAR(50)\n);", "explanation": "规范化设计将客户信息和地址信息分开存储，减少了数据冗余，但需要连接查询；反规范化设计将所有信息存储在一个表中，提高了查询性能但增加了数据冗余和更新复杂性。"}]}}, {"name": "Data Type Selection", "trans": ["合理选择数据类型"], "usage": {"syntax": "CREATE TABLE 表名 (\n  列名1 数据类型1,\n  列名2 数据类型2,\n  ...\n);", "description": "合理选择数据类型对表的存储空间、查询性能和数据完整性有重要影响。应根据数据的实际需求选择最合适的类型，避免过度分配空间。", "parameters": [{"name": "表名", "description": "要创建的表名"}, {"name": "列名", "description": "表的字段名"}, {"name": "数据类型", "description": "字段的数据类型"}], "returnValue": "无返回值，仅为设计示例", "examples": [{"code": "-- 不良设计：数据类型过大\nCREATE TABLE products_poor (\n  id BIGINT PRIMARY KEY, -- 过大，INT通常足够\n  name VARCHAR(255),     -- 过大，实际产品名可能不超过100\n  price DECIMAL(10,2),   -- 合理\n  description TEXT,      -- 合理\n  created_at DATETIME,   -- 合理\n  is_active TINYINT      -- 不合理，用于布尔值应使用TINYINT(1)\n);\n\n-- 优化设计：合理选择数据类型\nCREATE TABLE products_optimized (\n  id INT UNSIGNED PRIMARY KEY, -- 无符号INT，足够大多数应用\n  name VARCHAR(100),           -- 更合理的长度\n  price DECIMAL(10,2),         -- 合理\n  description TEXT,            -- 合理\n  created_at DATETIME,         -- 合理\n  is_active BOOLEAN            -- 使用BOOLEAN（实际是TINYINT(1)的别名）\n);\n\n-- 针对大量数值型ID的优化\nCREATE TABLE large_table (\n  id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT, -- 无符号INT\n  reference_id CHAR(36),                      -- UUID存储\n  small_number TINYINT UNSIGNED,              -- 0-255范围的小数值\n  status ENUM('pending', 'active', 'deleted') -- 使用ENUM代替VARCHAR\n);", "explanation": "优化后的表使用了更合适的数据类型：使用无符号整型节省空间，调整VARCHAR长度匹配实际需求，对状态字段使用ENUM类型，对布尔值使用BOOLEAN类型。这些优化可以减少存储空间并提高查询性能。"}]}}, {"name": "Partitioned Tables", "trans": ["分区表"], "usage": {"syntax": "CREATE TABLE 表名 (\n  ...\n)\nPARTITION BY 分区类型 (分区表达式)\n(PARTITION 分区名 VALUES 分区值定义, ...);", "description": "分区表是将大表分成更小的、更易管理的部分的技术。MySQL支持多种分区类型，包括RANGE、LIST、HASH和KEY分区。分区可以提高查询性能、简化数据管理并支持更大的表。", "parameters": [{"name": "表名", "description": "要创建的分区表名"}, {"name": "分区类型", "description": "分区的类型，如RANGE、LIST、HASH或KEY"}, {"name": "分区表达式", "description": "用于确定数据分区的表达式或列"}, {"name": "分区名", "description": "分区的名称"}, {"name": "分区值定义", "description": "定义分区包含的值范围或列表"}], "returnValue": "无返回值，仅为设计示例", "examples": [{"code": "-- 按日期范围分区的订单表\nCREATE TABLE orders (\n  order_id INT NOT NULL,\n  customer_id INT,\n  order_date DATE,\n  total_amount DECIMAL(10,2),\n  PRIMARY KEY (order_id, order_date)\n)\nPARTITION BY RANGE (YEAR(order_date)) (\n  PARTITION p2020 VALUES LESS THAN (2021),\n  PARTITION p2021 VALUES LESS THAN (2022),\n  PARTITION p2022 VALUES LESS THAN (2023),\n  PARTITION p2023 VALUES LESS THAN (2024),\n  PARTITION future VALUES LESS THAN MAXVALUE\n);\n\n-- 按散列值分区的用户表\nCREATE TABLE users (\n  user_id INT NOT NULL,\n  username VARCHAR(50),\n  email VARCHAR(100),\n  created_at DATETIME,\n  PRIMARY KEY (user_id)\n)\nPARTITION BY HASH(user_id)\nPARTITIONS 4;\n\n-- 按列表分区的区域销售表\nCREATE TABLE regional_sales (\n  sale_id INT NOT NULL,\n  sale_date DATE,\n  product_id INT,\n  region VARCHAR(50),\n  amount DECIMAL(10,2),\n  PRIMARY KEY (sale_id, region)\n)\nPARTITION BY LIST COLUMNS(region) (\n  PARTITION p_east VALUES IN ('East', 'Northeast'),\n  PARTITION p_west VALUES IN ('West', 'Northwest'),\n  PARTITION p_south VALUES IN ('South', 'Southeast'),\n  PARTITION p_north VALUES IN ('North', 'Central'),\n  PARTITION p_others VALUES IN ('Other', 'Unknown')\n);", "explanation": "第一个例子按年份对订单表进行RANGE分区；第二个例子按用户ID的散列值对用户表进行HASH分区；第三个例子按区域名称对销售表进行LIST分区。分区表可以提高大表的查询性能，特别是当查询条件包含分区键时。"}]}}, {"name": "Vertical Partitioning", "trans": ["垂直分区"], "usage": {"syntax": "-- 将一个宽表拆分为多个表\nCREATE TABLE 核心表 (...); \nCREATE TABLE 扩展表 (...);", "description": "垂直分区是将表中的列分割到不同表中的技术，通常将经常访问的列放在一个表中，而将不常用的大字段放在另一个表中。这种设计可以提高查询性能和缓存效率。", "parameters": [{"name": "核心表", "description": "包含频繁访问列的表"}, {"name": "扩展表", "description": "包含不常用或大字段的表"}], "returnValue": "无返回值，仅为设计示例", "examples": [{"code": "-- 原始宽表设计\nCREATE TABLE articles_wide (\n  article_id INT PRIMARY KEY,\n  title VARCHAR(200),\n  author_id INT,\n  publish_date DATE,\n  category VARCHAR(50),\n  summary VARCHAR(500),\n  content TEXT,        -- 大文本字段\n  metadata JSON,       -- 大JSON字段\n  full_text LONGTEXT   -- 非常大的文本\n);\n\n-- 垂直分区后的设计\n-- 核心表：包含常用字段\nCREATE TABLE articles_core (\n  article_id INT PRIMARY KEY,\n  title VARCHAR(200),\n  author_id INT,\n  publish_date DATE,\n  category VARCHAR(50),\n  summary VARCHAR(500)\n);\n\n-- 扩展表：包含大字段\nCREATE TABLE articles_content (\n  article_id INT PRIMARY KEY,\n  content TEXT,\n  metadata JSON,\n  full_text LONGTEXT,\n  FOREIGN KEY (article_id) REFERENCES articles_core(article_id)\n);\n\n-- 查询常用信息（只需访问核心表）\nSELECT title, author_id, publish_date, summary \nFROM articles_core \nWHERE publish_date > '2023-01-01';\n\n-- 查询完整文章（需要连接两个表）\nSELECT c.title, c.summary, e.content \nFROM articles_core c\nJOIN articles_content e ON c.article_id = e.article_id\nWHERE c.article_id = 123;", "explanation": "这个例子将一个包含多个大字段的文章表拆分为核心表和内容表。核心表包含频繁查询的元数据，而内容表包含较大的文本字段。这种设计可以提高常用查询的性能，因为它们只需要访问较小的核心表。"}]}}, {"name": "Table Design Optimization Assignment", "trans": ["表设计优化练习"], "usage": {"syntax": "# 表设计优化练习", "description": "完成以下练习，巩固MySQL表设计优化的基本技术。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n假设你正在为一个电子商务网站设计数据库。需要存储以下信息：\n- 用户信息（ID、姓名、邮箱、电话、注册日期等）\n- 用户地址（多个地址，包括收货地址和账单地址）\n- 产品信息（ID、名称、描述、价格、库存、类别等）\n- 产品详情（详细描述、规格参数、多张图片URL等）\n- 订单信息（ID、用户ID、订单日期、总金额、状态等）\n- 订单项目（订单ID、产品ID、数量、单价等）\n- 历史订单数据（需要保存5年的订单记录）\n\n请完成以下任务：\n\n1. 设计规范化的表结构，至少包含6个表，并说明每个表的主键和外键。\n\n2. 针对产品表，选择合适的数据类型，特别是考虑产品名称、价格和库存字段。\n\n3. 设计一个分区方案，对订单表按年份进行分区，以便高效管理历史订单数据。\n\n4. 应用垂直分区原则，将产品表拆分为核心产品信息表和产品详情表。\n\n5. 考虑是否有需要进行反规范化设计的场景，并解释原因。", "explanation": "通过这个练习，你将学习如何应用规范化、选择合适的数据类型、设计分区表以及使用垂直分区来优化表设计，提高数据库性能和可维护性。"}]}}]}