{"name": "Microservice Communication", "trans": ["微服务通信"], "methods": [{"name": "RestTemplate", "trans": ["RestTemplate客户端"], "usage": {"syntax": "RestTemplate restTemplate = new RestTemplate();\nResponseEntity<T> response = restTemplate.exchange(url, HttpMethod, HttpEntity, responseType);", "description": "RestTemplate是Spring提供的同步HTTP客户端，用于服务间调用，支持各种HTTP方法、请求头管理和响应处理。", "parameters": [{"name": "url", "description": "请求地址，支持URI模板变量"}, {"name": "HttpMethod", "description": "HTTP方法：GET、POST、PUT、DELETE等"}, {"name": "HttpEntity", "description": "包含请求头和请求体的实体对象"}, {"name": "responseType", "description": "响应类型的Class对象"}], "returnValue": "ResponseEntity对象，包含响应状态、响应头和响应体", "examples": [{"code": "// 1. 创建RestTemplate实例\nRestTemplate restTemplate = new RestTemplate();\n\n// 2. 简单GET请求\nUser user = restTemplate.getForObject(\"http://api.example.com/users/{id}\", User.class, 1);\n\n// 3. 带请求头的POST请求\nHttpHeaders headers = new HttpHeaders();\nheaders.setContentType(MediaType.APPLICATION_JSON);\n\nUserDTO userDTO = new UserDTO(\"张三\", \"<EMAIL>\");\nHttpEntity<UserDTO> requestEntity = new HttpEntity<>(userDTO, headers);\n\nResponseEntity<User> response = restTemplate.exchange(\n    \"http://api.example.com/users\",\n    HttpMethod.POST,\n    requestEntity,\n    User.class\n);\n\n// 4. 检查响应状态\nif (response.getStatusCode().is2xxSuccessful()) {\n    User createdUser = response.getBody();\n    System.out.println(\"用户创建成功: \" + createdUser.getId());\n}", "explanation": "展示了RestTemplate常见用法，包括简单GET请求、带请求头的POST请求及响应处理。"}]}}, {"name": "WebClient", "trans": ["WebClient响应式客户端"], "usage": {"syntax": "WebClient webClient = WebClient.create(baseUrl);\nMono<T> response = webClient.method(HttpMethod).uri(uriPath).bodyValue(body).retrieve().bodyToMono(responseType);", "description": "WebClient是Spring 5引入的非阻塞响应式HTTP客户端，支持异步请求和响应式流，适合高并发场景。", "parameters": [{"name": "baseUrl", "description": "基础URL地址"}, {"name": "HttpMethod", "description": "HTTP方法：GET、POST、PUT等"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "请求路径，支持URI模板"}, {"name": "body", "description": "请求体对象"}, {"name": "responseType", "description": "响应类型的Class对象"}], "returnValue": "Mono或Flux对象，表示异步响应结果", "examples": [{"code": "// 1. 创建WebClient实例\nWebClient webClient = WebClient.builder()\n    .baseUrl(\"http://api.example.com\")\n    .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)\n    .build();\n\n// 2. 发送GET请求\nMono<User> userMono = webClient.get()\n    .uri(\"/users/{id}\", 1)\n    .retrieve()\n    .bodyToMono(User.class);\n\n// 3. 处理响应（响应式方式）\nuserMono.subscribe(user -> {\n    System.out.println(\"用户名: \" + user.getName());\n}, error -> {\n    System.err.println(\"请求失败: \" + error.getMessage());\n});\n\n// 4. 发送POST请求\nUserDTO newUser = new UserDTO(\"李四\", \"<EMAIL>\");\n\nwebClient.post()\n    .uri(\"/users\")\n    .bodyValue(newUser)\n    .retrieve()\n    .bodyToMono(User.class)\n    .subscribe(createdUser -> {\n        System.out.println(\"用户创建成功: \" + createdUser.getId());\n    });", "explanation": "展示了WebClient的基本用法，包括创建实例、发送GET/POST请求和响应式处理响应。"}]}}, {"name": "Service Communication Best Practices", "trans": ["服务间通信最佳实践"], "usage": {"syntax": "配置超时、重试、熔断、负载均衡等机制", "description": "微服务通信中应遵循一系列最佳实践，包括超时控制、重试策略、熔断机制、负载均衡等，以提高系统可靠性和韧性。", "parameters": [{"name": "超时设置", "description": "防止长时间阻塞"}, {"name": "重试策略", "description": "应对临时故障"}, {"name": "熔断机制", "description": "防止雪崩效应"}, {"name": "负载均衡", "description": "分散请求到多个实例"}], "returnValue": "高可用、高韧性的服务通信", "examples": [{"code": "// 1. RestTemplate配置超时\nRestTemplate restTemplate = new RestTemplateBuilder()\n    .setConnectTimeout(Duration.ofSeconds(3))  // 连接超时3秒\n    .setReadTimeout(Duration.ofSeconds(5))    // 读取超时5秒\n    .build();\n\n// 2. 使用Resilience4j实现重试\n@Bean\npublic RetryRegistry retryRegistry() {\n    RetryConfig config = RetryConfig.custom()\n        .maxAttempts(3)  // 最多重试3次\n        .waitDuration(Duration.ofMillis(500))  // 重试间隔500ms\n        .retryExceptions(SocketTimeoutException.class)  // 针对超时异常重试\n        .build();\n    return RetryRegistry.of(config);\n}\n\n// 3. 使用Resilience4j实现熔断\n@Bean\npublic CircuitBreakerRegistry circuitBreakerRegistry() {\n    CircuitBreakerConfig config = CircuitBreakerConfig.custom()\n        .failureRateThreshold(50)  // 50%失败率触发熔断\n        .waitDurationInOpenState(Duration.ofSeconds(10))  // 熔断10秒后尝试恢复\n        .build();\n    return CircuitBreakerRegistry.of(config);\n}\n\n// 4. 使用负载均衡的RestTemplate\n@Bean\n@LoadBalanced  // 启用负载均衡\npublic RestTemplate loadBalancedRestTemplate() {\n    return new RestTemplate();\n}\n\n// 5. 使用服务发现调用服务（以Eureka为例）\nloadBalancedRestTemplate.getForObject(\n    \"http://user-service/users/{id}\",  // 使用服务名而非具体IP:端口\n    User.class,\n    1\n);", "explanation": "展示了微服务通信中的多种最佳实践实现，包括超时控制、重试、熔断和负载均衡等。"}]}}, {"name": "Feign Client", "trans": ["Feign声明式客户端"], "usage": {"syntax": "@FeignClient(name = \"service-name\")\npublic interface UserClient { @GetMapping(\"/users/{id}\") User getUser(@PathVariable(\"id\") Long id); }", "description": "Feign是声明式REST客户端，通过接口定义和注解简化服务调用，支持负载均衡、重试等机制，常用于Spring Cloud生态。", "parameters": [{"name": "name", "description": "目标服务名称"}, {"name": "url", "description": "直接指定服务地址（可选）"}, {"name": "fallback", "description": "服务降级实现类（可选）"}], "returnValue": "根据接口方法定义的返回类型", "examples": [{"code": "// 1. 添加Feign依赖\n// build.gradle\n// implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'\n\n// 2. 启用Feign客户端\n@SpringBootApplication\n@EnableFeignClients  // 启用Feign\npublic class Application {\n    public static void main(String[] args) {\n        SpringApplication.run(Application.class, args);\n    }\n}\n\n// 3. 定义Feign客户端接口\n@FeignClient(name = \"user-service\")  // 服务名称\npublic interface UserClient {\n    \n    @GetMapping(\"/users/{id}\")\n    User getUser(@PathVariable(\"id\") Long id);\n    \n    @PostMapping(\"/users\")\n    User createUser(@RequestBody UserDTO user);\n}\n\n// 4. 使用Feign客户端\n@Service\npublic class UserService {\n    \n    private final UserClient userClient;\n    \n    public UserService(UserClient userClient) {\n        this.userClient = userClient;\n    }\n    \n    public User getUserById(Long id) {\n        return userClient.getUser(id);  // 像调用本地方法一样\n    }\n}", "explanation": "展示了Feign客户端的定义和使用方式，实现了声明式的服务调用。"}]}}, {"name": "Microservice Communication Assignment", "trans": ["微服务通信练习"], "usage": {"syntax": "# 微服务通信练习", "description": "完成以下练习，巩固Spring Boot微服务通信相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 使用RestTemplate实现一个服务调用，包含GET和POST请求，并处理异常情况。\n2. 使用WebClient实现同样的服务调用，对比与RestTemplate的差异。\n3. 为RestTemplate配置超时、重试和负载均衡。\n4. 使用Feign客户端实现一个声明式服务接口并调用。\n5. 实现一个简单的服务降级机制，当目标服务不可用时返回默认结果。", "explanation": "通过这些练习掌握Spring Boot微服务通信的核心用法和最佳实践。"}]}}]}