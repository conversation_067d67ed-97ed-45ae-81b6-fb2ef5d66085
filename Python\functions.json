{"name": "Functions", "trans": ["函数基础"], "methods": [{"name": "Function Definition and Call", "trans": ["函数定义与调用"], "usage": {"syntax": "def 函数名(参数):\n    代码块\n\n函数名(参数)", "description": "使用def关键字定义函数，调用时传递参数。函数体需缩进。", "parameters": [{"name": "参数", "description": "函数的输入参数，可有多个或无"}], "returnValue": "函数的返回值，若无return则为None", "examples": [{"code": "def greet(name):\n    print('Hello,', name)\ngreet('<PERSON>')  # 输出Hello, <PERSON>", "explanation": "演示了函数的定义和调用。"}]}}, {"name": "Parameter Types (Positional, Keyword, Default, Variable) ", "trans": ["参数类型（位置、关键字、默认、可变）"], "usage": {"syntax": "def func(a, b=2, *args, **kwargs):\n    pass\nfunc(1)\nfunc(1, 3)\nfunc(1, 2, 3, 4, x=5)", "description": "参数分为位置参数、默认参数、可变参数(*args)、关键字参数(**kwargs)。调用时可按顺序或指定参数名。", "parameters": [{"name": "a", "description": "位置参数"}, {"name": "b", "description": "默认参数"}, {"name": "*args", "description": "可变参数，接收多个位置参数"}, {"name": "**kwargs", "description": "关键字参数，接收多个键值对"}], "returnValue": "函数的返回值，若无return则为None", "examples": [{"code": "def demo(a, b=2, *args, **kwargs):\n    print(a, b, args, kwargs)\ndemo(1)\ndemo(1, 3)\ndemo(1, 2, 3, 4, x=5)\n# 输出：\n# 1 2 () {}\n# 1 3 () {}\n# 1 2 (3, 4) {'x': 5}", "explanation": "演示了不同类型参数的定义和调用。"}]}}, {"name": "Return Value", "trans": ["返回值"], "usage": {"syntax": "return 表达式", "description": "return语句用于函数返回结果。若无return或return后无表达式，返回None。", "parameters": [{"name": "表达式", "description": "要返回的值，可省略"}], "returnValue": "返回的值或None", "examples": [{"code": "def add(a, b):\n    return a + b\nresult = add(2, 3)\nprint(result)  # 5", "explanation": "演示了return语句的用法。"}]}}, {"name": "Variable Scope (LEGB Rule)", "trans": ["变量作用域（LEGB规则）"], "usage": {"syntax": "局部 > 嵌套 > 全局 > 内建", "description": "LEGB规则：Local(局部)、Enclosing(嵌套)、Global(全局)、Built-in(内建)依次查找变量名。", "parameters": [], "returnValue": "变量的作用域说明", "examples": [{"code": "x = 'global'\ndef outer():\n    x = 'enclosing'\n    def inner():\n        x = 'local'\n        print(x)\n    inner()\nouter()  # 输出local", "explanation": "演示了LEGB作用域查找顺序。"}]}}, {"name": "global and nonlocal", "trans": ["global与nonlocal"], "usage": {"syntax": "global 变量名\nnonlocal 变量名", "description": "global声明全局变量，nonlocal声明外层非全局变量。用于在函数内部修改外部变量。", "parameters": [{"name": "变量名", "description": "要声明的变量名"}], "returnValue": "无返回值", "examples": [{"code": "x = 1\ndef foo():\n    global x\n    x = 2\ndef outer():\n    y = 3\n    def inner():\n        nonlocal y\n        y = 4\n    inner()\n    print(y)  # 4", "explanation": "演示了global和nonlocal的用法。"}]}}, {"name": "Lambda Function", "trans": ["匿名函数lambda"], "usage": {"syntax": "lambda 参数: 表达式", "description": "lambda用于定义匿名函数，常用于简单函数或作为参数传递。", "parameters": [{"name": "参数", "description": "lambda的输入参数"}], "returnValue": "lambda表达式的计算结果", "examples": [{"code": "f = lambda x, y: x + y\nprint(f(2, 3))  # 5", "explanation": "演示了lambda匿名函数的定义和调用。"}]}}, {"name": "Function Docstring", "trans": ["函数文档字符串"], "usage": {"syntax": "def func():\n    '''文档字符串'''", "description": "函数首行的三引号字符串作为文档说明，可用help()查看。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "def add(a, b):\n    '''返回a和b的和'''\n    return a + b\nprint(add.__doc__)  # 返回a和b的和", "explanation": "演示了函数文档字符串的定义和查看。"}]}}, {"name": "Function as Object", "trans": ["函数作为对象"], "usage": {"syntax": "函数名可以赋值、作为参数、作为返回值", "description": "Python函数是一等对象，可赋值给变量、作为参数传递、作为返回值返回。", "parameters": [], "returnValue": "函数对象", "examples": [{"code": "def foo():\n    print('foo')\nf = foo\nf()  # foo\ndef bar(func):\n    func()\nbar(foo)  # foo", "explanation": "演示了函数对象的赋值和传递。"}]}}, {"name": "Functions Assignment", "trans": ["函数基础练习"], "usage": {"syntax": "# 函数基础练习", "description": "完成以下练习，巩固函数基础相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 定义一个函数，返回两个数的最大值。\n2. 定义一个带默认参数的函数，计算n的阶乘。\n3. 用lambda表达式实现两个数相乘。\n4. 编写一个函数，统计字符串中某字符出现的次数。", "explanation": "练习要求动手实践函数定义、参数、返回值、lambda、文档字符串等。"}]}}]}