{"name": "Regular Expressions", "trans": ["正则表达式"], "methods": [{"name": "Basic Regex Syntax", "trans": ["正则基础语法"], "usage": {"syntax": "/pattern/flags\nnew RegExp('pattern', 'flags')", "description": "正则表达式用于匹配字符串，可用字面量或RegExp对象创建。常用元字符有. ^ $ * + ? [] {} | () \\等，flags如g/i/m。", "parameters": [{"name": "pattern", "description": "匹配规则。"}, {"name": "flags", "description": "修饰符，如g(全局)、i(忽略大小写)、m(多行)。"}], "returnValue": "返回正则表达式对象。", "examples": [{"code": "const reg1 = /abc/i;\nconst reg2 = new RegExp('abc', 'g');\nconsole.log(reg1.test('ABC')); // true", "explanation": "两种正则声明方式和常用修饰符。"}]}}, {"name": "Common Regex Methods", "trans": ["常用正则方法"], "usage": {"syntax": "reg.test(str);\nreg.exec(str);\nstr.match(reg);\nstr.replace(reg, val);\nstr.search(reg);\nstr.split(reg);", "description": "test检测是否匹配，exec返回匹配结果，match/replace/search/split为字符串方法，结合正则使用。", "parameters": [{"name": "test", "description": "检测字符串是否匹配。"}, {"name": "exec", "description": "返回匹配结果数组。"}, {"name": "match", "description": "返回所有匹配项。"}, {"name": "replace", "description": "替换匹配内容。"}, {"name": "search", "description": "查找匹配位置。"}, {"name": "split", "description": "按正则分割字符串。"}], "returnValue": "返回布尔值、数组、字符串或索引。", "examples": [{"code": "const str = 'a1b2c3';\nconst reg = /\\d/g;\nconsole.log(reg.test(str)); // true\nconsole.log(reg.exec(str)); // ['1']\nconsole.log(str.match(reg)); // ['1','2','3']\nconsole.log(str.replace(reg,'#')); // a#b#c#\nconsole.log(str.search(/b/)); // 2\nconsole.log(str.split(/\\d/)); // ['a','b','c','']", "explanation": "演示常用正则方法的用法。"}]}}, {"name": "Regex Application Scenarios", "trans": ["正则表达式应用场景"], "usage": {"syntax": "// 邮箱校验\n/^\\w+([-+.']\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$/\n// 手机号校验\n/^1[3-9]\\d{9}$/", "description": "正则常用于表单校验、文本提取、批量替换等场景。", "parameters": [{"name": "邮箱校验", "description": "验证邮箱格式。"}, {"name": "手机号校验", "description": "验证手机号格式。"}], "returnValue": "返回布尔值或匹配结果。", "examples": [{"code": "const email = '<EMAIL>';\nconst reg = /^\\w+([-+.']\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$/;\nconsole.log(reg.test(email)); // true\nconst phone = '13812345678';\nconsole.log(/^1[3-9]\\d{9}$/.test(phone)); // true", "explanation": "邮箱和手机号正则校验。"}]}}, {"name": "作业：正则表达式实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 声明正则表达式\n// 2. 使用常用方法处理字符串\n// 3. 实现表单校验等应用", "description": "通过实践正则声明、方法、应用场景，掌握JS正则表达式基础。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 正则声明/方法\n// 2. 校验/提取/替换", "explanation": "作业提示，需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nconst reg = /\\d+/g;\nconst str = 'a1b2c3';\nconsole.log(str.match(reg));\nconsole.log(str.replace(reg,'#'));\nconsole.log(/^1[3-9]\\d{9}$/.test('13812345678'));", "explanation": "涵盖正则声明、方法、校验的正确实现。"}]}}]}