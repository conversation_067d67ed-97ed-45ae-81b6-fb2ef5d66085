{"name": "Backup and Recovery Exercise", "trans": ["备份与恢复练习"], "methods": [{"name": "Export and Restore Student Table", "trans": ["导出并恢复学生表"], "usage": {"syntax": "-- 导出学生表数据\nmysqldump -u 用户名 -p testdb student > student_backup.sql\n\n-- 在testdb中新建student_copy表\nCREATE TABLE student_copy LIKE student;\n\n-- 恢复数据到student_copy表\nmysql -u 用户名 -p testdb < student_backup.sql", "description": "本练习演示如何使用mysqldump工具导出学生表数据，并将其恢复到新表student_copy，实现表级数据迁移和备份恢复。", "parameters": [{"name": "用户名", "description": "数据库登录用户名"}, {"name": "student", "description": "原始学生表名"}, {"name": "student_copy", "description": "恢复目标表名"}, {"name": "student_backup.sql", "description": "学生表备份文件名"}], "returnValue": "无返回值", "examples": [{"code": "-- 导出学生表数据\nmysqldump -u root -p testdb student > student_backup.sql\n\n-- 新建student_copy表\nCREATE TABLE student_copy LIKE student;\n\n-- 恢复数据到student_copy表\nmysql -u root -p testdb < student_backup.sql", "explanation": "本例展示了如何将学生表数据导出为SQL文件，并恢复到新表student_copy，实现数据迁移和备份恢复。"}]}}, {"name": "Backup and Recovery Assignment", "trans": ["备份与恢复练习题"], "usage": {"syntax": "# 备份与恢复练习题", "description": "完成以下练习，巩固MySQL表级数据备份与恢复的操作技能。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 使用mysqldump导出testdb.student表的数据到student_backup.sql。\n2. 在testdb中新建student_copy表，并将备份数据恢复到该表。\n3. 验证student_copy表中的数据与原student表一致。\n4. 总结表级备份与恢复的常见应用场景。", "explanation": "通过这些练习，你将掌握MySQL表级数据的导出、恢复和验证等实用技能。"}]}}]}