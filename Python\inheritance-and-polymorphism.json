{"name": "Inheritance and Polymorphism", "trans": ["继承与多态"], "methods": [{"name": "Single and Multiple Inheritance", "trans": ["单继承与多继承"], "usage": {"syntax": "class 子类(父类):\n    ...\nclass 子类(父类1, 父类2):\n    ...", "description": "子类可继承父类属性和方法，支持单继承和多继承。多继承时按MRO顺序查找。", "parameters": [{"name": "子类", "description": "继承父类的新类"}, {"name": "父类", "description": "被继承的类，可多个"}], "returnValue": "子类对象", "examples": [{"code": "class Animal:\n    pass\nclass Dog(Animal):\n    pass\nclass A: pass\nclass B: pass\nclass C(A, B): pass", "explanation": "演示了单继承和多继承的语法。"}]}}, {"name": "Method Override", "trans": ["方法重写"], "usage": {"syntax": "class 子类(父类):\n    def 方法(self):\n        ...", "description": "子类可重写父类方法，实现不同的行为。调用时优先使用子类实现。", "parameters": [{"name": "self", "description": "实例自身的引用"}], "returnValue": "方法的返回值，若无return则为None", "examples": [{"code": "class Animal:\n    def speak(self):\n        print('动物叫')\nclass Dog(Animal):\n    def speak(self):\n        print('汪汪')\nd = Dog()\nd.speak()  # 汪汪", "explanation": "演示了方法重写。"}]}}, {"name": "super() Function", "trans": ["super()函数"], "usage": {"syntax": "super().方法名(参数)", "description": "super()用于调用父类方法，常用于子类重写方法时保留父类逻辑。", "parameters": [{"name": "self", "description": "实例自身的引用"}], "returnValue": "父类方法的返回值", "examples": [{"code": "class Animal:\n    def speak(self):\n        print('动物叫')\nclass Cat(Animal):\n    def speak(self):\n        super().speak()\n        print('喵喵')\nc = Cat()\nc.speak()  # 动物叫\n# 喵喵", "explanation": "演示了super()调用父类方法。"}]}}, {"name": "Polymorphism and Duck Ty<PERSON>", "trans": ["多态与鸭子类型"], "usage": {"syntax": "不同类实现同名方法，统一调用", "description": "多态指不同类对象可通过统一接口调用。鸭子类型强调只要有同名方法即可，无需继承关系。", "parameters": [{"name": "对象", "description": "实现同名方法的不同类实例"}], "returnValue": "方法的返回值，取决于具体实现", "examples": [{"code": "class Cat:\n    def speak(self):\n        print('喵')\nclass Dog:\n    def speak(self):\n        print('汪')\ndef animal_speak(animal):\n    animal.speak()\nanimal_speak(Cat())  # 喵\nanimal_speak(Dog())  # 汪", "explanation": "演示了多态和鸭子类型。"}]}}, {"name": "Abstract Class and Interface", "trans": ["抽象类与接口"], "usage": {"syntax": "from abc import ABC, abstractmethod\nclass Base(ABC):\n    @abstractmethod\n    def foo(self):\n        pass", "description": "抽象类不能实例化，只能被继承。抽象方法需子类实现。Python无接口关键字，常用抽象类实现接口。", "parameters": [{"name": "ABC", "description": "抽象基类"}, {"name": "abstractmethod", "description": "抽象方法装饰器"}], "returnValue": "抽象类或子类对象", "examples": [{"code": "from abc import ABC, abstractmethod\nclass Animal(ABC):\n    @abstractmethod\n    def speak(self):\n        pass\nclass Dog(Animal):\n    def speak(self):\n        print('汪')\nd = Dog()\nd.speak()  # 汪", "explanation": "演示了抽象类和抽象方法。"}]}}, {"name": "MRO and C3 Linearization", "trans": ["MRO与C3线性化"], "usage": {"syntax": "类名.__mro__ 或 类名.mro()", "description": "MRO（方法解析顺序）决定多继承时方法查找顺序。C3线性化是Python的MRO算法。", "parameters": [{"name": "类名", "description": "要查询的类"}], "returnValue": "方法解析顺序的元组或列表", "examples": [{"code": "class A: pass\nclass B(A): pass\nclass C(B): pass\nprint(C.__mro__)\nprint(C.mro())", "explanation": "演示了MRO的查询方法。"}]}}, {"name": "Inheritance and Polymorphism Assignment", "trans": ["继承与多态练习"], "usage": {"syntax": "# 继承与多态练习", "description": "完成以下练习，巩固继承与多态相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 定义一个Animal基类和Dog子类，重写speak方法。\n2. 用super()调用父类方法。\n3. 编写一个抽象类Shape，子类实现area方法。\n4. 查询多继承类的MRO顺序。", "explanation": "练习要求动手实践继承、重写、super、抽象类、多态、MRO等。"}]}}]}