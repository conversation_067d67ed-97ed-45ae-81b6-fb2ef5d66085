# Spring Boot学习路线与核心内容

## 1. Spring Boot基础   //已完成

### 入门与环境搭建
- Spring Boot简介
- 版本选择与兼容性
- 开发环境搭建
- 第一个Spring Boot项目
- 目录结构解析
- 启动原理

### 配置管理
- application.properties与application.yml
- 配置优先级与加载顺序
- 外部化配置
- Profile多环境配置
- 配置热更新
- 类型安全配置（@ConfigurationProperties）
- 配置加密与安全

### 自动配置原理
- @SpringBootApplication注解
- 自动装配机制（@EnableAutoConfiguration）
- 条件装配（@Conditional）
- 自定义Starter
- 禁用/排除自动配置

## 2. Web开发       //已完成

### RESTful API开发
- @RestController与@RequestMapping
- GET/POST/PUT/DELETE请求
- 路径参数与请求参数
- 请求体与响应体
- JSON序列化与反序列化
- 响应状态码与异常处理
- 全局异常处理（@ControllerAdvice）

### 静态资源与模板引擎
- 静态资源映射
- 常用模板引擎（Thymeleaf、Freemarker）
- 页面渲染流程
- 静态资源缓存

### 跨域与文件上传
- CORS配置
- 文件上传与下载
- Multipart配置

### 拦截器与过滤器
- HandlerInterceptor用法
- Filter用法
- 全局请求日志
- 请求参数校验（JSR-303）

## 3. 数据访问      //已完成

### 数据源与JDBC
- 数据源配置
- 多数据源管理
- JdbcTemplate用法
- 事务管理

### Spring Data JPA
- JPA依赖与配置
- 实体类与Repository
- 常用查询方法
- 分页与排序
- 自定义SQL与@Query
- 事务注解（@Transactional）

### MyBatis集成
- MyBatis依赖与配置
- Mapper接口与XML
- 动态SQL
- 分页插件
- 多表关联查询

### Redis与缓存
- Redis集成与配置
- String/Hash/List等操作
- 缓存注解（@Cacheable等）
- 分布式锁
- 缓存穿透与雪崩防护

## 4. 安全与认证        //已完成

### Spring Security
- 依赖与基本配置
- 用户认证与授权
- 自定义登录/登出
- 权限注解（@PreAuthorize等）
- 密码加密与存储
- 会话管理
- 跨站请求伪造（CSRF）防护

### OAuth2与JWT
- OAuth2授权模式
- JWT原理与集成
- 单点登录（SSO）
- Token刷新与失效

## 5. 服务通信与微服务  //已完成

### RestTemplate与WebClient
- RestTemplate用法
- WebClient响应式调用
- 服务间调用最佳实践

### Spring Cloud基础
- 服务注册与发现（Eureka/Nacos）
- 负载均衡（Ribbon/LoadBalancer）
- 配置中心（Config）
- 服务熔断与降级（Hystrix/Resilience4j）
- 网关（Gateway）
- 分布式链路追踪（Sleuth/Zipkin）

## 6. 消息与异步        //已完成

### 消息队列
- RabbitMQ集成
- Kafka集成
- 消息生产与消费
- 消息确认与重试
- 延迟队列

### 异步任务与定时任务
- @Async异步方法
- 线程池配置
- @Scheduled定时任务
- 分布式定时任务（xxl-job等）

## 7. 测试          //已完成

### 单元测试
- JUnit5集成
- MockMvc测试Web接口
- Mockito/MockBean用法
- 数据库测试（@DataJpaTest）
- 参数化测试

### 集成测试
- @SpringBootTest用法
- 测试环境隔离
- 测试数据准备
- 容器化测试（Testcontainers）

## 8. 部署与运维        //已完成

### 打包与部署
- Maven/Gradle打包
- 可执行JAR/WAR
- 外部配置与环境变量
- 日志管理（Logback/Log4j2）
- 进程管理（systemd、Supervisor）

### 容器化与云原生
- Dockerfile编写
- 镜像构建与推送
- K8s部署与配置
- 配置中心与密钥管理
- 健康检查与自动重启

### 监控与告警
- Actuator健康检查
- 自定义监控指标
- Prometheus/Grafana集成
- 日志采集与分析
- 告警通知

## 9. 性能优化与最佳实践    //已完成

### 性能调优
- 启动速度优化
- 内存与GC调优
- 数据库性能优化
- 连接池优化
- 缓存优化
- 热点数据与热点接口优化

### 代码与架构最佳实践
- 分层架构设计
- 领域驱动设计（DDD）
- 统一异常与日志处理
- 配置与代码分离
- 依赖注入与解耦
- 常见安全漏洞防护
- 版本升级与兼容性

## 10. Spring生态与扩展     //已完成

### 常用Spring生态组件
- Spring Boot Admin
- Spring Batch
- Spring Session
- Spring AMQP
- Spring WebFlux
- Spring Native

### 项目实战与案例
- 多模块项目结构
- 典型业务场景实战
- 常见问题与故障排查
- 真实项目部署流程
- 代码规范与文档编写 