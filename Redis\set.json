{"name": "Set", "trans": ["集合类型"], "methods": [{"name": "Set Commands", "trans": ["集合操作命令"], "usage": {"syntax": "SADD key member [member ...]\nSREM key member [member ...]\nSMEMBERS key\nSISMEMBER key member\nSCARD key", "description": "SADD添加成员，SREM移除成员，SMEMBERS获取所有成员，SISMEMBER判断成员是否存在，SCARD获取集合大小。适合去重、标签、无序集合等场景。", "parameters": [{"name": "key", "description": "集合键名"}, {"name": "member", "description": "集合成员"}], "returnValue": "添加/移除的数量、成员列表、布尔值或集合大小", "examples": [{"code": "$ redis-cli sadd tags java python go\n$ redis-cli srem tags go\n$ redis-cli smembers tags\n$ redis-cli sismember tags java\n$ redis-cli scard tags", "explanation": "演示了集合的增删查和判断操作。"}]}}, {"name": "Deduplication and Tag System", "trans": ["去重与标签系统"], "usage": {"syntax": "SADD/SMEMBERS/SINTER/SDIFF/SUNION", "description": "集合类型天然去重，适合实现标签系统、共同好友、兴趣匹配等。SINTER取交集，SDIFF取差集，SUNION取并集。", "parameters": [{"name": "key", "description": "集合键名"}], "returnValue": "集合运算结果", "examples": [{"code": "$ redis-cli sadd user:1:tags java python\n$ redis-cli sadd user:2:tags python go\n$ redis-cli sinter user:1:tags user:2:tags # 共同标签\n$ redis-cli sunion user:1:tags user:2:tags # 所有标签", "explanation": "演示了集合的去重和集合运算。"}]}}, {"name": "Practice: Set类型练习", "trans": ["集合类型练习"], "usage": {"syntax": "# Redis集合类型练习", "description": "完成以下练习，掌握集合类型的常用操作和去重应用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用SADD添加多个标签。\n2. 用SMEMBERS获取所有标签。\n3. 用SISMEMBER判断标签是否存在。\n4. 用SINTER/SUNION实现共同标签和所有标签。", "explanation": "通过这些练习掌握集合类型的常用命令和去重应用。"}, {"code": "# 正确实现示例\n$ redis-cli sadd tags java python go\n$ redis-cli smembers tags\n$ redis-cli sismember tags python\n$ redis-cli sadd tags2 python c++\n$ redis-cli sinter tags tags2\n$ redis-cli sunion tags tags2", "explanation": "展示了练习的标准实现过程。"}]}}]}