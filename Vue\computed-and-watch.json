{"name": "Computed and Watch", "trans": ["计算属性与侦听器"], "methods": [{"name": "Computed Usage", "trans": ["computed用法"], "usage": {"syntax": "import { computed } from 'vue'\nconst double = computed(() => count.value * 2)", "description": "computed用于声明依赖其他响应式数据的派生值，具有缓存特性，只有依赖变化时才重新计算。", "parameters": [{"name": "computed(函数)", "description": "返回派生值的getter函数"}], "returnValue": "返回计算后的派生值，具备缓存", "examples": [{"code": "import { ref, computed } from 'vue'\nconst count = ref(1)\nconst double = computed(() => count.value * 2)\ncount.value = 2 // double自动变为4", "explanation": "double依赖count，count变化时自动更新。"}]}}, {"name": "Watch Usage", "trans": ["watch用法"], "usage": {"syntax": "import { watch } from 'vue'\nwatch(source, callback)", "description": "watch用于侦听响应式数据变化并执行副作用操作，适合异步请求、手动操作等场景。", "parameters": [{"name": "source", "description": "要侦听的数据源"}, {"name": "callback", "description": "数据变化时执行的回调"}], "returnValue": "无返回值，数据变化时自动执行回调", "examples": [{"code": "import { ref, watch } from 'vue'\nconst count = ref(0)\nwatch(count, (newVal, oldVal) => {\n  console.log(newVal, oldVal)\n})", "explanation": "count变化时自动执行回调。"}]}}, {"name": "watchEffect", "trans": ["watchEffect"], "usage": {"syntax": "import { watchEffect } from 'vue'\nwatchEffect(() => { ... })", "description": "watchEffect自动收集依赖的响应式数据，任意依赖变化时立即执行副作用，适合快速响应多依赖变化。", "parameters": [{"name": "副作用函数", "description": "依赖响应式数据的函数"}], "returnValue": "无返回值，依赖变化时自动执行副作用", "examples": [{"code": "import { ref, watchEffect } from 'vue'\nconst a = ref(1)\nconst b = ref(2)\nwatchEffect(() => {\n  console.log(a.value + b.value)\n})", "explanation": "a或b变化时，副作用函数都会重新执行。"}]}}, {"name": "Watch Multiple Sources", "trans": ["侦听多个源"], "usage": {"syntax": "watch([source1, source2], callback)", "description": "watch支持侦听多个响应式数据源，任一变化时触发回调。", "parameters": [{"name": "[source1, source2]", "description": "要侦听的多个数据源数组"}, {"name": "callback", "description": "数据变化时执行的回调"}], "returnValue": "无返回值，任一数据源变化时执行回调", "examples": [{"code": "import { ref, watch } from 'vue'\nconst a = ref(1)\nconst b = ref(2)\nwatch([a, b], ([newA, newB], [oldA, oldB]) => {\n  console.log(newA, newB)\n})", "explanation": "a或b任一变化时，回调都会执行。"}]}}, {"name": "Deep and Immediate Watch", "trans": ["深度与立即侦听"], "usage": {"syntax": "watch(obj, callback, { deep: true, immediate: true })", "description": "deep: true可侦听对象内部属性变化，immediate: true首次立即执行回调。", "parameters": [{"name": "deep", "description": "是否深度侦听"}, {"name": "immediate", "description": "是否立即执行回调"}], "returnValue": "无返回值，满足条件时自动执行回调", "examples": [{"code": "import { reactive, watch } from 'vue'\nconst obj = reactive({ a: 1 })\nwatch(obj, (val) => { console.log(val) }, { deep: true, immediate: true })", "explanation": "deep监听对象内部属性，immediate首次立即执行。"}]}}, {"name": "Computed and Watch Assignment", "trans": ["计算属性与侦听器练习"], "usage": {"syntax": "# 计算属性与侦听器练习", "description": "完成以下练习，巩固computed和watch相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用computed实现一个派生属性。\n2. 用watch监听数据变化。\n3. 用watchEffect响应多个依赖。\n4. 用watch侦听多个源和深度监听。", "explanation": "通过这些练习掌握computed、watch、watchEffect、深度与立即侦听。"}]}}]}