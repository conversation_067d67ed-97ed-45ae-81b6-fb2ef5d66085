{"name": "Route Transition and Animation", "trans": ["页面跳转与动画"], "methods": [{"name": "Page Transition Animation", "trans": ["页面切换动画"], "usage": {"syntax": "Navigator.push(context, MaterialPageRoute(...))，默认有平台动画，可用PageRouteBuilder自定义动画。", "description": "Flutter页面跳转默认带有平台风格的切换动画，也可通过PageRouteBuilder自定义动画效果。", "parameters": [{"name": "transitionDuration", "description": "动画持续时间"}, {"name": "transitionsBuilder", "description": "自定义动画构建函数"}], "returnValue": "无返回值，页面切换时自动播放动画。", "examples": [{"code": "// 默认页面切换动画示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      home: FirstPage(),\n    );\n  }\n}\n\nclass FirstPage extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('第一页')),\n      body: Center(\n        child: ElevatedButton(\n          child: Text('跳转到第二页'),\n          onPressed: () {\n            Navigator.push(context, MaterialPageRoute(builder: (_) => SecondPage()));\n          },\n        ),\n      ),\n    );\n  }\n}\n\nclass SecondPage extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('第二页')),\n      body: Center(child: Text('页面切换动画')),\n    );\n  }\n}", "explanation": "演示了Flutter默认的页面切换动画。"}]}}, {"name": "Custom Route Animation", "trans": ["自定义路由动画"], "usage": {"syntax": "Navigator.push(context, PageRouteBuilder(...))，可自定义动画类型和效果。", "description": "通过PageRouteBuilder可自定义页面切换动画，如淡入淡出、缩放、滑动等。", "parameters": [{"name": "pageBuilder", "description": "页面构建函数"}, {"name": "transitionsBuilder", "description": "动画构建函数，定义动画效果"}], "returnValue": "无返回值，页面切换时自动播放自定义动画。", "examples": [{"code": "// 自定义路由动画（淡入淡出）示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      home: FirstPage(),\n    );\n  }\n}\n\nclass FirstPage extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('第一页')),\n      body: Center(\n        child: ElevatedButton(\n          child: Text('自定义动画跳转'),\n          onPressed: () {\n            Navigator.push(context, PageRouteBuilder(\n              pageBuilder: (context, animation, secondaryAnimation) => SecondPage(),\n              transitionsBuilder: (context, animation, secondaryAnimation, child) {\n                return FadeTransition(\n                  opacity: animation,\n                  child: child,\n                );\n              },\n              transitionDuration: Duration(milliseconds: 600),\n            ));\n          },\n        ),\n      ),\n    );\n  }\n}\n\nclass SecondPage extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('第二页')),\n      body: Center(child: Text('自定义路由动画')),\n    );\n  }\n}", "explanation": "演示了如何通过PageRouteBuilder实现自定义淡入淡出动画。"}]}}, {"name": "Assignment", "trans": ["作业：实现自定义页面切换动画。"], "usage": {"syntax": "无", "description": "请实现两个页面，使用PageRouteBuilder自定义切换动画（如缩放、滑动或淡入淡出）。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 作业参考实现（缩放动画）\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      home: FirstPage(),\n    );\n  }\n}\n\nclass FirstPage extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('第一页')),\n      body: Center(\n        child: ElevatedButton(\n          child: Text('缩放动画跳转'),\n          onPressed: () {\n            Navigator.push(context, PageRouteBuilder(\n              pageBuilder: (context, animation, secondaryAnimation) => SecondPage(),\n              transitionsBuilder: (context, animation, secondaryAnimation, child) {\n                return ScaleTransition(\n                  scale: animation,\n                  child: child,\n                );\n              },\n              transitionDuration: Duration(milliseconds: 500),\n            ));\n          },\n        ),\n      ),\n    );\n  }\n}\n\nclass SecondPage extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('第二页')),\n      body: Center(child: Text('缩放动画页面')),\n    );\n  }\n}", "explanation": "作业要求实现自定义页面切换动画，如缩放、滑动或淡入淡出。"}]}}]}