{"name": "Jotai/Valtio", "trans": ["Jotai/Valtio"], "methods": [{"name": "Jotai Atom", "trans": ["<PERSON><PERSON>"], "usage": {"syntax": "import { atom } from 'jotai';\nconst countAtom = atom(initialValue);", "description": "Jo<PERSON>中的atom是状态的基本单位，类似于React的useState，但可在组件间共享。每个atom代表一个独立的状态单元。", "parameters": [{"name": "initialValue", "description": "atom的初始值"}], "returnValue": "返回一个atom对象，可用于useAtom等hook", "examples": [{"code": "import { atom } from 'jotai';\n\n// 创建一个基本的原子状态\nconst countAtom = atom(0); // 初始值为0\n\n// 创建一个文本输入原子状态\nconst textAtom = atom(''); // 初始值为空字符串\n\n// 创建一个对象原子状态\nconst userAtom = atom({ name: '张三', age: 25 }); // 初始对象值", "explanation": "创建各种类型的原子状态，包括数字、字符串和对象。"}]}}, {"name": "Jotai State Usage", "trans": ["Jotai状态使用"], "usage": {"syntax": "const [value, setValue] = useAtom(atom);\nconst value = useAtomValue(atom);\nconst setValue = useSetAtom(atom);", "description": "Jo<PERSON>提供了多种hook来读取和更新atom状态，支持组件级别的细粒度更新。", "parameters": [{"name": "atom", "description": "要使用的atom对象"}], "returnValue": "返回当前atom的值和更新函数（根据使用的hook不同而异）", "examples": [{"code": "import { useAtom, useAtomValue, useSetAtom } from 'jotai';\nimport { countAtom } from './atoms';\n\nfunction Counter() {\n  // 读取和写入状态\n  const [count, setCount] = useAtom(countAtom);\n  \n  return (\n    <div>\n      <p>计数: {count}</p>\n      <button onClick={() => setCount(count + 1)}>加一</button>\n    </div>\n  );\n}\n\nfunction ReadOnlyCounter() {\n  // 只读状态\n  const count = useAtomValue(countAtom);\n  return <p>当前计数: {count}</p>;\n}\n\nfunction CounterControls() {\n  // 只写状态\n  const setCount = useSetAtom(countAtom);\n  return (\n    <div>\n      <button onClick={() => setCount(0)}>重置</button>\n      <button onClick={() => setCount(n => n + 5)}>加五</button>\n    </div>\n  );\n}", "explanation": "演示了Jotai的三种主要hook：useAtom用于读写，useAtomValue只读，useSetAtom只写。"}]}}, {"name": "Jo<PERSON> Derived Atoms", "trans": ["<PERSON><PERSON>派生原子"], "usage": {"syntax": "const derivedAtom = atom(get => get(baseAtom) * 2);\nconst writableAtom = atom(get => get(baseAtom), (get, set, newValue) => {...});", "description": "Jotai支持从现有atom派生新atom，可以是只读的计算状态，也可以是可写的派生状态。", "parameters": [{"name": "get", "description": "读取函数，可访问其他atom的值"}, {"name": "set", "description": "可选的写入函数，定义如何更新其他atom"}], "returnValue": "返回派生的atom对象", "examples": [{"code": "import { atom } from 'jotai';\n\n// 基础原子\nconst countAtom = atom(0);\n\n// 只读派生原子 - 双倍值\nconst doubleCountAtom = atom(\n  get => get(countAtom) * 2 // 从countAtom派生\n);\n\n// 可写派生原子\nconst incrementByAtom = atom(\n  get => get(countAtom), // 读取逻辑\n  (get, set, amount) => { // 写入逻辑\n    set(countAtom, get(countAtom) + amount);\n  }\n);\n\n// 使用方式\nfunction Counter() {\n  const [count] = useAtom(countAtom);\n  const [doubleCount] = useAtom(doubleCountAtom);\n  const [_, incrementBy] = useAtom(incrementByAtom);\n  \n  return (\n    <div>\n      <p>计数: {count}, 双倍值: {doubleCount}</p>\n      <button onClick={() => incrementBy(3)}>加三</button>\n    </div>\n  );\n}", "explanation": "展示了Jotai的派生原子功能，包括只读派生和可写派生。"}]}}, {"name": "Jotai Async Atoms", "trans": ["Jo<PERSON>异步原子"], "usage": {"syntax": "const asyncAtom = atom(async (get) => {...});\nconst asyncAtom = atomWithDefault(loadingFn);", "description": "Jo<PERSON>支持异步原子状态，可直接在atom中使用Promise、async/await，简化异步数据处理。", "parameters": [], "returnValue": "返回一个处理异步操作的atom", "examples": [{"code": "import { atom } from 'jotai';\n\n// 异步获取用户信息的原子\nconst userAtom = atom(async () => {\n  const response = await fetch('https://api.example.com/user');\n  return response.json(); // 返回Promise\n});\n\n// 依赖其他原子的异步原子\nconst userIdAtom = atom(1);\nconst userByIdAtom = atom(async (get) => {\n  const id = get(userIdAtom); // 获取当前用户ID\n  const response = await fetch(`https://api.example.com/user/${id}`);\n  return response.json();\n});\n\n// 在组件中使用\nfunction UserProfile() {\n  const [user, setUser] = useAtom(userAtom);\n  const [userId, setUserId] = useAtom(userIdAtom);\n  const [userById] = useAtom(userByIdAtom);\n\n  if (user.loading) return <p>加载中...</p>;\n  if (user.error) return <p>错误: {user.error.message}</p>;\n  \n  return (\n    <div>\n      <h2>{user.name}的个人资料</h2>\n      <p>ID: {userId}</p>\n      <button onClick={() => setUserId(userId + 1)}>查看下一位用户</button>\n      {userById && <p>按ID加载: {userById.name}</p>}\n    </div>\n  );\n}", "explanation": "演示了Jotai的异步原子状态，支持直接在atom中使用异步操作和处理加载状态。"}]}}, {"name": "Valtio Proxy State", "trans": ["Valtio代理状态"], "usage": {"syntax": "import { proxy } from 'valtio';\nconst state = proxy({ count: 0 });", "description": "Valtio使用JavaScript Proxy直接包装普通对象，实现响应式状态管理。可以直接修改状态，无需使用特殊API。", "parameters": [{"name": "initialState", "description": "初始状态对象"}], "returnValue": "返回一个响应式代理对象", "examples": [{"code": "import { proxy } from 'valtio';\n\n// 创建一个响应式状态\nconst state = proxy({\n  count: 0,\n  text: '',\n  user: {\n    name: '张三',\n    isAdmin: false\n  },\n  todos: []\n});\n\n// 直接修改状态\nstate.count++;\nstate.text = '新文本';\nstate.user.name = '李四';\nstate.todos.push({ id: 1, text: '学习Valtio', done: false });", "explanation": "展示了Valtio的基本用法，创建代理状态对象并直接修改。"}]}}, {"name": "Valtio State Usage", "trans": ["Valtio状态使用"], "usage": {"syntax": "import { useSnapshot } from 'valtio';\nconst snap = useSnapshot(state);", "description": "通过useSnapshot hook获取状态的不可变快照，组件会在状态变化时自动重新渲染。", "parameters": [{"name": "state", "description": "由proxy创建的响应式状态对象"}], "returnValue": "返回状态的不可变快照", "examples": [{"code": "import { useSnapshot } from 'valtio';\nimport { state } from './state';\n\nfunction Counter() {\n  // 获取状态快照\n  const snap = useSnapshot(state);\n  \n  return (\n    <div>\n      <p>计数: {snap.count}</p>\n      <button onClick={() => state.count++}>加一</button>\n      <button onClick={() => state.count = 0}>重置</button>\n    </div>\n  );\n}\n\nfunction UserProfile() {\n  const snap = useSnapshot(state);\n  \n  return (\n    <div>\n      <h3>用户信息</h3>\n      <p>姓名: {snap.user.name}</p>\n      <p>权限: {snap.user.isAdmin ? '管理员' : '普通用户'}</p>\n      <button onClick={() => state.user.isAdmin = !state.user.isAdmin}>\n        切换权限\n      </button>\n    </div>\n  );\n}", "explanation": "展示了如何在组件中使用Valtio状态，读取使用快照，修改直接操作原始状态。"}]}}, {"name": "Valtio Derived State", "trans": ["<PERSON><PERSON><PERSON>派生状态"], "usage": {"syntax": "import { derive } from 'valtio/utils';\nconst derived = derive({ computed: (get) => get(state).count * 2 });", "description": "Valtio提供derive工具创建派生状态，类似计算属性，可以依赖其他状态自动计算。", "parameters": [], "returnValue": "返回派生状态对象", "examples": [{"code": "import { proxy } from 'valtio';\nimport { derive } from 'valtio/utils';\n\n// 基础状态\nconst state = proxy({\n  count: 0,\n  items: [\n    { id: 1, text: '学习React', completed: true },\n    { id: 2, text: '学习Valtio', completed: false }\n  ]\n});\n\n// 派生状态\nconst derivedState = derive({\n  // 计算双倍值\n  doubleCount: (get) => get(state).count * 2,\n  // 计算待办事项统计\n  todoStats: (get) => {\n    const items = get(state).items;\n    return {\n      total: items.length,\n      completed: items.filter(item => item.completed).length,\n      remaining: items.filter(item => !item.completed).length\n    };\n  }\n});\n\n// 在组件中使用\nfunction TodoStats() {\n  const snap = useSnapshot(state);\n  const derived = useSnapshot(derivedState);\n  \n  return (\n    <div>\n      <p>计数: {snap.count} (双倍值: {derived.doubleCount})</p>\n      <p>待办统计: 总数 {derived.todoStats.total}, 已完成 {derived.todoStats.completed}</p>\n      <button onClick={() => state.count++}>增加计数</button>\n      <button onClick={() => {\n        state.items.push({ id: Date.now(), text: '新任务', completed: false });\n      }}>添加任务</button>\n    </div>\n  );\n}", "explanation": "展示了Valtio的派生状态功能，通过derive工具创建计算属性，自动跟踪依赖变化。"}]}}, {"name": "Persistence and Subscription", "trans": ["持久化和订阅"], "usage": {"syntax": "import { watch } from 'valtio/utils';\nwatch(state, () => {...});\n\n// Jotai持久化\nimport { atomWithStorage } from 'jotai/utils';", "description": "Jo<PERSON>和Valtio都支持状态的持久化和订阅机制，可以实现本地存储同步和其他副作用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// Valtio持久化和订阅\nimport { proxy } from 'valtio';\nimport { watch, subscribeKey } from 'valtio/utils';\n\nconst state = proxy({\n  count: Number(localStorage.getItem('count')) || 0,\n  user: JSON.parse(localStorage.getItem('user')) || null\n});\n\n// 监听整个状态变化\nwatch(state, () => {\n  console.log('状态已更新:', state);\n});\n\n// 监听特定键的变化\nsubscribeKey(state, 'count', (value) => {\n  localStorage.setItem('count', String(value));\n});\n\nsubscribeKey(state, 'user', (value) => {\n  localStorage.setItem('user', JSON.stringify(value));\n});\n\n// Jotai持久化\nimport { atom } from 'jotai';\nimport { atomWithStorage } from 'jotai/utils';\n\n// 自动持久化到localStorage\nconst countAtom = atomWithStorage('count', 0);\nconst userAtom = atomWithStorage('user', { name: '未登录' });", "explanation": "展示了Valtio的watch和subscribeKey实现状态订阅和持久化，以及Jotai的atomWithStorage直接实现持久化。"}]}}, {"name": "Comparison with Other State Libraries", "trans": ["与其他状态库对比"], "usage": {"syntax": "// Jo<PERSON>/<PERSON><PERSON><PERSON> vs <PERSON>ux/Context/Zustand/Recoil", "description": "<PERSON><PERSON>和Valtio是轻量级原子化状态管理库，与Redux、Context、Zustand和Recoil相比具有不同特点和适用场景。", "parameters": [], "returnValue": "各状态库的对比结果", "examples": [{"code": "/*\n特点对比：\n\n1. <PERSON><PERSON> vs Recoil:\n   - 相似点: 都基于原子模型，支持原子派生\n   - 区别: <PERSON><PERSON>更轻量，API更简洁，无需唯一key，捆绑大小更小\n\n2. <PERSON><PERSON><PERSON> vs Immer/MobX:\n   - 相似点: 都使用Proxy实现可变式API\n   - 区别: Valtio更轻量，使用不可变快照渲染，与React集成更好\n\n3. <PERSON><PERSON>/Valtio vs Redux:\n   - 优势: 无模板代码，细粒度更新，扩展性更好\n   - 劣势: 生态系统小，中间件和工具较少\n\n4. <PERSON><PERSON>/Valtio vs Context:\n   - 优势: 避免重渲染问题，更好的性能，无Provider嵌套地狱\n   - 相似点: 都比Redux更轻量简单\n\n5. <PERSON><PERSON>/Valtio vs Zustand:\n   - 相似点: 都轻量简单，支持中间件\n   - 区别: Zustand更类似Redux，Jotai/Valtio更关注原子/代理模型\n*/", "explanation": "对比了Jotai/Valtio与其他流行状态管理库的异同和各自优势。"}]}}, {"name": "Use Cases Analysis", "trans": ["适用场景分析"], "usage": {"syntax": "// Jo<PERSON>适合原子化状态，Valtio适合对象状态", "description": "<PERSON><PERSON>和Valtio有各自适合的应用场景，选择时应考虑项目需求和团队偏好。", "parameters": [], "returnValue": "适用场景分析结果", "examples": [{"code": "/*\n适用场景分析:\n\n1. <PERSON><PERSON>适合:\n   - 需要原子化、细粒度状态管理的应用\n   - 有多个相互依赖的状态\n   - 需要从React组件外部访问状态\n   - 注重性能优化的场景\n   - 小型到中型应用\n\n2. Valtio适合:\n   - 喜欢可变编程风格的团队\n   - 需要处理深层嵌套对象状态\n   - 从非React代码访问和修改状态\n   - 从MobX或类似库迁移的项目\n   - 喜欢简单直接API的团队\n\n3. 不适合的场景:\n   - 大型企业应用可能更适合Redux\n   - 需要强大中间件生态的项目\n   - 需要时间旅行调试的复杂应用\n   - 团队习惯于严格不可变模式\n*/", "explanation": "分析了Jotai和Valtio各自适合的使用场景和项目类型。"}]}}, {"name": "作业：实现Jotai/Valtio待办事项应用", "trans": ["作业"], "usage": {"syntax": "// 要求：\n// 1. 用Jotai或Valtio实现一个待办事项应用\n// 2. 支持添加、删除、完成/取消完成待办事项\n// 3. 显示总数、已完成数、未完成数统计\n// 4. 实现持久化到localStorage", "description": "实现一个使用Jotai或Valtio状态管理的待办事项应用，展示所学知识的综合应用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下仅为提示\n// 1. 定义状态：待办项列表、过滤条件\n// 2. 实现增删改查功能\n// 3. 计算派生数据：统计信息\n// 4. 持久化到localStorage", "explanation": "作业提示，学生需自行完成实现。"}, {"code": "// Jotai实现方案\nimport React, { useState } from 'react';\nimport { atom, useAtom } from 'jotai';\nimport { atomWithStorage } from 'jotai/utils';\n\n// 1. 定义持久化存储的待办事项原子\nconst todosAtom = atomWithStorage('todos', [\n  { id: 1, text: '学习React', completed: false },\n  { id: 2, text: '学习Jotai', completed: false }\n]);\n\n// 2. 派生原子：统计信息\nconst statsAtom = atom(get => {\n  const todos = get(todosAtom);\n  return {\n    total: todos.length,\n    completed: todos.filter(t => t.completed).length,\n    remaining: todos.filter(t => !t.completed).length\n  };\n});\n\n// 3. 过滤原子\nconst filterAtom = atom('all'); // 'all', 'active', 'completed'\n\n// 4. 过滤后的待办事项\nconst filteredTodosAtom = atom(get => {\n  const filter = get(filterAtom);\n  const todos = get(todosAtom);\n  \n  switch (filter) {\n    case 'active': return todos.filter(t => !t.completed);\n    case 'completed': return todos.filter(t => t.completed);\n    default: return todos;\n  }\n});\n\nfunction TodoApp() {\n  const [newTodo, setNewTodo] = useState('');\n  const [todos, setTodos] = useAtom(todosAtom);\n  const [filteredTodos] = useAtom(filteredTodosAtom);\n  const [filter, setFilter] = useAtom(filterAtom);\n  const [stats] = useAtom(statsAtom);\n  \n  // 添加新待办\n  const handleAdd = (e) => {\n    e.preventDefault();\n    if (!newTodo.trim()) return;\n    \n    setTodos([...todos, {\n      id: Date.now(),\n      text: newTodo,\n      completed: false\n    }]);\n    setNewTodo('');\n  };\n  \n  // 切换完成状态\n  const toggleTodo = (id) => {\n    setTodos(todos.map(todo =>\n      todo.id === id ? { ...todo, completed: !todo.completed } : todo\n    ));\n  };\n  \n  // 删除待办\n  const deleteTodo = (id) => {\n    setTodos(todos.filter(todo => todo.id !== id));\n  };\n  \n  return (\n    <div className=\"todo-app\">\n      <h1>Jotai 待办事项应用</h1>\n      \n      {/* 添加表单 */}\n      <form onSubmit={handleAdd}>\n        <input\n          value={newTodo}\n          onChange={(e) => setNewTodo(e.target.value)}\n          placeholder=\"添加新待办...\"\n        />\n        <button type=\"submit\">添加</button>\n      </form>\n      \n      {/* 统计信息 */}\n      <div className=\"stats\">\n        <span>总计: {stats.total}</span>\n        <span>已完成: {stats.completed}</span>\n        <span>未完成: {stats.remaining}</span>\n      </div>\n      \n      {/* 过滤器 */}\n      <div className=\"filters\">\n        <button onClick={() => setFilter('all')} disabled={filter === 'all'}>全部</button>\n        <button onClick={() => setFilter('active')} disabled={filter === 'active'}>未完成</button>\n        <button onClick={() => setFilter('completed')} disabled={filter === 'completed'}>已完成</button>\n      </div>\n      \n      {/* 待办列表 */}\n      <ul className=\"todo-list\">\n        {filteredTodos.map(todo => (\n          <li key={todo.id} className={todo.completed ? 'completed' : ''}>\n            <input\n              type=\"checkbox\"\n              checked={todo.completed}\n              onChange={() => toggleTodo(todo.id)}\n            />\n            <span>{todo.text}</span>\n            <button onClick={() => deleteTodo(todo.id)}>删除</button>\n          </li>\n        ))}\n      </ul>\n    </div>\n  );\n}\n\nexport default function App() {\n  return <TodoApp />;\n}", "explanation": "使用Jotai实现的完整待办事项应用，包含添加、删除、切换状态、过滤和统计功能，并通过atomWithStorage实现持久化。"}]}}]}