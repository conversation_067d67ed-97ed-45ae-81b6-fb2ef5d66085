{"name": "Event Model", "trans": ["事件模型"], "methods": [{"name": "Event Capturing & Bubbling", "trans": ["事件捕获与冒泡"], "usage": {"syntax": "// 添加事件监听器\nelement.addEventListener('event', handler, useCapture);\n// 阻止冒泡\nevent.stopPropagation();\n// 阻止捕获和冒泡阶段的所有后续事件处理\nevent.stopImmediatePropagation();", "description": "DOM事件传播有三个阶段：捕获阶段（从外到内）、目标阶段和冒泡阶段（从内到外）。默认情况下，事件处理在冒泡阶段执行，但可通过addEventListener的第三个参数启用捕获阶段处理。", "parameters": [{"name": "event", "description": "事件类型，如'click'、'mouseover'等。"}, {"name": "handler", "description": "事件处理函数。"}, {"name": "useCapture", "description": "布尔值，指定事件是否在捕获阶段处理，默认为false（冒泡阶段）。"}], "returnValue": "无返回值。", "examples": [{"code": "// HTML结构\n/*\n<div id=\"outer\">\n  <div id=\"middle\">\n    <div id=\"inner\">点击我</div>\n  </div>\n</div>\n*/\n\n// 获取元素\nconst outer = document.getElementById('outer');\nconst middle = document.getElementById('middle');\nconst inner = document.getElementById('inner');\n\n// 冒泡阶段（默认）\nouter.addEventListener('click', function(e) {\n  console.log('外层 冒泡');\n});\n\nmiddle.addEventListener('click', function(e) {\n  console.log('中层 冒泡');\n});\n\ninner.addEventListener('click', function(e) {\n  console.log('内层 冒泡');\n});\n\n// 捕获阶段\nouter.addEventListener('click', function(e) {\n  console.log('外层 捕获');\n}, true);\n\nmiddle.addEventListener('click', function(e) {\n  console.log('中层 捕获');\n}, true);\n\ninner.addEventListener('click', function(e) {\n  console.log('内层 捕获');\n}, true);\n\n// 点击inner元素时的输出顺序：\n// 外层 捕获\n// 中层 捕获\n// 内层 捕获\n// 内层 冒泡\n// 中层 冒泡\n// 外层 冒泡", "explanation": "展示事件捕获和冒泡的执行顺序，点击最内层元素时，事件先从外到内进行捕获，然后从内到外进行冒泡。"}, {"code": "// 阻止事件冒泡示例\nmiddle.addEventListener('click', function(e) {\n  console.log('中层 冒泡');\n  e.stopPropagation(); // 阻止事件继续冒泡到outer\n});\n\n// 阻止同一元素的后续处理函数\ninner.addEventListener('click', function(e) {\n  console.log('内层 第一个处理函数');\n  e.stopImmediatePropagation(); // 阻止内层的第二个处理函数执行\n});\n\ninner.addEventListener('click', function(e) {\n  console.log('内层 第二个处理函数'); // 不会执行\n});\n\n// 事件委托示例（利用冒泡）\ndocument.getElementById('menu').addEventListener('click', function(e) {\n  if (e.target.matches('.menu-item')) {\n    console.log('点击了菜单项:', e.target.textContent);\n  }\n});", "explanation": "展示如何使用stopPropagation()阻止事件冒泡，以及使用stopImmediatePropagation()阻止同一元素的后续处理函数执行。"}]}}, {"name": "Event Object & Properties", "trans": ["事件对象与属性"], "usage": {"syntax": "element.addEventListener('event', function(event) {\n  // 访问事件对象属性\n  event.target;\n  event.currentTarget;\n  event.type;\n  event.preventDefault();\n});", "description": "事件处理函数接收一个事件对象，包含事件的详细信息和有用的方法。常用属性包括target（触发事件的元素）、currentTarget（绑定事件的元素）、type（事件类型）等。", "parameters": [{"name": "event", "description": "事件对象，包含事件相关信息和方法。"}], "returnValue": "无返回值。", "examples": [{"code": "// 基本事件对象属性\ndocument.getElementById('myButton').addEventListener('click', function(event) {\n  // 事件类型\n  console.log('事件类型:', event.type); // 'click'\n  \n  // 事件目标（触发事件的元素）\n  console.log('目标元素:', event.target);\n  \n  // 当前元素（绑定事件处理函数的元素）\n  console.log('当前元素:', event.currentTarget);\n  \n  // 事件发生时间\n  console.log('事件时间:', event.timeStamp);\n  \n  // 阻止默认行为\n  event.preventDefault();\n  \n  // 事件是否在冒泡阶段\n  console.log('是否冒泡阶段:', event.eventPhase === Event.BUBBLING_PHASE);\n  \n  // 事件是否在捕获阶段\n  console.log('是否捕获阶段:', event.eventPhase === Event.CAPTURING_PHASE);\n});\n\n// 鼠标事件特有属性\ndocument.addEventListener('mousemove', function(event) {\n  // 鼠标位置（相对于视口）\n  console.log('客户端坐标:', event.clientX, event.clientY);\n  \n  // 鼠标位置（相对于页面）\n  console.log('页面坐标:', event.pageX, event.pageY);\n  \n  // 鼠标位置（相对于屏幕）\n  console.log('屏幕坐标:', event.screenX, event.screenY);\n  \n  // 按下的鼠标按键\n  console.log('按键:', event.button);\n  \n  // 是否按下修饰键\n  console.log('Ctrl键:', event.ctrlKey);\n  console.log('Shift键:', event.shiftKey);\n});\n\n// 键盘事件特有属性\ndocument.addEventListener('keydown', function(event) {\n  // 按键码\n  console.log('按键码:', event.keyCode); // 已弃用\n  console.log('按键:', event.key);\n  console.log('按键代码:', event.code);\n  \n  // 是否按下修饰键\n  console.log('Alt键:', event.altKey);\n  \n  // 阻止特定按键的默认行为\n  if (event.key === 'Tab') {\n    event.preventDefault();\n  }\n});", "explanation": "展示常见事件对象的属性和方法，包括通用属性、鼠标事件特有属性和键盘事件特有属性。"}, {"code": "// target vs currentTarget\n// HTML结构\n/*\n<div id=\"parent\">\n  <button id=\"child\">点击我</button>\n</div>\n*/\n\ndocument.getElementById('parent').addEventListener('click', function(event) {\n  console.log('目标元素:', event.target.id); // 'child'（实际被点击的元素）\n  console.log('当前元素:', event.currentTarget.id); // 'parent'（绑定事件处理函数的元素）\n  \n  // 检查是否直接点击了父元素\n  if (event.target === event.currentTarget) {\n    console.log('直接点击了父元素');\n  } else {\n    console.log('点击了父元素内的子元素');\n  }\n});\n\n// 表单事件对象\ndocument.getElementById('myForm').addEventListener('submit', function(event) {\n  event.preventDefault(); // 阻止表单提交\n  \n  // 获取表单数据\n  const formData = new FormData(event.target);\n  for (const [name, value] of formData.entries()) {\n    console.log(name + ':', value);\n  }\n});\n\n// 触摸事件对象\ndocument.getElementById('touchArea').addEventListener('touchstart', function(event) {\n  // 阻止滚动\n  event.preventDefault();\n  \n  // 获取触摸点\n  const touch = event.touches[0];\n  console.log('触摸坐标:', touch.clientX, touch.clientY);\n  \n  // 多点触摸\n  console.log('触摸点数量:', event.touches.length);\n});", "explanation": "展示target和currentTarget的区别，以及表单事件和触摸事件的特有属性和用法。"}]}}, {"name": "Custom Events", "trans": ["自定义事件"], "usage": {"syntax": "// 创建自定义事件\nconst event = new CustomEvent('myEvent', {\n  detail: { /* 自定义数据 */ },\n  bubbles: true,\n  cancelable: true\n});\n\n// 分发事件\nelement.dispatchEvent(event);\n\n// 监听自定义事件\nelement.addEventListener('myEvent', handler);", "description": "自定义事件允许创建和触发自定义的事件类型，可用于组件间通信。使用CustomEvent构造函数创建，detail属性可携带自定义数据，通过dispatchEvent方法触发。", "parameters": [{"name": "eventName", "description": "自定义事件的名称。"}, {"name": "options", "description": "事件配置对象，包含detail（自定义数据）、bubbles（是否冒泡）、cancelable（是否可取消）等属性。"}], "returnValue": "dispatchEvent返回布尔值，表示事件是否被取消。", "examples": [{"code": "// 创建和触发基本自定义事件\nconst button = document.getElementById('myButton');\n\n// 监听自定义事件\nbutton.addEventListener('userLogin', function(e) {\n  console.log('用户登录事件触发');\n  console.log('用户数据:', e.detail);\n});\n\n// 创建自定义事件\nfunction simulateLogin() {\n  const loginEvent = new CustomEvent('userLogin', {\n    detail: {\n      userId: 123,\n      username: 'john_doe',\n      timestamp: new Date().getTime()\n    },\n    bubbles: true, // 允许事件冒泡\n    cancelable: true // 允许事件被取消\n  });\n  \n  // 触发事件\n  button.dispatchEvent(loginEvent);\n}\n\n// 调用函数触发自定义事件\nsimulateLogin();", "explanation": "展示如何创建、监听和触发基本的自定义事件，并通过detail属性传递数据。"}, {"code": "// 组件通信示例\nclass ShoppingCart {\n  constructor() {\n    this.items = [];\n    this.element = document.getElementById('shopping-cart');\n  }\n  \n  addItem(item) {\n    this.items.push(item);\n    \n    // 创建并触发自定义事件\n    const event = new CustomEvent('cartUpdated', {\n      bubbles: true,\n      detail: {\n        items: this.items,\n        itemCount: this.items.length,\n        lastItem: item\n      }\n    });\n    \n    this.element.dispatchEvent(event);\n  }\n}\n\n// 使用自定义事件实现组件间通信\nconst cart = new ShoppingCart();\n\n// 购物车组件监听\ndocument.getElementById('shopping-cart').addEventListener('cartUpdated', function(e) {\n  console.log('购物车更新:', e.detail.itemCount, '件商品');\n  // 更新购物车UI\n});\n\n// 导航栏组件监听同一事件\ndocument.getElementById('navbar').addEventListener('cartUpdated', function(e) {\n  // 更新导航栏上的购物车计数器\n  document.getElementById('cart-count').textContent = e.detail.itemCount;\n});\n\n// 添加商品\ndocument.getElementById('add-to-cart').addEventListener('click', function() {\n  cart.addItem({\n    id: Date.now(),\n    name: '商品' + Math.floor(Math.random() * 100),\n    price: Math.floor(Math.random() * 100) + 1\n  });\n});\n\n// 取消自定义事件\ndocument.getElementById('shopping-cart').addEventListener('cartUpdated', function(e) {\n  if (e.detail.itemCount > 10) {\n    console.log('购物车已满，无法添加更多商品');\n    e.preventDefault(); // 如果事件是cancelable:true，可以取消事件\n  }\n}, false);", "explanation": "展示如何使用自定义事件实现组件间通信，以购物车更新为例，多个组件可以监听同一事件并各自更新UI。"}]}}, {"name": "Event Delegation", "trans": ["事件委托"], "usage": {"syntax": "parentElement.addEventListener('event', function(event) {\n  if (event.target.matches('selector')) {\n    // 处理事件\n  }\n});", "description": "事件委托是一种将事件监听器添加到父元素而不是每个子元素的技术，利用事件冒泡原理。适用于动态添加的元素、大量同类元素，可提高性能和简化代码。", "parameters": [{"name": "event", "description": "事件对象。"}, {"name": "selector", "description": "CSS选择器，用于匹配目标元素。"}], "returnValue": "无返回值。", "examples": [{"code": "// 基本事件委托\n// HTML结构\n/*\n<ul id=\"task-list\">\n  <li class=\"task\">任务1 <button class=\"delete\">删除</button></li>\n  <li class=\"task\">任务2 <button class=\"delete\">删除</button></li>\n  <li class=\"task\">任务3 <button class=\"delete\">删除</button></li>\n</ul>\n*/\n\nconst taskList = document.getElementById('task-list');\n\n// 使用事件委托处理点击事件\ntaskList.addEventListener('click', function(event) {\n  // 检查点击的是否为删除按钮\n  if (event.target.classList.contains('delete')) {\n    // 获取包含按钮的任务项\n    const taskItem = event.target.closest('.task');\n    console.log('删除任务:', taskItem.textContent.trim());\n    taskItem.remove();\n  }\n  \n  // 检查点击的是否为任务项本身\n  if (event.target.classList.contains('task')) {\n    console.log('切换任务状态:', event.target.textContent.trim());\n    event.target.classList.toggle('completed');\n  }\n});\n\n// 动态添加新任务\nfunction addTask(text) {\n  const li = document.createElement('li');\n  li.className = 'task';\n  li.innerHTML = text + ' <button class=\"delete\">删除</button>';\n  taskList.appendChild(li);\n}\n\n// 添加新任务（无需为新元素单独绑定事件）\naddTask('任务4');", "explanation": "展示基本的事件委托模式，通过在父元素上监听事件，处理子元素的点击，包括动态添加的元素。"}, {"code": "// 复杂事件委托示例\n// HTML结构\n/*\n<div id=\"data-table\">\n  <table>\n    <thead>\n      <tr>\n        <th data-sort=\"name\">名称</th>\n        <th data-sort=\"price\">价格</th>\n        <th data-sort=\"date\">日期</th>\n        <th>操作</th>\n      </tr>\n    </thead>\n    <tbody>\n      <tr data-id=\"1\">\n        <td>商品1</td>\n        <td>100</td>\n        <td>2023-01-15</td>\n        <td>\n          <button class=\"edit\">编辑</button>\n          <button class=\"delete\">删除</button>\n        </td>\n      </tr>\n      <!-- 更多行... -->\n    </tbody>\n  </table>\n</div>\n*/\n\nconst dataTable = document.getElementById('data-table');\n\n// 使用matches方法和事件委托\ndataTable.addEventListener('click', function(event) {\n  // 排序表头\n  if (event.target.matches('th[data-sort]')) {\n    const sortField = event.target.dataset.sort;\n    console.log('按字段排序:', sortField);\n    sortTable(sortField);\n  }\n  \n  // 编辑按钮\n  if (event.target.matches('.edit')) {\n    const row = event.target.closest('tr');\n    const id = row.dataset.id;\n    console.log('编辑ID:', id);\n    editItem(id);\n  }\n  \n  // 删除按钮\n  if (event.target.matches('.delete')) {\n    const row = event.target.closest('tr');\n    const id = row.dataset.id;\n    console.log('删除ID:', id);\n    deleteItem(id);\n  }\n});\n\n// 处理表格行悬停效果\ndataTable.addEventListener('mouseover', function(event) {\n  if (event.target.tagName === 'TD') {\n    const row = event.target.parentNode;\n    row.classList.add('highlight');\n  }\n});\n\ndataTable.addEventListener('mouseout', function(event) {\n  if (event.target.tagName === 'TD') {\n    const row = event.target.parentNode;\n    row.classList.remove('highlight');\n  }\n});\n\n// 模拟函数\nfunction sortTable(field) { /* 排序逻辑 */ }\nfunction editItem(id) { /* 编辑逻辑 */ }\nfunction deleteItem(id) { /* 删除逻辑 */ }", "explanation": "展示更复杂的事件委托示例，处理表格中的多种交互，包括排序、编辑、删除和悬停效果，使用matches方法和closest方法精确定位元素。"}]}}, {"name": "作业：交互式菜单系统", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 创建多级菜单系统，支持展开/折叠\n// 2. 使用事件委托处理菜单交互\n// 3. 实现自定义事件通知菜单状态变化", "description": "通过实现交互式菜单系统，综合应用事件捕获与冒泡、事件对象、自定义事件和事件委托等知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 创建HTML结构\n// 2. 使用事件委托处理点击事件\n// 3. 实现菜单展开/折叠逻辑\n// 4. 添加自定义事件通知状态变化", "explanation": "作业提示，需结合本节内容完成实现。"}, {"code": "// 正确实现示例\n// HTML结构\n/*\n<div class=\"menu-container\">\n  <ul id=\"main-menu\" class=\"menu\">\n    <li class=\"menu-item\">\n      <div class=\"menu-title\">产品 <span class=\"toggle\">+</span></div>\n      <ul class=\"submenu\">\n        <li class=\"menu-item\">手机</li>\n        <li class=\"menu-item\">电脑</li>\n        <li class=\"menu-item\">\n          <div class=\"menu-title\">配件 <span class=\"toggle\">+</span></div>\n          <ul class=\"submenu\">\n            <li class=\"menu-item\">耳机</li>\n            <li class=\"menu-item\">充电器</li>\n          </ul>\n        </li>\n      </ul>\n    </li>\n    <li class=\"menu-item\">\n      <div class=\"menu-title\">服务 <span class=\"toggle\">+</span></div>\n      <ul class=\"submenu\">\n        <li class=\"menu-item\">维修</li>\n        <li class=\"menu-item\">咨询</li>\n      </ul>\n    </li>\n  </ul>\n</div>\n*/\n\n// JavaScript实现\nclass MenuSystem {\n  constructor(menuId) {\n    this.menuElement = document.getElementById(menuId);\n    this.init();\n  }\n  \n  init() {\n    // 初始化时折叠所有子菜单\n    const submenus = this.menuElement.querySelectorAll('.submenu');\n    submenus.forEach(submenu => {\n      submenu.style.display = 'none';\n    });\n    \n    // 使用事件委托绑定点击事件\n    this.menuElement.addEventListener('click', this.handleClick.bind(this));\n    \n    // 监听自定义菜单事件\n    this.menuElement.addEventListener('menuToggle', this.handleMenuToggle.bind(this));\n  }\n  \n  handleClick(event) {\n    // 检查是否点击了菜单标题或切换按钮\n    const menuTitle = event.target.closest('.menu-title');\n    if (!menuTitle) return;\n    \n    // 找到对应的子菜单\n    const menuItem = menuTitle.parentElement;\n    const submenu = menuItem.querySelector('.submenu');\n    if (!submenu) return;\n    \n    // 切换子菜单显示状态\n    const isExpanded = submenu.style.display !== 'none';\n    submenu.style.display = isExpanded ? 'none' : 'block';\n    \n    // 更新切换按钮文本\n    const toggleBtn = menuTitle.querySelector('.toggle');\n    if (toggleBtn) {\n      toggleBtn.textContent = isExpanded ? '+' : '-';\n    }\n    \n    // 创建并触发自定义事件\n    const toggleEvent = new CustomEvent('menuToggle', {\n      bubbles: true,\n      detail: {\n        menuItem: menuItem,\n        expanded: !isExpanded,\n        level: this.getMenuLevel(menuItem)\n      }\n    });\n    \n    menuItem.dispatchEvent(toggleEvent);\n    \n    // 阻止事件冒泡，防止触发父菜单的点击事件\n    event.stopPropagation();\n  }\n  \n  handleMenuToggle(event) {\n    console.log(`菜单 ${event.detail.level} 级: ${event.detail.expanded ? '展开' : '折叠'}`);\n    \n    // 可以在这里添加额外的处理逻辑，如动画效果\n    const menuItem = event.detail.menuItem;\n    if (event.detail.expanded) {\n      menuItem.classList.add('expanded');\n    } else {\n      menuItem.classList.remove('expanded');\n    }\n  }\n  \n  getMenuLevel(menuItem) {\n    // 计算菜单层级\n    let level = 1;\n    let parent = menuItem.parentElement;\n    \n    while (parent && parent !== this.menuElement) {\n      if (parent.classList.contains('submenu')) {\n        level++;\n      }\n      parent = parent.parentElement;\n    }\n    \n    return level;\n  }\n  \n  // 公共方法：展开特定菜单\n  expandMenu(selector) {\n    const menuItem = this.menuElement.querySelector(selector);\n    if (!menuItem) return;\n    \n    const menuTitle = menuItem.querySelector('.menu-title');\n    const submenu = menuItem.querySelector('.submenu');\n    const toggleBtn = menuItem.querySelector('.toggle');\n    \n    if (menuTitle && submenu) {\n      submenu.style.display = 'block';\n      if (toggleBtn) toggleBtn.textContent = '-';\n      \n      // 创建并触发自定义事件\n      const toggleEvent = new CustomEvent('menuToggle', {\n        bubbles: true,\n        detail: {\n          menuItem: menuItem,\n          expanded: true,\n          level: this.getMenuLevel(menuItem)\n        }\n      });\n      \n      menuItem.dispatchEvent(toggleEvent);\n    }\n  }\n}\n\n// 初始化菜单系统\ndocument.addEventListener('DOMContentLoaded', () => {\n  const menuSystem = new MenuSystem('main-menu');\n  \n  // 示例：通过API展开特定菜单\n  // menuSystem.expandMenu('.menu-item:first-child');\n});", "explanation": "完整实现交互式菜单系统，使用事件委托处理菜单点击，通过自定义事件通知菜单状态变化，包含多级菜单的展开/折叠功能。"}]}}]}