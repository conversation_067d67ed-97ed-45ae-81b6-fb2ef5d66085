{"name": "Forms Handling", "trans": ["表单处理"], "methods": [{"name": "Controlled Components", "trans": ["受控组件"], "usage": {"syntax": "function Form() {\n  const [value, setValue] = useState('');\n  return (\n    <input value={value} onChange={e => setValue(e.target.value)} />\n  );\n}", "description": "受控组件是指表单元素的值由React状态（state）控制。每当输入发生变化时，通过onChange事件更新state，state的值再反映到表单元素上。这样可以完全掌控表单数据，便于校验、重置和联动。", "parameters": [{"name": "value", "description": "输入框的当前值，由state控制"}, {"name": "onChange", "description": "输入变化时的事件处理函数，负责更新state"}], "returnValue": "无返回值，表单数据存储在React state中", "examples": [{"code": "// 受控组件示例：单行文本输入\nimport React, { useState } from 'react';\n\nfunction NameInput() {\n  const [name, setName] = useState('');\n  return (\n    <div>\n      <label>姓名：\n        <input\n          value={name}\n          onChange={e => setName(e.target.value)}\n        />\n      </label>\n      <p>当前输入：{name}</p>\n    </div>\n  );\n}\n\n// 受控组件示例：多行文本、下拉框、复选框\nfunction MultiInput() {\n  const [desc, setDesc] = useState('');\n  const [gender, setGender] = useState('male');\n  const [agree, setAgree] = useState(false);\n  return (\n    <form>\n      <textarea\n        value={desc}\n        onChange={e => setDesc(e.target.value)}\n      />\n      <select value={gender} onChange={e => setGender(e.target.value)}>\n        <option value=\"male\">男</option>\n        <option value=\"female\">女</option>\n      </select>\n      <label>\n        <input\n          type=\"checkbox\"\n          checked={agree}\n          onChange={e => setAgree(e.target.checked)}\n        />同意协议\n      </label>\n    </form>\n  );\n}", "explanation": "第一个例子展示了受控输入框的基本用法，输入值始终由state控制。第二个例子展示了多种表单元素的受控写法，包括多行文本、下拉框和复选框。受控组件让表单数据和UI保持同步，便于后续处理。"}]}}, {"name": "Uncontrolled Components", "trans": ["非受控组件"], "usage": {"syntax": "function Form() {\n  const inputRef = useRef();\n  function handleSubmit(e) {\n    e.preventDefault();\n    alert(inputRef.current.value);\n  }\n  return (\n    <form onSubmit={handleSubmit}>\n      <input ref={inputRef} />\n      <button type=\"submit\">提交</button>\n    </form>\n  );\n}", "description": "非受控组件是指表单元素的值由DOM自身维护，React通过ref获取或操作表单值。适用于简单场景或需要直接操作DOM的情况。非受控组件不需要每次输入都更新state，代码更简洁，但不易做复杂校验和联动。", "parameters": [{"name": "ref", "description": "用于获取DOM节点的引用对象"}, {"name": "inputRef.current.value", "description": "通过ref访问的输入框当前值"}], "returnValue": "无返回值，表单数据存储在DOM节点中，通过ref访问", "examples": [{"code": "// 非受控组件示例：使用ref获取输入值\nimport React, { useRef } from 'react';\n\nfunction UncontrolledInput() {\n  const inputRef = useRef();\n  function showValue() {\n    alert(inputRef.current.value);\n  }\n  return (\n    <div>\n      <input ref={inputRef} defaultValue=\"默认值\" />\n      <button onClick={showValue}>显示输入值</button>\n    </div>\n  );\n}\n\n// 非受控表单提交\nfunction UncontrolledForm() {\n  const nameRef = useRef();\n  const ageRef = useRef();\n  function handleSubmit(e) {\n    e.preventDefault();\n    alert(`姓名：${nameRef.current.value} 年龄：${ageRef.current.value}`);\n  }\n  return (\n    <form onSubmit={handleSubmit}>\n      <input ref={nameRef} placeholder=\"姓名\" />\n      <input ref={ageRef} placeholder=\"年龄\" />\n      <button type=\"submit\">提交</button>\n    </form>\n  );\n}", "explanation": "第一个例子展示了如何用ref获取输入框的值，适合只需读取一次输入的场景。第二个例子展示了非受控表单的提交，所有数据都通过ref获取。非受控组件适合简单、无需频繁校验的表单。"}]}}, {"name": "Form Element Types", "trans": ["表单元素类型"], "usage": {"syntax": "<input type=\"text\" />\n<input type=\"checkbox\" />\n<input type=\"radio\" />\n<textarea />\n<select>...</select>", "description": "React支持多种表单元素类型，包括文本框、密码框、复选框、单选框、多行文本、下拉选择等。每种类型的受控写法略有不同，需根据实际需求选择合适的表单元素。", "parameters": [{"name": "type", "description": "表单元素的类型，如text、checkbox、radio等"}, {"name": "value/checked", "description": "元素的值或选中状态"}, {"name": "onChange", "description": "变化时的事件处理函数"}], "returnValue": "无返回值，表单数据通过state或ref管理", "examples": [{"code": "// 常见表单元素类型的受控写法\nimport React, { useState } from 'react';\n\nfunction FormElements() {\n  const [text, setText] = useState('');\n  const [checked, setChecked] = useState(false);\n  const [gender, setGender] = useState('male');\n  const [desc, setDesc] = useState('');\n  const [city, setCity] = useState('beijing');\n  return (\n    <form>\n      <input\n        type=\"text\"\n        value={text}\n        onChange={e => setText(e.target.value)}\n        placeholder=\"文本输入\"\n      />\n      <input\n        type=\"checkbox\"\n        checked={checked}\n        onChange={e => setChecked(e.target.checked)}\n      />同意\n      <label>\n        <input\n          type=\"radio\"\n          value=\"male\"\n          checked={gender === 'male'}\n          onChange={e => setGender(e.target.value)}\n        />男\n      </label>\n      <label>\n        <input\n          type=\"radio\"\n          value=\"female\"\n          checked={gender === 'female'}\n          onChange={e => setGender(e.target.value)}\n        />女\n      </label>\n      <textarea\n        value={desc}\n        onChange={e => setDesc(e.target.value)}\n        placeholder=\"多行文本\"\n      />\n      <select value={city} onChange={e => setCity(e.target.value)}>\n        <option value=\"beijing\">北京</option>\n        <option value=\"shanghai\">上海</option>\n        <option value=\"guangzhou\">广州</option>\n      </select>\n    </form>\n  );\n}", "explanation": "本例展示了文本框、复选框、单选框、多行文本和下拉框的受控写法。每种表单元素都通过value/checked和onChange与state绑定，实现数据同步。"}]}}, {"name": "Handling Multiple Inputs", "trans": ["处理多个输入"], "usage": {"syntax": "function Form() {\n  const [form, setForm] = useState({ name: '', age: '' });\n  function handleChange(e) {\n    setForm({ ...form, [e.target.name]: e.target.value });\n  }\n  return (\n    <form>\n      <input name=\"name\" value={form.name} onChange={handleChange} />\n      <input name=\"age\" value={form.age} onChange={handleChange} />\n    </form>\n  );\n}", "description": "在表单中处理多个输入时，通常将所有字段值存储在一个对象state中。通过name属性区分不同输入，onChange事件统一处理，动态更新对应字段。这样可以方便地管理和扩展表单字段。", "parameters": [{"name": "form", "description": "包含所有输入字段的state对象"}, {"name": "handleChange", "description": "统一处理所有输入变化的函数"}, {"name": "name", "description": "输入元素的name属性，用于区分字段"}], "returnValue": "无返回值，所有输入值存储在state对象中", "examples": [{"code": "// 处理多个输入的受控表单\nimport React, { useState } from 'react';\n\nfunction UserForm() {\n  const [form, setForm] = useState({\n    username: '',\n    email: '',\n    age: ''\n  });\n  function handleChange(e) {\n    setForm({ ...form, [e.target.name]: e.target.value });\n  }\n  return (\n    <form>\n      <input\n        name=\"username\"\n        value={form.username}\n        onChange={handleChange}\n        placeholder=\"用户名\"\n      />\n      <input\n        name=\"email\"\n        value={form.email}\n        onChange={handleChange}\n        placeholder=\"邮箱\"\n      />\n      <input\n        name=\"age\"\n        value={form.age}\n        onChange={handleChange}\n        placeholder=\"年龄\"\n      />\n      <p>当前表单：{JSON.stringify(form)}</p>\n    </form>\n  );\n}", "explanation": "本例展示了如何用一个state对象统一管理多个输入字段。handleChange函数根据name属性动态更新对应字段，适合字段较多或动态表单场景。"}]}}, {"name": "Form Submission", "trans": ["表单提交"], "usage": {"syntax": "function Form() {\n  function handleSubmit(e) {\n    e.preventDefault();\n    // 处理表单数据\n  }\n  return (\n    <form onSubmit={handleSubmit}>\n      <input ... />\n      <button type=\"submit\">提交</button>\n    </form>\n  );\n}", "description": "表单提交时，通常通过onSubmit事件拦截默认行为（防止页面刷新），在事件处理函数中收集和处理表单数据。可以进行校验、发送请求等操作。", "parameters": [{"name": "onSubmit", "description": "表单提交事件处理函数"}, {"name": "e.<PERSON><PERSON><PERSON><PERSON>", "description": "阻止表单默认提交行为"}], "returnValue": "无返回值，表单数据通过自定义逻辑处理", "examples": [{"code": "// 受控表单提交示例\nimport React, { useState } from 'react';\n\nfunction LoginForm() {\n  const [form, setForm] = useState({ username: '', password: '' });\n  function handleChange(e) {\n    setForm({ ...form, [e.target.name]: e.target.value });\n  }\n  function handleSubmit(e) {\n    e.preventDefault();\n    alert(`用户名：${form.username} 密码：${form.password}`);\n    // 这里可以发送请求\n  }\n  return (\n    <form onSubmit={handleSubmit}>\n      <input\n        name=\"username\"\n        value={form.username}\n        onChange={handleChange}\n        placeholder=\"用户名\"\n      />\n      <input\n        name=\"password\"\n        type=\"password\"\n        value={form.password}\n        onChange={handleChange}\n        placeholder=\"密码\"\n      />\n      <button type=\"submit\">登录</button>\n    </form>\n  );\n}", "explanation": "本例展示了如何通过onSubmit事件处理表单提交，阻止默认行为并收集表单数据。适用于登录、注册等需要提交数据的场景。"}]}}, {"name": "Form Validation", "trans": ["表单验证"], "usage": {"syntax": "function Form() {\n  const [form, setForm] = useState({ name: '', email: '' });\n  const [error, setError] = useState('');\n  function handleSubmit(e) {\n    e.preventDefault();\n    if (!form.name) { setError('姓名必填'); return; }\n    if (!/^\\S+@\\S+\\.\\S+$/.test(form.email)) { setError('邮箱格式错误'); return; }\n    setError('');\n    // 提交数据\n  }\n  // ...\n}", "description": "表单验证用于确保用户输入的数据符合要求。可以在提交时手动校验，也可以在输入时实时校验。常见做法包括必填校验、格式校验、长度限制等。", "parameters": [{"name": "error", "description": "存储校验错误信息的state"}, {"name": "handleSubmit", "description": "提交时进行校验的函数"}], "returnValue": "无返回值，校验结果通过state反馈到UI", "examples": [{"code": "// 简单表单验证示例\nimport React, { useState } from 'react';\n\nfunction RegisterForm() {\n  const [form, setForm] = useState({ name: '', email: '' });\n  const [error, setError] = useState('');\n  function handleChange(e) {\n    setForm({ ...form, [e.target.name]: e.target.value });\n  }\n  function handleSubmit(e) {\n    e.preventDefault();\n    if (!form.name) { setError('姓名必填'); return; }\n    if (!/^\\S+@\\S+\\.\\S+$/.test(form.email)) { setError('邮箱格式错误'); return; }\n    setError('');\n    alert('提交成功');\n  }\n  return (\n    <form onSubmit={handleSubmit}>\n      <input\n        name=\"name\"\n        value={form.name}\n        onChange={handleChange}\n        placeholder=\"姓名\"\n      />\n      <input\n        name=\"email\"\n        value={form.email}\n        onChange={handleChange}\n        placeholder=\"邮箱\"\n      />\n      {error && <p style={{ color: 'red' }}>{error}</p>}\n      <button type=\"submit\">注册</button>\n    </form>\n  );\n}", "explanation": "本例展示了如何在表单提交时进行必填和邮箱格式校验，校验结果通过error state反馈到UI。"}]}}, {"name": "Complex Form Solutions", "trans": ["复杂表单解决方案"], "usage": {"syntax": "// 使用第三方库如formik\nimport { useFormik } from 'formik';\n\nconst formik = useFormik({\n  initialValues: { name: '', email: '' },\n  onSubmit: values => { /* 处理提交 */ },\n  validate: values => { /* 校验逻辑 */ }\n});\n\n<form onSubmit={formik.handleSubmit}>\n  <input name=\"name\" value={formik.values.name} onChange={formik.handleChange} />\n  <input name=\"email\" value={formik.values.email} onChange={formik.handleChange} />\n</form>", "description": "对于字段多、校验复杂、交互丰富的表单，推荐使用如formik、react-hook-form等第三方库。它们提供了高效的状态管理、校验、错误提示、动态字段等功能，大大简化复杂表单开发。", "parameters": [{"name": "useFormik/useForm", "description": "第三方表单库的hook，用于管理表单状态和校验"}, {"name": "initialValues", "description": "表单初始值对象"}, {"name": "validate", "description": "自定义校验函数"}], "returnValue": "无返回值，表单数据和校验由库自动管理", "examples": [{"code": "// 使用formik管理复杂表单\nimport React from 'react';\nimport { useFormik } from 'formik';\n\nfunction SignupForm() {\n  const formik = useFormik({\n    initialValues: {\n      username: '',\n      email: '',\n      password: ''\n    },\n    validate: values => {\n      const errors = {};\n      if (!values.username) errors.username = '用户名必填';\n      if (!/^\\S+@\\S+\\.\\S+$/.test(values.email)) errors.email = '邮箱格式错误';\n      if (values.password.length < 6) errors.password = '密码至少6位';\n      return errors;\n    },\n    onSubmit: values => {\n      alert(JSON.stringify(values));\n    }\n  });\n  return (\n    <form onSubmit={formik.handleSubmit}>\n      <input\n        name=\"username\"\n        value={formik.values.username}\n        onChange={formik.handleChange}\n        placeholder=\"用户名\"\n      />\n      {formik.errors.username && <div style={{color:'red'}}>{formik.errors.username}</div>}\n      <input\n        name=\"email\"\n        value={formik.values.email}\n        onChange={formik.handleChange}\n        placeholder=\"邮箱\"\n      />\n      {formik.errors.email && <div style={{color:'red'}}>{formik.errors.email}</div>}\n      <input\n        name=\"password\"\n        type=\"password\"\n        value={formik.values.password}\n        onChange={formik.handleChange}\n        placeholder=\"密码\"\n      />\n      {formik.errors.password && <div style={{color:'red'}}>{formik.errors.password}</div>}\n      <button type=\"submit\">注册</button>\n    </form>\n  );\n}", "explanation": "本例展示了如何用formik管理复杂表单，包括状态、校验和错误提示。对于大表单或动态表单，推荐使用专业库提升开发效率和可维护性。"}]}}, {"name": "Assignment: 表单处理练习", "trans": ["作业：表单处理练习"], "usage": {"syntax": "// 作业要求见description", "description": "1. 创建一个注册表单，包含用户名、邮箱、密码字段，要求全部为受控组件。\n2. 实现表单提交，提交时进行必填和邮箱格式校验。\n3. 尝试用一个state对象管理所有输入。\n4. （进阶）使用formik或react-hook-form重构表单，体验第三方库的优势。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 示例：注册表单作业\nimport React, { useState } from 'react';\n\nfunction RegisterForm() {\n  const [form, setForm] = useState({ username: '', email: '', password: '' });\n  const [error, setError] = useState('');\n  function handleChange(e) {\n    setForm({ ...form, [e.target.name]: e.target.value });\n  }\n  function handleSubmit(e) {\n    e.preventDefault();\n    if (!form.username) { setError('用户名必填'); return; }\n    if (!/^\\S+@\\S+\\.\\S+$/.test(form.email)) { setError('邮箱格式错误'); return; }\n    if (form.password.length < 6) { setError('密码至少6位'); return; }\n    setError('');\n    alert('注册成功');\n  }\n  return (\n    <form onSubmit={handleSubmit}>\n      <input name=\"username\" value={form.username} onChange={handleChange} placeholder=\"用户名\" />\n      <input name=\"email\" value={form.email} onChange={handleChange} placeholder=\"邮箱\" />\n      <input name=\"password\" type=\"password\" value={form.password} onChange={handleChange} placeholder=\"密码\" />\n      {error && <div style={{color:'red'}}>{error}</div>}\n      <button type=\"submit\">注册</button>\n    </form>\n  );\n}\n\n// 作业：\n// 1. 用formik重构RegisterForm，实现相同功能\n// 2. 尝试添加更多字段和校验逻辑", "explanation": "本作业要求你综合运用本节知识，练习受控组件、表单提交、校验和第三方库。通过动手实践，加深对React表单处理机制的理解。"}]}}]}