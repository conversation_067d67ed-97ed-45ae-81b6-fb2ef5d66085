{"name": "useMemo", "trans": ["useMemo钩子"], "methods": [{"name": "Value Memoization", "trans": ["值记忆化"], "usage": {"syntax": "const memoizedValue = useMemo(() => computeExpensiveValue(a, b), [a, b])", "description": "useMemo 是 React 的一个 Hook，用于记忆化计算值的结果。它接收一个创建函数和一个依赖项数组作为参数，仅在依赖项发生变化时重新计算记忆化的值。这可以避免在每次渲染时都进行昂贵的计算，从而提高组件性能。", "parameters": [{"name": "createFn", "description": "一个函数，用于计算需要被记忆化的值"}, {"name": "dependencies", "description": "依赖项数组，只有当这些依赖项发生变化时，才会重新计算值"}], "returnValue": "返回记忆化的计算值，仅在依赖项变化时更新", "examples": [{"code": "import React, { useState, useMemo } from 'react';\n\nfunction ExpensiveCalculation({ a, b }) {\n  // 使用 useMemo 记忆化计算结果\n  const memoizedResult = useMemo(() => {\n    console.log('执行昂贵计算...');\n    // 模拟复杂计算\n    return a * b * Math.random() * 100000;\n  }, [a, b]); // 只有当 a 或 b 变化时才重新计算\n\n  return (\n    <div>\n      <p>输入值: a = {a}, b = {b}</p>\n      <p>计算结果: {memoizedResult.toFixed(2)}</p>\n    </div>\n  );\n}\n\nfunction App() {\n  const [a, setA] = useState(1);\n  const [b, setB] = useState(2);\n  const [counter, setCounter] = useState(0);\n\n  return (\n    <div>\n      <div>\n        <label>A: </label>\n        <input \n          type=\"number\" \n          value={a} \n          onChange={(e) => setA(Number(e.target.value))}\n        />\n      </div>\n      <div>\n        <label>B: </label>\n        <input \n          type=\"number\" \n          value={b} \n          onChange={(e) => setB(Number(e.target.value))}\n        />\n      </div>\n      <button onClick={() => setCounter(counter + 1)}>\n        重新渲染 (计数: {counter})\n      </button>\n      <ExpensiveCalculation a={a} b={b} />\n    </div>\n  );", "explanation": "这个例子展示了 useMemo 的基本用法。在 ExpensiveCalculation 组件中，使用 useMemo 记忆化了一个昂贵的计算结果。只有当输入参数 a 或 b 变化时，才会重新执行计算；当父组件因为 counter 状态变化而重新渲染时，计算不会重新执行。"}]}}, {"name": "Dependency Array", "trans": ["依赖数组"], "usage": {"syntax": "useMemo(() => computation, [dep1, dep2, ...])", "description": "useMemo 的第二个参数是依赖数组，决定了计算函数何时需要重新执行。依赖数组中的值如果发生变化，React 将重新执行计算函数；如果依赖数组中的值保持不变，React 将返回上次记忆化的结果。依赖数组的工作方式类似于 useEffect 和 useCallback。", "parameters": [{"name": "dependencies", "description": "一个数组，包含计算函数中使用的外部变量"}], "returnValue": "无返回值", "examples": [{"code": "import React, { useState, useMemo } from 'react';\n\nfunction FilteredList() {\n  const [items] = useState(['苹果', '香蕉', '橙子', '葡萄', '猕猴桃', '西瓜']);\n  const [filter, setFilter] = useState('');\n  const [count, setCount] = useState(0);\n  \n  // 空依赖数组 - 计算只执行一次\n  const constantValue = useMemo(() => {\n    console.log('计算常量值');\n    return Math.random();\n  }, []);\n  \n  // 单一依赖 - 只有 filter 变化时才重新计算\n  const filteredItems = useMemo(() => {\n    console.log('过滤列表项...');\n    return items.filter(item => item.includes(filter));\n  }, [filter, items]);\n  \n  return (\n    <div>\n      <div>\n        <input\n          value={filter}\n          onChange={(e) => setFilter(e.target.value)}\n          placeholder=\"输入过滤条件...\"\n        />\n      </div>\n      <button onClick={() => setCount(count + 1)}>\n        增加计数 ({count})\n      </button>\n      <p>常量值: {constantValue}</p>\n      <ul>\n        {filteredItems.map((item, index) => (\n          <li key={index}>{item}</li>\n        ))}\n      </ul>\n    </div>\n  );\n}", "explanation": "这个例子展示了不同依赖数组配置的 useMemo 用法。constantValue 使用空依赖数组，所以只计算一次；filteredItems 依赖于 filter 和 items，只有它们变化时才重新计算。当点击按钮增加 count 时，这些记忆化的值不会重新计算。"}]}}, {"name": "Computed Properties", "trans": ["计算属性"], "usage": {"syntax": "const computedValue = useMemo(() => computeFromProps(props), [dependencies])", "description": "useMemo 可以用来创建类似 Vue 中计算属性的功能，即基于其他状态或属性派生出的值。这些计算属性只有在依赖的状态或属性变化时才会重新计算，避免了每次渲染都进行不必要的计算。使用 useMemo 实现计算属性特别适合复杂的数据转换、过滤或聚合操作。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import React, { useState, useMemo } from 'react';\n\nfunction ProductList() {\n  const [products, setProducts] = useState([\n    { id: 1, name: '笔记本电脑', price: 6999, category: '电子产品' },\n    { id: 2, name: '手机', price: 3999, category: '电子产品' },\n    { id: 3, name: '书籍', price: 59, category: '图书' },\n    { id: 4, name: '鼠标', price: 99, category: '电子产品' },\n    { id: 5, name: '键盘', price: 249, category: '电子产品' },\n    { id: 6, name: '显示器', price: 1299, category: '电子产品' }\n  ]);\n  \n  const [minPrice, setMinPrice] = useState(0);\n  const [maxPrice, setMaxPrice] = useState(10000);\n  const [selectedCategory, setSelectedCategory] = useState('所有');\n  \n  // 计算属性：过滤后的产品列表\n  const filteredProducts = useMemo(() => {\n    console.log('计算过滤后的产品...');\n    return products.filter(product => {\n      const matchesPrice = product.price >= minPrice && product.price <= maxPrice;\n      const matchesCategory = selectedCategory === '所有' || product.category === selectedCategory;\n      return matchesPrice && matchesCategory;\n    });\n  }, [products, minPrice, maxPrice, selectedCategory]);\n  \n  // 计算属性：产品数量统计\n  const stats = useMemo(() => {\n    console.log('计算产品统计...');\n    const totalProducts = filteredProducts.length;\n    const totalValue = filteredProducts.reduce((sum, product) => sum + product.price, 0);\n    const averagePrice = totalProducts > 0 ? totalValue / totalProducts : 0;\n    \n    return {\n      totalProducts,\n      totalValue,\n      averagePrice\n    };\n  }, [filteredProducts]);\n  \n  // 计算属性：可用类别列表\n  const categories = useMemo(() => {\n    console.log('计算可用类别...');\n    const uniqueCategories = new Set(products.map(product => product.category));\n    return ['所有', ...uniqueCategories];\n  }, [products]);\n  \n  return (\n    <div>\n      <div className=\"filters\">\n        <div>\n          <label>最低价格: </label>\n          <input \n            type=\"number\" \n            value={minPrice} \n            onChange={(e) => setMinPrice(Number(e.target.value))}\n          />\n        </div>\n        <div>\n          <label>最高价格: </label>\n          <input \n            type=\"number\" \n            value={maxPrice} \n            onChange={(e) => setMaxPrice(Number(e.target.value))}\n          />\n        </div>\n        <div>\n          <label>类别: </label>\n          <select \n            value={selectedCategory} \n            onChange={(e) => setSelectedCategory(e.target.value)}\n          >\n            {categories.map(category => (\n              <option key={category} value={category}>{category}</option>\n            ))}\n          </select>\n        </div>\n      </div>\n      \n      <div className=\"stats\">\n        <p>产品数量: {stats.totalProducts}</p>\n        <p>总价值: ¥{stats.totalValue}</p>\n        <p>平均价格: ¥{stats.averagePrice.toFixed(2)}</p>\n      </div>\n      \n      <ul className=\"product-list\">\n        {filteredProducts.map(product => (\n          <li key={product.id}>\n            <h3>{product.name}</h3>\n            <p>价格: ¥{product.price}</p>\n            <p>类别: {product.category}</p>\n          </li>\n        ))}\n      </ul>\n    </div>\n  );\n}", "explanation": "这个例子展示了如何使用 useMemo 实现计算属性。我们定义了三个计算属性：filteredProducts（根据价格和类别过滤的产品列表）、stats（产品统计信息）和 categories（可用类别列表）。每个计算属性都只在其依赖变化时重新计算，例如 stats 依赖于 filteredProducts，只有当过滤后的产品列表变化时才会重新计算统计信息。"}]}}, {"name": "Complex Calculation Optimization", "trans": ["复杂计算优化"], "usage": {"syntax": "useMemo(() => expensiveCalculation(params), [params])", "description": "useMemo 特别适合优化复杂计算，如大量数据处理、复杂算法、递归操作、图表数据准备等。通过避免每次渲染都重新执行这些昂贵的计算，可以显著提高应用性能。识别应用中的性能瓶颈，并在适当的地方应用 useMemo，可以使应用保持流畅响应。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import React, { useState, useMemo } from 'react';\n\n// 复杂计算：生成斐波那契数列\nfunction fibon<PERSON>ci(n) {\n  if (n <= 1) return n;\n  return fibonacci(n - 1) + fibonacci(n - 2);\n}\n\n// 复杂计算：素数检测\nfunction isPrime(num) {\n  if (num <= 1) return false;\n  if (num <= 3) return true;\n  if (num % 2 === 0 || num % 3 === 0) return false;\n  \n  let i = 5;\n  while (i * i <= num) {\n    if (num % i === 0 || num % (i + 2) === 0) return false;\n    i += 6;\n  }\n  return true;\n}\n\n// 复杂计算：查找区间内的所有素数\nfunction findPrimes(start, end) {\n  const primes = [];\n  for (let i = start; i <= end; i++) {\n    if (isPrime(i)) {\n      primes.push(i);\n    }\n  }\n  return primes;\n}\n\nfunction ComplexCalculations() {\n  const [fibN, setFibN] = useState(30);\n  const [primeRange, setPrimeRange] = useState({ start: 1, end: 1000 });\n  const [rerender, setRerender] = useState(0);\n  \n  // 使用 useMemo 优化斐波那契数计算\n  const fibResult = useMemo(() => {\n    console.log(`计算斐波那契数(${fibN})...`);\n    const startTime = performance.now();\n    const result = fibonacci(fibN);\n    const endTime = performance.now();\n    return { result, time: endTime - startTime };\n  }, [fibN]);\n  \n  // 使用 useMemo 优化素数查找\n  const primes = useMemo(() => {\n    console.log(`查找素数(${primeRange.start}-${primeRange.end})...`);\n    const startTime = performance.now();\n    const result = findPrimes(primeRange.start, primeRange.end);\n    const endTime = performance.now();\n    return { result, time: endTime - startTime };\n  }, [primeRange.start, primeRange.end]);\n  \n  return (\n    <div>\n      <h2>复杂计算优化</h2>\n      \n      <div>\n        <h3>斐波那契数列</h3>\n        <div>\n          <label>计算第 N 个斐波那契数: </label>\n          <input \n            type=\"number\" \n            value={fibN} \n            onChange={(e) => setFibN(Number(e.target.value))} \n            min=\"1\" \n            max=\"40\"\n          />\n        </div>\n        <p>\n          结果: {fibResult.result} (计算耗时: {fibResult.time.toFixed(2)}ms)\n        </p>\n      </div>\n      \n      <div>\n        <h3>素数查找</h3>\n        <div>\n          <label>起始值: </label>\n          <input \n            type=\"number\" \n            value={primeRange.start} \n            onChange={(e) => setPrimeRange(prev => ({ ...prev, start: Number(e.target.value) }))} \n            min=\"1\"\n          />\n        </div>\n        <div>\n          <label>结束值: </label>\n          <input \n            type=\"number\" \n            value={primeRange.end} \n            onChange={(e) => setPrimeRange(prev => ({ ...prev, end: Number(e.target.value) }))} \n            min={primeRange.start}\n            max=\"10000\"\n          />\n        </div>\n        <p>\n          找到 {primes.result.length} 个素数 (计算耗时: {primes.time.toFixed(2)}ms)\n        </p>\n        <div style={{ maxHeight: '200px', overflow: 'auto' }}>\n          {primes.result.join(', ')}\n        </div>\n      </div>\n      \n      <div>\n        <button onClick={() => setRerender(rerender + 1)}>\n          强制重新渲染 (计数: {rerender})\n        </button>\n        <p><small>点击按钮不会触发复杂计算，因为计算结果已被记忆化</small></p>\n      </div>\n    </div>\n  );\n}", "explanation": "这个例子展示了如何使用 useMemo 优化两个复杂计算：斐波那契数列计算和素数查找。这些都是计算密集型操作，如果不使用 useMemo，每次组件重新渲染时都会重新执行这些昂贵的计算，导致性能问题。通过使用 useMemo，这些计算只在依赖项（如 fibN 或 primeRange）变化时才会重新执行，而点击\"强制重新渲染\"按钮时不会触发重新计算。例子还显示了计算耗时，帮助理解优化的效果。"}]}}, {"name": "useMemo vs useCallback", "trans": ["与useCallback对比"], "usage": {"syntax": "// useMemo: const memoizedValue = useMemo(() => computeValue(a, b), [a, b]);\n// useCallback: const memoizedCallback = useCallback(() => doSomething(a, b), [a, b]);", "description": "useMemo 和 useCallback 都是 React 的性能优化 Hook，它们有相似的用法但用途不同。useMemo 用于记忆化计算值的结果，而 useCallback 用于记忆化回调函数。简单来说，useMemo 记忆化值，useCallback 记忆化函数。在语法上，useMemo 的回调函数需要返回一个值，而 useCallback 直接返回记忆化的函数本身。选择使用哪个 Hook 取决于你需要记忆化的是计算结果还是函数引用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import React, { useState, useMemo, useCallback } from 'react';\n\nfunction MemoComparisonExample() {\n  const [count, setCount] = useState(0);\n  const [text, setText] = useState('');\n  \n  // 使用 useMemo 记忆化计算结果\n  const expensiveValue = useMemo(() => {\n    console.log('计算 expensiveValue');\n    // 假设这是一个昂贵的计算\n    return count * 2;\n  }, [count]); // 只依赖于 count\n  \n  // 使用 useCallback 记忆化函数\n  const handleButtonClick = useCallback(() => {\n    console.log('处理按钮点击，当前 count:', count);\n    setCount(count + 1);\n  }, [count]); // 只依赖于 count\n  \n  // 不使用 useMemo - 每次渲染都会重新计算\n  const nonMemoizedValue = count * 2;\n  console.log('重新计算 nonMemoizedValue:', nonMemoizedValue);\n  \n  // 不使用 useCallback - 每次渲染都会创建新的函数引用\n  const nonMemoizedCallback = () => {\n    console.log('处理文本变化');\n    // 一些与 count 或 text 无关的操作\n  };\n  \n  return (\n    <div>\n      <h2>useMemo vs useCallback</h2>\n      \n      <div>\n        <p>Count: {count}</p>\n        <p>Memoized Value (useMemo): {expensiveValue}</p>\n        <p>Non-memoized Value: {nonMemoizedValue}</p>\n        <button onClick={handleButtonClick}>增加计数</button>\n      </div>\n      \n      <div>\n        <input\n          type=\"text\"\n          value={text}\n          onChange={(e) => setText(e.target.value)}\n          placeholder=\"输入不会影响记忆化的值\"\n        />\n      </div>\n      \n      <div>\n        <ChildComponent\n          onMemoizedClick={handleButtonClick} // 使用 useCallback 记忆化的函数\n          onNonMemoizedClick={nonMemoizedCallback} // 非记忆化的函数\n          text={text}\n        />\n      </div>\n    </div>\n  );\n}\n\n// 使用 React.memo 包装的子组件\nconst ChildComponent = React.memo(({ onMemoizedClick, onNonMemoizedClick, text }) => {\n  console.log('ChildComponent 渲染, text:', text);\n  return (\n    <div>\n      <p>子组件文本: {text}</p>\n      <button onClick={onMemoizedClick}>记忆化点击</button>\n      <button onClick={onNonMemoizedClick}>非记忆化点击</button>\n    </div>\n  );\n});\n\n// 对比: 简化语法展示区别\nfunction SyntaxComparisonExample() {\n  const [a, setA] = useState(5);\n  const [b, setB] = useState(10);\n  \n  // useMemo: 记忆化计算值\n  const sum = useMemo(() => {\n    return a + b; // 返回计算结果\n  }, [a, b]);\n  \n  // useCallback: 记忆化函数\n  const handleSum = useCallback(() => {\n    console.log(a + b); // 不返回值，而是保存函数引用\n  }, [a, b]);\n  \n  return (\n    <div>\n      <p>a: {a}, b: {b}</p>\n      <p>记忆化计算值 (useMemo): {sum}</p>\n      <button onClick={handleSum}>执行记忆化函数 (useCallback)</button>\n    </div>\n  );\n}", "explanation": "这个例子对比了 useMemo 和 useCallback 的用法和区别。useMemo 用于记忆化计算值 expensiveValue，只有当 count 变化时才重新计算；useCallback 用于记忆化函数 handleButtonClick，同样只有当 count 变化时才创建新函数。当输入框文本变化导致组件重新渲染时，记忆化的值和函数引用不会改变，而非记忆化的值和函数则会重新计算/创建。使用 React.memo 包装的子组件可以避免因为非记忆化函数引用变化而不必要地重新渲染。"}]}}, {"name": "Best Practices", "trans": ["最佳实践"], "usage": {"syntax": "useMemo(() => computation, dependencies)", "description": "useMemo 是一个强大的工具，但应该明智地使用它。最佳实践包括：1) 只对昂贵的计算使用 useMemo，不要过度优化；2) 确保依赖数组包含计算中使用的所有外部变量；3) 考虑提取复杂计算逻辑到组件外部；4) 使用 React DevTools Profiler 识别实际性能瓶颈；5) 避免在 useMemo 回调中执行有副作用的操作，这应该在 useEffect 中进行。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import React, { useState, useMemo } from 'react';\n\n// 最佳实践1：将复杂计算抽取到组件外部\nfunction calculateStats(data) {\n  console.log('执行昂贵的统计计算...');\n  // 模拟复杂计算\n  let sum = 0;\n  let max = -Infinity;\n  let min = Infinity;\n  \n  for (const value of data) {\n    sum += value;\n    max = Math.max(max, value);\n    min = Math.min(min, value);\n  }\n  \n  return {\n    sum,\n    average: data.length > 0 ? sum / data.length : 0,\n    max,\n    min\n  };\n}\n\nfunction BestPracticesExample() {\n  const [data, setData] = useState([10, 23, 45, 67, 89, 12, 34, 56, 78, 90]);\n  const [counter, setCounter] = useState(0);\n  \n  // 最佳实践2：只对昂贵的计算使用 useMemo\n  const stats = useMemo(() => calculateStats(data), [data]);\n  \n  // 不需要记忆化的简单计算\n  const dataLength = data.length;\n  \n  // 最佳实践3：使用函数式更新避免依赖项\n  const addRandomValue = () => {\n    setData(prevData => [...prevData, Math.floor(Math.random() * 100)]);\n  };\n  \n  return (\n    <div>\n      <h2>useMemo 最佳实践</h2>\n      \n      <div>\n        <p>数据点数量: {dataLength}</p>\n        <p>总和: {stats.sum}</p>\n        <p>平均值: {stats.average.toFixed(2)}</p>\n        <p>最大值: {stats.max}</p>\n        <p>最小值: {stats.min}</p>\n      </div>\n      \n      <div>\n        <button onClick={addRandomValue}>添加随机值</button>\n        <button onClick={() => setCounter(counter + 1)}>\n          增加计数器 (无关操作): {counter}\n        </button>\n      </div>\n      \n      <div>\n        <h3>数据:</h3>\n        <ul>\n          {data.map((value, index) => (\n            <li key={index}>{value}</li>\n          ))}\n        </ul>\n      </div>\n    </div>\n  );\n}\n\n// 最佳实践4：避免过度使用 useMemo\nfunction AvoidOverOptimization() {\n  const [name, setName] = useState('');\n  \n  // 不需要 useMemo 的简单操作\n  const greeting = `你好，${name || '访客'}`;\n  \n  // 只有在有明显性能问题时才使用 useMemo\n  // 不要这样做：\n  // const greeting = useMemo(() => `你好，${name || '访客'}`, [name]);\n  \n  return (\n    <div>\n      <input\n        value={name}\n        onChange={(e) => setName(e.target.value)}\n        placeholder=\"输入你的名字\"\n      />\n      <p>{greeting}</p>\n    </div>\n  );\n}", "explanation": "这个例子演示了使用 useMemo 的几个最佳实践。我们将复杂计算提取到组件外部，只对昂贵的统计计算使用 useMemo，而不对简单计算使用。通过设置一个无关的计数器，我们可以看到当组件因为与数据无关的状态变化而重新渲染时，统计计算不会重新执行。例子还展示了避免过度优化的原则，对于简单操作不应该使用 useMemo。"}]}}, {"name": "Assignment: Create a Data Dashboard", "trans": ["作业：创建数据仪表板"], "usage": {"syntax": "// 使用 useMemo 优化数据仪表板", "description": "创建一个数据仪表板应用，显示大量数据的各种统计信息和可视化结果。使用 useMemo 优化计算密集型操作，确保仪表板在用户交互时保持流畅响应。实践中应用 useMemo 的最佳实践，只对真正昂贵的计算使用记忆化。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 任务要求：\n// 1. 创建一个数据仪表板，显示销售数据的各种统计信息\n// 2. 使用 useMemo 优化计算密集型操作\n// 3. 实现过滤和排序功能，并确保在这些操作时保持良好性能\n// 4. 应用 useMemo 的最佳实践\n\n// 参考实现框架：\n\nimport React, { useState, useMemo } from 'react';\n\nfunction DataDashboard() {\n  // 模拟销售数据\n  const [sales, setSales] = useState([\n    // 示例数据格式：\n    // { id: 1, product: '产品A', category: '电子', price: 1999, quantity: 5, date: '2023-01-15' },\n    // 需要添加更多模拟数据...\n  ]);\n  \n  // 过滤条件\n  const [filters, setFilters] = useState({\n    category: 'all',\n    minPrice: 0,\n    maxPrice: Infinity,\n    startDate: '',\n    endDate: ''\n  });\n  \n  // 排序\n  const [sortConfig, setSortConfig] = useState({\n    key: 'date',\n    direction: 'desc'\n  });\n  \n  // 使用 useMemo 优化过滤后的数据\n  const filteredSales = useMemo(() => {\n    console.log('计算过滤后的销售数据...');\n    // 实现过滤逻辑\n    return sales.filter(sale => {\n      // 根据 filters 对象实现过滤条件\n      // ...\n    });\n  }, [sales, filters]);\n  \n  // 使用 useMemo 优化排序后的数据\n  const sortedSales = useMemo(() => {\n    console.log('排序过滤后的销售数据...');\n    // 实现排序逻辑\n    return [...filteredSales].sort((a, b) => {\n      // 根据 sortConfig 实现排序逻辑\n      // ...\n    });\n  }, [filteredSales, sortConfig]);\n  \n  // 使用 useMemo 计算统计信息\n  const statistics = useMemo(() => {\n    console.log('计算销售统计信息...');\n    // 计算总销售额、平均订单价值、最畅销商品等\n    // ...\n    return {\n      totalSales: 0,\n      averageOrderValue: 0,\n      topProduct: '',\n      // 其他统计指标...\n    };\n  }, [sortedSales]);\n  \n  // 实现处理过滤变化的函数\n  const handleFilterChange = (name, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  \n  // 实现处理排序变化的函数\n  const handleSortChange = (key) => {\n    setSortConfig(prev => ({\n      key,\n      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'\n    }));\n  };\n  \n  return (\n    <div className=\"dashboard\">\n      <h1>销售数据仪表板</h1>\n      \n      {/* 过滤控件 */}\n      <div className=\"filters\">\n        {/* 实现类别、价格范围、日期范围的过滤控件 */}\n      </div>\n      \n      {/* 统计卡片 */}\n      <div className=\"statistics\">\n        {/* 显示各种统计信息的卡片 */}\n      </div>\n      \n      {/* 数据表格 */}\n      <table className=\"data-table\">\n        <thead>\n          {/* 表头，带有排序功能 */}\n        </thead>\n        <tbody>\n          {/* 渲染排序和过滤后的销售数据 */}\n        </tbody>\n      </table>\n    </div>\n  );\n}\n\n// 提示：\n// 1. 添加足够多的模拟数据以测试性能优化效果\n// 2. 完善过滤和排序逻辑\n// 3. 实现统计计算\n// 4. 添加合适的UI组件和样式\n// 5. 考虑如何通过 useMemo 优化性能，特别是在处理大量数据时", "explanation": "这个作业要求学生创建一个使用 useMemo 优化的数据仪表板。学生需要实现销售数据的过滤、排序和统计计算，并使用 useMemo 优化这些计算密集型操作。这个练习结合了 useMemo 的各种用法和最佳实践，特别适合理解何时以及如何使用 useMemo 进行性能优化。"}]}}]}