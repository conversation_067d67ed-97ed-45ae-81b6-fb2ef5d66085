{"name": "Input and Output", "trans": ["输入输出"], "methods": [{"name": "input Function", "trans": ["input函数"], "usage": {"syntax": "input('提示信息')", "description": "input()用于接收用户输入，返回字符串。可传入提示信息。", "parameters": [{"name": "提示信息", "description": "输入时显示的提示文本，可省略"}], "returnValue": "用户输入的字符串", "examples": [{"code": "name = input('请输入姓名：')\nprint('你好,', name)", "explanation": "演示了input函数的用法。"}]}}, {"name": "print Function", "trans": ["print函数"], "usage": {"syntax": "print(值, ..., sep=' ', end='\n')", "description": "print()用于输出内容，可指定多个值、分隔符sep和结尾end。", "parameters": [{"name": "值", "description": "要输出的内容，可多个"}, {"name": "sep", "description": "分隔符，默认空格"}, {"name": "end", "description": "结尾字符，默认换行"}], "returnValue": "无返回值", "examples": [{"code": "print('A', 'B', 'C', sep='-', end='!')  # 输出：A-B-C!", "explanation": "演示了print函数的常用参数。"}]}}, {"name": "Formatted Output", "trans": ["格式化输出（%、format、f-string）"], "usage": {"syntax": "'%d' % 值\n'{}'.format(值)\nf'{值}'", "description": "支持三种常用格式化方式：百分号%、str.format()、f-string（推荐，3.6+）。", "parameters": [{"name": "值", "description": "要格式化的内容"}], "returnValue": "格式化后的字符串", "examples": [{"code": "age = 18\nprint('我今年%d岁' % age)\nprint('我今年{}岁'.format(age))\nprint(f'我今年{age}岁')", "explanation": "演示了三种格式化输出方式。"}]}}, {"name": "Standard Input/Output and Redirection", "trans": ["标准输入输出与重定向"], "usage": {"syntax": "import sys\nsys.stdin\nsys.stdout\nsys.stderr", "description": "sys.stdin、sys.stdout、sys.stderr分别表示标准输入、输出、错误流。可重定向到文件。", "parameters": [{"name": "sys.stdin", "description": "标准输入流"}, {"name": "sys.stdout", "description": "标准输出流"}, {"name": "sys.stderr", "description": "标准错误流"}], "returnValue": "文件对象或流对象", "examples": [{"code": "import sys\nprint('hello', file=sys.stdout)\nwith open('out.txt', 'w') as f:\n    print('写入文件', file=f)", "explanation": "演示了标准输出和重定向。"}]}}, {"name": "Input and Output Assignment", "trans": ["输入输出练习"], "usage": {"syntax": "# 输入输出练习", "description": "完成以下练习，巩固输入输出相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 输入姓名和年龄并格式化输出。\n2. 用print输出多行内容到文件。\n3. 用input和print实现简单交互。\n4. 用f-string格式化输出浮点数保留两位小数。", "explanation": "练习要求动手实践input、print、格式化、重定向等。"}]}}]}