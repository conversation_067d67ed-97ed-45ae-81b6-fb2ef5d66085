{"name": "Component Testing", "trans": ["组件测试"], "methods": [{"name": "Component Render Testing", "trans": ["组件渲染测试"], "usage": {"syntax": "import { render, screen } from '@testing-library/react';\nrender(<MyComponent />);\nexpect(screen.getByText('内容')).toBeInTheDocument();", "description": "组件渲染测试用于验证组件能否正确渲染指定内容，常用React Testing Library进行DOM断言。", "parameters": [{"name": "component", "description": "待测试的React组件。"}], "returnValue": "无返回值，断言通过即测试通过。", "examples": [{"code": "import { render, screen } from '@testing-library/react';\nfunction Hello() { return <div>你好</div>; }\ntest('渲染内容', () => {\n  render(<Hello />);\n  expect(screen.getByText('你好')).toBeInTheDocument();\n});", "explanation": "测试组件是否正确渲染指定内容。"}]}}, {"name": "Interaction Testing", "trans": ["交互测试"], "usage": {"syntax": "import { render, screen } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nrender(<Button />);\nuserEvent.click(screen.getByRole('button'));\nexpect(...).toBe(...);", "description": "交互测试用于验证用户操作（如点击、输入）后组件的行为和状态变化。", "parameters": [{"name": "component", "description": "待测试的React组件。"}, {"name": "userEvent", "description": "用户交互模拟工具。"}], "returnValue": "无返回值，断言通过即测试通过。", "examples": [{"code": "import { render, screen } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nfunction Counter() {\n  const [count, setCount] = useState(0);\n  return <button onClick={() => setCount(c => c + 1)}>计数: {count}</button>;\n}\ntest('点击按钮递增', () => {\n  render(<Counter />);\n  userEvent.click(screen.getByRole('button'));\n  expect(screen.getByText('计数: 1')).toBeInTheDocument();\n});", "explanation": "测试点击按钮后计数是否递增。"}]}}, {"name": "Snapshot Testing", "trans": ["快照测试"], "usage": {"syntax": "import { render } from '@testing-library/react';\nimport renderer from 'react-test-renderer';\nconst tree = renderer.create(<MyComponent />).toJSON();\nexpect(tree).toMatchSnapshot();", "description": "快照测试用于检测组件渲染输出是否发生变化，适合回归测试和UI一致性校验。", "parameters": [{"name": "component", "description": "待测试的React组件。"}], "returnValue": "无返回值，快照一致即测试通过。", "examples": [{"code": "import renderer from 'react-test-renderer';\nfunction Hello() { return <div>Hi</div>; }\ntest('快照测试', () => {\n  const tree = renderer.create(<Hello />).toJSON();\n  expect(tree).toMatchSnapshot();\n});", "explanation": "生成组件快照并校验渲染输出是否一致。"}]}}, {"name": "Event Testing", "trans": ["事件测试"], "usage": {"syntax": "import { render, screen } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nrender(<Button onClick={fn} />);\nuserEvent.click(screen.getByRole('button'));\nexpect(fn).toHaveBeenCalled();", "description": "事件测试用于验证事件处理函数是否被正确调用，常用jest.fn()配合userEvent模拟事件。", "parameters": [{"name": "component", "description": "待测试的React组件。"}, {"name": "fn", "description": "事件处理函数mock。"}], "returnValue": "无返回值，断言通过即测试通过。", "examples": [{"code": "import { render, screen } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nconst handleClick = jest.fn();\nfunction Button() { return <button onClick={handleClick}>点我</button>; }\ntest('点击事件', () => {\n  render(<Button />);\n  userEvent.click(screen.getByRole('button'));\n  expect(handleClick).toHaveBeenCalled();\n});", "explanation": "测试点击事件处理函数是否被调用。"}]}}, {"name": "State Change Testing", "trans": ["状态变化测试"], "usage": {"syntax": "import { render, screen } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nrender(<Counter />);\nuserEvent.click(screen.getByRole('button'));\nexpect(screen.getByText('计数: 1')).toBeInTheDocument();", "description": "状态变化测试关注组件内部状态在交互后的变化，常结合事件和断言验证。", "parameters": [{"name": "component", "description": "待测试的React组件。"}], "returnValue": "无返回值，断言通过即测试通过。", "examples": [{"code": "import { render, screen } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nfunction Counter() {\n  const [count, setCount] = useState(0);\n  return <button onClick={() => setCount(c => c + 1)}>计数: {count}</button>;\n}\ntest('状态变化', () => {\n  render(<Counter />);\n  userEvent.click(screen.getByRole('button'));\n  expect(screen.getByText('计数: 1')).toBeInTheDocument();\n});", "explanation": "测试组件状态变化是否正确。"}]}}, {"name": "Props Validation", "trans": ["属性验证"], "usage": {"syntax": "import { render, screen } from '@testing-library/react';\nrender(<Hello name=\"<PERSON>\" />);\nexpect(screen.getByText('Hello, <PERSON>')).toBeInTheDocument();", "description": "属性验证测试用于检查组件能否根据不同props正确渲染内容。", "parameters": [{"name": "component", "description": "待测试的React组件。"}, {"name": "props", "description": "传递给组件的属性。"}], "returnValue": "无返回值，断言通过即测试通过。", "examples": [{"code": "import { render, screen } from '@testing-library/react';\nfunction Hello({ name }) { return <div>Hello, {name}</div>; }\ntest('属性验证', () => {\n  render(<Hello name=\"<PERSON>\" />);\n  expect(screen.getByText('Hello, <PERSON>')).toBeInTheDocument();\n});", "explanation": "测试组件能否根据props正确渲染内容。"}]}}, {"name": "作业：组件测试实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 编写组件渲染、交互、快照、事件、状态和属性测试\n// 2. 覆盖常见组件测试场景", "description": "通过实践组件渲染、交互、快照、事件、状态和属性测试，掌握React组件测试方法。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 编写渲染、交互、快照、事件、状态和属性测试", "explanation": "作业提示，学生需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nimport { render, screen } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nfunction Counter() {\n  const [count, setCount] = useState(0);\n  return <button onClick={() => setCount(c => c + 1)}>计数: {count}</button>;\n}\ntest('渲染', () => {\n  render(<Counter />);\n  expect(screen.getByText('计数: 0')).toBeInTheDocument();\n});\ntest('交互', () => {\n  render(<Counter />);\n  userEvent.click(screen.getByRole('button'));\n  expect(screen.getByText('计数: 1')).toBeInTheDocument();\n});", "explanation": "组件测试的正确实现示例，覆盖渲染和交互。"}]}}]}