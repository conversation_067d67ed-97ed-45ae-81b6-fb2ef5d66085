{"name": "Index Basics", "trans": ["索引基础"], "methods": [{"name": "Create Index", "trans": ["创建索引"], "usage": {"syntax": "CREATE INDEX 索引名 ON 表名(列名);\nCREATE INDEX 索引名 ON 表名(列名(长度));", "description": "索引是提高数据检索效率的数据结构，特别是在大型表上。创建索引可以加快WHERE子句、ORDER BY子句和JOIN操作的执行速度。", "parameters": [{"name": "索引名", "description": "要创建的索引名称"}, {"name": "表名", "description": "要添加索引的表"}, {"name": "列名", "description": "要索引的字段名"}, {"name": "长度", "description": "可选，字符串列的前缀长度"}], "returnValue": "无返回值，索引被创建", "examples": [{"code": "-- 在学生表的姓名字段上创建索引\nCREATE INDEX idx_student_name ON students(name);\n\n-- 在长文本字段上创建前缀索引\nCREATE INDEX idx_article_title ON articles(title(50));", "explanation": "第一个例子在students表的name字段上创建索引；第二个例子在articles表的title字段的前50个字符上创建索引，适用于长文本字段。"}]}}, {"name": "Unique Index", "trans": ["唯一索引"], "usage": {"syntax": "CREATE UNIQUE INDEX 索引名 ON 表名(列名);", "description": "唯一索引不仅可以提高查询速度，还可以保证索引列的值唯一（NULL除外）。它类似于UNIQUE约束，但可以应用于表中已有的列。", "parameters": [{"name": "索引名", "description": "要创建的唯一索引名称"}, {"name": "表名", "description": "要添加唯一索引的表"}, {"name": "列名", "description": "要建立唯一索引的字段名"}], "returnValue": "无返回值，唯一索引被创建", "examples": [{"code": "-- 在学生表的学号字段上创建唯一索引\nCREATE UNIQUE INDEX idx_student_id ON students(student_id);\n\n-- 在用户表的邮箱字段上创建唯一索引\nCREATE UNIQUE INDEX idx_user_email ON users(email);", "explanation": "第一个例子确保student_id列的值唯一；第二个例子确保email列的值唯一，可用于防止重复注册。"}]}}, {"name": "Composite Index", "trans": ["复合索引"], "usage": {"syntax": "CREATE INDEX 索引名 ON 表名(列名1, 列名2, ...);", "description": "复合索引是在多个列上创建的索引，可以加速基于这些列的查询。复合索引的列顺序很重要，应该根据查询模式优化。", "parameters": [{"name": "索引名", "description": "要创建的复合索引名称"}, {"name": "表名", "description": "要添加复合索引的表"}, {"name": "列名1, 列名2, ...", "description": "要建立复合索引的多个字段名"}], "returnValue": "无返回值，复合索引被创建", "examples": [{"code": "-- 在学生表的班级和年龄字段上创建复合索引\nCREATE INDEX idx_class_age ON students(class_id, age);\n\n-- 在订单表上创建复合索引\nCREATE INDEX idx_order_customer_date ON orders(customer_id, order_date);", "explanation": "第一个例子创建了一个复合索引，可以加速按班级和年龄查询的操作；第二个例子创建的复合索引可以加速按客户和日期查询订单的操作。"}]}}, {"name": "Drop Index", "trans": ["删除索引"], "usage": {"syntax": "DROP INDEX 索引名 ON 表名;", "description": "删除不再需要的索引。索引虽然提高了查询速度，但会降低更新速度并占用存储空间，因此不需要的索引应当删除。", "parameters": [{"name": "索引名", "description": "要删除的索引名称"}, {"name": "表名", "description": "索引所在的表名"}], "returnValue": "无返回值，索引被删除", "examples": [{"code": "-- 删除学生表上的姓名索引\nDROP INDEX idx_student_name ON students;\n\n-- 删除用户表上的邮箱唯一索引\nDROP INDEX idx_user_email ON users;", "explanation": "第一个例子删除了students表上的name字段索引；第二个例子删除了users表上的email字段唯一索引。"}]}}, {"name": "Show Indexes", "trans": ["查看索引"], "usage": {"syntax": "SHOW INDEX FROM 表名;\nSHOW INDEXES FROM 表名;\nSHOW KEYS FROM 表名;", "description": "查看表上已有的索引信息，包括索引名称、列名、是否唯一等。这三个命令是同义词，效果相同。", "parameters": [{"name": "表名", "description": "要查看索引的表名"}], "returnValue": "表中所有索引的详细信息", "examples": [{"code": "-- 查看学生表的所有索引\nSHOW INDEX FROM students;\n\n-- 使用另一种语法查看索引\nSHOW KEYS FROM orders;", "explanation": "这两个命令分别显示students表和orders表的所有索引信息，包括索引名、列名、基数等。"}]}}, {"name": "Index Types", "trans": ["索引类型"], "usage": {"syntax": "CREATE INDEX 索引名 ON 表名(列名) USING {BTREE | HASH};", "description": "MySQL支持多种索引类型，主要有B-Tree索引和Hash索引。InnoDB存储引擎默认使用B-Tree索引，适合范围查询；Hash索引适合等值查询。", "parameters": [{"name": "索引名", "description": "要创建的索引名称"}, {"name": "表名", "description": "要添加索引的表"}, {"name": "列名", "description": "要索引的字段名"}, {"name": "索引类型", "description": "BTREE或HASH"}], "returnValue": "无返回值，索引被创建", "examples": [{"code": "-- 创建B-Tree索引（InnoDB默认）\nCREATE INDEX idx_student_name ON students(name) USING BTREE;\n\n-- 创建Hash索引（仅部分存储引擎支持）\nCREATE INDEX idx_user_id ON users(id) USING HASH;", "explanation": "第一个例子显式指定使用B-Tree索引；第二个例子指定使用Hash索引，但需要注意，InnoDB存储引擎不支持显式的Hash索引。"}]}}, {"name": "Index Basics Assignment", "trans": ["索引基础练习"], "usage": {"syntax": "# 索引基础练习", "description": "完成以下练习，巩固MySQL索引的基本操作。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n假设有以下表结构：\n\n-- 学生表\nCREATE TABLE students (\n  id INT PRIMARY KEY,\n  student_id VARCHAR(20),\n  name VARCHAR(50),\n  email VARCHAR(100),\n  class_id INT,\n  age INT,\n  enrollment_date DATE\n);\n\n-- 课程表\nCREATE TABLE courses (\n  id INT PRIMARY KEY,\n  course_code VARCHAR(20),\n  course_name VARCHAR(100),\n  credit INT\n);\n\n-- 选课记录表\nCREATE TABLE enrollments (\n  id INT PRIMARY KEY,\n  student_id INT,\n  course_id INT,\n  grade DECIMAL(5,2),\n  enrollment_date DATE\n);\n\n请完成以下操作：\n1. 在students表的student_id字段上创建唯一索引。\n2. 在students表的name字段上创建普通索引。\n3. 在students表的class_id和age字段上创建复合索引。\n4. 在enrollments表的student_id和course_id字段上创建复合索引。\n5. 查看students表上的所有索引。\n6. 删除students表上的name字段索引。", "explanation": "通过实际操作掌握索引的创建、查看和删除，以及不同类型索引的应用场景。"}]}}]}