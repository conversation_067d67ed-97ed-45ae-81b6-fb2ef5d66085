{"name": "Component Composition", "trans": ["组合组件"], "methods": [{"name": "Children Prop", "trans": ["children属性"], "usage": {"syntax": "function Wrapper({ children }) {\n  return <div className=\"wrapper\">{children}</div>;\n}", "description": "children属性是React组件的内置属性，用于接收组件标签包裹的内容。通过children可以实现灵活的内容插槽和组件嵌套。", "parameters": [{"name": "children", "description": "被包裹的任意React节点"}], "returnValue": "渲染包含children内容的组件", "examples": [{"code": "function Card({ children }) {\n  return <div className=\"card\">{children}</div>;\n}\n// 使用\n<Card>\n  <h2>标题</h2>\n  <p>内容</p>\n</Card>", "explanation": "Card组件通过children属性实现内容插槽，父组件可以传递任意内容。"}]}}, {"name": "Specialized Components", "trans": ["特殊化组件"], "usage": {"syntax": "function Dialog({ title, children }) {\n  return <div className=\"dialog\">\n    <h3>{title}</h3>\n    <div>{children}</div>\n  </div>;\n}\nfunction WelcomeDialog() {\n  return <Dialog title=\"欢迎\">欢迎使用本系统！</Dialog>;\n}", "description": "通过组合和props传递，可以基于通用组件创建更具体的特殊化组件，提升复用性和可维护性。", "parameters": [{"name": "title", "description": "对话框标题"}, {"name": "children", "description": "对话框内容"}], "returnValue": "渲染带有特定内容和样式的特殊化组件", "examples": [{"code": "// 通用Dialog组件和特殊化WelcomeDialog\nfunction Dialog({ title, children }) {\n  return <div className=\"dialog\">\n    <h3>{title}</h3>\n    <div>{children}</div>\n  </div>;\n}\nfunction WelcomeDialog() {\n  return <Dialog title=\"欢迎\">欢迎使用本系统！</Dialog>;\n}", "explanation": "WelcomeDialog通过组合Dialog和传递props，实现了内容和样式的特殊化。"}]}}, {"name": "Composition vs Inheritance", "trans": ["组合VS继承"], "usage": {"syntax": "// 推荐组合而非继承\nfunction FancyBorder({ children }) {\n  return <div className=\"fancy-border\">{children}</div>;\n}", "description": "React推荐通过组合而不是继承来实现组件复用和扩展。组合更灵活、解耦，适合UI组件开发。", "parameters": [{"name": "children", "description": "被包裹的内容"}], "returnValue": "渲染组合后的组件内容", "examples": [{"code": "// 组合方式\nfunction Page({ children }) {\n  return <main>{children}</main>;\n}\n// 继承方式（不推荐）\nclass Page extends React.Component {\n  render() {\n    return <main>{this.props.children}</main>;\n  }\n}", "explanation": "推荐使用函数式组件和组合，避免继承带来的复杂性。"}]}}, {"name": "Component Wrapping Techniques", "trans": ["组件包装技巧"], "usage": {"syntax": "function withLogger(Component) {\n  return function Wrapper(props) {\n    console.log('渲染', Component.name);\n    return <Component {...props} />;\n  };\n}", "description": "可以通过高阶组件（HOC）、render props等方式对组件进行包装，增强功能或注入逻辑。", "parameters": [{"name": "Component", "description": "被包装的组件"}], "returnValue": "返回增强后的新组件", "examples": [{"code": "// 高阶组件包装\nfunction withLogger(Component) {\n  return function Wrapper(props) {\n    console.log('渲染', Component.name);\n    return <Component {...props} />;\n  };\n}\n// 用法\nconst LoggedButton = withLogger(Button);", "explanation": "with<PERSON><PERSON><PERSON>高阶组件为被包装组件增加了日志功能。"}]}}, {"name": "Layout Components", "trans": ["布局组件"], "usage": {"syntax": "function Row({ children }) {\n  return <div style={{ display: 'flex' }}>{children}</div>;\n}", "description": "布局组件用于组织和排列子组件，常见如Row、Col、Grid等。通过children实现灵活布局。", "parameters": [{"name": "children", "description": "需要布局的内容"}], "returnValue": "渲染布局后的内容", "examples": [{"code": "function Row({ children }) {\n  return <div style={{ display: 'flex' }}>{children}</div>;\n}\n// 用法\n<Row>\n  <div>左</div>\n  <div>右</div>\n</Row>", "explanation": "Row布局组件通过children实现横向排列。"}]}}, {"name": "Container Components", "trans": ["容器组件"], "usage": {"syntax": "function ListContainer({ items, renderItem }) {\n  return <ul>{items.map(renderItem)}</ul>;\n}", "description": "容器组件负责数据和逻辑，展示组件负责渲染UI。常见如列表容器、表单容器等。", "parameters": [{"name": "items", "description": "数据数组"}, {"name": "renderItem", "description": "渲染每一项的函数"}], "returnValue": "渲染数据驱动的内容", "examples": [{"code": "function ListContainer({ items, renderItem }) {\n  return <ul>{items.map(renderItem)}</ul>;\n}\n// 用法\n<ListContainer\n  items={[1,2,3]}\n  renderItem={item => <li key={item}>{item}</li>}\n/>", "explanation": "ListContainer容器组件通过props实现数据和渲染分离。"}]}}, {"name": "作业：实现一个可复用的Tabs组件", "trans": ["作业"], "usage": {"syntax": "// 需求：实现一个Tabs组件\n// 1. 支持任意数量的Tab和内容。\n// 2. 通过children传递TabPane。\n// 3. 支持高亮当前Tab。\n// 4. 代码结构清晰，注释完整。", "description": "请实现一个可复用的Tabs组件，要求如上。提交时请附上完整代码和注释。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 示例代码略，请学生自行实现\n// 提示：可以用Tabs和TabPane组合实现。", "explanation": "作业要求学生综合运用组合、children和高亮逻辑。"}, {"code": "import React, { useState } from 'react';\n// TabPane组件\nfunction TabPane({ children }) {\n  return <div>{children}</div>;\n}\n// Tabs组件\nfunction Tabs({ children }) {\n  const [active, setActive] = useState(0);\n  return (\n    <div>\n      <div style={{ display: 'flex' }}>\n        {React.Children.map(children, (child, idx) => (\n          <button\n            style={{\n              fontWeight: idx === active ? 'bold' : 'normal',\n              borderBottom: idx === active ? '2px solid #1890ff' : 'none',\n              marginRight: 8\n            }}\n            onClick={() => setActive(idx)}\n          >\n            {child.props.title}\n          </button>\n        ))}\n      </div>\n      <div style={{ marginTop: 16 }}>\n        {React.Children.toArray(children)[active]}\n      </div>\n    </div>\n  );\n}\n// 用法示例\nfunction Demo() {\n  return (\n    <Tabs>\n      <TabPane title=\"Tab1\">内容1</TabPane>\n      <TabPane title=\"Tab2\">内容2</TabPane>\n      <TabPane title=\"Tab3\">内容3</TabPane>\n    </Tabs>\n  );\n}", "explanation": "这是一个完整的Tabs组件实现，支持任意Tab、children传递和高亮。"}]}}]}