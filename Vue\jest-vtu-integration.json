{"name": "Jest and VTU Integration", "trans": ["Jest与VTU结合"], "methods": [{"name": "Test Structure", "trans": ["测试结构"], "usage": {"syntax": "describe('组件名', () => {\n  it('描述', () => {\n    // 测试内容\n  })\n})", "description": "Jest结合VTU时，推荐使用describe分组、it编写用例，结构清晰，便于维护和阅读。", "parameters": [{"name": "describe", "description": "分组测试用例的方法"}, {"name": "it", "description": "单个测试用例的方法"}], "returnValue": "结构化的测试用例分组", "examples": [{"code": "describe('MyComponent', () => {\n  it('渲染内容', () => {\n    const wrapper = mount(MyComponent)\n    expect(wrapper.text()).toContain('Hello')\n  })\n})", "explanation": "结构化分组组件测试用例。"}]}}, {"name": "Mock Functions", "trans": ["mock函数"], "usage": {"syntax": "const fn = jest.fn()\nfn.mockReturnValue(1)", "description": "通过jest.fn()创建mock函数，结合mockReturnValue等方法，隔离依赖，便于测试。", "parameters": [{"name": "jest.fn", "description": "创建mock函数的方法"}, {"name": "mockReturnValue", "description": "指定mock返回值"}], "returnValue": "可控的mock函数对象", "examples": [{"code": "const fn = jest.fn()\nfn.mockReturnValue(1)\nexpect(fn()).toBe(1)", "explanation": "mock函数返回固定值，便于断言。"}]}}, {"name": "Snapshot Testing", "trans": ["快照测试"], "usage": {"syntax": "expect(wrapper.html()).toMatchSnapshot()", "description": "结合Jest的toMatchSnapshot方法，自动对比组件渲染输出，检测UI变更。", "parameters": [{"name": "toMatchSnapshot", "description": "生成并对比快照"}], "returnValue": "快照对比结果，检测UI变更", "examples": [{"code": "expect(wrapper.html()).toMatchSnapshot()", "explanation": "断言当前渲染输出与快照一致。"}]}}, {"name": "Hook Testing", "trans": ["钩子测试"], "usage": {"syntax": "jest.spyOn(lifecycle, 'onMounted')\n// 断言钩子被调用", "description": "通过jest.spyOn等方法，mock和断言生命周期钩子或自定义钩子的调用，确保钩子逻辑正确。", "parameters": [{"name": "jest.spyOn", "description": "监听函数或钩子的调用"}], "returnValue": "钩子调用的断言结果", "examples": [{"code": "jest.spyOn(lifecycle, 'onMounted')\n// 断言onMounted被调用", "explanation": "监听并断言生命周期钩子调用。"}]}}, {"name": "Coverage Report", "trans": ["覆盖率报告"], "usage": {"syntax": "npx jest --coverage", "description": "通过--coverage参数生成测试覆盖率报告，分析哪些代码未被测试，提升测试完整性。", "parameters": [{"name": "--coverage", "description": "生成覆盖率报告的参数"}], "returnValue": "测试覆盖率报告", "examples": [{"code": "npx jest --coverage", "explanation": "生成并查看测试覆盖率报告。"}]}}, {"name": "Continuous Integration", "trans": ["持续集成"], "usage": {"syntax": "// 配置GitHub Actions或GitLab CI\n- name: Run tests\n  run: npm test", "description": "结合持续集成工具（如GitHub Actions、GitLab CI等）自动运行测试，保证每次提交都通过测试。", "parameters": [{"name": "CI配置", "description": "持续集成平台的配置脚本"}], "returnValue": "自动化测试流程和结果", "examples": [{"code": "# .github/workflows/test.yml\nname: CI\non: [push]\njobs:\n  test:\n    runs-on: ubuntu-latest\n    steps:\n      - uses: actions/checkout@v2\n      - name: Install\n        run: npm install\n      - name: Run tests\n        run: npm test", "explanation": "GitHub Actions自动化测试配置示例。"}]}}, {"name": "Jest and VTU Integration Assignment", "trans": ["Jest与VTU结合练习"], "usage": {"syntax": "# Jest与VTU结合练习", "description": "完成以下练习，巩固Jest与VTU结合相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 按结构分组编写测试用例。\n2. 使用mock函数隔离依赖。\n3. 编写快照和钩子测试。\n4. 生成覆盖率报告。\n5. 配置持续集成自动运行测试。", "explanation": "通过这些练习掌握Jest与VTU结合的测试流程。"}]}}]}