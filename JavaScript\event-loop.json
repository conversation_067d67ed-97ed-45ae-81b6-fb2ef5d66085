{"name": "Event Loop & Microtasks", "trans": ["事件循环与微任务"], "methods": [{"name": "Event Loop Mechanism", "trans": ["事件循环机制"], "usage": {"syntax": "// 事件循环不需要特定语法，是JavaScript运行时的内部机制", "description": "事件循环是JavaScript处理异步操作的机制，由调用栈、任务队列和微任务队列组成。JavaScript是单线程的，事件循环使其能够非阻塞地处理I/O操作。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "console.log('1'); // 同步代码\n\nsetTimeout(() => {\n  console.log('2'); // 宏任务\n}, 0);\n\nPromise.resolve().then(() => {\n  console.log('3'); // 微任务\n});\n\nconsole.log('4'); // 同步代码\n\n// 输出顺序: 1, 4, 3, 2", "explanation": "演示事件循环执行顺序：先执行同步代码，然后是微任务队列，最后是宏任务队列。"}, {"code": "// 调用栈溢出示例\nfunction recursion() {\n  recursion(); // 无限递归导致栈溢出\n}\n\n// 避免栈溢出的异步递归\nfunction asyncRecursion(count, max) {\n  console.log(count);\n  if (count < max) {\n    setTimeout(() => {\n      asyncRecursion(count + 1, max);\n    }, 0); // 使用事件循环避免栈溢出\n  }\n}\n\nasyncRecursion(0, 10);", "explanation": "使用setTimeout将递归放入事件循环，避免调用栈溢出。"}]}}, {"name": "Macrotasks & Microtasks", "trans": ["宏任务与微任务"], "usage": {"syntax": "// 宏任务\nsetTimeout(() => {}, delay);\nsetInterval(() => {}, delay);\n\n// 微任务\nPromise.resolve().then(() => {});\nqueueMicrotask(() => {});", "description": "宏任务在下一轮事件循环执行，包括setTimeout、setInterval、I/O、UI渲染等。微任务在当前事件循环结束前执行，包括Promise回调、queueMicrotask等。", "parameters": [{"name": "宏任务", "description": "setTimeout、setInterval、I/O、UI渲染等。"}, {"name": "微任务", "description": "Promise回调、queueMicrotask等。"}], "returnValue": "无返回值", "examples": [{"code": "console.log('开始');\n\n// 宏任务\nsetTimeout(() => {\n  console.log('宏任务1');\n  \n  // 在宏任务中的微任务\n  Promise.resolve().then(() => {\n    console.log('宏任务1中的微任务');\n  });\n  \n  // 在宏任务中的宏任务\n  setTimeout(() => {\n    console.log('宏任务1中的宏任务');\n  }, 0);\n}, 0);\n\n// 微任务\nPromise.resolve().then(() => {\n  console.log('微任务1');\n  \n  // 在微任务中的微任务\n  Promise.resolve().then(() => {\n    console.log('微任务1中的微任务');\n  });\n  \n  // 在微任务中的宏任务\n  setTimeout(() => {\n    console.log('微任务1中的宏任务');\n  }, 0);\n});\n\nconsole.log('结束');\n\n// 输出顺序:\n// 开始\n// 结束\n// 微任务1\n// 微任务1中的微任务\n// 宏任务1\n// 宏任务1中的微任务\n// 微任务1中的宏任务\n// 宏任务1中的宏任务", "explanation": "详细演示宏任务和微任务的执行顺序，以及它们嵌套时的行为。"}, {"code": "// 使用queueMicrotask\nconsole.log('开始');\n\nqueueMicrotask(() => {\n  console.log('微任务');\n});\n\nsetTimeout(() => {\n  console.log('宏任务');\n}, 0);\n\nconsole.log('结束');\n\n// 输出: 开始, 结束, 微任务, 宏任务", "explanation": "使用queueMicrotask显式创建微任务，比Promise.then更直接。"}]}}, {"name": "setTimeout/setInterval", "trans": ["setTimeout/setInterval"], "usage": {"syntax": "const timeoutId = setTimeout(callback, delay, ...args);\nconst intervalId = setInterval(callback, delay, ...args);\nclearTimeout(timeoutId);\nclearInterval(intervalId);", "description": "setTimeout在指定延迟后执行一次回调，setInterval每隔指定时间重复执行回调。两者都返回ID，可用于取消。", "parameters": [{"name": "callback", "description": "要执行的函数。"}, {"name": "delay", "description": "延迟时间，单位毫秒。"}, {"name": "...args", "description": "传递给回调的参数。"}], "returnValue": "返回定时器ID，可用于取消定时器。", "examples": [{"code": "// setTimeout基本用法\nconst timeoutId = setTimeout((name) => {\n  console.log(`Hello, ${name}!`);\n}, 1000, 'World');\n\n// 取消定时器\n// clearTimeout(timeoutId);", "explanation": "setTimeout在1秒后执行回调，并传递参数。"}, {"code": "// setInterval基本用法\nlet count = 0;\nconst intervalId = setInterval(() => {\n  console.log(`计数: ${++count}`);\n  if (count >= 5) {\n    clearInterval(intervalId); // 达到条件后停止\n    console.log('计时器已停止');\n  }\n}, 1000);", "explanation": "setInterval每秒执行一次，计数到5后通过clearInterval停止。"}, {"code": "// setTimeout实现setInterval的效果\nfunction customInterval(callback, delay) {\n  let id;\n  \n  function repeat() {\n    callback();\n    id = setTimeout(repeat, delay);\n  }\n  \n  id = setTimeout(repeat, delay);\n  \n  return {\n    clear: () => clearTimeout(id)\n  };\n}\n\nconst timer = customInterval(() => console.log('tick'), 1000);\n// 停止: timer.clear();", "explanation": "使用setTimeout递归实现setInterval效果，避免setInterval可能的执行堆积问题。"}]}}, {"name": "requestAnimationFrame", "trans": ["requestAnimationFrame"], "usage": {"syntax": "const frameId = requestAnimationFrame(callback);\ncancelAnimationFrame(frameId);", "description": "requestAnimationFrame请求浏览器在下次重绘前执行回调，通常用于动画。比setTimeout更高效，会自动适应屏幕刷新率，并在标签页不可见时暂停。", "parameters": [{"name": "callback", "description": "下次重绘前执行的函数，接收时间戳参数。"}], "returnValue": "返回请求ID，可用于取消请求。", "examples": [{"code": "// 基本动画\nlet start;\nlet element = document.getElementById('animatedBox');\n\nfunction step(timestamp) {\n  if (!start) start = timestamp;\n  const progress = timestamp - start;\n  \n  // 移动元素\n  element.style.transform = `translateX(${Math.min(progress / 10, 200)}px)`;\n  \n  // 200px后停止\n  if (progress < 2000) {\n    requestAnimationFrame(step);\n  }\n}\n\nrequestAnimationFrame(step);", "explanation": "使用requestAnimationFrame创建平滑动画，根据时间戳计算进度。"}, {"code": "// 与setTimeout对比\n// setTimeout方式\nfunction animateWithTimeout() {\n  let pos = 0;\n  function frame() {\n    pos += 2;\n    element.style.left = pos + 'px';\n    if (pos < 300) {\n      setTimeout(frame, 16); // 约60fps\n    }\n  }\n  setTimeout(frame, 16);\n}\n\n// requestAnimationFrame方式\nfunction animateWithRAF() {\n  let pos = 0;\n  function frame() {\n    pos += 2;\n    element.style.left = pos + 'px';\n    if (pos < 300) {\n      requestAnimationFrame(frame);\n    }\n  }\n  requestAnimationFrame(frame);\n}", "explanation": "对比setTimeout和requestAnimationFrame实现动画，后者更高效且自动适应刷新率。"}]}}, {"name": "作业：事件循环与动画实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 实现一个动画，展示事件循环机制\n// 2. 对比宏任务、微任务和requestAnimationFrame", "description": "通过实践事件循环、宏微任务、定时器和动画帧，掌握JavaScript异步执行机制。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 创建动画函数\n// 2. 使用setTimeout、Promise和rAF\n// 3. 观察执行顺序", "explanation": "作业提示，需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nfunction eventLoopDemo() {\n  console.log('同步代码开始');\n  \n  // 宏任务\n  setTimeout(() => {\n    console.log('宏任务执行');\n    \n    // 宏任务中的微任务\n    Promise.resolve().then(() => {\n      console.log('宏任务中的微任务');\n    });\n  }, 0);\n  \n  // 微任务\n  Promise.resolve().then(() => {\n    console.log('微任务执行');\n    \n    // 微任务中的微任务\n    queueMicrotask(() => {\n      console.log('微任务中的微任务');\n    });\n  });\n  \n  // 动画帧\n  requestAnimationFrame(() => {\n    console.log('动画帧执行');\n  });\n  \n  console.log('同步代码结束');\n}\n\n// 执行示例\neventLoopDemo();\n\n// 预期输出顺序：\n// 同步代码开始\n// 同步代码结束\n// 微任务执行\n// 微任务中的微任务\n// 动画帧执行 (在下一次重绘前)\n// 宏任务执行\n// 宏任务中的微任务", "explanation": "完整实现，展示同步代码、微任务、宏任务和动画帧的执行顺序。"}]}}]}