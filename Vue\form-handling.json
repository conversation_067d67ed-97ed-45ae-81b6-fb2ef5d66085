{"name": "Form Handling", "trans": ["表单处理"], "methods": [{"name": "v-model Usage", "trans": ["v-model用法"], "usage": {"syntax": "<input v-model=\"value\" />", "description": "v-model用于在表单控件和数据之间实现双向绑定，自动同步输入值和数据。适用于input、textarea、select等。", "parameters": [{"name": "v-model", "description": "绑定的数据变量名"}], "returnValue": "输入值与数据变量实时同步", "examples": [{"code": "<template>\n  <input v-model=\"username\" />\n  <p>输入：{{ username }}</p>\n</template>\n<script>\nexport default {\n  data() { return { username: '' } }\n}\n</script>", "explanation": "输入框内容与username变量实时同步。"}]}}, {"name": "Form Element Types", "trans": ["表单元素类型"], "usage": {"syntax": "<input v-model=\"text\" />\n<textarea v-model=\"desc\"></textarea>\n<select v-model=\"option\"><option value=\"A\">A</option></select>\n<input type=\"checkbox\" v-model=\"checked\" />", "description": "v-model可用于多种表单元素，包括文本框、文本域、下拉框、单选框、复选框等。不同类型的表单控件绑定方式略有差异。", "parameters": [{"name": "input/textarea/select/checkbox/radio", "description": "支持的表单元素类型"}], "returnValue": "表单控件的值与数据变量同步", "examples": [{"code": "<template>\n  <input v-model=\"text\" />\n  <textarea v-model=\"desc\"></textarea>\n  <select v-model=\"option\"><option value=\"A\">A</option></select>\n  <input type=\"checkbox\" v-model=\"checked\" />\n</template>\n<script>\nexport default {\n  data() {\n    return { text: '', desc: '', option: 'A', checked: false }\n  }\n}\n</script>", "explanation": "演示了多种表单元素的v-model绑定。"}]}}, {"name": "Modifiers (.lazy, .number, .trim)", "trans": ["修饰符（.lazy、.number、.trim）"], "usage": {"syntax": "<input v-model.lazy=\"msg\" />\n<input v-model.number=\"age\" />\n<input v-model.trim=\"name\" />", "description": ".lazy：在change事件后同步数据；.number：自动将输入值转为数字；.trim：自动去除首尾空格。", "parameters": [{"name": ".lazy", "description": "change事件后同步"}, {"name": ".number", "description": "输入值转为数字"}, {"name": ".trim", "description": "去除首尾空格"}], "returnValue": "输入值经过修饰符处理后同步到数据变量", "examples": [{"code": "<template>\n  <input v-model.lazy=\"msg\" />\n  <input v-model.number=\"age\" />\n  <input v-model.trim=\"name\" />\n</template>\n<script>\nexport default {\n  data() { return { msg: '', age: 0, name: '' } }\n}\n</script>", "explanation": "分别演示了.lazy、.number、.trim修饰符的用法。"}]}}, {"name": "Form Validation", "trans": ["表单校验"], "usage": {"syntax": "<form @submit.prevent=\"onSubmit\">...</form>", "description": "表单校验用于验证用户输入的合法性。可通过手动校验、computed、第三方库（如vee-validate）实现。", "parameters": [{"name": "@submit.prevent", "description": "阻止默认提交并执行校验"}], "returnValue": "校验通过或失败，决定是否提交表单", "examples": [{"code": "<template>\n  <form @submit.prevent=\"onSubmit\">\n    <input v-model=\"email\" />\n    <button type=\"submit\">提交</button>\n    <p v-if=\"error\">{{ error }}</p>\n  </form>\n</template>\n<script>\nexport default {\n  data() { return { email: '', error: '' } },\n  methods: {\n    onSubmit() {\n      if (!this.email.includes('@')) {\n        this.error = '邮箱格式错误'\n      } else {\n        this.error = ''\n        // 提交表单\n      }\n    }\n  }\n}\n</script>", "explanation": "onSubmit方法校验邮箱格式，错误时显示提示。"}]}}, {"name": "Dynamic Form", "trans": ["动态表单"], "usage": {"syntax": "<div v-for=\"(item, i) in items\" :key=\"i\">\n  <input v-model=\"item.value\" />\n</div>", "description": "动态表单用于根据数据动态渲染多个输入项，常用于添加、删除表单项。", "parameters": [{"name": "v-for", "description": "遍历渲染多个输入控件"}], "returnValue": "动态生成的表单项与数据同步", "examples": [{"code": "<template>\n  <div v-for=\"(item, i) in items\" :key=\"i\">\n    <input v-model=\"item.value\" />\n  </div>\n  <button @click=\"add\">添加</button>\n</template>\n<script>\nexport default {\n  data() { return { items: [{ value: '' }] } },\n  methods: {\n    add() { this.items.push({ value: '' }) }\n  }\n}\n</script>", "explanation": "通过v-for和v-model实现动态添加输入项。"}]}}, {"name": "Controlled and Uncontrolled Forms", "trans": ["受控与非受控表单"], "usage": {"syntax": "// 受控：v-model绑定数据\n<input v-model=\"value\" />\n// 非受控：ref获取DOM值\n<input ref=\"inputRef\" />", "description": "受控表单通过v-model与数据双向绑定，非受控表单通过ref直接操作DOM获取值。", "parameters": [{"name": "v-model/ref", "description": "受控与非受控的实现方式"}], "returnValue": "受控表单数据自动同步，非受控需手动获取值", "examples": [{"code": "<template>\n  <input v-model=\"msg\" />\n  <input ref=\"inputRef\" />\n  <button @click=\"getValue\">获取值</button>\n</template>\n<script>\nexport default {\n  data() { return { msg: '' } },\n  methods: {\n    getValue() {\n      alert(this.$refs.inputRef.value)\n    }\n  }\n}\n</script>", "explanation": "msg为受控表单，inputRef为非受控表单，通过ref获取值。"}]}}, {"name": "Form Handling Assignment", "trans": ["表单处理练习"], "usage": {"syntax": "# 表单处理练习", "description": "完成以下练习，巩固表单处理相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用v-model实现输入框双向绑定。\n2. 用修饰符处理输入。\n3. 实现一个简单的表单校验。\n4. 实现动态添加输入项。\n5. 区分受控与非受控表单。", "explanation": "通过这些练习掌握表单双向绑定、校验、动态表单和受控/非受控用法。"}]}}]}