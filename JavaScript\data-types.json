{"name": "Data Types", "trans": ["数据类型"], "methods": [{"name": "Primitive Types", "trans": ["基本数据类型"], "usage": {"syntax": "let n = 1; // Number\nlet s = 'hello'; // String\nlet b = true; // Boolean\nlet u = undefined; // Undefined\nlet nl = null; // Null\nlet sym = Symbol('id'); // Symbol\nlet big = 123n; // BigInt", "description": "JavaScript的基本数据类型包括Number、String、Boolean、Null、Undefined、Symbol、BigInt，均为不可变类型。", "parameters": [{"name": "Number", "description": "数字类型，表示整数和浮点数。"}, {"name": "String", "description": "字符串类型，表示文本数据。"}, {"name": "Boolean", "description": "布尔类型，true或false。"}, {"name": "<PERSON><PERSON>", "description": "空值，表示无对象。"}, {"name": "Undefined", "description": "未定义，变量未赋值时的默认值。"}, {"name": "Symbol", "description": "唯一值，常用于对象属性。"}, {"name": "BigInt", "description": "大整数类型，处理超大整数。"}], "returnValue": "无返回值，声明变量。", "examples": [{"code": "let age = 18; // Number\nlet name = '张三'; // String\nlet isOk = false; // Boolean\nlet nothing = null; // Null\nlet notSet; // Undefined\nlet id = Symbol('唯一标识'); // Symbol\nlet bigNum = 9007199254740991n; // BigInt", "explanation": "展示所有基本数据类型的声明。"}]}}, {"name": "Reference Types", "trans": ["引用数据类型"], "usage": {"syntax": "let obj = {a: 1}; // Object\nlet arr = [1,2,3]; // Array\nlet fn = function(){}; // Function\nlet date = new Date(); // Date\nlet reg = /abc/; // RegExp", "description": "引用数据类型包括Object、Array、Function、Date、RegExp等，存储的是地址（引用），可变。", "parameters": [{"name": "Object", "description": "对象类型，键值对集合。"}, {"name": "Array", "description": "数组类型，元素有序集合。"}, {"name": "Function", "description": "函数类型，可执行代码块。"}, {"name": "Date", "description": "日期对象。"}, {"name": "RegExp", "description": "正则表达式对象。"}], "returnValue": "无返回值，声明引用类型变量。", "examples": [{"code": "let person = {name: '李四', age: 20};\nlet list = [10, 20, 30];\nlet sayHi = function() { console.log('Hi!'); };\nlet now = new Date();\nlet pattern = /\\d+/; // 或 let pattern = new RegExp('\\d+');", "explanation": "展示常见引用类型的声明。"}]}}, {"name": "Type Checking & Conversion", "trans": ["类型判断与转换"], "usage": {"syntax": "typeof value; // 基本类型判断\nvalue instanceof Array; // 判断是否为数组\nNumber('123'); // 转为数字\nString(123); // 转为字符串\nBoolean(0); // 转为布尔值", "description": "typeof用于判断基本类型，instanceof用于判断引用类型。类型转换可用Number、String、Boolean等函数。", "parameters": [{"name": "typeof", "description": "判断基本数据类型。"}, {"name": "instanceof", "description": "判断对象类型。"}, {"name": "Number/String/Boolean", "description": "类型转换函数。"}], "returnValue": "返回类型字符串或转换后的值。", "examples": [{"code": "typeof 123; // 'number'\ntypeof 'abc'; // 'string'\n[1,2,3] instanceof Array; // true\nNumber('456'); // 456\nBoolean(''); // false", "explanation": "类型判断和转换的常见用法。"}]}}, {"name": "Shallow Copy & Deep Copy", "trans": ["浅拷贝与深拷贝"], "usage": {"syntax": "let a = {x:1};\nlet b = a; // 引用赋值\nlet c = {...a}; // 浅拷贝\nlet d = JSON.parse(JSON.stringify(a)); // 深拷贝", "description": "浅拷贝只复制一层属性，深拷贝会递归复制所有层级。常用...展开运算符实现浅拷贝，JSON方法实现深拷贝。", "parameters": [{"name": "=", "description": "引用赋值，两个变量指向同一对象。"}, {"name": "...", "description": "展开运算符，浅拷贝对象。"}, {"name": "JSON.parse(JSON.stringify())", "description": "深拷贝对象。"}], "returnValue": "返回新对象或引用。", "examples": [{"code": "let obj1 = {a:1};\nlet obj2 = obj1; // 引用赋值\nobj2.a = 100;\nconsole.log(obj1.a); // 100\n\nlet obj3 = {...obj1}; // 浅拷贝\nobj3.a = 200;\nconsole.log(obj1.a); // 100\n\nlet obj4 = JSON.parse(JSON.stringify(obj1)); // 深拷贝\nobj4.a = 300;\nconsole.log(obj1.a); // 100", "explanation": "演示引用赋值、浅拷贝和深拷贝的区别。"}]}}, {"name": "作业：数据类型实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 声明所有基本和引用类型变量\n// 2. 判断变量类型并转换\n// 3. 实现浅拷贝和深拷贝\n// 4. 比较不同拷贝方式的效果", "description": "通过实践数据类型声明、类型判断与转换、浅/深拷贝，掌握JS数据类型基础。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 声明所有类型变量\n// 2. 判断类型并转换\n// 3. 实现浅/深拷贝\n// 4. 比较结果", "explanation": "作业提示，需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nlet n = 1, s = 'a', b = true, u, nl = null, sym = Symbol(), big = 1n;\nlet obj = {x:1}, arr = [1], fn = ()=>{}, date = new Date(), reg = /a/;\nconsole.log(typeof n, typeof s, typeof b, typeof u, typeof nl, typeof sym, typeof big);\nconsole.log(obj instanceof Object, arr instanceof Array);\nlet shallow = {...obj};\nlet deep = JSON.parse(JSON.stringify(obj));\nshallow.x = 2;\ndeep.x = 3;\nconsole.log(obj.x); // 1\nconsole.log(shallow.x); // 2\nconsole.log(deep.x); // 3", "explanation": "数据类型声明、判断、转换、浅/深拷贝的正确实现。"}]}}]}