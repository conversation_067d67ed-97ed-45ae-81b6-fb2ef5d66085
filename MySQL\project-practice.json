{"name": "Project Practice", "trans": ["项目实战"], "methods": [{"name": "Project Overview", "trans": ["项目概述"], "usage": {"syntax": "-- 学生成绩管理系统：\n-- 包含学生表、课程表、成绩表，支持学生、课程、成绩的增删改查，成绩统计与排名。", "description": "本项目实战以学生成绩管理系统为例，涵盖数据库设计、表结构创建、核心功能实现和典型业务场景，帮助系统掌握MySQL综合应用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "-- 1. 学生表\nCREATE TABLE student (\n  student_id INT PRIMARY KEY AUTO_INCREMENT,\n  name VA<PERSON>HA<PERSON>(50),\n  gender CHAR(1),\n  birthday DATE\n);\n\n-- 2. 课程表\nCREATE TABLE course (\n  course_id INT PRIMARY KEY AUTO_INCREMENT,\n  course_name VARCHAR(50),\n  teacher VARCHAR(50)\n);\n\n-- 3. 成绩表\nCREATE TABLE score (\n  score_id INT PRIMARY KEY AUTO_INCREMENT,\n  student_id INT,\n  course_id INT,\n  score DECIMAL(5,2),\n  exam_date DATE,\n  FOREIGN KEY (student_id) REFERENCES student(student_id),\n  FOREIGN KEY (course_id) REFERENCES course(course_id)\n);", "explanation": "本例给出了学生、课程、成绩三张表的设计，包含主键、外键和常用字段。"}]}}, {"name": "CRUD Operations", "trans": ["增删改查"], "usage": {"syntax": "-- 增加学生\nINSERT INTO student (name, gender, birthday) VALUES ('张三', 'M', '2005-05-01');\n-- 修改学生信息\nUPDATE student SET name = '李四' WHERE student_id = 1;\n-- 删除学生\nDELETE FROM student WHERE student_id = 1;\n-- 查询所有学生\nSELECT * FROM student;", "description": "实现学生、课程、成绩的增删改查操作，支持基本数据管理。", "parameters": [], "returnValue": "无返回值或返回查询结果", "examples": [{"code": "-- 增加课程\nINSERT INTO course (course_name, teacher) VALUES ('数学', '王老师');\n-- 增加成绩\nINSERT INTO score (student_id, course_id, score, exam_date) VALUES (1, 1, 95.5, '2024-06-01');\n-- 查询某学生所有成绩\nSELECT s.name, c.course_name, sc.score FROM student s\nJOIN score sc ON s.student_id = sc.student_id\nJOIN course c ON sc.course_id = c.course_id\nWHERE s.name = '张三';", "explanation": "本例展示了学生、课程、成绩的增删改查及多表联合查询。"}]}}, {"name": "Score Statistics and Ranking", "trans": ["成绩统计与排名"], "usage": {"syntax": "-- 统计每个学生的平均分\nSELECT s.name, AVG(sc.score) AS avg_score FROM student s\nJOIN score sc ON s.student_id = sc.student_id\nGROUP BY s.student_id\nORDER BY avg_score DESC;\n\n-- 查询某课程的最高分、最低分、平均分\nSELECT c.course_name, MAX(sc.score) AS max_score, MIN(sc.score) AS min_score, AVG(sc.score) AS avg_score\nFROM course c\nJOIN score sc ON c.course_id = sc.course_id\nWHERE c.course_name = '数学'\nGROUP BY c.course_id;", "description": "实现学生成绩的统计分析，包括平均分、最高分、最低分、排名等功能。", "parameters": [], "returnValue": "返回统计和排名结果", "examples": [{"code": "-- 查询全体学生总分排名\nSELECT s.name, SUM(sc.score) AS total_score FROM student s\nJOIN score sc ON s.student_id = sc.student_id\nGROUP BY s.student_id\nORDER BY total_score DESC;", "explanation": "本例展示了如何统计学生总分并按成绩排名。"}]}}, {"name": "Project Practice Assignment", "trans": ["项目实战练习"], "usage": {"syntax": "# 项目实战练习", "description": "完成以下综合练习，巩固学生成绩管理系统的数据库设计与开发能力。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 设计并创建学生、课程、成绩三张表，设置主键和外键。\n2. 实现学生、课程、成绩的增删改查功能。\n3. 查询某学生的所有课程成绩和平均分。\n4. 统计每门课程的最高分、最低分和平均分。\n5. 实现学生总分排名功能。\n6. 总结项目开发过程中的数据库设计要点和优化建议。", "explanation": "通过这些综合练习，你将系统掌握MySQL在实际项目中的建模、查询和统计分析能力。"}]}}, {"name": "View Application", "trans": ["视图应用"], "usage": {"syntax": "-- 创建学生成绩视图\nCREATE VIEW v_student_score AS\nSELECT s.name, c.course_name, sc.score\nFROM student s\nJOIN score sc ON s.student_id = sc.student_id\nJOIN course c ON sc.course_id = c.course_id;\n-- 查询视图\nSELECT * FROM v_student_score WHERE name = '张三';", "description": "通过视图简化多表查询，便于数据展示和权限控制。", "parameters": [], "returnValue": "返回视图查询结果", "examples": [{"code": "-- 查询所有学生的课程成绩（通过视图）\nSELECT * FROM v_student_score;", "explanation": "本例展示了如何通过视图统一查询学生成绩信息。"}]}}, {"name": "Stored Procedures and Functions", "trans": ["存储过程与函数"], "usage": {"syntax": "-- 创建统计学生平均分的函数\nDELIMITER //\nCREATE FUNCTION get_avg_score(stu_id INT)\nRETURNS DECIMAL(5,2)\nBEGIN\n  DECLARE avg_score DECIMAL(5,2);\n  SELECT AVG(score) INTO avg_score FROM score WHERE student_id = stu_id;\n  RETURN avg_score;\nEND //\nDELIMITER ;\n-- 调用函数\nSELECT get_avg_score(1);", "description": "通过存储过程和函数实现复杂业务逻辑，提高代码复用性和安全性。", "parameters": [], "returnValue": "返回函数或过程的执行结果", "examples": [{"code": "-- 创建统计某课程最高分的存储过程\nDELIMITER //\nCREATE PROCEDURE get_max_score(IN cid INT, OUT max_score DECIMAL(5,2))\nBEGIN\n  SELECT MAX(score) INTO max_score FROM score WHERE course_id = cid;\nEND //\nDELIMITER ;\n-- 调用存储过程\nCALL get_max_score(1, @max_score);\nSELECT @max_score;", "explanation": "本例展示了如何用存储过程统计课程最高分。"}]}}, {"name": "Trigger Application", "trans": ["触发器应用"], "usage": {"syntax": "-- 创建成绩变动日志表\nCREATE TABLE score_log (\n  log_id INT PRIMARY KEY AUTO_INCREMENT,\n  student_id INT,\n  course_id INT,\n  old_score DECIMAL(5,2),\n  new_score DECIMAL(5,2),\n  change_time DATETIME\n);\n-- 创建触发器\nDELIMITER //\nCREATE TRIGGER after_score_update\nAFTER UPDATE ON score\nFOR EACH ROW\nBEGIN\n  INSERT INTO score_log(student_id, course_id, old_score, new_score, change_time)\n  VALUES (OLD.student_id, OLD.course_id, OLD.score, NEW.score, NOW());\nEND //\nDELIMITER ;", "description": "通过触发器实现成绩变动自动记录，便于数据追踪和审计。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "-- 修改成绩后自动记录日志\nUPDATE score SET score = 88 WHERE score_id = 1;\n-- 查询日志\nSELECT * FROM score_log;", "explanation": "本例展示了成绩变动后如何自动记录到日志表。"}]}}, {"name": "Backup and Recovery", "trans": ["数据备份与恢复"], "usage": {"syntax": "-- 备份数据库\nmysqldump -u root -p student_db > student_db.sql\n-- 恢复数据库\nmysql -u root -p student_db < student_db.sql", "description": "通过mysqldump命令实现数据库的备份与恢复，保障数据安全。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "-- 导出成绩表数据\nmysqldump -u root -p student_db score > score.sql\n-- 恢复成绩表数据\nmysql -u root -p student_db < score.sql", "explanation": "本例展示了如何备份和恢复成绩表数据。"}]}}, {"name": "Permission and Security", "trans": ["权限分配与安全"], "usage": {"syntax": "-- 创建只读用户\nCREATE USER 'readonly'@'%' IDENTIFIED BY 'password';\nGRANT SELECT ON student_db.* TO 'readonly'@'%';\n-- 撤销权限\nREVOKE INSERT, UPDATE, DELETE ON student_db.* FROM 'readonly'@'%';", "description": "通过用户权限分配，保障数据库安全，防止数据被误操作。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "-- 查看用户权限\nSHOW GRANTS FOR 'readonly'@'%';", "explanation": "本例展示了如何分配和查看数据库用户权限。"}]}}, {"name": "Query Optimization and Indexing", "trans": ["查询优化与索引"], "usage": {"syntax": "-- 创建索引\nCREATE INDEX idx_score_student ON score(student_id);\n-- 使用EXPLAIN分析查询\nEXPLAIN SELECT * FROM score WHERE student_id = 1;", "description": "通过索引和EXPLAIN分析优化查询性能，提升系统响应速度。", "parameters": [], "returnValue": "返回查询执行计划或优化结果", "examples": [{"code": "-- 查询成绩表时使用索引优化\nSELECT * FROM score WHERE student_id = 1;", "explanation": "本例展示了如何通过索引提升查询效率。"}]}}, {"name": "Comprehensive Project Exercise", "trans": ["综合项目练习"], "usage": {"syntax": "# 综合项目练习", "description": "完成一个完整的数据库设计、数据录入、查询优化、权限分配和备份恢复流程。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 设计并实现学生成绩管理系统的全部表结构和约束。\n2. 编写视图、存储过程、触发器实现业务自动化。\n3. 录入测试数据并进行统计分析。\n4. 配置只读用户并测试权限。\n5. 备份并恢复数据库，验证数据完整性。\n6. 使用EXPLAIN和索引优化查询性能。", "explanation": "通过这些综合练习，全面提升MySQL项目开发与运维能力。"}]}}]}