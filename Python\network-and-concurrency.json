{"name": "Network and Concurrency", "trans": ["网络与并发"], "methods": [{"name": "requests Library", "trans": ["requests库"], "usage": {"syntax": "import requests\nrequests.get(url)", "description": "requests库用于发送HTTP请求，常用于网络数据交互，支持GET、POST等多种请求方式。", "parameters": [{"name": "url", "description": "请求的目标网址"}, {"name": "params/data", "description": "请求参数或数据"}], "returnValue": "Response对象，包含响应内容、状态码等", "examples": [{"code": "import requests\nr = requests.get('https://httpbin.org/get')\nprint(r.status_code)\nprint(r.text)", "explanation": "演示了requests库的GET请求用法。"}]}}, {"name": "urllib", "trans": ["urllib模块"], "usage": {"syntax": "import urllib.request\nurllib.request.urlopen(url)", "description": "urllib是Python内置的HTTP请求库，支持基本的网页抓取和下载。", "parameters": [{"name": "url", "description": "请求的网址"}], "returnValue": "HTTPResponse对象，包含响应内容等", "examples": [{"code": "import urllib.request\nresponse = urllib.request.urlopen('https://httpbin.org/get')\nprint(response.read().decode())", "explanation": "演示了urllib的基本用法。"}]}}, {"name": "Socket Programming", "trans": ["socket编程"], "usage": {"syntax": "import socket\ns = socket.socket()", "description": "socket模块用于底层网络通信，可实现TCP/UDP客户端和服务器。", "parameters": [{"name": "socket.socket", "description": "创建套接字对象"}, {"name": "bind/connect", "description": "绑定地址或连接服务器"}], "returnValue": "Socket对象", "examples": [{"code": "import socket\ns = socket.socket()\ns.connect(('example.com', 80))\ns.send(b'GET / HTTP/1.0\r\n\r\n')\nprint(s.recv(1024))\ns.close()", "explanation": "演示了socket客户端的基本用法。"}]}}, {"name": "threading and multiprocessing", "trans": ["threading与multiprocessing"], "usage": {"syntax": "import threading, multiprocessing", "description": "threading用于多线程并发，multiprocessing用于多进程并行，适合不同场景的并发需求。", "parameters": [{"name": "Thread/Process", "description": "线程或进程对象"}, {"name": "target", "description": "线程/进程执行的目标函数"}], "returnValue": "Thread或Process对象", "examples": [{"code": "import threading\ndef worker():\n    print('线程工作')\nt = threading.Thread(target=worker)\nt.start()\nt.join()", "explanation": "演示了threading模块的基本用法。"}, {"code": "import multiprocessing\ndef worker():\n    print('进程工作')\np = multiprocessing.Process(target=worker)\np.start()\np.join()", "explanation": "演示了multiprocessing模块的基本用法。"}]}}, {"name": "asyncio Asynchronous Programming", "trans": ["asyncio异步编程"], "usage": {"syntax": "import asyncio\nasync def func(): ...", "description": "asyncio用于异步IO编程，支持协程、事件循环等高并发场景。", "parameters": [{"name": "async def", "description": "定义协程函数"}, {"name": "await", "description": "等待异步操作完成"}], "returnValue": "协程对象或运行结果", "examples": [{"code": "import asyncio\nasync def main():\n    print('异步任务')\nasyncio.run(main())", "explanation": "演示了asyncio的基本用法。"}]}}, {"name": "queue", "trans": ["queue队列模块"], "usage": {"syntax": "import queue\nq = queue.Queue()", "description": "queue模块提供线程安全的队列，常用于多线程/多进程间通信。", "parameters": [{"name": "Queue", "description": "队列对象"}, {"name": "put/get", "description": "入队和出队操作"}], "returnValue": "Queue对象", "examples": [{"code": "import queue\nq = queue.Queue()\nq.put(1)\nprint(q.get())", "explanation": "演示了queue模块的基本用法。"}]}}, {"name": "Network and Concurrency Assignment", "trans": ["网络与并发练习"], "usage": {"syntax": "# 网络与并发练习", "description": "完成以下练习，巩固网络与并发相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用requests获取一个网页内容。\n2. 用socket实现简单TCP客户端。\n3. 用threading创建两个线程并输出内容。\n4. 用asyncio实现一个异步函数。\n5. 用queue实现生产者-消费者模型。", "explanation": "练习要求动手实践requests、socket、threading、asyncio、queue等。"}]}}]}