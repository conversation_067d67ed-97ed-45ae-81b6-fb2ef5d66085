{"name": "Coding Style and Best Practices", "trans": ["编码规范与最佳实践"], "methods": [{"name": "Naming Conventions", "trans": ["命名规范"], "usage": {"syntax": "变量名/函数名/类名/常量名的命名规则", "description": "良好的命名规范有助于提升代码可读性和可维护性。常见命名风格包括：\n- 变量/函数：camelCase（小驼峰）\n- 类/构造函数：PascalCase（大驼峰）\n- 常量：全大写加下划线（UPPER_SNAKE_CASE）", "parameters": [{"name": "identifier", "description": "需要命名的变量、函数、类、常量等标识符"}], "returnValue": "无返回值", "examples": [{"code": "// 变量和函数使用小驼峰\nlet userName = '张三';\nfunction getUserInfo() { /* ... */ }\n\n// 类和构造函数使用大驼峰\nclass UserProfile { /* ... */ }\nfunction Person() { /* ... */ }\n\n// 常量使用全大写加下划线\nconst MAX_COUNT = 100;\nconst API_BASE_URL = 'https://api.example.com';", "explanation": "示例展示了不同类型标识符的命名规范，有助于一眼区分变量、函数、类和常量。"}]}}, {"name": "Code Style", "trans": ["代码风格"], "usage": {"syntax": "统一的缩进、空格、分号、括号等代码格式", "description": "统一的代码风格有助于团队协作和代码维护。常见规范包括：\n- 缩进：2或4个空格，不用Tab\n- 语句结尾加分号\n- 运算符前后加空格\n- 大括号风格统一（如K&R风格）", "parameters": [{"name": "code", "description": "需要规范格式的代码片段"}], "returnValue": "无返回值", "examples": [{"code": "// 推荐的代码风格\nfunction sum(a, b) {\n  return a + b; // 缩进2或4空格，语句结尾加分号\n}\n\nif (isValid) {\n  doSomething();\n} else {\n  doOther();\n}\n\n// 运算符前后加空格\nlet total = price * count + tax;", "explanation": "示例展示了统一的缩进、分号、空格和大括号风格，提升代码整洁度和一致性。"}]}}, {"name": "Comments and Documentation", "trans": ["注释与文档"], "usage": {"syntax": "// 单行注释\n/* 多行注释 */\n/** 文档注释 */", "description": "合理的注释和文档有助于他人理解代码。注释应简明扼要，解释复杂逻辑或特殊用法。常见注释类型：\n- 单行注释：//\n- 多行注释：/* ... */\n- 文档注释：/** ... */，用于函数、类、模块说明", "parameters": [{"name": "comment", "description": "需要添加注释的代码或文档内容"}], "returnValue": "无返回值", "examples": [{"code": "// 单行注释：解释变量用途\nlet age = 18; // 用户年龄\n\n/* 多行注释：解释复杂逻辑 */\nfunction calcScore(a, b) {\n  /*\n    1. 先加权\n    2. 再取平均\n  */\n  return (a * 0.6 + b * 0.4) / 2;\n}\n\n/**\n * 计算两数之和\n * @param {number} a 第一个数\n * @param {number} b 第二个数\n * @returns {number} 和\n */\nfunction sum(a, b) {\n  return a + b;\n}", "explanation": "示例展示了单行注释、多行注释和文档注释的用法，便于代码自解释和自动生成文档。"}]}}, {"name": "Readability and Maintainability", "trans": ["代码可读性与可维护性"], "usage": {"syntax": "良好的结构、简洁的逻辑、适当的抽象和模块化", "description": "高可读性和可维护性的代码应具备：\n- 单一职责：每个函数/模块只做一件事\n- 适当拆分：复杂逻辑分解为小函数\n- 明确命名：变量/函数/模块名表达真实含义\n- 避免魔法数字和硬编码\n- 适当抽象和复用", "parameters": [{"name": "code", "description": "需要优化可读性和可维护性的代码片段"}], "returnValue": "无返回值", "examples": [{"code": "// 不推荐：函数过长且命名不清\nfunction fn(a, b, c) {\n  let x = a + b;\n  // ...很多复杂逻辑\n  return x + c;\n}\n\n// 推荐：拆分小函数，命名清晰\nfunction add(a, b) {\n  return a + b;\n}\nfunction calcTotal(a, b, c) {\n  const sum = add(a, b);\n  // ...其他逻辑\n  return sum + c;\n}", "explanation": "示例对比了低可读性和高可读性的代码，强调了函数拆分和命名的重要性。"}]}}, {"name": "Best Practices Assignment", "trans": ["规范与最佳实践练习"], "usage": {"syntax": "// 规范与最佳实践练习", "description": "完成以下练习，巩固编码规范与最佳实践。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "/*\n练习：重构下列代码，使其符合命名规范、代码风格、注释和可读性要求\n\nfunction f(a,b){let c=a+b;return c;}\n*/\n\n// 你的重构代码：\n/**\n * 计算两数之和\n * @param {number} a 第一个数\n * @param {number} b 第二个数\n * @returns {number} 和\n */\nfunction sum(a, b) {\n  return a + b;\n}\n", "explanation": "练习要求对原始代码进行重构，提升命名、风格、注释和可读性，符合最佳实践。"}]}}]}