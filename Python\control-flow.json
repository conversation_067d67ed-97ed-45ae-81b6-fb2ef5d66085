{"name": "Control Flow", "trans": ["流程控制"], "methods": [{"name": "If Statement", "trans": ["if条件语句"], "usage": {"syntax": "if 条件:\n    代码块\nelif 条件:\n    代码块\nelse:\n    代码块", "description": "if语句用于条件判断，支持if-elif-else多分支结构。条件为True时执行对应代码块。", "parameters": [{"name": "条件", "description": "任意可转换为布尔值的表达式"}], "returnValue": "无返回值", "examples": [{"code": "x = 10\nif x > 0:\n    print('正数')\nelif x == 0:\n    print('零')\nelse:\n    print('负数')", "explanation": "演示了if-elif-else条件分支的用法。"}]}}, {"name": "For Loop", "trans": ["for循环"], "usage": {"syntax": "for 变量 in 可迭代对象:\n    代码块", "description": "for循环用于遍历序列、列表、字典、字符串等可迭代对象。每次循环变量取一个元素。", "parameters": [{"name": "变量", "description": "循环中每次赋值的变量名"}, {"name": "可迭代对象", "description": "如list、tuple、str、dict等"}], "returnValue": "无返回值", "examples": [{"code": "for i in [1, 2, 3]:\n    print(i)\n# 输出：1 2 3", "explanation": "演示了for循环遍历列表。"}]}}, {"name": "While Loop", "trans": ["while循环"], "usage": {"syntax": "while 条件:\n    代码块", "description": "while循环在条件为True时反复执行代码块，适合不确定循环次数的场景。", "parameters": [{"name": "条件", "description": "循环继续执行的判断条件"}], "returnValue": "无返回值", "examples": [{"code": "n = 3\nwhile n > 0:\n    print(n)\n    n -= 1\n# 输出：3 2 1", "explanation": "演示了while循环的基本用法。"}]}}, {"name": "Break and Continue", "trans": ["break与continue"], "usage": {"syntax": "break\ncontinue", "description": "break用于提前终止循环，continue用于跳过本次循环进入下一次。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "for i in range(5):\n    if i == 3:\n        break\n    print(i)  # 输出0 1 2\nfor i in range(5):\n    if i % 2 == 0:\n        continue\n    print(i)  # 输出1 3", "explanation": "演示了break和continue的用法。"}]}}, {"name": "Pass Statement", "trans": ["pass语句"], "usage": {"syntax": "pass", "description": "pass是空语句，占位用，不做任何操作。常用于语法结构需要代码但暂时不写时。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "for i in range(3):\n    pass  # 占位，不执行任何操作", "explanation": "演示了pass语句的占位作用。"}]}}, {"name": "List Comprehension", "trans": ["列表推导式"], "usage": {"syntax": "[表达式 for 变量 in 可迭代对象 if 条件]", "description": "列表推导式用于快速生成新列表，支持条件过滤和表达式计算，简洁高效。", "parameters": [{"name": "表达式", "description": "生成新元素的表达式"}, {"name": "变量", "description": "循环变量"}, {"name": "可迭代对象", "description": "如list、range等"}, {"name": "条件", "description": "可选，过滤条件"}], "returnValue": "新生成的列表", "examples": [{"code": "squares = [x * x for x in range(5)]\nprint(squares)  # [0, 1, 4, 9, 16]", "explanation": "演示了列表推导式的用法。"}]}}, {"name": "Conditional Expression", "trans": ["条件表达式"], "usage": {"syntax": "x if 条件 else y", "description": "条件表达式（三元表达式）用于根据条件选择值，简化if-else结构。", "parameters": [{"name": "条件", "description": "判断条件"}, {"name": "x, y", "description": "条件为True或False时的返回值"}], "returnValue": "x或y，取决于条件真假", "examples": [{"code": "a = 5\nb = 10\nmax_val = a if a > b else b\nprint(max_val)  # 10", "explanation": "演示了条件表达式的用法。"}]}}, {"name": "Control Flow Assignment", "trans": ["流程控制练习"], "usage": {"syntax": "# 流程控制练习", "description": "完成以下练习，巩固流程控制相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 判断一个整数是正数、负数还是零。\n2. 用for循环打印1到10的偶数。\n3. 用while循环计算1到100的和。\n4. 用列表推导式生成所有能被3整除的1~30的平方数。", "explanation": "练习要求动手实践if、for、while、break、continue、列表推导式等流程控制结构。"}]}}]}