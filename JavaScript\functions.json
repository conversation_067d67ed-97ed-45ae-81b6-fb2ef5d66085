{"name": "Functions", "trans": ["函数基础"], "methods": [{"name": "Function Declaration & Expression", "trans": ["函数声明与表达式"], "usage": {"syntax": "function add(a, b) { return a + b; }\nconst sub = function(a, b) { return a - b; };", "description": "函数声明使用function关键字直接定义，函数表达式将函数赋值给变量。声明提升只对函数声明有效。", "parameters": [{"name": "a, b", "description": "函数参数。"}], "returnValue": "返回函数执行结果。", "examples": [{"code": "console.log(add(2, 3)); // 5\nconsole.log(sub(5, 2)); // 3", "explanation": "演示函数声明和表达式的用法。"}]}}, {"name": "Parameters & Default Values", "trans": ["参数与默认值"], "usage": {"syntax": "function greet(name = '匿名') {\n  return '你好，' + name;\n}", "description": "函数参数可设置默认值，调用时未传参则使用默认值。", "parameters": [{"name": "name", "description": "参数，默认值为'匿名'。"}], "returnValue": "返回问候语字符串。", "examples": [{"code": "console.log(greet()); // 你好，匿名\nconsole.log(greet('小明')); // 你好，小明", "explanation": "演示参数默认值的用法。"}]}}, {"name": "Rest Parameters & arguments", "trans": ["剩余参数与arguments"], "usage": {"syntax": "function sum(...nums) {\n  return nums.reduce((a, b) => a + b, 0);\n}\nfunction showArgs() {\n  console.log(arguments);\n}", "description": "...剩余参数用于收集所有传入参数为数组。arguments为类数组对象，包含所有实参。", "parameters": [{"name": "...nums", "description": "收集所有参数为数组。"}, {"name": "arguments", "description": "函数内的类数组对象，包含所有实参。"}], "returnValue": "返回参数处理结果或输出所有参数。", "examples": [{"code": "console.log(sum(1,2,3)); // 6\nshowArgs(1, 'a', true); // Arguments(3) [1, 'a', true]", "explanation": "演示剩余参数和arguments的用法。"}]}}, {"name": "Anonymous & Arrow Functions", "trans": ["匿名函数与箭头函数"], "usage": {"syntax": "const fn = function(x) { return x * 2; };\nconst arrow = x => x * 2;", "description": "匿名函数没有名字，常用于回调。箭头函数语法简洁，不绑定this。", "parameters": [{"name": "x", "description": "函数参数。"}], "returnValue": "返回参数处理结果。", "examples": [{"code": "console.log([1,2,3].map(function(x){ return x*2; })); // [2,4,6]\nconsole.log([1,2,3].map(x => x*2)); // [2,4,6]", "explanation": "演示匿名函数和箭头函数的用法。"}]}}, {"name": "Immediately Invoked Function Expression (IIFE)", "trans": ["立即执行函数（IIFE）"], "usage": {"syntax": "(function(){ /* 代码 */ })();\n(() => { /* 代码 */ })();", "description": "IIFE定义后立即执行，常用于创建独立作用域，避免变量污染全局。", "parameters": [], "returnValue": "返回IIFE执行结果。", "examples": [{"code": "(function(){ console.log('立即执行'); })();\n(() => { console.log('箭头IIFE'); })();", "explanation": "演示两种IIFE写法。"}]}}, {"name": "作业：函数基础实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 用声明和表达式定义函数\n// 2. 使用参数默认值和剩余参数\n// 3. 匿名函数与箭头函数\n// 4. IIFE实现独立作用域", "description": "通过实践函数声明、参数、箭头函数、IIFE等，掌握JS函数基础。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 声明/表达式/箭头函数\n// 2. 参数默认值/剩余参数\n// 3. IIFE", "explanation": "作业提示，需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nfunction add(a, b = 1) { return a + b; }\nconst sum = (...args) => args.reduce((a,b)=>a+b,0);\n(function(){ console.log('IIFE'); })();\nconsole.log(add(2));\nconsole.log(sum(1,2,3));", "explanation": "涵盖声明、参数、箭头函数、IIFE的正确实现。"}]}}]}