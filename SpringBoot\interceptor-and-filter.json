{"name": "Interceptor and Filter", "trans": ["拦截器与过滤器"], "methods": [{"name": "HandlerInterceptor Usage", "trans": ["HandlerInterceptor用法"], "usage": {"syntax": "实现HandlerInterceptor接口，重写preHandle/postHandle/afterCompletion方法", "description": "HandlerInterceptor用于拦截和处理Controller请求前后逻辑，如登录校验、权限控制、日志记录等。", "parameters": [{"name": "preHandle", "description": "请求进入Controller前执行，返回false可中断请求"}, {"name": "postHandle", "description": "Controller处理后、视图渲染前执行"}, {"name": "afterCompletion", "description": "请求完成后执行，适合资源清理"}], "returnValue": "拦截和处理请求的能力", "examples": [{"code": "@Component\npublic class AuthInterceptor implements HandlerInterceptor {\n    @Override\n    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {\n        // 登录校验逻辑\n        return true;\n    }\n}\n// 注册拦截器\n@Configuration\npublic class WebConfig implements WebMvcConfigurer {\n    @Override\n    public void addInterceptors(InterceptorRegistry registry) {\n        registry.addInterceptor(new AuthInterceptor()).addPathPatterns(\"/**\");\n    }\n}", "explanation": "实现HandlerInterceptor并注册到WebMvcConfigurer即可生效。"}]}}, {"name": "Filter <PERSON>age", "trans": ["Filter用法"], "usage": {"syntax": "实现javax.servlet.Filter接口，重写doFilter方法", "description": "Filter可对所有HTTP请求进行预处理和后处理，常用于日志、权限、编码等全局处理。", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "处理请求和响应的核心方法"}], "returnValue": "全局请求处理能力", "examples": [{"code": "@WebFilter(urlPatterns = \"/*\")\npublic class LogFilter implements Filter {\n    @Override\n    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {\n        System.out.println(\"请求进入Filter\");\n        chain.doFilter(request, response);\n    }\n}\n// 启动类加@ServletComponentScan启用@WebFilter注解", "explanation": "Filter适合全局处理所有请求，需@ServletComponentScan激活。"}]}}, {"name": "Global Request Logging", "trans": ["全局请求日志"], "usage": {"syntax": "通过拦截器或过滤器统一记录请求日志", "description": "可在拦截器或过滤器中统一记录请求的URL、参数、响应时间等信息，便于排查和监控。", "parameters": [{"name": "HttpServletRequest", "description": "获取请求信息"}, {"name": "System.currentTimeMillis", "description": "记录请求耗时"}], "returnValue": "统一的请求日志输出", "examples": [{"code": "public class LogInterceptor implements HandlerInterceptor {\n    private ThreadLocal<Long> startTime = new ThreadLocal<>();\n    @Override\n    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {\n        startTime.set(System.currentTimeMillis());\n        return true;\n    }\n    @Override\n    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {\n        long duration = System.currentTimeMillis() - startTime.get();\n        System.out.println(\"请求\" + request.getRequestURI() + \"耗时：\" + duration + \"ms\");\n    }\n}", "explanation": "通过拦截器记录每个请求的耗时和路径。"}]}}, {"name": "Request Parameter Validation (JSR-303)", "trans": ["请求参数校验（JSR-303）"], "usage": {"syntax": "@Valid/@Validated注解结合JSR-303注解实现参数校验", "description": "Spring Boot集成JSR-303标准，支持@Valid/@Validated注解和@NotNull、@Size等参数校验注解，自动校验请求参数。", "parameters": [{"name": "@Valid/@Validated", "description": "方法参数或类上标注，触发校验"}, {"name": "JSR-303注解", "description": "@NotNull、@Size、@Email等"}], "returnValue": "自动校验并抛出异常的参数对象", "examples": [{"code": "public class UserDTO {\n    @NotNull\n    private String name;\n    @Email\n    private String email;\n    // getter/setter\n}\n\n@PostMapping(\"/user\")\npublic String addUser(@Valid @RequestBody UserDTO user) {\n    return \"添加成功\";\n}", "explanation": "@Valid结合JSR-303注解实现请求参数自动校验。"}]}}, {"name": "Interceptor and Filter Assignment", "trans": ["拦截器与过滤器练习"], "usage": {"syntax": "# 拦截器与过滤器练习", "description": "完成以下练习，巩固Spring Boot拦截器与过滤器相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 实现一个登录校验拦截器。\n2. 实现一个全局请求日志Filter。\n3. 用JSR-303注解实现参数校验。\n4. 理解拦截器与过滤器的区别和适用场景。", "explanation": "通过这些练习掌握Spring Boot拦截器与过滤器的核心用法。"}]}}]}