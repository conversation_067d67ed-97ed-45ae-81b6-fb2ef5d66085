{"name": "Vue Test Utils", "trans": ["Vue Test Utils"], "methods": [{"name": "Render Component", "trans": ["渲染组件"], "usage": {"syntax": "const wrapper = mount(MyComponent)", "description": "使用mount方法渲染Vue组件，返回wrapper对象用于后续测试。", "parameters": [{"name": "mount", "description": "挂载并渲染组件的方法"}, {"name": "MyComponent", "description": "待测试的组件对象"}], "returnValue": "组件的wrapper包装对象", "examples": [{"code": "const wrapper = mount(MyComponent)", "explanation": "渲染MyComponent组件，获取wrapper对象。"}]}}, {"name": "Find Element", "trans": ["查询元素"], "usage": {"syntax": "const button = wrapper.find('button')", "description": "通过find方法查找组件内的元素或子组件，便于后续交互和断言。", "parameters": [{"name": "find", "description": "查找元素或子组件的方法"}, {"name": "选择器", "description": "CSS选择器或组件名"}], "returnValue": "找到的元素或子组件的wrapper对象", "examples": [{"code": "const button = wrapper.find('button')", "explanation": "查找组件内的button元素。"}]}}, {"name": "Trigger Event", "trans": ["触发事件"], "usage": {"syntax": "await button.trigger('click')", "description": "通过trigger方法模拟用户事件，如点击、输入等，测试组件的交互逻辑。", "parameters": [{"name": "trigger", "description": "触发事件的方法"}, {"name": "事件名", "description": "如'click'、'input'等"}], "returnValue": "事件触发后的异步操作结果", "examples": [{"code": "await button.trigger('click')", "explanation": "模拟点击button按钮。"}]}}, {"name": "Assert Result", "trans": ["断言结果"], "usage": {"syntax": "expect(wrapper.text()).toContain('内容')", "description": "通过expect等断言方法，验证组件渲染、状态、事件等是否符合预期。", "parameters": [{"name": "expect", "description": "断言方法"}, {"name": "toContain/toBe等", "description": "具体断言类型"}], "returnValue": "断言通过或失败的结果", "examples": [{"code": "expect(wrapper.text()).toContain('Hello')", "explanation": "断言组件文本内容包含Hello。"}]}}, {"name": "Async Testing", "trans": ["异步测试"], "usage": {"syntax": "await wrapper.find('button').trigger('click')\nawait nextTick()\nexpect(wrapper.text()).toContain('完成')", "description": "结合async/await和nextTick，测试异步更新和事件，确保断言在异步完成后执行。", "parameters": [{"name": "nextTick", "description": "等待DOM异步更新的方法"}], "returnValue": "异步操作后的断言结果", "examples": [{"code": "await wrapper.find('button').trigger('click')\nawait nextTick()\nexpect(wrapper.text()).toContain('完成')", "explanation": "等待异步更新后再断言内容。"}]}}, {"name": "Best Practices", "trans": ["最佳实践"], "usage": {"syntax": "// 结构清晰、命名规范、只测行为不测实现", "description": "编写高质量测试用例应结构清晰、命名规范、只测试行为不依赖实现细节，保证测试可维护性和健壮性。", "parameters": [{"name": "结构清晰", "description": "分组、注释、setup/teardown"}, {"name": "命名规范", "description": "用例和变量命名清晰"}, {"name": "行为驱动", "description": "只测外部行为"}], "returnValue": "高可维护性的测试代码", "examples": [{"code": "describe('MyComponent', () => {\n  it('点击按钮后显示内容', async () => {\n    const wrapper = mount(MyComponent)\n    await wrapper.find('button').trigger('click')\n    expect(wrapper.text()).toContain('已点击')\n  })\n})", "explanation": "结构清晰、行为驱动的测试用例。"}]}}, {"name": "Vue Test Utils Assignment", "trans": ["Vue Test Utils练习"], "usage": {"syntax": "# Vue Test Utils练习", "description": "完成以下练习，巩固Vue Test Utils相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用mount渲染组件。\n2. 用find和trigger测试交互。\n3. 编写断言验证渲染和事件。\n4. 编写异步测试。\n5. 按最佳实践组织测试代码。", "explanation": "通过这些练习掌握Vue Test Utils的用法和规范。"}]}}]}