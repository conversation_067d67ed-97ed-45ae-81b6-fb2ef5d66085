{"name": "Layout Widgets", "trans": ["布局组件"], "methods": [{"name": "Row, Column", "trans": ["Row、Column"], "usage": {"syntax": "<PERSON>(children: [...]), <PERSON><PERSON><PERSON>(children: [...])", "description": "Row用于水平布局，Column用于垂直布局，是Flutter最常用的线性布局组件。", "parameters": [{"name": "children", "description": "子组件数组"}, {"name": "mainAxisAlignment", "description": "主轴对齐方式"}, {"name": "crossAxisAlignment", "description": "交叉轴对齐方式"}], "returnValue": "无返回值，直接渲染在界面上。", "examples": [{"code": "// Row和Column布局示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      home: Scaffold(\n        appBar: AppBar(title: Text('Row和Column示例')),\n        body: Column(\n          children: [\n            Row(\n              mainAxisAlignment: MainAxisAlignment.center,\n              children: [\n                Icon(Icons.star, color: Colors.orange),\n                Sized<PERSON>ox(width: 8),\n                Text('水平排列'),\n              ],\n            ),\n            Divider(),\n            Column(\n              crossAxisAlignment: CrossAxisAlignment.start,\n              children: [\n                Text('垂直排列1'),\n                Text('垂直排列2'),\n              ],\n            ),\n          ],\n        ),\n      ),\n    );\n  }\n}", "explanation": "演示了Row和Column的基本用法，分别实现水平和垂直布局。"}]}}, {"name": "Stack, Align, Positioned", "trans": ["Stack、Align、Positioned"], "usage": {"syntax": "<PERSON><PERSON>(children: [...]), <PERSON><PERSON>(alignment: ...), <PERSON><PERSON><PERSON><PERSON>(...) ", "description": "Stack用于堆叠布局，Align用于对子组件进行对齐，Positioned用于Stack内绝对定位。", "parameters": [{"name": "children", "description": "Stack的子组件数组"}, {"name": "alignment", "description": "Align的对齐方式"}, {"name": "left/top/right/bottom", "description": "Positioned的定位参数"}], "returnValue": "无返回值，直接渲染在界面上。", "examples": [{"code": "// Stack、<PERSON><PERSON>、Positioned示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      home: Scaffold(\n        appBar: AppBar(title: Text('Stack/Align/Positioned示例')),\n        body: Center(\n          child: Stack(\n            alignment: Alignment.center,\n            children: [\n              Container(width: 120, height: 120, color: Colors.blue[100]),\n              <PERSON><PERSON>(\n                alignment: Alignment.topRight,\n                child: Icon(Icons.star, color: Colors.orange),\n              ),\n              Positioned(\n                left: 10,\n                bottom: 10,\n                child: Text('绝对定位'),\n              ),\n            ],\n          ),\n        ),\n      ),\n    );\n  }\n}", "explanation": "演示了Stack的堆叠、Align的对齐和Positioned的绝对定位用法。"}]}}, {"name": "Expanded, Flexible, Spacer", "trans": ["Expanded、Flexible、Spacer"], "usage": {"syntax": "Expanded(child: ...), Flexible(child: ...), Spacer()", "description": "Expanded和Flexible用于弹性布局，自动分配剩余空间，Spacer用于占位和分隔。", "parameters": [{"name": "child", "description": "子组件"}, {"name": "flex", "description": "弹性系数，决定空间分配比例"}], "returnValue": "无返回值，直接渲染在界面上。", "examples": [{"code": "// Expanded、Flexible、Spacer示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      home: Scaffold(\n        appBar: AppBar(title: Text('弹性布局示例')),\n        body: Row(\n          children: [\n            Expanded(child: Container(color: Colors.red, height: 40)),\n            Spacer(),\n            Flexible(flex: 2, child: Container(color: Colors.green, height: 40)),\n          ],\n        ),\n      ),\n    );\n  }\n}", "explanation": "演示了Expanded、Flexible和Spacer的弹性布局和占位用法。"}]}}, {"name": "Padding, Margin, Center", "trans": ["Padding、Margin、Center"], "usage": {"syntax": "Padding(padding: ...), Container(margin: ...), <PERSON>(child: ...) ", "description": "Padding用于内边距，Container的margin属性用于外边距，Center用于居中显示子组件。", "parameters": [{"name": "padding", "description": "内边距EdgeInsets"}, {"name": "margin", "description": "外边距EdgeInsets"}, {"name": "child", "description": "子组件"}], "returnValue": "无返回值，直接渲染在界面上。", "examples": [{"code": "// Padding、<PERSON><PERSON>、Center示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      home: Scaffold(\n        appBar: AppBar(title: Text('边距与居中示例')),\n        body: Center(\n          child: Container(\n            margin: EdgeInsets.all(20), // 外边距\n            padding: EdgeInsets.symmetric(horizontal: 30, vertical: 10), // 内边距\n            color: Colors.blue[100],\n            child: Text('带边距的内容'),\n          ),\n        ),\n      ),\n    );\n  }\n}", "explanation": "演示了Padding、Container的margin和Center的用法。"}]}}, {"name": "Responsive Layout", "trans": ["自适应布局"], "usage": {"syntax": "LayoutBuilder(builder: ...), MediaQuery.of(context).size", "description": "自适应布局用于根据屏幕尺寸动态调整界面，常用LayoutBuilder和MediaQuery。", "parameters": [{"name": "builder", "description": "LayoutBuilder的构建函数，提供约束信息"}, {"name": "MediaQuery", "description": "获取屏幕尺寸等信息"}], "returnValue": "无返回值，直接渲染在界面上。", "examples": [{"code": "// 自适应布局示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      home: Scaffold(\n        appBar: AppBar(title: Text('自适应布局示例')),\n        body: LayoutBuilder(\n          builder: (context, constraints) {\n            // 根据宽度自适应布局\n            if (constraints.maxWidth < 600) {\n              return Center(child: Text('小屏幕布局'));\n            } else {\n              return Center(child: Text('大屏幕布局'));\n            }\n          },\n        ),\n      ),\n    );\n  }\n}", "explanation": "演示了LayoutBuilder实现自适应布局的基本用法。"}]}}, {"name": "Assignment", "trans": ["作业：实现一个包含Row、Column、Stack和自适应布局的页面。"], "usage": {"syntax": "无", "description": "请用Flutter实现一个页面，包含Row、Column、Stack、Padding等布局组件，并根据屏幕宽度自适应显示不同内容。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 作业参考实现\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      home: Scaffold(\n        appBar: AppBar(title: Text('作业实现')),\n        body: LayoutBuilder(\n          builder: (context, constraints) {\n            if (constraints.maxWidth < 600) {\n              // 小屏幕：Column+Row+Stack\n              return Column(\n                children: [\n                  Row(\n                    mainAxisAlignment: MainAxisAlignment.center,\n                    children: [\n                      Icon(Icons.star),\n                      Text('Row布局'),\n                    ],\n                  ),\n                  Stack(\n                    alignment: Alignment.center,\n                    children: [\n                      Container(width: 100, height: 100, color: Colors.blue[100]),\n                      Positioned(bottom: 8, right: 8, child: Icon(Icons.add)),\n                    ],\n                  ),\n                ],\n              );\n            } else {\n              // 大屏幕：显示不同内容\n              return Center(child: Text('大屏幕自适应内容'));\n            }\n          },\n        ),\n      ),\n    );\n  }\n}", "explanation": "作业要求实现包含多种布局组件和自适应布局的页面。"}]}}]}