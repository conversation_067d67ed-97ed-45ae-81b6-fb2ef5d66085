{"name": "Practical Cases", "trans": ["实战案例"], "methods": [{"name": "Distributed Lock Implementation", "trans": ["分布式锁实现"], "usage": {"syntax": "通过SET NX EX命令实现分布式锁，保证多实例互斥。", "description": "利用Redis的SET key value NX EX命令实现分布式锁，适合高并发场景下的资源互斥控制。", "parameters": [{"name": "SET NX EX", "description": "原子性设置key并带过期时间。"}, {"name": "DEL", "description": "释放锁时删除key。"}], "returnValue": "无返回值", "examples": [{"code": "# 获取锁\nSET lock_key unique_value NX EX 10\n# 释放锁\nDEL lock_key\n", "explanation": "通过SET NX EX实现分布式锁，DEL释放锁。"}]}}, {"name": "Cache Penetration/Breakdown/Avalanche Protection", "trans": ["缓存穿透/击穿/雪崩防护"], "usage": {"syntax": "通过布隆过滤器、互斥锁、预热等手段防护缓存问题。", "description": "缓存穿透可用布隆过滤器拦截无效请求，击穿用互斥锁防止并发穿透，雪崩通过预热、分批过期等方式缓解。", "parameters": [{"name": "布隆过滤器", "description": "防止缓存穿透。"}, {"name": "互斥锁", "description": "防止缓存击穿。"}, {"name": "分批过期", "description": "缓解缓存雪崩。"}], "returnValue": "无返回值", "examples": [{"code": "# 布隆过滤器示例\nBF.ADD user_ids 123\nBF.EXISTS user_ids 123\n# 互斥锁防击穿\nSET lock_key unique NX EX 5\n# 分批设置过期时间\nEXPIRE key $RANDOM\n", "explanation": "通过多种手段防护缓存穿透、击穿和雪崩。"}]}}, {"name": "Message Queue and Leaderboard", "trans": ["消息队列与排行榜"], "usage": {"syntax": "利用List/ZSet实现消息队列和排行榜功能。", "description": "List结构可实现简单消息队列，ZSet可实现积分排行榜和优先队列。", "parameters": [{"name": "LPUSH/RPOP", "description": "消息队列入队和出队。"}, {"name": "ZADD/ZREVRANGE", "description": "排行榜添加和查询。"}], "returnValue": "无返回值", "examples": [{"code": "# 消息队列\nLPUSH queue msg1\nRPOP queue\n# 排行榜\nZADD rank 100 user1\nZREVRANGE rank 0 9 WITHSCORES\n", "explanation": "List实现消息队列，ZSet实现排行榜。"}]}}, {"name": "Session Sharing", "trans": ["Session共享"], "usage": {"syntax": "通过Hash结构存储Session，实现多服务共享。", "description": "利用Hash结构存储用户Session信息，实现分布式系统下的会话共享。", "parameters": [{"name": "HSET/HGET", "description": "存取Session信息。"}], "returnValue": "无返回值", "examples": [{"code": "# 存储Session\nHSET session:user1 token abc123\n# 获取Session\nHGET session:user1 token\n", "explanation": "Hash结构实现Session共享。"}]}}, {"name": "Rate Limiting and Counter", "trans": ["限流与计数器"], "usage": {"syntax": "通过INCR/EXPIRE等命令实现限流和计数。", "description": "利用INCR、EXPIRE等命令实现接口限流、访问计数等功能，适合高并发场景。", "parameters": [{"name": "INCR", "description": "自增计数。"}, {"name": "EXPIRE", "description": "设置过期时间。"}], "returnValue": "无返回值", "examples": [{"code": "# 限流计数\nINCR api:count\nEXPIRE api:count 60\n", "explanation": "INCR+EXPIRE实现限流和计数。"}]}}, {"name": "实战案例作业", "trans": ["作业：实战案例"], "usage": {"syntax": "请完成以下任务：\n1. 实现分布式锁。\n2. 防护缓存穿透/击穿/雪崩。\n3. 实现消息队列和排行榜。\n4. 实现Session共享和限流。", "description": "通过实际操作掌握Redis在实战中的多种应用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 作业示例\n# 1. 用SET NX EX实现分布式锁\n# 2. 用布隆过滤器防穿透\n# 3. 用List/ZSet实现队列和排行榜\n# 4. 用Hash/INCR实现Session和限流\n", "explanation": "通过动手实验掌握实战案例的实现。"}]}}, {"name": "实战案例正确实现示例", "trans": ["正确实现卡片"], "usage": {"syntax": "实战案例的标准实现与验证流程。", "description": "展示如何标准实现分布式锁、缓存防护、消息队列、Session共享和限流。", "parameters": [{"name": "SET NX EX", "description": "分布式锁。"}, {"name": "布隆过滤器", "description": "防护缓存穿透。"}, {"name": "List/ZSet", "description": "队列和排行榜。"}, {"name": "Hash/INCR", "description": "Session和限流。"}], "returnValue": "无返回值", "examples": [{"code": "# 标准实现流程\n1. SET NX EX实现分布式锁\n2. BF.ADD/EXISTS防穿透\n3. LPUSH/ZADD实现队列和排行榜\n4. HSET/INCR实现Session和限流\n", "explanation": "标准实战案例实现流程，保证功能正确高效。"}]}}]}