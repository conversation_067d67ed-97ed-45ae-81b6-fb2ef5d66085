{"name": "Query Optimization", "trans": ["查询优化"], "methods": [{"name": "EXPLAIN Analysis", "trans": ["EXPLAIN分析"], "usage": {"syntax": "EXPLAIN SELECT 查询语句;", "description": "EXPLAIN命令用于分析SELECT查询的执行计划，显示MySQL优化器如何执行查询，包括表的访问顺序、使用的索引、连接类型等信息。通过分析这些信息，可以找出查询的性能瓶颈并进行优化。", "parameters": [{"name": "查询语句", "description": "需要分析的SELECT查询语句"}], "returnValue": "返回查询执行计划的详细信息表", "examples": [{"code": "-- 分析简单查询的执行计划\nEXPLAIN SELECT * FROM students WHERE class_id = 3;\n\n-- 分析连接查询的执行计划\nEXPLAIN SELECT s.name, c.course_name \nFROM students s \nJOIN enrollments e ON s.id = e.student_id \nJOIN courses c ON e.course_id = c.id \nWHERE s.age > 20;", "explanation": "第一个例子分析了一个简单的WHERE条件查询；第二个例子分析了一个三表连接查询。EXPLAIN结果会显示每个表的访问方式、使用的索引和连接类型等。"}]}}, {"name": "Query Rewriting", "trans": ["查询重写"], "usage": {"syntax": "-- 原查询\n原始SQL语句;\n\n-- 优化后查询\n优化后SQL语句;", "description": "查询重写是通过修改SQL语句的结构和写法来提高查询效率的技术。常见的查询重写技术包括避免使用SELECT *、优化WHERE子句顺序、使用EXISTS代替IN、拆分复杂查询等。", "parameters": [{"name": "原始SQL语句", "description": "需要优化的SQL查询"}, {"name": "优化后SQL语句", "description": "重写后的更高效SQL查询"}], "returnValue": "无返回值，仅为示例说明", "examples": [{"code": "-- 原查询：使用SELECT *\nSELECT * FROM students WHERE age > 20;\n\n-- 优化后：只选择需要的列\nSELECT id, name, age FROM students WHERE age > 20;\n\n-- 原查询：使用OR可能导致索引失效\nSELECT * FROM students WHERE class_id = 3 OR age > 20;\n\n-- 优化后：使用UNION将OR拆分\nSELECT * FROM students WHERE class_id = 3\nUNION\nSELECT * FROM students WHERE age > 20 AND class_id != 3;", "explanation": "第一个例子通过只选择需要的列减少数据传输；第二个例子通过将OR条件拆分为UNION查询，使每个子查询都能使用索引。"}]}}, {"name": "Covering Index", "trans": ["覆盖索引"], "usage": {"syntax": "CREATE INDEX 索引名 ON 表名(列1, 列2, ...);\nSELECT 列1, 列2, ... FROM 表名 WHERE 条件;", "description": "覆盖索引是指查询的所有列都包含在索引中，这样MySQL可以直接从索引中获取数据，而无需回表查询实际的数据行，从而大幅提高查询效率。", "parameters": [{"name": "索引名", "description": "要创建的索引名称"}, {"name": "表名", "description": "要添加索引的表"}, {"name": "列1, 列2, ...", "description": "要包含在索引中的列"}, {"name": "条件", "description": "查询的WHERE条件"}], "returnValue": "无返回值，仅为示例说明", "examples": [{"code": "-- 创建覆盖索引\nCREATE INDEX idx_name_age_class ON students(name, age, class_id);\n\n-- 使用覆盖索引的查询（所有查询的列都在索引中）\nSELECT name, age, class_id FROM students WHERE age > 20;\n\n-- EXPLAIN分析可以看到Extra列显示\"Using index\"，表示使用了覆盖索引\nEXPLAIN SELECT name, age, class_id FROM students WHERE age > 20;", "explanation": "这个例子创建了一个包含name、age和class_id三个字段的索引，然后执行的查询只选择这三个字段并按age条件过滤。MySQL可以直接从索引中获取所有数据，不需要访问表中的实际行。"}]}}, {"name": "Slow Query Log", "trans": ["慢查询日志"], "usage": {"syntax": "-- 查看慢查询日志是否启用\nSHOW VARIABLES LIKE 'slow_query_log';\n\n-- 启用慢查询日志\nSET GLOBAL slow_query_log = 'ON';\n\n-- 设置慢查询时间阈值（秒）\nSET GLOBAL long_query_time = 1;", "description": "慢查询日志记录执行时间超过指定阈值的查询，是识别需要优化的SQL语句的重要工具。通过分析慢查询日志，可以找出性能瓶颈并进行有针对性的优化。", "parameters": [{"name": "slow_query_log", "description": "慢查询日志的开关，ON为启用，OFF为禁用"}, {"name": "long_query_time", "description": "慢查询的时间阈值，单位为秒，执行时间超过此值的查询会被记录"}], "returnValue": "无返回值，仅为配置说明", "examples": [{"code": "-- 查看慢查询日志配置\nSHOW VARIABLES LIKE '%slow_query%';\nSHOW VARIABLES LIKE 'long_query_time';\n\n-- 启用慢查询日志并设置阈值为0.5秒\nSET GLOBAL slow_query_log = 'ON';\nSET GLOBAL long_query_time = 0.5;\n\n-- 查看慢查询日志文件位置\nSHOW VARIABLES LIKE 'slow_query_log_file';\n\n-- 使用mysqldumpslow工具分析慢查询日志（在服务器命令行执行）\n-- mysqldumpslow -s t -t 10 /var/lib/mysql/slow-query.log", "explanation": "这个例子展示了如何查看和配置慢查询日志，包括启用日志、设置时间阈值和查看日志文件位置。最后一行展示了如何使用mysqldumpslow工具分析慢查询日志，按执行时间排序并显示前10条。"}]}}, {"name": "Index Optimization", "trans": ["索引优化"], "usage": {"syntax": "-- 检查索引使用情况\nEXPLAIN SELECT ... WHERE ...;\n\n-- 优化索引\nCREATE INDEX 索引名 ON 表名(列名);", "description": "索引优化是查询优化的核心，包括选择合适的列建立索引、避免索引失效、利用前缀索引等技术。正确的索引可以显著提高查询性能。", "parameters": [{"name": "索引名", "description": "要创建的索引名称"}, {"name": "表名", "description": "要添加索引的表"}, {"name": "列名", "description": "要索引的字段"}], "returnValue": "无返回值，仅为示例说明", "examples": [{"code": "-- 索引优化案例\n\n-- 1. 避免索引失效：不要在索引列上使用函数\n-- 不好的写法（会导致索引失效）\nSELECT * FROM students WHERE YEAR(enrollment_date) = 2023;\n-- 优化后的写法\nSELECT * FROM students WHERE enrollment_date BETWEEN '2023-01-01' AND '2023-12-31';\n\n-- 2. 前缀索引：对长文本字段使用前缀索引\nCREATE INDEX idx_content ON articles(content(200));\n\n-- 3. 复合索引的最左前缀原则\n-- 创建复合索引\nCREATE INDEX idx_name_age_class ON students(name, age, class_id);\n-- 能使用索引的查询\nSELECT * FROM students WHERE name = 'John';\nSELECT * FROM students WHERE name = 'John' AND age = 20;\n-- 不能完全使用索引的查询\nSELECT * FROM students WHERE age = 20; -- 不使用name列", "explanation": "这个例子展示了几种常见的索引优化技术：避免在索引列上使用函数、对长文本使用前缀索引、遵循复合索引的最左前缀原则等。"}]}}, {"name": "Query Optimization Assignment", "trans": ["查询优化练习"], "usage": {"syntax": "# 查询优化练习", "description": "完成以下练习，巩固MySQL查询优化的基本技术。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n假设有以下表结构和数据：\n\n```sql\n-- 创建表\nCREATE TABLE products (\n  id INT PRIMARY KEY,\n  name VARCHAR(100),\n  category VARCHAR(50),\n  price DECIMAL(10,2),\n  stock INT,\n  description TEXT,\n  created_at DATETIME\n);\n\nCREATE TABLE orders (\n  id INT PRIMARY KEY,\n  customer_id INT,\n  order_date DATETIME,\n  status VARCHAR(20)\n);\n\nCREATE TABLE order_items (\n  id INT PRIMARY KEY,\n  order_id INT,\n  product_id INT,\n  quantity INT,\n  price DECIMAL(10,2)\n);\n```\n\n请完成以下任务：\n\n1. 使用EXPLAIN分析以下查询，并解释结果：\n```sql\nSELECT p.name, SUM(oi.quantity) as total_sold\nFROM products p\nJOIN order_items oi ON p.id = oi.product_id\nWHERE p.category = 'Electronics'\nGROUP BY p.id;\n```\n\n2. 为上述查询设计合适的索引，提高查询效率。\n\n3. 重写以下查询，使其更高效：\n```sql\nSELECT * FROM products \nWHERE price > 100 OR category = 'Books';\n```\n\n4. 创建一个覆盖索引，优化以下查询：\n```sql\nSELECT name, price, category FROM products \nWHERE category = 'Electronics' AND price > 500;\n```\n\n5. 配置慢查询日志，记录执行时间超过0.5秒的查询。", "explanation": "通过这些练习，你将学习如何分析查询执行计划、创建合适的索引、重写查询以及使用覆盖索引和慢查询日志来优化MySQL查询。"}]}}]}