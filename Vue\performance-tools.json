{"name": "Performance Analysis Tools", "trans": ["性能分析工具"], "methods": [{"name": "Vue Devtools", "trans": ["Vue调试工具"], "usage": {"syntax": "// 安装浏览器插件或npm包\n// 浏览器打开Vue Devtools面板", "description": "Vue Devtools是官方提供的调试工具，可实时查看组件树、响应式数据、事件流、性能分析等，适合Vue项目开发与性能排查。", "parameters": [{"name": "组件树", "description": "可视化展示所有组件结构"}, {"name": "性能分析", "description": "分析组件渲染耗时"}], "returnValue": "可视化调试与性能分析界面", "examples": [{"code": "// 安装Vue Devtools插件后，F12打开Vue面板，查看组件状态和性能信息。", "explanation": "通过Vue Devtools直观分析组件和性能。"}]}}, {"name": "Chrome Performance", "trans": ["Chrome性能面板"], "usage": {"syntax": "// F12打开开发者工具 -> Performance面板 -> Record", "description": "Chrome开发者工具的Performance面板可录制页面运行过程，分析JS、渲染、网络等性能瓶颈，适合前端性能深度排查。", "parameters": [{"name": "录制", "description": "记录页面运行过程"}, {"name": "火焰图", "description": "可视化各阶段耗时"}], "returnValue": "详细的性能分析报告", "examples": [{"code": "// 打开Performance面板，点击Record，操作页面后停止，分析火焰图。", "explanation": "通过火焰图定位性能瓶颈。"}]}}, {"name": "Lighthouse", "trans": ["Lighthouse性能评测"], "usage": {"syntax": "// Chrome开发者工具 -> Lighthouse面板 -> 生成报告", "description": "Lighthouse是Chrome内置的自动化性能评测工具，可生成页面性能、可访问性、SEO等多维度报告，给出优化建议。", "parameters": [{"name": "性能评分", "description": "页面加载、交互等评分"}, {"name": "优化建议", "description": "自动生成的性能优化建议"}], "returnValue": "详细的性能与优化报告", "examples": [{"code": "// 打开Lighthouse面板，选择类型后生成报告，查看各项评分和建议。", "explanation": "快速获得页面性能全貌和优化方向。"}]}}, {"name": "Performance Bottleneck Diagnosis", "trans": ["性能瓶颈排查"], "usage": {"syntax": "// 结合多种工具定位慢点\n// 关注长任务、重渲染、内存泄漏等", "description": "性能瓶颈排查需结合Devtools、Performance、Lighthouse等工具，关注长任务、频繁重渲染、内存泄漏等问题，定位并优化。", "parameters": [{"name": "长任务分析", "description": "查找耗时操作"}, {"name": "内存泄漏检测", "description": "监控内存变化"}], "returnValue": "定位到的性能瓶颈点及优化建议", "examples": [{"code": "// 通过Performance面板发现某函数耗时过长，结合Vue Devtools定位具体组件。", "explanation": "多工具协作精准定位性能问题。"}]}}, {"name": "Performance Tools Assignment", "trans": ["性能分析工具练习"], "usage": {"syntax": "# 性能分析工具练习", "description": "完成以下练习，巩固性能分析工具相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用Vue Devtools分析组件树和性能。\n2. 用Performance面板录制并分析火焰图。\n3. 用Lighthouse生成性能报告。\n4. 结合多工具定位并优化性能瓶颈。", "explanation": "通过这些练习掌握前端性能分析与优化流程。"}]}}]}