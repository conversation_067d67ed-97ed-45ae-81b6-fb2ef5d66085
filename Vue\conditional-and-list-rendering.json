{"name": "Conditional and List Rendering", "trans": ["条件与列表渲染"], "methods": [{"name": "v-if / v-else / v-else-if", "trans": ["v-if/v-else/v-else-if"], "usage": {"syntax": "<div v-if=\"isShow\">显示</div>\n<div v-else-if=\"isLoading\">加载中</div>\n<div v-else>隐藏</div>", "description": "v-if/v-else-if/v-else用于根据条件动态渲染不同内容。v-if为真时渲染，v-else-if为多个条件分支，v-else为兜底。", "parameters": [{"name": "v-if/v-else-if/v-else", "description": "条件表达式"}], "returnValue": "根据条件渲染对应的DOM元素", "examples": [{"code": "<template>\n  <div v-if=\"isLogin\">欢迎</div>\n  <div v-else-if=\"isLoading\">加载中</div>\n  <div v-else>请登录</div>\n</template>\n<script>\nexport default {\n  data() { return { isLogin: false, isLoading: false } }\n}\n</script>", "explanation": "根据isLogin和isLoading渲染不同内容。"}]}}, {"name": "v-show", "trans": ["v-show"], "usage": {"syntax": "<div v-show=\"isVisible\">可见内容</div>", "description": "v-show根据条件切换元素的display样式，适合频繁切换显示/隐藏的场景。元素始终在DOM中，只是显示或隐藏。", "parameters": [{"name": "v-show", "description": "布尔条件表达式"}], "returnValue": "根据条件切换元素可见性", "examples": [{"code": "<template>\n  <div v-show=\"isVisible\">这段内容可见</div>\n</template>\n<script>\nexport default {\n  data() { return { isVisible: true } }\n}\n</script>", "explanation": "isVisible为true时内容可见，否则隐藏。"}]}}, {"name": "v-for", "trans": ["v-for"], "usage": {"syntax": "<li v-for=\"item in items\" :key=\"item.id\">{{ item.name }}</li>", "description": "v-for用于遍历数组或对象，动态渲染列表项。建议为每项提供唯一key以优化渲染性能。", "parameters": [{"name": "item in items", "description": "遍历的数组或对象"}, {"name": ":key", "description": "每项的唯一标识"}], "returnValue": "渲染出对应的列表DOM结构", "examples": [{"code": "<template>\n  <ul>\n    <li v-for=\"user in users\" :key=\"user.id\">{{ user.name }}</li>\n  </ul>\n</template>\n<script>\nexport default {\n  data() {\n    return { users: [{id:1, name:'A'},{id:2, name:'B'}] }\n  }\n}\n</script>", "explanation": "v-for遍历users数组，渲染每个用户的姓名。"}]}}, {"name": "Key Usage", "trans": ["key的作用"], "usage": {"syntax": "<li v-for=\"item in items\" :key=\"item.id\"></li>", "description": "key用于唯一标识每个列表项，帮助Vue高效复用和更新DOM，避免渲染错误和性能问题。", "parameters": [{"name": ":key", "description": "每项的唯一标识，推荐用id"}], "returnValue": "提升渲染性能和正确性", "examples": [{"code": "<li v-for=\"item in items\" :key=\"item.id\">{{ item.name }}</li>", "explanation": "为每个列表项指定唯一key，避免渲染异常。"}]}}, {"name": "List Item Component Extraction", "trans": ["列表项组件提取"], "usage": {"syntax": "// 提取为子组件\n<UserItem v-for=\"user in users\" :key=\"user.id\" :user=\"user\" />", "description": "将列表项提取为独立子组件，提升复用性和可维护性，便于管理复杂列表项结构。", "parameters": [{"name": "子组件", "description": "用于渲染单个列表项的组件"}], "returnValue": "每个列表项由独立组件渲染，结构清晰", "examples": [{"code": "// UserItem.vue\n<template>\n  <li>{{ user.name }}</li>\n</template>\n<script>\nexport default { props: ['user'] }\n</script>\n// 父组件\n<UserItem v-for=\"user in users\" :key=\"user.id\" :user=\"user\" />", "explanation": "将列表项封装为UserItem组件，提升复用性。"}]}}, {"name": "List Performance Optimization", "trans": ["列表性能优化"], "usage": {"syntax": "v-show/v-if配合v-for、虚拟列表、懒加载等", "description": "通过合理使用v-show/v-if、虚拟列表、懒加载等技术优化大数据量列表的渲染性能。", "parameters": [{"name": "虚拟列表/懒加载", "description": "提升性能的常用技术"}], "returnValue": "提升大列表渲染效率，减少内存消耗", "examples": [{"code": "// 虚拟列表示例（以第三方库vue-virtual-scroller为例）\n<virtual-list :size=\"30\" :remain=\"10\" :bench=\"5\" :item=\"item\" :item-key=\"'id'\" :data-key=\"'id'\" :data-sources=\"items\">\n  <template #default=\"{ item }\">\n    <div>{{ item.name }}</div>\n  </template>\n</virtual-list>", "explanation": "使用虚拟列表技术只渲染可视区域，大幅提升性能。"}]}}, {"name": "Conditional and List Rendering Assignment", "trans": ["条件与列表渲染练习"], "usage": {"syntax": "# 条件与列表渲染练习", "description": "完成以下练习，巩固条件与列表渲染相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用v-if/v-else切换内容显示。\n2. 用v-show控制元素可见性。\n3. 用v-for渲染用户列表并指定key。\n4. 将列表项提取为子组件。\n5. 尝试用虚拟列表优化大数据渲染。", "explanation": "通过这些练习掌握条件渲染、列表渲染、key用法和性能优化。"}]}}]}