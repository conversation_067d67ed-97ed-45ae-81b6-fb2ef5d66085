# Redis学习路线与核心内容

## 1. Redis基础     //已完成

### 介绍与应用场景
- Redis是什么
- Redis的特点
- 典型应用场景
- Redis与Memcached对比

### 安装与环境配置
- Windows/Linux/Mac安装
- 配置文件详解
- 启动与关闭
- 客户端工具选择

### 基本架构与原理
- Redis单线程模型
- 内存管理机制
- 网络通信原理
- 数据持久化原理

## 2. 核心数据结构与命令    //已完成

### String
- 基本操作命令
- 字符串常用应用
- 计数与限流

### List
- 列表操作命令
- 队列与消息队列实现

### Hash
- 哈希表操作命令
- 对象存储与缓存

### Set
- 集合操作命令
- 去重与标签系统

### Sorted Set
- 有序集合操作命令
- 排行榜与优先队列

### HyperLogLog、Bitmap、Geo
- 基数统计
- 位图操作
- 地理位置数据

## 3. 事务与Lua脚本     //已完成

### 事务机制
- MULTI/EXEC/DISCARD/WATCH
- 事务特性与注意事项

### Lua脚本
- EVAL命令
- 脚本原子性
- 常见脚本应用

## 4. 持久化机制        //已完成

### RDB持久化
- 快照原理
- 配置与触发时机
- RDB文件管理

### AOF持久化
- 日志追加原理
- AOF重写与恢复
- AOF与RDB对比

### 混合持久化
- 配置与原理
- 适用场景

## 5. 主从复制与高可用      //已完成

### 主从复制
- 复制原理
- 配置与管理
- 延迟与一致性

### Sentinel高可用
- Sentinel架构
- 自动故障转移
- 配置与监控

### Redis Cluster集群
- 分片原理
- 节点管理与扩容
- 集群数据一致性

## 6. 性能优化与运维        //已完成

### 性能优化
- 内存优化
- 慢查询分析
- 热点key与大key处理
- 网络与IO优化

### 运维管理
- 备份与恢复
- 日志与监控
- 常用运维命令
- 升级与迁移

## 7. 安全与访问控制        //已完成

### 认证与权限
- requirepass配置
- ACL访问控制
- 加密与安全建议

### 网络安全
- 端口与防火墙
- 只绑定本地/内网
- 防止未授权访问

## 8. 常见错误与调试        //已完成

### 常见错误
- 连接超时
- 内存溢出
- 持久化失败
- 主从同步异常

### 调试技巧
- 日志分析
- redis-cli调试
- 慢查询与监控

## 9. 项目实战与练习        //已完成

### 实战案例
- 分布式锁实现
- 缓存穿透/击穿/雪崩防护
- 消息队列与排行榜
- Session共享
- 限流与计数器

### 练习与项目
- 实现简单缓存系统
- 编写Lua脚本实现原子操作
- 配置主从复制与Sentinel
- 持久化与备份恢复演练
- 性能分析与优化实践
- 安全加固与访问控制 