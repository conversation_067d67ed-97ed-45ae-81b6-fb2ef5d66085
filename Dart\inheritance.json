{"name": "Inheritance", "trans": ["继承与多态"], "methods": [{"name": "Inheritance and Super", "trans": ["继承与super"], "usage": {"syntax": "// 父类定义\nclass 父类 {\n  // 属性和方法\n}\n\n// 子类继承父类\nclass 子类 extends 父类 {\n  // 子类特有的属性和方法\n  \n  // 调用父类方法\n  void 方法() {\n    super.父类方法();\n  }\n  \n  // 调用父类构造函数\n  子类(参数) : super(参数);\n}", "description": "Dart中的继承允许一个类（子类）获取另一个类（父类或超类）的属性和方法。通过继承，子类可以重用父类的代码，并且可以添加新的功能或修改现有功能。Dart使用extends关键字实现继承关系，一个类只能直接继承一个父类（单继承）。子类自动获得父类的所有非私有成员（属性和方法）。使用super关键字可以引用父类中的方法、构造函数和属性。子类可以重写（override）父类的方法，使用@override注解可以清晰地表明重写意图并让编译器进行验证。子类构造函数需要调用父类构造函数，如果不显式调用，Dart会自动调用父类的无参构造函数。继承适用于'是一种'（is-a）关系，如猫是一种动物。Dart中所有类都直接或间接继承自Object类，Object是Dart类层次结构的根。", "parameters": [], "returnValue": "无", "examples": [{"code": "void main() {\n  // 创建并使用Vehicle对象\n  var vehicle = Vehicle('通用交通工具', 2022);\n  vehicle.displayInfo();\n  vehicle.startEngine();\n  \n  // 创建并使用Car对象（Vehicle的子类）\n  var car = Car('丰田卡罗拉', 2022, 5);\n  car.displayInfo();\n  car.startEngine();\n  car.drive();\n  \n  // 创建并使用ElectricCar对象（Car的子类）\n  var electricCar = ElectricCar('特斯拉Model 3', 2023, 5, 75);\n  electricCar.displayInfo();\n  electricCar.startEngine();\n  electricCar.drive();\n  electricCar.charge();\n  \n  // 创建并使用Motorcycle对象（Vehicle的另一个子类）\n  var motorcycle = Motorcycle('哈雷戴维森', 2021, false);\n  motorcycle.displayInfo();\n  motorcycle.startEngine();\n  motorcycle.wheelie();\n  \n  // 演示构造函数继承\n  var book = Book('1984', '乔治·奥威尔');\n  book.displayInfo();\n  \n  var textBook = TextBook('数据结构', '高德纳', '计算机科学');\n  textBook.displayInfo();\n  textBook.study();\n  \n  // 演示super的使用\n  var bird = Bird();\n  bird.fly();\n  \n  var eagle = Eagle();\n  eagle.fly();\n}\n\n// 基类（父类）\nclass Vehicle {\n  String model;\n  int year;\n  \n  // 父类构造函数\n  Vehicle(this.model, this.year);\n  \n  // 父类方法\n  void displayInfo() {\n    print('车辆信息:');\n    print('- 型号: $model');\n    print('- 年份: $year');\n  }\n  \n  void startEngine() {\n    print('$model 启动引擎...');\n  }\n}\n\n// 子类继承Vehicle\nclass Car extends Vehicle {\n  int seats;\n  \n  // 子类构造函数调用父类构造函数\n  Car(String model, int year, this.seats) : super(model, year);\n  \n  // 重写父类方法\n  @override\n  void displayInfo() {\n    // 调用父类方法\n    super.displayInfo();\n    print('- 座位数: $seats');\n  }\n  \n  // 子类特有方法\n  void drive() {\n    print('$model 正在行驶...');\n  }\n}\n\n// 多层继承：ElectricCar继承自Car\nclass ElectricCar extends Car {\n  double batteryCapacity; // 电池容量，单位kWh\n  \n  ElectricCar(String model, int year, int seats, this.batteryCapacity) \n      : super(model, year, seats);\n  \n  @override\n  void displayInfo() {\n    super.displayInfo();\n    print('- 电池容量: ${batteryCapacity}kWh');\n  }\n  \n  @override\n  void startEngine() {\n    print('$model 启动电机...');\n  }\n  \n  void charge() {\n    print('$model 正在充电...');\n  }\n}\n\n// 另一个继承自Vehicle的子类\nclass Motorcycle extends Vehicle {\n  bool hasSidecar;\n  \n  Motorcycle(String model, int year, this.hasSidecar) : super(model, year);\n  \n  @override\n  void displayInfo() {\n    super.displayInfo();\n    print('- 边车: ${hasSidecar ? \"有\" : \"无\"}');\n  }\n  \n  void wheelie() {\n    if (!hasSidecar) {\n      print('$model 正在翘前轮!🏍️');\n    } else {\n      print('$model 有边车，不能翘前轮!');\n    }\n  }\n}\n\n// 演示构造函数继承\nclass Book {\n  String title;\n  String author;\n  \n  Book(this.title, this.author);\n  \n  void displayInfo() {\n    print('书籍: \"$title\" 作者: $author');\n  }\n}\n\nclass TextBook extends Book {\n  String subject;\n  \n  // 使用初始化列表调用父类构造函数\n  TextBook(String title, String author, this.subject) : super(title, author);\n  \n  @override\n  void displayInfo() {\n    super.displayInfo();\n    print('学科: $subject');\n  }\n  \n  void study() {\n    print('学习 \"$title\" 中的$subject知识');\n  }\n}\n\n// 演示super关键字的使用\nclass Bird {\n  void fly() {\n    print('鸟儿在飞翔');\n  }\n}\n\nclass Eagle extends Bird {\n  @override\n  void fly() {\n    // 调用父类的fly方法\n    super.fly();\n    print('老鹰飞得更高更快');\n  }\n}", "explanation": "这个示例展示了Dart中继承和super关键字的使用方式。首先定义了Vehicle作为基类，包含共享的属性和方法。Car类继承自Vehicle，通过extends关键字建立继承关系，并使用super调用父类构造函数和方法。ElectricCar进一步继承自Car，展示了多层继承。Motorcycle作为Vehicle的另一个子类，展示了如何从同一个父类派生不同的子类。Book和TextBook类演示了构造函数的继承和初始化列表的使用。Bird和Eagle类专门展示了super关键字的使用，在重写方法时调用父类版本。这些例子覆盖了Dart继承的核心概念：方法重写、构造函数继承和super关键字的使用。"}]}}, {"name": "Abstract Classes and Interfaces", "trans": ["抽象类与接口"], "usage": {"syntax": "// 抽象类\nabstract class 抽象类名 {\n  // 抽象方法（没有方法体）\n  void 抽象方法名();\n  \n  // 普通方法（有方法体）\n  void 普通方法名() {\n    // 方法实现\n  }\n}\n\n// 接口实现（Dart中没有interface关键字，类自动作为接口）\nclass 实现类 implements 接口类 {\n  // 必须实现接口类中的所有方法\n  @override\n  void 方法名() {\n    // 方法实现\n  }\n}\n\n// 多接口实现\nclass 实现类 implements 接口类1, 接口类2 {\n  // 必须实现所有接口类中的方法\n}", "description": "Dart中的抽象类和接口用于定义类的行为规范，而不关注具体实现细节。抽象类使用abstract关键字声明，可以包含抽象方法（没有实现体）和具体方法（有实现体）。抽象类不能被直接实例化，必须被其他类继承并实现其所有抽象方法。Dart没有专门的interface关键字，每个类都隐式定义了一个接口，包含该类的所有实例成员及其实现的接口。通过implements关键字实现接口，实现类必须重新实现接口中的所有方法和属性。一个类可以实现多个接口（逗号分隔），这是Dart实现多重继承的方式之一。抽象类和接口的区别在于：抽象类可以有构造函数和方法实现，实现类使用extends继承；而接口类的所有方法必须在实现类中重新实现，使用implements关键字。抽象类适用于有共享实现的场景，接口适用于只需共享方法签名的场景。", "parameters": [], "returnValue": "无", "examples": [{"code": "void main() {\n  // 抽象类不能被实例化\n  // var shape = Shape(); // 错误\n  \n  // 使用抽象类的子类\n  var circle = Circle(5);\n  var rectangle = Rectangle(4, 6);\n  \n  print('圆形面积: ${circle.calculateArea()}');\n  print('圆形周长: ${circle.calculatePerimeter()}');\n  circle.draw();\n  \n  print('矩形面积: ${rectangle.calculateArea()}');\n  print('矩形周长: ${rectangle.calculatePerimeter()}');\n  rectangle.draw();\n  \n  // 创建一个形状列表（多态性）\n  List<Shape> shapes = [circle, rectangle];\n  \n  // 遍历形状列表并调用方法\n  for (var shape in shapes) {\n    print('\\n形状信息:');\n    print('- 面积: ${shape.calculateArea()}');\n    print('- 周长: ${shape.calculatePerimeter()}');\n    shape.draw();\n  }\n  \n  // 接口实现示例\n  var student = Student('张三', 20, '12345');\n  var employee = Employee('李四', 30, 'E001');\n  \n  student.displayInfo();\n  employee.displayInfo();\n  \n  // 多接口实现示例\n  var car = Car('丰田卡罗拉');\n  car.start();\n  car.stop();\n  car.refuel();\n  car.displayInfo();\n  \n  // 抽象类与接口组合使用\n  var digitalProduct = DigitalProduct('Photoshop软件', 999, 'Adobe');\n  var physicalProduct = PhysicalProduct('办公椅', 599, 10);\n  \n  digitalProduct.displayInfo();\n  digitalProduct.deliver();\n  \n  physicalProduct.displayInfo();\n  physicalProduct.deliver();\n}\n\n// 抽象类定义\nabstract class Shape {\n  // 抽象方法（没有方法体）\n  double calculateArea();\n  double calculatePerimeter();\n  \n  // 具体方法（有方法体）\n  void draw() {\n    print('绘制 ${this.runtimeType}');\n  }\n}\n\n// 继承抽象类\nclass Circle extends Shape {\n  double radius;\n  \n  Circle(this.radius);\n  \n  // 实现抽象方法\n  @override\n  double calculateArea() {\n    return 3.14 * radius * radius;\n  }\n  \n  @override\n  double calculatePerimeter() {\n    return 2 * 3.14 * radius;\n  }\n  \n  // 可选：覆盖具体方法\n  @override\n  void draw() {\n    print('绘制圆形，半径: $radius');\n  }\n}\n\n// 另一个继承抽象类的类\nclass Rectangle extends Shape {\n  double width;\n  double height;\n  \n  Rectangle(this.width, this.height);\n  \n  @override\n  double calculateArea() {\n    return width * height;\n  }\n  \n  @override\n  double calculatePerimeter() {\n    return 2 * (width + height);\n  }\n  \n  @override\n  void draw() {\n    print('绘制矩形，宽: $width, 高: $height');\n  }\n}\n\n// 作为接口的类\nclass Person {\n  String name;\n  int age;\n  \n  Person(this.name, this.age);\n  \n  void displayInfo() {\n    print('姓名: $name, 年龄: $age');\n  }\n}\n\n// 实现接口（注意使用implements而不是extends）\nclass Student implements Person {\n  // 必须重新声明Person中的所有属性\n  @override\n  String name;\n  \n  @override\n  int age;\n  \n  // Student特有的属性\n  String studentId;\n  \n  Student(this.name, this.age, this.studentId);\n  \n  // 必须实现Person中的所有方法\n  @override\n  void displayInfo() {\n    print('学生信息 - 姓名: $name, 年龄: $age, 学号: $studentId');\n  }\n}\n\n// 另一个实现Person接口的类\nclass Employee implements Person {\n  @override\n  String name;\n  \n  @override\n  int age;\n  \n  String employeeId;\n  \n  Employee(this.name, this.age, this.employeeId);\n  \n  @override\n  void displayInfo() {\n    print('员工信息 - 姓名: $name, 年龄: $age, 工号: $employeeId');\n  }\n}\n\n// 定义多个接口\nclass Vehicle {\n  String model;\n  \n  Vehicle(this.model);\n  \n  void start() {\n    print('$model 启动');\n  }\n  \n  void stop() {\n    print('$model 停止');\n  }\n  \n  void displayInfo() {\n    print('车辆: $model');\n  }\n}\n\nclass FuelSystem {\n  void refuel() {\n    print('加油...');\n  }\n}\n\n// 实现多个接口\nclass Car implements Vehicle, FuelSystem {\n  @override\n  String model;\n  \n  Car(this.model);\n  \n  @override\n  void start() {\n    print('汽车 $model 启动引擎');\n  }\n  \n  @override\n  void stop() {\n    print('汽车 $model 熄火');\n  }\n  \n  @override\n  void refuel() {\n    print('汽车 $model 正在加油');\n  }\n  \n  @override\n  void displayInfo() {\n    print('汽车信息 - 型号: $model');\n  }\n}\n\n// 抽象类与接口组合使用\nabstract class Product {\n  String name;\n  double price;\n  \n  Product(this.name, this.price);\n  \n  // 抽象方法\n  void deliver();\n  \n  // 具体方法\n  void displayInfo() {\n    print('产品: $name, 价格: ￥$price');\n  }\n}\n\n// 用作接口的类\nclass DigitalItem {\n  String publisher;\n  \n  DigitalItem(this.publisher);\n  \n  void showPublisher() {\n    print('发行商: $publisher');\n  }\n}\n\n// 组合抽象类继承和接口实现\nclass DigitalProduct extends Product implements DigitalItem {\n  @override\n  String publisher;\n  \n  DigitalProduct(String name, double price, this.publisher) : super(name, price);\n  \n  @override\n  void deliver() {\n    print('数字产品 \"$name\" 通过电子邮件发送');\n  }\n  \n  @override\n  void showPublisher() {\n    print('数字产品发行商: $publisher');\n  }\n  \n  @override\n  void displayInfo() {\n    super.displayInfo();\n    showPublisher();\n  }\n}\n\nclass PhysicalProduct extends Product {\n  int weight; // 重量，单位为克\n  \n  PhysicalProduct(String name, double price, this.weight) : super(name, price);\n  \n  @override\n  void deliver() {\n    print('实物产品 \"$name\" 通过快递发送，重量: ${weight}克');\n  }\n  \n  @override\n  void displayInfo() {\n    super.displayInfo();\n    print('重量: ${weight}克');\n  }\n}", "explanation": "这个示例展示了Dart中抽象类和接口的使用。首先，定义了Shape抽象类，包含抽象方法和具体方法。Circle和Rectangle类继承自Shape并实现了抽象方法。接着演示了如何将普通类（Person）用作接口，Student和Employee类通过implements关键字实现了Person接口。Car类展示了如何同时实现多个接口（Vehicle和FuelSystem）。最后，DigitalProduct类展示了如何组合使用抽象类继承（extends Product）和接口实现（implements DigitalItem）。这些例子覆盖了Dart中抽象类和接口的核心概念：抽象方法定义、具体方法继承、接口实现和多接口实现。"}]}}, {"name": "Mixin and With Keyword", "trans": ["mixin与with关键字"], "usage": {"syntax": "// 定义mixin（不能有构造函数）\nmixin Mixin名称 {\n  // 属性和方法\n  void 方法名() {\n    // 方法实现\n  }\n}\n\n// 使用mixin\nclass 类名 with Mixin名称1, Mixin名称2 {\n  // 类的成员\n}\n\n// 限制mixin使用范围\nmixin Mixin名称 on 基类名 {\n  // 此mixin只能用于基类或其子类\n}", "description": "Dart中的mixin是一种在多个类层次结构中重用类代码的方法，可以让你在不使用继承的情况下向类添加功能。mixin使用关键字mixin定义，不能有构造函数，使用with关键字将mixin应用到类上。一个类可以使用多个mixin，通过逗号分隔。mixin是Dart实现代码重用的方式之一，特别适合用于向不同类层次结构中添加相同行为。与继承和接口不同，mixin更像是将其代码\"混入\"到类中，类会获得mixin中定义的所有属性和方法。mixin的应用顺序很重要，后面的mixin可以覆盖前面mixin中的方法。可以使用on关键字限制mixin只能用于特定类型或其子类，这样mixin中就可以使用on指定类型的方法。mixin解决了Dart单继承的限制，允许类从多个源继承行为，实现了多重继承的效果。mixin不能被实例化，它只是一种代码复用机制。", "parameters": [], "returnValue": "无", "examples": [{"code": "void main() {\n  // 使用基本mixin\n  var duck = Duck();\n  duck.swim();\n  duck.fly();\n  duck.walk();\n  \n  // 测试多个mixin的组合\n  var superHero = SuperHero('超人');\n  superHero.displayInfo();\n  superHero.fly();\n  superHero.fight();\n  superHero.useSpecialPower();\n  \n  // 测试mixin方法重写\n  var advancedPlayer = AdvancedMusicPlayer();\n  advancedPlayer.play(); // 调用的是AudioPlayer中的方法\n  advancedPlayer.pause(); // 调用的是VideoPlayer中的方法\n  \n  // 测试带on限制的mixin\n  var normalVehicle = NormalVehicle();\n  normalVehicle.startEngine();\n  \n  var electricVehicle = ElectricVehicle();\n  electricVehicle.startEngine(); // 调用的是ElectricEngineMixin中的方法\n  electricVehicle.displayBatteryInfo();\n  \n  // 测试mixin链\n  var smartphone = Smartphone();\n  smartphone.turnOn();\n  smartphone.connectToWifi();\n  smartphone.makeCall('************');\n  smartphone.takePhoto();\n  smartphone.turnOff();\n  \n  // 测试mixin状态管理\n  var todo1 = Todo('学习Dart', false);\n  var todo2 = Todo('学习Flutter', true);\n  \n  var todoList = TodoList();\n  todoList.addTodo(todo1);\n  todoList.addTodo(todo2);\n  \n  todoList.displayTodos();\n  print('待完成任务数: ${todoList.getIncompleteCount()}');\n  \n  todoList.toggleTodo(0); // 将第一个任务标记为完成\n  todoList.displayTodos();\n  print('待完成任务数: ${todoList.getIncompleteCount()}');\n}\n\n// 基本mixin定义\nmixin SwimAbility {\n  void swim() {\n    print('正在游泳');\n  }\n}\n\nmixin FlyAbility {\n  void fly() {\n    print('正在飞翔');\n  }\n}\n\nmixin WalkAbility {\n  void walk() {\n    print('正在行走');\n  }\n}\n\n// 在类中使用多个mixin\nclass Duck with SwimAbility, FlyAbility, WalkAbility {\n  // Duck获得了所有三个mixin的能力\n}\n\n// 组合类继承和mixin\nclass Person {\n  String name;\n  \n  Person(this.name);\n  \n  void displayInfo() {\n    print('人物: $name');\n  }\n}\n\n// 定义多个能力mixin\nmixin FlightAbility {\n  void fly() {\n    print('飞行能力启动');\n  }\n}\n\nmixin CombatAbility {\n  void fight() {\n    print('战斗能力启动');\n  }\n}\n\nmixin SuperPowerAbility {\n  void useSpecialPower() {\n    print('特殊能力启动');\n  }\n}\n\n// 组合继承和多个mixin\nclass SuperHero extends Person with FlightAbility, CombatAbility, SuperPowerAbility {\n  SuperHero(String name) : super(name);\n  \n  @override\n  void displayInfo() {\n    super.displayInfo();\n    print('这是一个超级英雄');\n  }\n}\n\n// 演示mixin方法重写\nmixin AudioPlayer {\n  void play() {\n    print('播放音频');\n  }\n  \n  void pause() {\n    print('暂停音频');\n  }\n}\n\nmixin VideoPlayer {\n  void play() {\n    print('播放视频');\n  }\n  \n  void pause() {\n    print('暂停视频并显示当前帧');\n  }\n}\n\n// 注意mixin的顺序，后面的会覆盖前面的同名方法\nclass AdvancedMusicPlayer with AudioPlayer, VideoPlayer {\n  // play方法使用的是VideoPlayer中的版本（后声明的mixin优先）\n  // 但我们可以选择覆盖它\n  @override\n  void play() {\n    print('高级播放器播放音频和视频');\n  }\n}\n\n// 使用on关键字限制mixin使用范围\nclass Vehicle {\n  void startEngine() {\n    print('启动常规引擎');\n  }\n}\n\n// 这个mixin只能用于Vehicle或其子类\nmixin ElectricEngineMixin on Vehicle {\n  int batteryLevel = 100;\n  \n  @override\n  void startEngine() {\n    print('启动电动引擎，电池电量: $batteryLevel%');\n  }\n  \n  void displayBatteryInfo() {\n    print('当前电池电量: $batteryLevel%');\n  }\n}\n\n// 普通车辆\nclass NormalVehicle extends Vehicle {\n  // 使用默认的startEngine\n}\n\n// 电动车辆，必须继承Vehicle才能使用ElectricEngineMixin\nclass ElectricVehicle extends Vehicle with ElectricEngineMixin {\n  // 获得了ElectricEngineMixin中的所有功能\n}\n\n// mixin链示例\nmixin PowerState {\n  bool _isPoweredOn = false;\n  \n  void turnOn() {\n    _isPoweredOn = true;\n    print('设备已开机');\n  }\n  \n  void turnOff() {\n    _isPoweredOn = false;\n    print('设备已关机');\n  }\n  \n  bool get isPoweredOn => _isPoweredOn;\n}\n\nmixin NetworkConnection {\n  bool _isConnected = false;\n  \n  void connectToWifi() {\n    _isConnected = true;\n    print('已连接到WiFi');\n  }\n  \n  void disconnect() {\n    _isConnected = false;\n    print('已断开网络连接');\n  }\n}\n\nmixin PhoneFeatures {\n  void makeCall(String number) {\n    print('正在拨打电话: $number');\n  }\n  \n  void receiveCall(String caller) {\n    print('接收到来自 $caller 的电话');\n  }\n}\n\nmixin CameraFeatures {\n  void takePhoto() {\n    print('拍照');\n  }\n  \n  void recordVideo() {\n    print('录制视频');\n  }\n}\n\n// 组合多个mixin创建复杂功能\nclass Smartphone with PowerState, NetworkConnection, PhoneFeatures, CameraFeatures {\n  String model = 'Generic Smartphone';\n}\n\n// mixin用于状态管理\nclass Todo {\n  String title;\n  bool completed;\n  \n  Todo(this.title, this.completed);\n}\n\nmixin TodoManager {\n  List<Todo> _todos = [];\n  \n  void addTodo(Todo todo) {\n    _todos.add(todo);\n  }\n  \n  void removeTodo(int index) {\n    if (index >= 0 && index < _todos.length) {\n      _todos.removeAt(index);\n    }\n  }\n  \n  void toggleTodo(int index) {\n    if (index >= 0 && index < _todos.length) {\n      _todos[index].completed = !_todos[index].completed;\n    }\n  }\n  \n  void displayTodos() {\n    print('\\n任务列表:');\n    for (var i = 0; i < _todos.length; i++) {\n      var status = _todos[i].completed ? '✓' : '○';\n      print('$status ${_todos[i].title}');\n    }\n  }\n  \n  int getIncompleteCount() {\n    return _todos.where((todo) => !todo.completed).length;\n  }\n}\n\n// 使用mixin添加任务管理功能\nclass TodoList with TodoManager {\n  // 自动获得TodoManager中的所有功能\n}", "explanation": "这个示例展示了Dart中mixin的多种用法。首先，创建了基本的行为mixin（SwimAbility、FlyAbility和WalkAbility），并在Duck类中使用with关键字组合它们。然后，展示了如何将mixin与类继承结合使用，SuperHero类继承自Person并混入了多种能力。AdvancedMusicPlayer类演示了mixin方法覆盖，当多个mixin具有相同方法时，后声明的mixin方法优先。ElectricEngineMixin使用on关键字限制只能应用于Vehicle及其子类。Smartphone类展示了如何组合多个mixin创建复杂功能，混入了电源管理、网络连接、电话功能和相机功能。最后，TodoManager mixin展示了如何使用mixin实现状态管理，TodoList类通过混入获得了完整的任务管理功能。这些示例涵盖了Dart中mixin的主要特性和应用场景。"}]}}, {"name": "Override and Polymorphism", "trans": ["重写与多态"], "usage": {"syntax": "// 父类定义方法\nclass 父类 {\n  void 方法名() {\n    // 父类实现\n  }\n}\n\n// 子类重写方法\nclass 子类 extends 父类 {\n  @override\n  void 方法名() {\n    // 子类实现\n  }\n}\n\n// 使用多态\n父类 对象 = 子类的实例;\n对象.方法名(); // 调用的是子类的实现", "description": "在Dart中，方法重写（Override）和多态（Polymorphism）是面向对象编程的核心概念。方法重写允许子类提供父类方法的特定实现，使用@override注解标记重写方法，这不是必须的，但推荐使用，因为它可以帮助编译器检查是否正确重写了父类方法。子类方法必须与父类方法具有相同的名称、参数列表和返回类型。多态允许将子类的实例赋值给父类类型的变量，然后调用方法时，实际执行的是子类中的实现。这种\"一个接口，多种实现\"的能力是面向对象编程的强大特性。多态使代码更加灵活、可扩展，可以编写处理父类对象的代码，而这些代码可以与所有子类对象一起工作。多态通常与继承和方法重写一起使用，创建通用代码，同时允许特定的行为。Dart中的多态在集合（如List<父类>）和参数（接受父类类型）中特别有用，可以存储或处理任何子类对象。", "parameters": [], "returnValue": "无", "examples": [{"code": "void main() {\n  // 创建不同的动物\n  var cat = Cat();\n  var dog = Dog();\n  var cow = Cow();\n  \n  // 直接调用方法\n  print('直接调用:');\n  cat.makeSound();\n  dog.makeSound();\n  cow.makeSound();\n  \n  // 通过多态调用方法\n  print('\\n通过多态调用:');\n  List<Animal> animals = [cat, dog, cow];\n  \n  for (var animal in animals) {\n    animal.makeSound(); // 根据实际类型调用不同的实现\n  }\n  \n  // 多态与方法参数\n  print('\\n通过函数参数的多态:');\n  makeAnimalSound(cat);\n  makeAnimalSound(dog);\n  makeAnimalSound(cow);\n  \n  // 更复杂的多态示例\n  print('\\n形状多态示例:');\n  var circle = Circle(5);\n  var rectangle = Rectangle(4, 6);\n  var triangle = Triangle(3, 4, 5);\n  \n  printShapeInfo(circle);\n  printShapeInfo(rectangle);\n  printShapeInfo(triangle);\n  \n  // 多态与继承链\n  print('\\n多层继承的多态:');\n  var employee = Employee('张三', '财务部');\n  var manager = Manager('李四', '人事部', 5);\n  var executive = Executive('王五', '执行部门', 10, 'CEO');\n  \n  List<Person> people = [employee, manager, executive];\n  for (var person in people) {\n    person.displayInfo();\n  }\n  \n  // 协变返回类型示例\n  print('\\n协变返回类型:');\n  var basicFactory = BasicWidgetFactory();\n  var advancedFactory = AdvancedWidgetFactory();\n  \n  var basicButton = basicFactory.createButton();\n  var advancedButton = advancedFactory.createButton();\n  \n  basicButton.render();\n  advancedButton.render();\n  \n  // 运行时类型检查\n  print('\\n运行时类型检查:');\n  checkAnimalType(cat);\n  checkAnimalType(dog);\n  checkAnimalType(cow);\n}\n\n// 基类\nclass Animal {\n  void makeSound() {\n    print('动物发出声音');\n  }\n}\n\n// 子类\nclass Cat extends Animal {\n  @override\n  void makeSound() {\n    print('猫: 喵喵');\n  }\n}\n\nclass Dog extends Animal {\n  @override\n  void makeSound() {\n    print('狗: 汪汪');\n  }\n}\n\nclass Cow extends Animal {\n  @override\n  void makeSound() {\n    print('牛: 哞哞');\n  }\n}\n\n// 使用多态的函数\nvoid makeAnimalSound(Animal animal) {\n  print('动物将要发声:');\n  animal.makeSound();\n}\n\n// 几何形状的多态示例\nabstract class Shape {\n  double calculateArea();\n  double calculatePerimeter();\n  String getShapeType();\n}\n\nclass Circle extends Shape {\n  double radius;\n  \n  Circle(this.radius);\n  \n  @override\n  double calculateArea() {\n    return 3.14 * radius * radius;\n  }\n  \n  @override\n  double calculatePerimeter() {\n    return 2 * 3.14 * radius;\n  }\n  \n  @override\n  String getShapeType() {\n    return '圆形';\n  }\n}\n\nclass Rectangle extends Shape {\n  double width;\n  double height;\n  \n  Rectangle(this.width, this.height);\n  \n  @override\n  double calculateArea() {\n    return width * height;\n  }\n  \n  @override\n  double calculatePerimeter() {\n    return 2 * (width + height);\n  }\n  \n  @override\n  String getShapeType() {\n    return '矩形';\n  }\n}\n\nclass Triangle extends Shape {\n  double a, b, c; // 三边长度\n  \n  Triangle(this.a, this.b, this.c);\n  \n  @override\n  double calculateArea() {\n    // 使用海伦公式计算面积\n    double s = (a + b + c) / 2;\n    return sqrt(s * (s - a) * (s - b) * (s - c));\n  }\n  \n  @override\n  double calculatePerimeter() {\n    return a + b + c;\n  }\n  \n  @override\n  String getShapeType() {\n    return '三角形';\n  }\n}\n\n// 用于打印形状信息的函数（展示多态）\nvoid printShapeInfo(Shape shape) {\n  print('${shape.getShapeType()}:');\n  print('- 面积: ${shape.calculateArea().toStringAsFixed(2)}');\n  print('- 周长: ${shape.calculatePerimeter().toStringAsFixed(2)}');\n}\n\n// 多层继承的多态示例\nclass Person {\n  String name;\n  \n  Person(this.name);\n  \n  void displayInfo() {\n    print('姓名: $name');\n  }\n}\n\nclass Employee extends Person {\n  String department;\n  \n  Employee(String name, this.department) : super(name);\n  \n  @override\n  void displayInfo() {\n    super.displayInfo();\n    print('部门: $department');\n  }\n}\n\nclass Manager extends Employee {\n  int teamSize;\n  \n  Manager(String name, String department, this.teamSize) \n      : super(name, department);\n  \n  @override\n  void displayInfo() {\n    super.displayInfo();\n    print('团队大小: $teamSize人');\n  }\n}\n\nclass Executive extends Manager {\n  String title;\n  \n  Executive(String name, String department, int teamSize, this.title) \n      : super(name, department, teamSize);\n  \n  @override\n  void displayInfo() {\n    super.displayInfo();\n    print('职位: $title');\n  }\n}\n\n// 协变返回类型示例\nclass Widget {\n  void render() {\n    print('渲染基本组件');\n  }\n}\n\nclass Button extends Widget {\n  @override\n  void render() {\n    print('渲染基本按钮');\n  }\n}\n\nclass FancyButton extends Button {\n  @override\n  void render() {\n    print('渲染高级按钮，带有动画和阴影');\n  }\n}\n\n// 工厂类\nclass WidgetFactory {\n  Widget createButton() {\n    return Button();\n  }\n}\n\n// 子类工厂，返回更具体的类型（协变返回类型）\nclass AdvancedWidgetFactory extends WidgetFactory {\n  @override\n  FancyButton createButton() { // 返回类型是Button的子类\n    return FancyButton();\n  }\n}\n\n// 不修改父类的工厂\nclass BasicWidgetFactory extends WidgetFactory {\n  // 使用父类的实现\n}\n\n// 运行时类型检查\nvoid checkAnimalType(Animal animal) {\n  if (animal is Cat) {\n    print('这是一只猫');\n    // 使用Cat特有的属性或方法\n    animal.makeSound(); // 编译器知道这是Cat类型\n  } else if (animal is Dog) {\n    print('这是一只狗');\n    // 使用Dog特有的属性或方法\n  } else if (animal is Cow) {\n    print('这是一头牛');\n    // 使用Cow特有的属性或方法\n  } else {\n    print('这是一种未知的动物');\n  }\n}\n\n// 辅助函数\ndouble sqrt(double value) {\n  return math.sqrt(value);\n}\n\n// 导入数学库（假设代码中使用）\nimport 'dart:math' as math;", "explanation": "这个示例全面展示了Dart中的方法重写和多态。首先通过Animal基类和Cat、Dog、Cow子类展示了基本的方法重写，每个子类都以不同方式实现了makeSound方法。然后通过将不同动物实例存储在Animal类型的列表中，演示了多态的基本用法。makeAnimalSound函数展示了多态如何使函数参数更加灵活。Shape抽象类及其子类（Circle、Rectangle、Triangle）展示了如何使用多态处理不同的几何形状。Person、Employee、Manager和Executive类展示了多层继承链中的多态行为。WidgetFactory类及其子类展示了协变返回类型，子类方法可以返回比父类方法更具体的类型。最后，checkAnimalType函数展示了运行时类型检查（is关键字）的使用，可以根据对象的实际类型执行不同的操作。这些示例覆盖了Dart中多态的各种应用场景。"}]}}]}