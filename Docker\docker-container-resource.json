{"name": "Container Resource Limiting", "trans": ["容器资源限制"], "methods": [{"name": "CPU & Memory Limit", "trans": ["CPU/内存限制"], "usage": {"syntax": "docker run --cpus=<数量> --memory=<大小> <镜像名>", "description": "通过--cpus和--memory参数限制容器可用的CPU核心数和内存大小，防止资源争用。", "parameters": [{"name": "--cpus", "description": "限制容器可用的CPU核心数，如1.5。"}, {"name": "--memory", "description": "限制容器可用的内存大小，如512m、2g。"}, {"name": "<镜像名>", "description": "要运行的镜像名称。"}], "returnValue": "无返回值，容器启动时资源受限。", "examples": [{"code": "# 限制CPU为1核，内存为256MB\ndocker run --cpus=1 --memory=256m nginx", "explanation": "启动nginx容器，最多使用1核CPU和256MB内存。"}]}}, {"name": "Restart Policy", "trans": ["容器重启策略"], "usage": {"syntax": "docker run --restart=<策略> <镜像名>", "description": "通过--restart参数设置容器的自动重启策略，提升服务可用性。常用策略有no、always、on-failure、unless-stopped。", "parameters": [{"name": "--restart", "description": "重启策略，如always、on-failure等。"}, {"name": "<镜像名>", "description": "要运行的镜像名称。"}], "returnValue": "无返回值，容器根据策略自动重启。", "examples": [{"code": "# 设置容器异常退出时自动重启\ndocker run --restart=on-failure nginx", "explanation": "nginx容器异常退出时会自动重启。"}, {"code": "# 设置始终自动重启\ndocker run --restart=always nginx", "explanation": "nginx容器无论如何退出都会自动重启。"}]}}, {"name": "Health Check", "trans": ["健康检查"], "usage": {"syntax": "docker run --health-cmd='<命令>' --health-interval=<间隔> <镜像名>", "description": "通过--health-cmd等参数为容器设置健康检查命令和检测间隔，及时发现服务异常。", "parameters": [{"name": "--health-cmd", "description": "健康检查执行的命令。"}, {"name": "--health-interval", "description": "健康检查的时间间隔，如30s。"}, {"name": "<镜像名>", "description": "要运行的镜像名称。"}], "returnValue": "无返回值，容器运行时自动健康检查。", "examples": [{"code": "# 每30秒检查nginx服务端口\ndocker run --health-cmd='curl -f http://localhost/' --health-interval=30s nginx", "explanation": "定期检查nginx服务是否正常。"}]}}, {"name": "Container Naming & Management", "trans": ["容器命名与管理"], "usage": {"syntax": "docker run --name <容器名> <镜像名>\ndocker rename <旧名> <新名>", "description": "通过--name参数为容器命名，使用docker rename修改容器名称，便于管理和脚本自动化。", "parameters": [{"name": "--name", "description": "容器启动时指定名称。"}, {"name": "docker rename", "description": "修改已存在容器的名称。"}, {"name": "<容器名>", "description": "容器名称。"}, {"name": "<镜像名>", "description": "要运行的镜像名称。"}], "returnValue": "无返回值，容器可自定义命名和重命名。", "examples": [{"code": "# 启动时命名\ndocker run --name myweb nginx\n# 重命名容器\ndocker rename myweb web-prod", "explanation": "先以myweb命名启动容器，再重命名为web-prod。"}]}}, {"name": "Assignment: 容器资源限制实践", "trans": ["作业：容器资源限制实践"], "usage": {"syntax": "请完成以下任务：\n1. 启动一个nginx容器，限制CPU为0.5核、内存128MB\n2. 设置容器异常退出时自动重启\n3. 为nginx容器添加健康检查，每30秒检测一次首页\n4. 启动时自定义容器名称并重命名", "description": "通过实际操作掌握容器的资源限制、重启策略、健康检查和命名管理。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 1. 启动并限制资源\ndocker run --cpus=0.5 --memory=128m --name testnginx nginx\n# 2. 设置自动重启\ndocker run --restart=on-failure nginx\n# 3. 添加健康检查\ndocker run --health-cmd='curl -f http://localhost/' --health-interval=30s nginx\n# 4. 重命名容器\ndocker rename testnginx nginx-prod", "explanation": "依次完成容器资源限制与管理的核心实践。"}]}}]}