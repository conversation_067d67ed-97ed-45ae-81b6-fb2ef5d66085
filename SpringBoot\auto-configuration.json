{"name": "Auto Configuration Principle", "trans": ["自动配置原理"], "methods": [{"name": "@SpringBootApplication Annotation", "trans": ["@SpringBootApplication注解"], "usage": {"syntax": "@SpringBootApplication // 标注在主类上", "description": "@SpringBootApplication是Spring Boot应用的入口注解，包含@Configuration、@EnableAutoConfiguration、@ComponentScan三大核心功能，简化配置。", "parameters": [{"name": "@SpringBootApplication", "description": "复合注解，标注在主启动类上"}], "returnValue": "自动配置和组件扫描的Spring Boot主类", "examples": [{"code": "@SpringBootApplication\npublic class DemoApplication {\n    public static void main(String[] args) {\n        SpringApplication.run(DemoApplication.class, args);\n    }\n}", "explanation": "主类加上@SpringBootApplication即可自动启用配置和组件扫描。"}]}}, {"name": "@EnableAutoConfiguration Mechanism", "trans": ["自动装配机制（@EnableAutoConfiguration）"], "usage": {"syntax": "@EnableAutoConfiguration // 启用自动装配", "description": "@EnableAutoConfiguration注解会根据classpath下的依赖自动配置Spring Bean，极大简化开发，无需手动配置常用组件。", "parameters": [{"name": "@EnableAutoConfiguration", "description": "自动装配注解，通常由@SpringBootApplication间接引入"}], "returnValue": "根据依赖自动装配的Bean实例", "examples": [{"code": "@EnableAutoConfiguration\npublic class DemoApp {\n    public static void main(String[] args) {\n        SpringApplication.run(DemoApp.class, args);\n    }\n}\n// 实际开发中无需单独加，@SpringBootApplication已包含该注解。", "explanation": "自动装配机制让开发者专注业务，无需繁琐配置。"}]}}, {"name": "Conditional Configuration (@Conditional)", "trans": ["条件装配（@Conditional）"], "usage": {"syntax": "@Conditional(条件类) // 满足条件才装配Bean", "description": "@Conditional注解可根据环境、类存在与否等条件决定是否装配Bean，是Spring Boot自动装配的核心机制。", "parameters": [{"name": "@Conditional", "description": "条件装配注解"}, {"name": "Condition实现类", "description": "自定义条件判断逻辑"}], "returnValue": "满足条件时才被注册的Bean", "examples": [{"code": "@Conditional(OnClassCondition.class)\n@Bean\npublic DataSource dataSource() {\n    return new HikariDataSource();\n}\n// 只有classpath下有指定类时才装配该Bean。", "explanation": "条件装配可实现按需加载，提升灵活性和性能。"}]}}, {"name": "Custom Starter", "trans": ["自定义Starter"], "usage": {"syntax": "创建spring-boot-starter-xxx模块，提供自动配置和依赖", "description": "自定义Starter可将通用功能封装为独立模块，自动集成到Spring Boot项目，便于复用和维护。", "parameters": [{"name": "Starter模块", "description": "以spring-boot-starter-开头的Maven模块"}, {"name": "自动配置类", "description": "@Configuration+@Conditional实现自动装配"}], "returnValue": "可复用的自动装配模块", "examples": [{"code": "// 1. 新建spring-boot-starter-demo模块\n// 2. 编写自动配置类\n@Configuration\n@ConditionalOnClass(name = \"com.example.DemoService\")\npublic class DemoAutoConfiguration {\n    @Bean\n    public DemoService demoService() {\n        return new DemoService();\n    }\n}\n// 3. 在resources/META-INF/spring.factories中注册自动配置类", "explanation": "自定义Starter可实现企业级功能模块化和自动集成。"}]}}, {"name": "Disable or Exclude Auto Configuration", "trans": ["禁用/排除自动配置"], "usage": {"syntax": "@SpringBootApplication(exclude = XxxAutoConfiguration.class)", "description": "可通过exclude属性或@EnableAutoConfiguration(exclude=...)禁用不需要的自动配置，避免冲突或提升性能。", "parameters": [{"name": "exclude", "description": "指定要排除的自动配置类"}], "returnValue": "去除指定自动配置后的Spring Boot应用", "examples": [{"code": "@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)\npublic class DemoApplication {\n    public static void main(String[] args) {\n        SpringApplication.run(DemoApplication.class, args);\n    }\n}\n// 也可用@EnableAutoConfiguration(exclude=...)实现同样效果。", "explanation": "排除不需要的自动配置，适合自定义数据源等场景。"}]}}, {"name": "Auto Configuration Assignment", "trans": ["自动配置原理练习"], "usage": {"syntax": "# 自动配置原理练习", "description": "完成以下练习，巩固Spring Boot自动配置原理相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 解释@SpringBootApplication注解的作用。\n2. 查看@EnableAutoConfiguration自动装配了哪些Bean。\n3. 编写一个@Conditional条件装配的Bean。\n4. 自定义一个Starter模块。\n5. 排除某个自动配置类并验证效果。", "explanation": "通过这些练习掌握Spring Boot自动配置的核心机制。"}]}}]}