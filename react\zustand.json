{"name": "Zustand", "trans": ["Zustand"], "methods": [{"name": "Creating Store", "trans": ["创建Store"], "usage": {"syntax": "import create from 'zustand';\nconst useStore = create(set => ({ ... }));", "description": "Zustand通过create函数创建store，store是一个自定义hook，直接在组件中调用。", "parameters": [{"name": "set", "description": "用于更新状态的函数"}], "returnValue": "自定义的store hook", "examples": [{"code": "import create from 'zustand';\n// 创建计数器store\nconst useCounterStore = create(set => ({\n  count: 0,\n  increment: () => set(state => ({ count: state.count + 1 })),\n  decrement: () => set(state => ({ count: state.count - 1 }))\n}));", "explanation": "通过create创建store，包含状态和操作方法。"}]}}, {"name": "State Reading and Updating", "trans": ["状态读取和更新"], "usage": {"syntax": "const value = useStore(state => state.value);\nuseStore.getState();\nuseStore.setState();", "description": "通过store hook读取和更新状态，支持选择性订阅和直接操作。", "parameters": [{"name": "selector", "description": "选择需要订阅的状态"}], "returnValue": "当前状态值或操作方法", "examples": [{"code": "// 在组件中使用store\nfunction Counter() {\n  // 只订阅count，组件只在count变化时更新\n  const count = useCounterStore(state => state.count);\n  const increment = useCounterStore(state => state.increment);\n  return (\n    <div>\n      <span>计数: {count}</span>\n      <button onClick={increment}>加一</button>\n    </div>\n  );\n}", "explanation": "通过selector选择性订阅状态，提升性能。"}]}}, {"name": "Async Operations", "trans": ["异步操作"], "usage": {"syntax": "set(() => ({ loading: true }));\nfetch(...).then(data => set({ data, loading: false }));", "description": "Zustand支持在store中直接编写异步操作，常用于数据请求和异步状态管理。", "parameters": [], "returnValue": "异步操作后的状态更新", "examples": [{"code": "const useUserStore = create(set => ({\n  user: null,\n  loading: false,\n  fetchUser: async (id) => {\n    set({ loading: true });\n    const res = await fetch(`/api/user/${id}`);\n    const user = await res.json();\n    set({ user, loading: false });\n  }\n}));", "explanation": "在store中直接定义异步方法，管理异步状态。"}]}}, {"name": "State Persistence", "trans": ["状态持久化"], "usage": {"syntax": "import { persist } from 'zustand/middleware';\nconst useStore = create(persist((set) => ({ ... }), options));", "description": "通过persist中间件可将状态持久化到localStorage等，页面刷新后自动恢复。", "parameters": [{"name": "options", "description": "持久化配置，如key、存储方式等"}], "returnValue": "持久化的store hook", "examples": [{"code": "import create from 'zustand';\nimport { persist } from 'zustand/middleware';\n// 创建持久化store\nconst usePersistStore = create(persist((set) => ({\n  count: 0,\n  increment: () => set(state => ({ count: state.count + 1 }))\n}), {\n  name: 'counter-storage' // 存储key\n}));", "explanation": "通过persist实现状态持久化。"}]}}, {"name": "Middleware", "trans": ["中间件"], "usage": {"syntax": "import { devtools } from 'zustand/middleware';\nconst useStore = create(devtools((set) => ({ ... }), options));", "description": "Zustand支持多种中间件，如devtools、persist、logger等，增强store功能。", "parameters": [{"name": "options", "description": "中间件配置"}], "returnValue": "增强功能的store hook", "examples": [{"code": "import create from 'zustand';\nimport { devtools } from 'zustand/middleware';\n// 创建带devtools的store\nconst useDevtoolsStore = create(devtools((set) => ({\n  count: 0,\n  inc: () => set(state => ({ count: state.count + 1 }))\n}), { name: 'CounterStore' }));", "explanation": "通过devtools中间件集成Redux DevTools。"}]}}, {"name": "Comparison with <PERSON><PERSON>", "trans": ["与Redux对比"], "usage": {"syntax": "// Zustand无需reducer和action，API更简洁", "description": "Zustand无需reducer和action，API简洁，易于组合和扩展，适合中小型项目。Redux适合大型、复杂状态管理。", "parameters": [], "returnValue": "Zustand与Redux的主要区别", "examples": [{"code": "// Redux需要reducer、action、dispatch\n// Zustand只需定义状态和方法，直接调用\n// Redux适合复杂场景，Zustand更轻量灵活", "explanation": "对比两者API和适用场景。"}]}}, {"name": "Practical Example", "trans": ["实际应用示例"], "usage": {"syntax": "// 见下方完整Todo示例", "description": "Zustand可用于实现Todo、购物车等常见全局状态管理。", "parameters": [], "returnValue": "完整的Zustand应用示例", "examples": [{"code": "import create from 'zustand';\n// 创建Todo store\nconst useTodoStore = create(set => ({\n  todos: [],\n  addTodo: (text) => set(state => ({ todos: [...state.todos, { text, done: false }] })),\n  toggleTodo: (index) => set(state => ({ todos: state.todos.map((t, i) => i === index ? { ...t, done: !t.done } : t) })),\n  removeTodo: (index) => set(state => ({ todos: state.todos.filter((_, i) => i !== index) }))\n}));\n// 组件中使用\nfunction TodoList() {\n  const { todos, addTodo, toggleTodo, removeTodo } = useTodoStore();\n  const [text, setText] = React.useState('');\n  return (\n    <div>\n      <input value={text} onChange={e => setText(e.target.value)} placeholder=\"添加待办\" />\n      <button onClick={() => { addTodo(text); setText(''); }}>添加</button>\n      <ul>\n        {todos.map((todo, i) => (\n          <li key={i} style={{ textDecoration: todo.done ? 'line-through' : 'none' }}>\n            {todo.text}\n            <button onClick={() => toggleTodo(i)}>切换</button>\n            <button onClick={() => removeTodo(i)}>删除</button>\n          </li>\n        ))}\n      </ul>\n    </div>\n  );\n}", "explanation": "完整实现Zustand全局Todo管理。"}]}}, {"name": "作业：实现一个Zustand计数器", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 用Zustand实现计数器。\n// 2. 支持加一、减一。\n// 3. 代码结构清晰，注释完整。", "description": "请实现上述Zustand计数器，提交时请附上完整代码和注释。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 示例代码略，请学生自行实现\n// 提示：用create、store hook、状态和方法实现。", "explanation": "作业要求学生掌握Zustand全流程。"}, {"code": "import create from 'zustand';\n// 1. 创建计数器store\nconst useCounterStore = create(set => ({\n  count: 0,\n  increment: () => set(state => ({ count: state.count + 1 })),\n  decrement: () => set(state => ({ count: state.count - 1 }))\n}));\n// 2. 组件中使用\nfunction Counter() {\n  const { count, increment, decrement } = useCounterStore();\n  return (\n    <div>\n      <span>计数: {count}</span>\n      <button onClick={increment}>加一</button>\n      <button onClick={decrement}>减一</button>\n    </div>\n  );\n}", "explanation": "完整实现Zustand计数器，支持加一减一。"}]}}]}