{"name": "Sorted Set", "trans": ["有序集合类型"], "methods": [{"name": "Sorted Set Commands", "trans": ["有序集合操作命令"], "usage": {"syntax": "ZADD key score member [score member ...]\nZREM key member [member ...]\nZRANGE key start stop [WITHSCORES]\nZREVRANGE key start stop [WITHSCORES]\nZSCORE key member", "description": "ZADD添加成员及分数，ZREM移除成员，ZRANGE/ZREVRANGE按分数正/逆序获取成员，ZSCORE获取成员分数。适合排行榜、优先队列等场景。", "parameters": [{"name": "key", "description": "有序集合键名"}, {"name": "score", "description": "分数（排序依据）"}, {"name": "member", "description": "成员"}, {"name": "start/stop", "description": "区间下标"}], "returnValue": "添加/移除数量、成员列表、分数值", "examples": [{"code": "$ redis-cli zadd rank 100 Tom 80 <PERSON> 90 Bob\n$ redis-cli zrange rank 0 -1 withscores\n$ redis-cli zrevrange rank 0 1 withscores\n$ redis-cli zscore rank Tom\n$ redis-cli zrem rank Alice", "explanation": "演示了有序集合的增删查和分数操作。"}]}}, {"name": "Leaderboard and Priority Queue", "trans": ["排行榜与优先队列"], "usage": {"syntax": "ZADD/ZREVRANGE/ZINCRBY/ZPOPMIN/ZPOPMAX", "description": "有序集合适合实现排行榜（按分数排序）、优先队列（按分数弹出）。ZINCRBY分数自增，ZPOPMIN/ZPOPMAX弹出最小/最大成员。", "parameters": [{"name": "key", "description": "排行榜或队列键名"}, {"name": "member", "description": "成员"}, {"name": "score", "description": "分数"}], "returnValue": "排行榜成员、分数或弹出成员", "examples": [{"code": "$ redis-cli zincrby rank 10 Tom\n$ redis-cli zrevrange rank 0 2 withscores # 前三名\n$ redis-cli zpopmax rank", "explanation": "演示了排行榜和优先队列的实现。"}]}}, {"name": "Practice: Sorted Set类型练习", "trans": ["有序集合类型练习"], "usage": {"syntax": "# Redis有序集合类型练习", "description": "完成以下练习，掌握有序集合的常用操作和排行榜实现。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用ZADD添加多个成员及分数。\n2. 用ZREVRANGE获取前N名。\n3. 用ZINCRBY为成员加分。\n4. 用ZPOPMAX弹出分数最高的成员。", "explanation": "通过这些练习掌握有序集合的常用命令和排行榜应用。"}, {"code": "# 正确实现示例\n$ redis-cli zadd score 100 Tom 90 Alice 80 Bob\n$ redis-cli zrevrange score 0 1 withscores\n$ redis-cli zincrby score 5 Bob\n$ redis-cli zpopmax score", "explanation": "展示了练习的标准实现过程。"}]}}]}