{"name": "Lifecycle Hooks", "trans": ["生命周期钩子"], "methods": [{"name": "onMounted", "trans": ["onMounted"], "usage": {"syntax": "import { onMounted } from 'vue'\nonMounted(fn)", "description": "onMounted在组件挂载后执行回调，常用于初始化、数据请求等。等价于选项式API的mounted。", "parameters": [{"name": "fn", "description": "组件挂载后执行的回调函数"}], "returnValue": "无返回值，组件挂载后自动执行回调", "examples": [{"code": "import { onMounted } from 'vue'\nonMounted(() => {\n  console.log('组件已挂载')\n})", "explanation": "组件挂载后打印日志。"}]}}, {"name": "onUpdated", "trans": ["onUpdated"], "usage": {"syntax": "import { onUpdated } from 'vue'\nonUpdated(fn)", "description": "onUpdated在组件更新后执行回调，适合处理依赖DOM更新的逻辑。等价于选项式API的updated。", "parameters": [{"name": "fn", "description": "组件更新后执行的回调函数"}], "returnValue": "无返回值，组件更新后自动执行回调", "examples": [{"code": "import { onUpdated } from 'vue'\nonUpdated(() => {\n  console.log('组件已更新')\n})", "explanation": "组件每次更新后打印日志。"}]}}, {"name": "onUnmounted", "trans": ["onUnmounted"], "usage": {"syntax": "import { onUnmounted } from 'vue'\nonUnmounted(fn)", "description": "onUnmounted在组件卸载前执行回调，常用于清理定时器、事件监听等。等价于选项式API的beforeDestroy/destroyed。", "parameters": [{"name": "fn", "description": "组件卸载前执行的回调函数"}], "returnValue": "无返回值，组件卸载前自动执行回调", "examples": [{"code": "import { onUnmounted } from 'vue'\nonUnmounted(() => {\n  console.log('组件即将卸载')\n})", "explanation": "组件卸载前打印日志。"}]}}, {"name": "onBeforeMount", "trans": ["onBeforeMount"], "usage": {"syntax": "import { onBeforeMount } from 'vue'\nonBeforeMount(fn)", "description": "onBeforeMount在组件挂载前执行回调，适合做初始化准备。等价于选项式API的beforeMount。", "parameters": [{"name": "fn", "description": "组件挂载前执行的回调函数"}], "returnValue": "无返回值，组件挂载前自动执行回调", "examples": [{"code": "import { onBeforeMount } from 'vue'\nonBeforeMount(() => {\n  console.log('组件即将挂载')\n})", "explanation": "组件挂载前打印日志。"}]}}, {"name": "onBeforeUpdate", "trans": ["onBeforeUpdate"], "usage": {"syntax": "import { onBeforeUpdate } from 'vue'\nonBeforeUpdate(fn)", "description": "onBeforeUpdate在组件更新前执行回调，适合保存旧数据或清理。等价于选项式API的beforeUpdate。", "parameters": [{"name": "fn", "description": "组件更新前执行的回调函数"}], "returnValue": "无返回值，组件更新前自动执行回调", "examples": [{"code": "import { onBeforeUpdate } from 'vue'\nonBeforeUpdate(() => {\n  console.log('组件即将更新')\n})", "explanation": "组件更新前打印日志。"}]}}, {"name": "onBeforeUnmount", "trans": ["onBeforeUnmount"], "usage": {"syntax": "import { onBeforeUnmount } from 'vue'\nonBeforeUnmount(fn)", "description": "onBeforeUnmount在组件卸载前执行回调，适合做最后的清理。等价于选项式API的beforeUnmount。", "parameters": [{"name": "fn", "description": "组件卸载前执行的回调函数"}], "returnValue": "无返回值，组件卸载前自动执行回调", "examples": [{"code": "import { onBeforeUnmount } from 'vue'\nonBeforeUnmount(() => {\n  console.log('组件即将卸载')\n})", "explanation": "组件卸载前打印日志。"}]}}, {"name": "onErrorCaptured", "trans": ["onErrorCaptured"], "usage": {"syntax": "import { onErrorCaptured } from 'vue'\nonErrorCaptured(fn)", "description": "onErrorCaptured用于捕获子组件的运行时错误，便于统一处理异常。等价于选项式API的errorCaptured。", "parameters": [{"name": "fn", "description": "错误捕获回调函数，参数为错误对象和组件实例"}], "returnValue": "返回true/false，决定是否继续向上传播错误", "examples": [{"code": "import { onErrorCaptured } from 'vue'\nonErrorCaptured((err, instance, info) => {\n  console.error('捕获到错误', err)\n  return false // 阻止错误继续上传\n})", "explanation": "捕获子组件错误并处理。"}]}}, {"name": "Lifecycle Hooks vs Options API", "trans": ["生命周期钩子与选项式对照"], "usage": {"syntax": "// 组合式API\nonMounted(fn)\n// 选项式API\nmounted() { ... }", "description": "组合式API通过onXxx注册生命周期钩子，选项式API通过生命周期方法声明。两者功能一致，写法不同。", "parameters": [{"name": "onXxx/mounted等", "description": "两种API的生命周期写法"}], "returnValue": "无返回值，便于理解两种API的差异和映射关系", "examples": [{"code": "// 组合式API\nonMounted(() => { ... })\n// 选项式API\nmounted() { ... }", "explanation": "两种API生命周期钩子的对照写法。"}]}}, {"name": "Lifecycle Hooks Assignment", "trans": ["生命周期钩子练习"], "usage": {"syntax": "# 生命周期钩子练习", "description": "完成以下练习，巩固生命周期钩子相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 在onMounted中打印日志。\n2. 在onUpdated中处理DOM。\n3. 在onUnmounted中清理定时器。\n4. 用onErrorCaptured捕获子组件错误。\n5. 对比组合式和选项式生命周期写法。", "explanation": "通过这些练习掌握各生命周期钩子的用法和API对照。"}]}}]}