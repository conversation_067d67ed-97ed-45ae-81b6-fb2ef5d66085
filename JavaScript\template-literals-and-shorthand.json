{"name": "Template Literals and Shorthand", "trans": ["模板字符串与简写"], "methods": [{"name": "Template Literals", "trans": ["模板字符串"], "usage": {"syntax": "const name = '<PERSON>';\nconst str = `Hello, ${name}!`;\nconst multi = `第一行\n第二行`;", "description": "模板字符串用反引号声明，支持变量插值和多行文本。", "parameters": [{"name": "`...${变量}...`", "description": "插值表达式。"}, {"name": "多行文本", "description": "可直接换行。"}], "returnValue": "返回拼接后的字符串。", "examples": [{"code": "const user = '小明';\nconst greet = `Hi, ${user}!`;\nconst lines = `A\nB`;\nconsole.log(greet, lines);", "explanation": "模板字符串插值和多行文本。"}]}}, {"name": "Property Shorthand", "trans": ["属性简写"], "usage": {"syntax": "const x = 1, y = 2;\nconst obj = { x, y };", "description": "对象字面量中属性名与变量名相同时可省略冒号和值。", "parameters": [{"name": "x, y", "description": "变量名即属性名。"}], "returnValue": "返回属性简写的对象。", "examples": [{"code": "const a = 1, b = 2;\nconst obj = { a, b };\nconsole.log(obj); // { a: 1, b: 2 }", "explanation": "属性简写的对象声明。"}]}}, {"name": "Method Shorthand", "trans": ["方法简写"], "usage": {"syntax": "const obj = {\n  foo() {\n    return 1;\n  }\n};", "description": "对象方法可用简写形式，省略function关键字和冒号。", "parameters": [{"name": "foo()", "description": "方法名直接加括号。"}], "returnValue": "返回方法简写的对象。", "examples": [{"code": "const obj = {\n  bar() { return 2; }\n};\nconsole.log(obj.bar()); // 2", "explanation": "方法简写的对象声明。"}]}}, {"name": "作业：模板字符串与简写实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 用模板字符串拼接变量和多行文本\n// 2. 用属性/方法简写声明对象", "description": "通过实践模板字符串、属性简写、方法简写，掌握ES6简洁语法。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 模板字符串/多行/插值\n// 2. 属性/方法简写", "explanation": "作业提示，需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nconst name = '<PERSON>';\nconst msg = `Hi,\n${name}!`;\nconst obj = { name, show() { return this.name; } };\nconsole.log(msg, obj.show());", "explanation": "涵盖模板字符串、属性简写、方法简写的正确实现。"}]}}]}