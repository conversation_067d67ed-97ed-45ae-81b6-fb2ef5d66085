{"name": "Other State Management Solutions", "trans": ["其他状态管理方案"], "methods": [{"name": "Inject/Provide", "trans": ["inject/provide"], "usage": {"syntax": "provide('key', value)\nconst val = inject('key')", "description": "通过provide/inject实现祖先组件与任意后代组件间的数据共享，适合跨层级依赖注入。", "parameters": [{"name": "provide", "description": "祖先组件提供数据"}, {"name": "inject", "description": "后代组件注入数据"}], "returnValue": "后代组件可访问祖先组件数据", "examples": [{"code": "// 祖先组件\nprovide('theme', 'dark')\n// 任意后代组件\nconst theme = inject('theme')", "explanation": "通过provide/inject实现主题等全局数据共享。"}]}}, {"name": "EventBus", "trans": ["eventBus"], "usage": {"syntax": "const bus = mitt()\nbus.emit('event', data)\nbus.on('event', handler)", "description": "eventBus通过事件机制实现任意组件间通信，适合兄弟组件、跨层级通信。Vue3推荐使用mitt等库。", "parameters": [{"name": "emit", "description": "发送事件"}, {"name": "on", "description": "监听事件"}], "returnValue": "实现全局事件通信", "examples": [{"code": "import mitt from 'mitt'\nconst bus = mitt()\nbus.on('custom', data => console.log(data))\nbus.emit('custom', 123)", "explanation": "通过mitt实现全局事件通信。"}]}}, {"name": "Composition API", "trans": ["组合式API"], "usage": {"syntax": "function useUser() {\n  const user = ref('')\n  return { user }\n}", "description": "通过自定义组合函数（composable）实现状态逻辑复用，适合局部或跨组件共享状态。", "parameters": [{"name": "composable", "description": "自定义组合函数"}], "returnValue": "可复用的响应式状态和方法", "examples": [{"code": "function useUser() {\n  const user = ref('')\n  return { user }\n}\n// 组件A\nconst { user } = useUser()\n// 组件B\nconst { user } = useUser()", "explanation": "通过组合函数实现状态逻辑复用。"}]}}, {"name": "Scenario Analysis", "trans": ["适用场景分析"], "usage": {"syntax": "// 选择合适的状态管理方案", "description": "根据项目规模和复杂度选择合适的状态管理方案：小型项目可用组合式API和provide/inject，中大型项目推荐Pinia/Vuex，eventBus适合临时全局事件通信。", "parameters": [{"name": "项目规模", "description": "小型/中型/大型"}, {"name": "通信复杂度", "description": "简单/复杂"}], "returnValue": "最佳实践建议", "examples": [{"code": "// 小型项目：组合式API、provide/inject\n// 中大型项目：Pinia/Vuex\n// 临时事件：eventBus", "explanation": "根据实际需求选择最合适的状态管理方式。"}]}}, {"name": "Other State Management Assignment", "trans": ["其他状态管理练习"], "usage": {"syntax": "# 其他状态管理练习", "description": "完成以下练习，巩固多种状态管理方案的应用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用provide/inject实现主题切换。\n2. 用mitt实现全局事件通信。\n3. 编写组合函数实现状态复用。\n4. 分析不同项目下的最佳状态管理方案。", "explanation": "通过这些练习掌握多种状态管理方案的用法和选择。"}]}}]}