{"name": "Custom Composables", "trans": ["自定义组合函数"], "methods": [{"name": "Create Custom Composable", "trans": ["创建自定义hook（composable）"], "usage": {"syntax": "function useXxx() {\n  // 组合逻辑\n  return { ... }\n}", "description": "自定义组合函数（composable）是Vue3中用于复用逻辑的函数，通常以use开头，内部可使用ref、reactive、watch等API。", "parameters": [{"name": "无固定参数", "description": "根据实际需求定义"}], "returnValue": "返回需要暴露给组件的响应式数据和方法", "examples": [{"code": "function useCounter() {\n  const count = ref(0)\n  const inc = () => count.value++\n  return { count, inc }\n}", "explanation": "定义一个计数器组合函数，实现计数和自增。"}]}}, {"name": "Logic Reuse", "trans": ["逻辑复用"], "usage": {"syntax": "const { data, method } = useXxx()", "description": "通过自定义组合函数，将通用逻辑抽离，多个组件可复用同一套逻辑，提升开发效率。", "parameters": [{"name": "useXxx", "description": "自定义组合函数"}], "returnValue": "复用的响应式数据和方法", "examples": [{"code": "// 组件A\nconst { count, inc } = useCounter()\n// 组件B\nconst { count, inc } = useCounter()", "explanation": "多个组件通过useCounter实现计数逻辑复用。"}]}}, {"name": "State Isolation", "trans": ["状态隔离"], "usage": {"syntax": "const { state } = useXxx()", "description": "每次调用组合函数都会返回独立的响应式数据，实现组件间状态隔离，互不影响。", "parameters": [{"name": "每次调用", "description": "返回独立的响应式对象"}], "returnValue": "独立的响应式状态", "examples": [{"code": "const { count } = useCounter() // 每个组件的count互不影响", "explanation": "每个组件调用useCounter得到独立的count。"}]}}, {"name": "Composable Parameters and Return", "trans": ["组合函数参数与返回"], "usage": {"syntax": "function useXxx(param) {\n  // 根据参数定制逻辑\n  return { ... }\n}", "description": "组合函数可接收参数，灵活定制逻辑，返回值一般为响应式数据和方法对象。", "parameters": [{"name": "param", "description": "自定义参数，类型不限"}], "returnValue": "响应式数据和方法对象", "examples": [{"code": "function useCounter(init = 0) {\n  const count = ref(init)\n  return { count }\n}\nconst { count } = useCounter(10)", "explanation": "通过参数设置初始值，返回响应式count。"}]}}, {"name": "Practical Example", "trans": ["实际应用示例"], "usage": {"syntax": "function useMouse() {\n  const x = ref(0)\n  const y = ref(0)\n  onMounted(() => {\n    window.addEventListener('mousemove', update)\n  })\n  onUnmounted(() => {\n    window.removeEventListener('mousemove', update)\n  })\n  function update(e) {\n    x.value = e.pageX\n    y.value = e.pageY\n  }\n  return { x, y }\n}", "description": "实际开发中可将鼠标位置、窗口尺寸、表单校验等逻辑封装为组合函数，提升复用性。", "parameters": [{"name": "无参数", "description": "根据实际需求"}], "returnValue": "响应式数据和方法", "examples": [{"code": "function useMouse() {\n  const x = ref(0)\n  const y = ref(0)\n  function update(e) {\n    x.value = e.pageX\n    y.value = e.pageY\n  }\n  onMounted(() => {\n    window.addEventListener('mousemove', update)\n  })\n  onUnmounted(() => {\n    window.removeEventListener('mousemove', update)\n  })\n  return { x, y }\n}", "explanation": "封装鼠标位置监听为组合函数，组件可直接复用。"}]}}, {"name": "Custom Composables Assignment", "trans": ["自定义组合函数练习"], "usage": {"syntax": "# 自定义组合函数练习", "description": "完成以下练习，巩固自定义组合函数相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 编写useCounter组合函数，实现计数和自增。\n2. 在多个组件中复用useCounter，验证状态隔离。\n3. 编写带参数的useXxx组合函数。\n4. 封装一个useMouse监听鼠标位置。", "explanation": "通过这些练习掌握自定义组合函数的编写和应用。"}]}}]}