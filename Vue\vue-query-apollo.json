{"name": "Vue Query / Vue Apollo", "trans": ["Vue Query/Vue Apollo"], "methods": [{"name": "Data Fetching", "trans": ["数据获取"], "usage": {"syntax": "const { data, isLoading } = useQuery(['user', id], () => fetchUser(id))", "description": "通过useQuery等API进行数据获取，支持自动缓存和状态管理。Vue Apollo通过useQuery获取GraphQL数据。", "parameters": [{"name": "useQuery", "description": "数据获取的核心API"}, {"name": "查询key/GraphQL查询", "description": "唯一标识和查询内容"}], "returnValue": "获取到的数据、加载状态等", "examples": [{"code": "const { data, isLoading } = useQuery(['user', id], () => fetchUser(id))", "explanation": "通过useQuery获取用户数据和加载状态。"}, {"code": "const { result, loading } = useQuery(gql`query { user { id name } }`)", "explanation": "Vue Apollo获取GraphQL用户数据。"}]}}, {"name": "Cache Management", "trans": ["缓存管理"], "usage": {"syntax": "queryClient.invalidateQueries('user')", "description": "通过invalidateQueries、setQueryData等API手动管理缓存，Vue Apollo通过cache.modify等方法管理GraphQL缓存。", "parameters": [{"name": "invalidateQueries", "description": "使缓存失效并重新请求"}, {"name": "setQueryData", "description": "手动设置缓存数据"}], "returnValue": "缓存更新后的数据状态", "examples": [{"code": "queryClient.invalidateQueries('user')", "explanation": "使user相关缓存失效，触发重新请求。"}, {"code": "apolloClient.cache.modify({ ... })", "explanation": "Vue Apollo手动修改GraphQL缓存。"}]}}, {"name": "Request Status", "trans": ["请求状态"], "usage": {"syntax": "const { isLoading, isError, error } = useQuery(...)", "description": "通过isLoading、isError等状态变量，实时反映请求进度和错误，便于UI展示。", "parameters": [{"name": "isLoading", "description": "加载中状态"}, {"name": "isError", "description": "请求失败状态"}, {"name": "error", "description": "错误信息对象"}], "returnValue": "请求的实时状态和错误信息", "examples": [{"code": "const { isLoading, isError, error } = useQuery(['user', id], () => fetchUser(id))", "explanation": "获取请求的加载、错误状态和错误信息。"}]}}, {"name": "Optimistic Update", "trans": ["乐观更新"], "usage": {"syntax": "useMutation(mutateFn, {\n  onMutate: async (newData) => {\n    queryClient.setQueryData(['user', newData.id], newData)\n  }\n})", "description": "通过onMutate等钩子实现乐观更新，先更新UI再请求后端，提升用户体验。", "parameters": [{"name": "useMutation", "description": "变更数据的API"}, {"name": "onMutate", "description": "乐观更新钩子"}], "returnValue": "乐观更新后的UI和数据状态", "examples": [{"code": "useMutation(mutateFn, {\n  onMutate: async (newData) => {\n    queryClient.setQueryData(['user', newData.id], newData)\n  }\n})", "explanation": "变更前先本地更新缓存，实现乐观UI。"}]}}, {"name": "Infinite Loading", "trans": ["无限加载"], "usage": {"syntax": "const { data, fetchNextPage, hasNextPage } = useInfiniteQuery(...)", "description": "通过useInfiniteQuery等API实现分页和无限滚动加载，适合大数据场景。", "parameters": [{"name": "useInfiniteQuery", "description": "无限加载的API"}, {"name": "fetchNextPage", "description": "加载下一页数据的方法"}], "returnValue": "分页加载的数据和控制方法", "examples": [{"code": "const { data, fetchNextPage, hasNextPage } = useInfiniteQuery(...)", "explanation": "实现列表的无限滚动加载。"}]}}, {"name": "Polling and Retry", "trans": ["轮询和重试"], "usage": {"syntax": "useQuery(key, fn, { refetchInterval: 5000, retry: 3 })", "description": "通过refetchInterval设置定时轮询，retry设置失败重试次数，提升数据实时性和健壮性。", "parameters": [{"name": "refetchInterval", "description": "轮询间隔时间"}, {"name": "retry", "description": "失败重试次数"}], "returnValue": "自动轮询和重试的数据获取结果", "examples": [{"code": "useQuery(key, fn, { refetchInterval: 5000, retry: 3 })", "explanation": "每5秒轮询一次，失败最多重试3次。"}]}}, {"name": "Prefetch Data", "trans": ["预取数据"], "usage": {"syntax": "queryClient.prefetchQuery(['user', id], () => fetchUser(id))", "description": "通过prefetchQuery等API提前获取并缓存数据，提升页面切换体验。", "parameters": [{"name": "prefetch<PERSON><PERSON>y", "description": "预取数据的API"}], "returnValue": "预先缓存的数据", "examples": [{"code": "queryClient.prefetchQuery(['user', id], () => fetchUser(id))", "explanation": "页面跳转前预取用户数据。"}]}}, {"name": "Vue Query / Vue Apollo Assignment", "trans": ["Vue Query/Vue Apollo练习"], "usage": {"syntax": "# Vue Query/Vue Apollo练习", "description": "完成以下练习，巩固Vue Query/Vue Apollo相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用useQuery获取数据。\n2. 手动管理缓存。\n3. 实现乐观更新和无限加载。\n4. 配置轮询和重试。\n5. 预取数据优化体验。", "explanation": "通过这些练习掌握Vue Query/Vue Apollo核心用法。"}]}}]}