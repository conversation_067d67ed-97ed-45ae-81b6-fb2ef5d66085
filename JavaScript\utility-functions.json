{"name": "Utility Functions", "trans": ["常用工具函数"], "methods": [{"name": "De<PERSON><PERSON>ce and Throttle", "trans": ["防抖与节流"], "usage": {"syntax": "debounce(callback, delay) / throttle(callback, interval)", "description": "防抖和节流是两种用于控制函数执行频率的技术，特别适用于处理高频触发的事件（如滚动、调整窗口大小、输入等）。防抖会在用户停止操作后延迟执行函数，而节流会按固定时间间隔执行函数。", "parameters": [{"name": "callback", "description": "需要控制执行频率的函数"}, {"name": "delay/interval", "description": "延迟时间（防抖）或间隔时间（节流），单位为毫秒"}], "returnValue": "返回一个新函数，该函数具有受控执行频率的特性", "examples": [{"code": "// 防抖函数实现\nfunction debounce(callback, delay = 300) {\n  let timer = null;\n  \n  return function(...args) {\n    // 如果已经设置了定时器，则清除之前的定时器\n    if (timer) clearTimeout(timer);\n    \n    // 设置新的定时器，延迟执行回调函数\n    timer = setTimeout(() => {\n      callback.apply(this, args);\n    }, delay);\n  };\n}\n\n// 使用防抖函数处理输入事件\nconst searchInput = document.getElementById('search-input');\nconst handleSearch = debounce(function(e) {\n  // 只有用户停止输入300ms后才会执行搜索\n  console.log('搜索:', e.target.value);\n  // 发送API请求等操作\n}, 300);\n\nsearchInput.addEventListener('input', handleSearch);\n\n// 节流函数实现\nfunction throttle(callback, interval = 300) {\n  let lastTime = 0;\n  \n  return function(...args) {\n    const now = Date.now();\n    \n    // 如果距离上次执行的时间大于等于指定间隔，则执行函数\n    if (now - lastTime >= interval) {\n      callback.apply(this, args);\n      lastTime = now; // 更新上次执行的时间\n    }\n  };\n}\n\n// 使用节流函数处理滚动事件\nconst handleScroll = throttle(function() {\n  // 每300ms最多执行一次\n  console.log('页面滚动位置:', window.scrollY);\n  // 执行滚动相关操作\n}, 300);\n\nwindow.addEventListener('scroll', handleScroll);", "explanation": "这个示例展示了防抖和节流函数的实现和使用方法。防抖函数用于处理输入事件，确保用户停止输入后才执行搜索；节流函数用于处理滚动事件，限制函数执行频率，提高性能。"}]}}, {"name": "Deep and Shallow Copy", "trans": ["深拷贝与浅拷贝"], "usage": {"syntax": "shallowCopy(obj) / deepCopy(obj)", "description": "浅拷贝只复制对象的第一层属性，而深层属性仍然共享引用；深拷贝则递归复制对象的所有层级，创建一个完全独立的副本。理解两者的区别对于避免意外的数据修改非常重要。", "parameters": [{"name": "obj", "description": "需要复制的对象或数组"}], "returnValue": "返回原对象的副本", "examples": [{"code": "// 浅拷贝方法\nfunction shallowCopy(obj) {\n  // 处理非对象情况\n  if (obj === null || typeof obj !== 'object') return obj;\n  \n  // 创建新的数组或对象\n  const copy = Array.isArray(obj) ? [] : {};\n  \n  // 复制属性\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      copy[key] = obj[key];\n    }\n  }\n  \n  return copy;\n}\n\n// 也可以使用内置方法进行浅拷贝\n// 对象浅拷贝\nconst original = { a: 1, b: { c: 2 } };\nconst shallowCopy1 = { ...original }; // 展开运算符\nconst shallowCopy2 = Object.assign({}, original); // Object.assign\n\n// 数组浅拷贝\nconst originalArray = [1, 2, [3, 4]];\nconst shallowArrayCopy1 = [...originalArray]; // 展开运算符\nconst shallowArrayCopy2 = originalArray.slice(); // slice方法\n\n// 深拷贝方法\nfunction deepCopy(obj) {\n  // 处理非对象情况\n  if (obj === null || typeof obj !== 'object') return obj;\n  \n  // 处理日期对象\n  if (obj instanceof Date) return new Date(obj);\n  \n  // 处理正则对象\n  if (obj instanceof RegExp) return new RegExp(obj);\n  \n  // 创建新的数组或对象\n  const copy = Array.isArray(obj) ? [] : {};\n  \n  // 递归复制所有属性\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      copy[key] = deepCopy(obj[key]);\n    }\n  }\n  \n  return copy;\n}\n\n// 使用JSON方法进行深拷贝（有局限性）\nfunction jsonDeepCopy(obj) {\n  // 注意：这种方法无法处理函数、undefined、循环引用等\n  return JSON.parse(JSON.stringify(obj));\n}\n\n// 测试深浅拷贝的区别\nconst original = { a: 1, b: { c: 2 } };\nconst shallow = shallowCopy(original);\nconst deep = deepCopy(original);\n\n// 修改嵌套对象\nshallow.b.c = 3;\nconsole.log(original.b.c); // 输出3，因为浅拷贝共享引用\n\ndeep.b.c = 4;\nconsole.log(original.b.c); // 输出3，因为深拷贝创建了独立副本", "explanation": "这个示例展示了浅拷贝和深拷贝的实现和区别。浅拷贝只复制对象的第一层，而深拷贝递归复制所有层级。示例中展示了多种实现方法，并通过修改嵌套对象来演示两者的区别。"}]}}, {"name": "Object Merging and Cloning", "trans": ["对象合并与克隆"], "usage": {"syntax": "mergeObjects(...objects) / cloneObject(obj)", "description": "对象合并是将多个对象的属性组合到一个对象中，克隆则是创建对象的副本。这些操作在处理配置对象、状态管理等场景中非常有用。", "parameters": [{"name": "objects", "description": "需要合并的多个对象"}, {"name": "obj", "description": "需要克隆的对象"}], "returnValue": "返回合并后的对象或克隆的对象", "examples": [{"code": "// 简单对象合并函数\nfunction mergeObjects(...objects) {\n  return objects.reduce((result, current) => {\n    // 遍历当前对象的所有属性\n    Object.keys(current).forEach(key => {\n      // 如果属性是对象且不是数组，则递归合并\n      if (isObject(current[key]) && isObject(result[key]) && !Array.isArray(current[key])) {\n        result[key] = mergeObjects(result[key], current[key]);\n      } else {\n        // 否则直接赋值\n        result[key] = current[key];\n      }\n    });\n    return result;\n  }, {});\n}\n\n// 辅助函数：检查是否为对象\nfunction isObject(item) {\n  return item && typeof item === 'object' && !Array.isArray(item);\n}\n\n// 使用Object.assign进行浅合并\nconst defaultConfig = { theme: 'light', fontSize: 14, showSidebar: true };\nconst userConfig = { theme: 'dark', fontSize: 16 };\n\nconst finalConfig1 = Object.assign({}, defaultConfig, userConfig);\nconsole.log(finalConfig1); // { theme: 'dark', fontSize: 16, showSidebar: true }\n\n// 使用展开运算符进行浅合并\nconst finalConfig2 = { ...defaultConfig, ...userConfig };\nconsole.log(finalConfig2); // { theme: 'dark', fontSize: 16, showSidebar: true }\n\n// 使用自定义函数进行深合并\nconst complexDefault = {\n  theme: { main: 'light', sidebar: 'gray' },\n  features: { comments: true, sharing: false }\n};\n\nconst complexUser = {\n  theme: { main: 'dark' },\n  features: { notifications: true }\n};\n\nconst finalComplex = mergeObjects(complexDefault, complexUser);\nconsole.log(finalComplex);\n/* 输出：\n{\n  theme: { main: 'dark', sidebar: 'gray' },\n  features: { comments: true, sharing: false, notifications: true }\n}\n*/", "explanation": "这个示例展示了不同的对象合并方法，包括使用Object.assign、展开运算符进行浅合并，以及使用自定义函数进行深度合并。深度合并会递归地合并嵌套对象的属性。"}]}}, {"name": "Array Deduplication and Grouping", "trans": ["数组去重与分组"], "usage": {"syntax": "removeDuplicates(array) / groupBy(array, key)", "description": "数组去重用于移除数组中的重复元素，数组分组用于根据特定条件将数组元素分类到不同组中。这些操作在数据处理和展示中非常常见。", "parameters": [{"name": "array", "description": "需要处理的数组"}, {"name": "key", "description": "分组依据的属性名或函数"}], "returnValue": "返回处理后的数组或对象", "examples": [{"code": "// 数组去重 - 使用Set\nfunction removeDuplicates(array) {\n  return [...new Set(array)];\n}\n\n// 数组去重 - 使用filter\nfunction removeDuplicatesByFilter(array) {\n  return array.filter((item, index) => array.indexOf(item) === index);\n}\n\n// 对象数组去重 - 根据指定属性\nfunction removeDuplicatesByProperty(array, property) {\n  const seen = new Set();\n  return array.filter(item => {\n    const value = item[property];\n    if (seen.has(value)) {\n      return false;\n    }\n    seen.add(value);\n    return true;\n  });\n}\n\n// 数组分组 - 根据属性\nfunction groupByProperty(array, property) {\n  return array.reduce((groups, item) => {\n    const key = item[property];\n    // 如果该分组不存在，则创建新分组\n    if (!groups[key]) {\n      groups[key] = [];\n    }\n    // 将当前项添加到对应分组\n    groups[key].push(item);\n    return groups;\n  }, {});\n}\n\n// 数组分组 - 根据函数\nfunction groupByFunction(array, fn) {\n  return array.reduce((groups, item) => {\n    const key = fn(item);\n    if (!groups[key]) {\n      groups[key] = [];\n    }\n    groups[key].push(item);\n    return groups;\n  }, {});\n}\n\n// 示例：基本数组去重\nconst numbers = [1, 2, 2, 3, 4, 4, 5];\nconst uniqueNumbers = removeDuplicates(numbers);\nconsole.log(uniqueNumbers); // [1, 2, 3, 4, 5]\n\n// 示例：对象数组去重\nconst users = [\n  { id: 1, name: '张三' },\n  { id: 2, name: '李四' },\n  { id: 1, name: '张三(重复)' },\n  { id: 3, name: '王五' }\n];\n\nconst uniqueUsers = removeDuplicatesByProperty(users, 'id');\nconsole.log(uniqueUsers); // 只保留id不重复的用户\n\n// 示例：按属性分组\nconst products = [\n  { category: '电子', name: '手机', price: 3999 },\n  { category: '电子', name: '笔记本', price: 6999 },\n  { category: '服装', name: 'T恤', price: 99 },\n  { category: '服装', name: '牛仔裤', price: 199 },\n  { category: '电子', name: '耳机', price: 999 }\n];\n\nconst productsByCategory = groupByProperty(products, 'category');\nconsole.log(productsByCategory);\n/* 输出：\n{\n  '电子': [\n    { category: '电子', name: '手机', price: 3999 },\n    { category: '电子', name: '笔记本', price: 6999 },\n    { category: '电子', name: '耳机', price: 999 }\n  ],\n  '服装': [\n    { category: '服装', name: 'T恤', price: 99 },\n    { category: '服装', name: '牛仔裤', price: 199 }\n  ]\n}\n*/\n\n// 示例：按函数分组（价格区间）\nconst productsByPriceRange = groupByFunction(products, product => {\n  if (product.price < 100) return '低价';\n  if (product.price < 1000) return '中价';\n  return '高价';\n});\n\nconsole.log(productsByPriceRange);\n/* 输出：\n{\n  '低价': [{ category: '服装', name: 'T恤', price: 99 }],\n  '中价': [\n    { category: '服装', name: '牛仔裤', price: 199 },\n    { category: '电子', name: '耳机', price: 999 }\n  ],\n  '高价': [\n    { category: '电子', name: '手机', price: 3999 },\n    { category: '电子', name: '笔记本', price: 6999 }\n  ]\n}\n*/", "explanation": "这个示例展示了多种数组去重和分组方法。数组去重包括使用Set、filter以及根据对象属性去重；数组分组包括根据对象属性和自定义函数分组。这些方法在处理数据集合时非常有用。"}]}}, {"name": "Function Currying and Composition", "trans": ["函数柯里化与组合"], "usage": {"syntax": "curry(fn) / compose(...fns)", "description": "函数柯里化是将接受多个参数的函数转换成一系列接受单个参数的函数；函数组合是将多个函数组合成一个函数，前一个函数的输出作为后一个函数的输入。这些技术是函数式编程的核心概念。", "parameters": [{"name": "fn", "description": "需要柯里化的函数"}, {"name": "fns", "description": "需要组合的多个函数"}], "returnValue": "返回柯里化后的函数或组合后的函数", "examples": [{"code": "// 函数柯里化\nfunction curry(fn) {\n  return function curried(...args) {\n    // 如果提供的参数数量足够，则直接调用原函数\n    if (args.length >= fn.length) {\n      return fn.apply(this, args);\n    }\n    \n    // 否则返回一个新函数，等待更多参数\n    return function(...moreArgs) {\n      return curried.apply(this, args.concat(moreArgs));\n    };\n  };\n}\n\n// 函数组合（从右到左执行）\nfunction compose(...fns) {\n  return function(x) {\n    return fns.reduceRight((result, fn) => fn(result), x);\n  };\n}\n\n// 函数管道（从左到右执行）\nfunction pipe(...fns) {\n  return function(x) {\n    return fns.reduce((result, fn) => fn(result), x);\n  };\n}\n\n// 柯里化示例\nfunction add(a, b, c) {\n  return a + b + c;\n}\n\nconst curriedAdd = curry(add);\nconsole.log(curriedAdd(1)(2)(3)); // 6\nconsole.log(curriedAdd(1, 2)(3)); // 6\nconsole.log(curriedAdd(1)(2, 3)); // 6\n\n// 实际应用：过滤特定属性的对象\nconst filterByProperty = curry(function(property, value, obj) {\n  return obj[property] === value;\n});\n\nconst filterByType = filterByProperty('type');\nconst filterProducts = filterByType('product');\nconst filterUsers = filterByType('user');\n\nconst items = [\n  { id: 1, type: 'product', name: '手机' },\n  { id: 2, type: 'user', name: '张三' },\n  { id: 3, type: 'product', name: '电视' }\n];\n\nconst products = items.filter(filterProducts);\nconsole.log(products); // [{ id: 1, type: 'product', name: '手机' }, { id: 3, type: 'product', name: '电视' }]\n\n// 函数组合示例\nfunction addOne(x) { return x + 1; }\nfunction double(x) { return x * 2; }\nfunction square(x) { return x * x; }\n\n// 先平方，再加倍，最后加1\nconst compute1 = compose(addOne, double, square);\nconsole.log(compute1(3)); // (3² * 2) + 1 = 19\n\n// 先加1，再加倍，最后平方\nconst compute2 = pipe(addOne, double, square);\nconsole.log(compute2(3)); // ((3 + 1) * 2)² = 64\n\n// 实际应用：数据转换管道\nconst users = [\n  { id: 1, name: '张三', age: 25 },\n  { id: 2, name: '李四', age: 17 },\n  { id: 3, name: '王五', age: 30 }\n];\n\n// 一系列数据转换函数\nconst filterAdults = users => users.filter(user => user.age >= 18);\nconst sortByAge = users => [...users].sort((a, b) => a.age - b.age);\nconst getNames = users => users.map(user => user.name);\nconst joinWithComma = names => names.join(', ');\n\n// 使用函数组合处理数据\nconst getAdultNamesSorted = pipe(\n  filterAdults,\n  sortByAge,\n  getNames,\n  joinWithComma\n);\n\nconsole.log(getAdultNamesSorted(users)); // '张三, 王五'", "explanation": "这个示例展示了函数柯里化和函数组合的实现和应用。柯里化允许将多参数函数转换为一系列单参数函数，函数组合则允许将多个函数组合成一个新函数。这些技术在函数式编程中用于创建更灵活、可复用的代码。"}]}}, {"name": "Utility Functions Assignment", "trans": ["工具函数练习"], "usage": {"syntax": "// 工具函数练习", "description": "完成以下练习来巩固对JavaScript工具函数的理解和应用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "/*\n练习：实现一个通用的缓存函数\n\n要求：\n1. 创建一个memoize函数，可以缓存任何函数的计算结果\n2. 缓存应该基于函数参数，相同的参数应返回缓存的结果\n3. 支持设置最大缓存数量，超出时删除最早的缓存\n4. 提供清除缓存的方法\n*/\n\n// 你的代码：\nfunction memoize(fn, maxSize = 100) {\n  // 存储缓存结果\n  const cache = new Map();\n  // 记录调用顺序\n  const keys = [];\n  \n  function getCacheKey(args) {\n    // 将参数转换为字符串作为缓存键\n    return JSON.stringify(args);\n  }\n  \n  function memoized(...args) {\n    const key = getCacheKey(args);\n    \n    // 如果缓存中存在结果，直接返回\n    if (cache.has(key)) {\n      // 更新使用顺序（将该键移到数组末尾表示最近使用）\n      const keyIndex = keys.indexOf(key);\n      keys.splice(keyIndex, 1);\n      keys.push(key);\n      return cache.get(key);\n    }\n    \n    // 计算结果\n    const result = fn.apply(this, args);\n    \n    // 如果达到最大缓存数量，删除最早的缓存\n    if (keys.length >= maxSize) {\n      const oldestKey = keys.shift();\n      cache.delete(oldestKey);\n    }\n    \n    // 存储新结果\n    cache.set(key, result);\n    keys.push(key);\n    \n    return result;\n  }\n  \n  // 添加清除缓存的方法\n  memoized.clearCache = function() {\n    cache.clear();\n    keys.length = 0;\n  };\n  \n  return memoized;\n}\n\n// 测试函数\nfunction fibonacci(n) {\n  if (n <= 1) return n;\n  return fibonacci(n - 1) + fibonacci(n - 2);\n}\n\n// 使用缓存优化的斐波那契函数\nconst memoizedFib = memoize(fibonacci);\n\n// 测试性能\nconsole.time('无缓存');\nconsole.log(fibonacci(35)); // 慢\nconsole.timeEnd('无缓存');\n\nconsole.time('有缓存');\nconsole.log(memoizedFib(35)); // 快\nconsole.timeEnd('有缓存');\n\n// 再次调用（应该非常快，直接从缓存获取）\nconsole.time('缓存命中');\nconsole.log(memoizedFib(35));\nconsole.timeEnd('缓存命中');\n\n// 清除缓存\nmemoizedFib.clearCache();\n\n// 清除后再次调用（应该重新计算）\nconsole.time('清除缓存后');\nconsole.log(memoizedFib(35));\nconsole.timeEnd('清除缓存后');", "explanation": "这个练习要求实现一个通用的缓存函数，可以缓存任何函数的计算结果。实现的memoize函数支持基于参数的缓存、设置最大缓存数量和清除缓存。示例中使用斐波那契函数演示了缓存的性能优势。"}]}}]}