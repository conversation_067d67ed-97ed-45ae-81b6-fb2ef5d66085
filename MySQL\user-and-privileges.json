{"name": "User and Privileges", "trans": ["用户与权限"], "methods": [{"name": "Create User", "trans": ["创建用户"], "usage": {"syntax": "CREATE USER '用户名'@'主机' IDENTIFIED BY '密码';", "description": "用于在MySQL中创建新用户账号。可以指定用户名、允许登录的主机和密码。", "parameters": [{"name": "用户名", "description": "新建用户的名称"}, {"name": "主机", "description": "允许登录的主机地址，常用'localhost'或'%'"}, {"name": "密码", "description": "用户登录密码"}], "returnValue": "无返回值", "examples": [{"code": "-- 创建只能本地登录的用户\nCREATE USER 'student'@'localhost' IDENTIFIED BY '123456';\n\n-- 创建可以远程登录的用户\nCREATE USER 'webuser'@'%' IDENTIFIED BY 'webpass';", "explanation": "第一个例子创建了一个只能在本机登录的用户student，第二个例子创建了一个可以远程登录的用户webuser。"}]}}, {"name": "Grant and Revoke Privileges", "trans": ["授权与回收权限"], "usage": {"syntax": "GRANT 权限列表 ON 数据库.表 TO '用户名'@'主机';\nREVOKE 权限列表 ON 数据库.表 FROM '用户名'@'主机';", "description": "GRANT用于授予用户在指定数据库或表上的权限，REVOKE用于回收权限。常见权限有SELECT、INSERT、UPDATE、DELETE、ALL等。", "parameters": [{"name": "权限列表", "description": "如SELECT、INSERT、UPDATE、ALL等"}, {"name": "数据库.表", "description": "授权或回收权限的对象，可用*.*表示所有库所有表"}, {"name": "用户名", "description": "目标用户名称"}, {"name": "主机", "description": "目标用户允许登录的主机"}], "returnValue": "无返回值", "examples": [{"code": "-- 授予student用户对testdb数据库所有表的全部权限\nGRANT ALL ON testdb.* TO 'student'@'localhost';\n\n-- 只授予webuser用户查询权限\nGRANT SELECT ON testdb.* TO 'webuser'@'%';\n\n-- 回收webuser的查询权限\nREVOKE SELECT ON testdb.* FROM 'webuser'@'%';", "explanation": "第一个例子授予student用户testdb库所有表的全部权限，第二个例子只授予webuser查询权限，第三个例子回收webuser的查询权限。"}]}}, {"name": "Show Privileges", "trans": ["查看权限"], "usage": {"syntax": "SHOW GRANTS FOR '用户名'@'主机';", "description": "用于查看指定用户当前拥有的所有权限。", "parameters": [{"name": "用户名", "description": "要查看权限的用户名"}, {"name": "主机", "description": "用户允许登录的主机"}], "returnValue": "返回该用户的授权语句列表", "examples": [{"code": "-- 查看student用户的权限\nSHOW GRANTS FOR 'student'@'localhost';", "explanation": "该命令会列出student@localhost拥有的所有权限。"}]}}, {"name": "Drop User", "trans": ["删除用户"], "usage": {"syntax": "DROP USER '用户名'@'主机';", "description": "用于删除MySQL中的用户账号，删除后该用户无法再登录数据库。", "parameters": [{"name": "用户名", "description": "要删除的用户名"}, {"name": "主机", "description": "用户允许登录的主机"}], "returnValue": "无返回值", "examples": [{"code": "-- 删除student用户\nDROP USER 'student'@'localhost';", "explanation": "该命令会删除student@localhost用户及其所有权限。"}]}}, {"name": "User and Privileges Assignment", "trans": ["用户与权限练习"], "usage": {"syntax": "# 用户与权限练习", "description": "完成以下练习，巩固MySQL用户与权限管理的基本操作。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 创建一个只能本地登录的用户testuser，密码为test123。\n2. 授予testuser对testdb数据库的全部权限。\n3. 查询testuser当前拥有的权限。\n4. 回收testuser对testdb的全部权限。\n5. 删除testuser用户。", "explanation": "通过这些练习，你将掌握MySQL用户的创建、授权、权限查看、回收和删除等常用管理操作。"}]}}]}