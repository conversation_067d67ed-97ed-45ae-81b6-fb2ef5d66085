{"topics": [{"id": "jsx", "name": "JSX语法", "file": "react/jsx.json", "description": "JSX语法规则、表达式、属性和编译转换"}, {"id": "basic-components", "name": "组件基础", "file": "react/basic-components.json", "description": "函数组件、类组件、组件渲染、导入导出、组合和拆分"}, {"id": "event-handling", "name": "事件处理", "file": "react/event-handling.json", "description": "React事件系统、事件处理函数绑定、传递参数、合成事件对象、事件委托、常见事件类型"}, {"id": "conditional-rendering", "name": "条件渲染", "file": "react/conditional-rendering.json", "description": "if语句条件渲染、三元表达式条件渲染、逻辑与(&&)条件渲染、变量控制条件渲染、阻止组件渲染、条件渲染最佳实践"}, {"id": "lists-and-keys", "name": "列表与Keys", "file": "react/lists-and-keys.json", "description": "使用map()渲染列表、key的作用和重要性、正确的key使用方式、索引作为key的问题、列表项组件提取、列表性能优化"}, {"id": "forms", "name": "表单处理", "file": "react/forms.json", "description": "受控组件、非受控组件、表单数据管理、表单验证、表单提交处理"}, {"id": "use-state", "name": "useState", "file": "react/use-state.json", "description": "useState基础、状态更新、对象/数组状态管理、惰性初始化、状态拆分"}, {"id": "use-effect", "name": "useEffect", "file": "react/use-effect.json", "description": "useEffect基础、副作用、依赖、清理、数据获取、订阅"}, {"id": "use-context", "name": "useContext", "file": "react/use-context.json", "description": "useContext基础、全局状态管理、性能优化、Context API最佳实践"}, {"id": "use-reducer", "name": "useReducer", "file": "react/useReducer.json", "description": "reducer函数、初始化状态、分发动作(dispatch)、复杂状态管理、与useState对比、惰性初始化、结合Context使用"}, {"id": "use-callback", "name": "useCallback", "file": "react/useCallback.json", "description": "函数记忆化、依赖数组、性能优化场景、与useEffect配合、常见错误、最佳实践"}, {"id": "use-memo", "name": "useMemo", "file": "react/useMemo.json", "description": "值记忆化、依赖数组、计算属性、复杂计算优化、与useCallback对比、最佳实践"}, {"id": "use-ref", "name": "useRef", "file": "react/useRef.json", "description": "引用DOM元素、存储可变值、保存前一个值、与useEffect结合、实现命令式操作、避免的陷阱"}, {"id": "custom-hooks", "name": "自定义Hooks", "file": "react/custom-hooks.json", "description": "自定义Hooks基础、状态管理、副作用处理、性能优化、最佳实践"}, {"id": "composition", "name": "组件组合", "file": "react/composition.json", "description": "高阶组件、布局组件、容器组件、状态提升、组件拆分、组件复用、组件通信"}, {"id": "hoc", "name": "高阶组件(HOC)", "file": "react/hoc.json", "description": "高阶组件(HOC)基础、功能增强、逻辑复用、组件增强、性能优化、最佳实践"}, {"id": "render-props", "name": "Render Props模式", "file": "react/render-props.json", "description": "Render Props模式基础、功能增强、逻辑复用、组件增强、性能优化、最佳实践"}, {"id": "compound-components", "name": "复合组件", "file": "react/compound-components.json", "description": "复合组件基础、功能增强、逻辑复用、组件增强、性能优化、最佳实践"}, {"id": "controlled-vs-uncontrolled", "name": "受控与非受控组件", "file": "react/controlled-vs-uncontrolled.json", "description": "受控组件与非受控组件基础、功能增强、逻辑复用、组件增强、性能优化、最佳实践"}, {"id": "context-api", "name": "Context API", "file": "react/context-api.json", "description": "Context API基础、功能增强、逻辑复用、组件增强、性能优化、最佳实践"}, {"id": "suspense-error-boundaries", "name": "Suspense与错误边界", "file": "react/suspense-error-boundaries.json", "description": "Suspense与错误边界基础、功能增强、逻辑复用、组件增强、性能优化、最佳实践"}, {"id": "built-in-state-management", "name": "React内置状态管理", "file": "react/built-in-state-management.json", "description": "React内置状态管理基础、功能增强、逻辑复用、组件增强、性能优化、最佳实践"}, {"id": "context-useReducer", "name": "Context与useReducer", "file": "react/context-useReducer.json", "description": "Context与useReducer基础、功能增强、逻辑复用、组件增强、性能优化、最佳实践"}, {"id": "redux-basics", "name": "Redux基础", "file": "react/redux-basics.json", "description": "Redux基础、功能增强、逻辑复用、组件增强、性能优化、最佳实践"}, {"id": "redux-toolkit", "name": "Redux Toolkit", "file": "react/redux-toolkit.json", "description": "Redux Toolkit基础、功能增强、逻辑复用、组件增强、性能优化、最佳实践"}, {"id": "zustand", "name": "Zustand", "file": "react/zustand.json", "description": "Zustand基础、状态管理、异步、持久化、中间件、对比Redux、实际应用等"}, {"id": "recoil", "name": "Recoil", "file": "react/recoil.json", "description": "Recoil原子状态、选择器、状态共享、异步数据、持久化、与Context对比"}, {"id": "jotai-valtio", "name": "Jotai/Valtio", "file": "react/jotai-valtio.json", "description": "原子化状态、代理状态、衍生状态、异步状态、与其他状态库对比、适用场景分析"}, {"id": "react-router", "name": "React Router基础", "file": "react/react-router.json", "description": "安装和设置、路由类型、路由匹配、导航链接、路由渲染、编程式导航、URL参数"}, {"id": "nested-routes", "name": "嵌套路由", "file": "react/nested-routes.json", "description": "路由嵌套设计、嵌套路由配置、嵌套路由渲染、路由组合、共享布局、索引路由"}, {"id": "dynamic-routes", "name": "动态路由", "file": "react/dynamic-routes.json", "description": "路径参数、查询参数、可选参数、参数验证、通配符路由、路由优先级"}, {"id": "route-guard", "name": "路由守卫", "file": "react/route-guard.json", "description": "介绍如何在React Router中实现路由拦截、身份验证、权限控制、重定向、导航确认和路由生命周期等守卫功能，保障页面安全和用户体验。"}, {"id": "lazy-and-code-splitting", "name": "懒加载与代码分割", "file": "react/lazy-and-code-splitting.json", "description": "介绍如何使用React.lazy、Suspense、路由级代码分割、预加载、加载状态处理和错误处理等技术，实现高效的前端性能优化。"}, {"id": "react-memo", "name": "React.memo", "file": "react/react-memo.json", "description": "介绍React.memo的组件记忆化、自定义比较函数、适用场景、与shouldComponentUpdate对比、优化策略和过度优化问题，帮助理解函数组件性能优化。"}, {"id": "lazy-components", "name": "懒加载组件", "file": "react/lazy-components.json", "description": "详细介绍组件懒加载的动态导入、条件加载、预加载技术、加载边界、失败处理和用户体验优化，帮助构建高性能React应用。"}, {"id": "virtual-list", "name": "虚拟列表", "file": "react/virtual-list.json", "description": "介绍虚拟列表的实现原理、性能优化技巧、常见问题和解决方案，帮助构建高效的数据渲染和滚动体验。"}, {"id": "cache-strategies", "name": "缓存策略", "file": "react/cache-strategies.json", "description": "详细介绍数据缓存、请求缓存、组件缓存、计算结果缓存、缓存失效策略和持久化缓存，帮助理解和实现高效的前端缓存机制。"}, {"id": "avoid-unnecessary-renders", "name": "避免不必要的渲染", "file": "react/avoid-unnecessary-renders.json", "description": "详细介绍渲染优化原则、React DevTools分析、重渲染原因识别、状态设计优化、组件分割策略和批量更新，帮助开发高性能React应用。"}, {"id": "performance-tools", "name": "性能分析工具", "file": "react/performance-tools.json", "description": "详细介绍React Profiler、Chrome Performance面板、Lighthouse、网络请求分析、渲染性能分析和内存泄漏排查，帮助定位和优化前端性能瓶颈。"}, {"id": "unit-testing", "name": "单元测试", "file": "react/unit-testing.json", "description": "详细介绍测试环境设置、测试库选择、组件单元测试、Hooks测试、函数测试、异步代码测试和模拟外部依赖，帮助掌握React单元测试方法。"}, {"id": "component-testing", "name": "组件测试", "file": "react/component-testing.json", "description": "详细介绍组件渲染测试、交互测试、快照测试、事件测试、状态变化测试和属性验证，帮助掌握React组件测试方法。"}, {"id": "integration-testing", "name": "集成测试", "file": "react/integration-testing.json", "description": "详细介绍组件集成、路由测试、状态管理测试、API交互测试、用户流程测试和测试覆盖率，帮助掌握React集成测试方法。"}, {"id": "testing-library", "name": "测试库", "file": "react/testing-library.json", "description": "详细介绍测试库选择、DOM断言、交互测试、快照测试、异步测试和最佳实践，帮助掌握React测试方法。"}, {"id": "jest-rtl-integration", "name": "Jest与RTL的结合使用", "file": "react/jest-rtl-integration.json", "description": "详细介绍测试结构、模拟函数、快照测试、钩子使用、测试覆盖率报告和持续集成，帮助掌握Jest与React Testing Library的高效集成测试方法。"}, {"id": "nextjs", "name": "Next.js", "file": "react/nextjs.json", "description": "详细介绍Next.js的服务端渲染(SSR)、静态站点生成(SSG)、增量静态再生成(ISR)、API路由、文件系统路由、图像优化和布局页面，帮助掌握高效的React全栈开发。"}, {"id": "typescript-integration", "name": "TypeScript与React", "file": "react/typescript-integration.json", "description": "详细介绍类型定义、组件类型、Props类型、Hooks类型、事件类型、通用组件和类型断言，帮助掌握TypeScript与React的高效集成开发。"}, {"id": "react-query-swr", "name": "React Query/SWR", "file": "react/react-query-swr.json", "description": "详细介绍数据获取、缓存管理、请求状态、乐观更新、无限加载、轮询和重试、预取数据，帮助掌握高效的React数据管理。"}, {"id": "styled-components-emotion", "name": "Styled Components/Emotion", "file": "react/styled-components-emotion.json", "description": "详细介绍组件样式化、主题设计、动态样式、全局样式、样式复用、CSS-in-JS性能和服务端渲染支持，帮助掌握现代React样式开发。"}, {"id": "animation-libraries", "name": "Framer Motion/React Spring", "file": "react/animation-libraries.json", "description": "详细介绍动画基础、转场动画、手势交互、页面转场、微交互、性能优化和可访问性，帮助掌握现代React动画开发。"}, {"id": "devtools", "name": "React DevTools", "file": "react/devtools.json", "description": "详细介绍组件检查、Props和State查看、性能分析、调试Hooks、组件过滤、时间旅行调试和网络环境模拟，帮助掌握React调试与性能分析。"}]}