{"name": "Practical Cases", "trans": ["实战案例"], "methods": [{"name": "Build and Run a Node.js App", "trans": ["构建并运行一个Node.js应用"], "usage": {"syntax": "# Dockerfile示例\nFROM node:18\nWORKDIR /app\nCOPY . .\nRUN npm install\nCMD [\"npm\", \"start\"]\n# 构建与运行\ndocker build -t node-demo .\ndocker run -d -p 3000:3000 node-demo", "description": "通过编写Dockerfile，构建并运行一个Node.js应用，实现应用容器化。", "parameters": [{"name": "Dockerfile", "description": "定义Node.js应用的构建过程。"}, {"name": "docker build", "description": "构建镜像。"}, {"name": "docker run", "description": "运行容器。"}], "returnValue": "无返回值，成功运行Node.js应用容器。", "examples": [{"code": "FROM node:18\nWORKDIR /app\nCOPY . .\nRUN npm install\nCMD [\"npm\", \"start\"]\ndocker build -t node-demo .\ndocker run -d -p 3000:3000 node-demo", "explanation": "完整流程：编写Dockerfile、构建镜像并运行Node.js服务。"}]}}, {"name": "Multi-container Web+DB Project", "trans": ["多容器Web+DB项目"], "usage": {"syntax": "# docker-compose.yml示例\nversion: '3'\nservices:\n  web:\n    build: ./web\n    ports:\n      - \"8080:80\"\n  db:\n    image: mysql:5.7\n    environment:\n      MYSQL_ROOT_PASSWORD: example\n# 启动\ndocker-compose up -d", "description": "通过Compose编排Web服务和数据库，实现多容器协作部署。", "parameters": [{"name": "docker-compose.yml", "description": "定义多服务编排。"}, {"name": "docker-compose up", "description": "启动所有服务。"}], "returnValue": "无返回值，Web与DB容器协同运行。", "examples": [{"code": "version: '3'\nservices:\n  web:\n    build: ./web\n    ports:\n      - \"8080:80\"\n  db:\n    image: mysql:5.7\n    environment:\n      MYSQL_ROOT_PASSWORD: example\ndocker-compose up -d", "explanation": "用Compose同时启动Web和数据库服务。"}]}}, {"name": "Microservices with Compose", "trans": ["使用Compose管理微服务"], "usage": {"syntax": "# docker-compose.yml多服务示例\nservices:\n  api:\n    build: ./api\n    ports:\n      - \"5000:5000\"\n  frontend:\n    build: ./frontend\n    ports:\n      - \"3000:3000\"\n  redis:\n    image: redis:alpine\n# 启动\ndocker-compose up -d", "description": "通过Compose统一管理多个微服务及依赖，简化开发与部署流程。", "parameters": [{"name": "docker-compose.yml", "description": "定义微服务及依赖。"}, {"name": "docker-compose up", "description": "一键启动所有微服务。"}], "returnValue": "无返回值，微服务协同运行。", "examples": [{"code": "services:\n  api:\n    build: ./api\n    ports:\n      - \"5000:5000\"\n  frontend:\n    build: ./frontend\n    ports:\n      - \"3000:3000\"\n  redis:\n    image: redis:alpine\ndocker-compose up -d", "explanation": "Compose统一管理API、前端和Redis服务。"}]}}, {"name": "Dockerized Frontend & Backend Project", "trans": ["Docker化前后端分离项目"], "usage": {"syntax": "# 前端Dockerfile\nFROM node:18\nWORKDIR /app\nCOPY . .\nRUN npm install && npm run build\nCMD [\"npm\", \"start\"]\n# 后端Dockerfile\nFROM python:3.10\nWORKDIR /srv\nCOPY . .\nRUN pip install -r requirements.txt\nCMD [\"python\", \"app.py\"]\n# Compose编排\nservices:\n  frontend:\n    build: ./frontend\n    ports:\n      - \"3000:3000\"\n  backend:\n    build: ./backend\n    ports:\n      - \"5000:5000\"\ndocker-compose up -d", "description": "分别为前后端编写Dockerfile并用Compose编排，实现前后端分离项目的容器化部署。", "parameters": [{"name": "前端Dockerfile", "description": "定义前端构建与运行。"}, {"name": "后端Dockerfile", "description": "定义后端构建与运行。"}, {"name": "docker-compose.yml", "description": "统一编排前后端服务。"}], "returnValue": "无返回值，前后端分离项目容器化部署。", "examples": [{"code": "# 前端Dockerfile\nFROM node:18\nWORKDIR /app\nCOPY . .\nRUN npm install && npm run build\nCMD [\"npm\", \"start\"]\n# 后端Dockerfile\nFROM python:3.10\nWORKDIR /srv\nCOPY . .\nRUN pip install -r requirements.txt\nCMD [\"python\", \"app.py\"]\nservices:\n  frontend:\n    build: ./frontend\n    ports:\n      - \"3000:3000\"\n  backend:\n    build: ./backend\n    ports:\n      - \"5000:5000\"\ndocker-compose up -d", "explanation": "前后端分别容器化并用Compose统一编排。"}]}}, {"name": "CI/CD Automated Deployment Integration", "trans": ["集成CI/CD自动化部署"], "usage": {"syntax": "# 以GitHub Actions为例\nname: CI\non: [push]\njobs:\n  build:\n    runs-on: ubuntu-latest\n    steps:\n      - uses: actions/checkout@v2\n      - name: Build Docker image\n        run: docker build -t myapp:latest .\n      - name: Login to Docker Hub\n        uses: docker/login-action@v2\n        with:\n          username: ${{ secrets.DOCKER_USER }}\n          password: ${{ secrets.DOCKER_PASS }}\n      - name: Push image\n        run: docker push myapp:latest", "description": "通过CI工具实现自动化构建、推送和部署镜像，提升交付效率和可靠性。", "parameters": [{"name": "CI配置文件", "description": "如GitHub Actions、GitLab CI等。"}, {"name": "docker build/push", "description": "自动化构建与推送镜像。"}], "returnValue": "无返回值，自动化交付与部署。", "examples": [{"code": "name: CI\non: [push]\njobs:\n  build:\n    runs-on: ubuntu-latest\n    steps:\n      - uses: actions/checkout@v2\n      - name: Build Docker image\n        run: docker build -t myapp:latest .\n      - name: Login to Docker Hub\n        uses: docker/login-action@v2\n        with:\n          username: ${{ secrets.DOCKER_USER }}\n          password: ${{ secrets.DOCKER_PASS }}\n      - name: Push image\n        run: docker push myapp:latest", "explanation": "GitHub Actions自动化构建、推送和部署镜像。"}]}}, {"name": "Assignment: 实战案例实践", "trans": ["作业：实战案例实践"], "usage": {"syntax": "请完成以下任务：\n1. 编写Dockerfile并运行Node.js应用\n2. 用Compose编排Web+DB项目\n3. 管理微服务Compose项目\n4. 实现前后端分离项目容器化\n5. 配置CI/CD自动化部署", "description": "通过实际操作掌握Docker在真实项目中的应用，包括单体、微服务、前后端分离和CI/CD集成。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 1. Node.js应用容器化\nFROM node:18...\ndocker build ...\ndocker run ...\n# 2. Web+DB Compose\ndocker-compose.yml...\n# 3. 微服务Compose\ndocker-compose.yml...\n# 4. 前后端分离Compose\ndocker-compose.yml...\n# 5. CI/CD自动化\n.github/workflows/docker.yml...", "explanation": "依次完成各类实战案例的容器化与自动化部署。"}]}}]}