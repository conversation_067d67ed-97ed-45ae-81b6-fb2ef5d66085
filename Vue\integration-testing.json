{"name": "Integration Testing", "trans": ["集成测试"], "methods": [{"name": "Router Testing", "trans": ["路由测试"], "usage": {"syntax": "const router = createRouter({ ... })\nconst wrapper = mount(App, { global: { plugins: [router] } })\nawait router.push('/about')\nexpect(wrapper.html()).toContain('About')", "description": "通过挂载路由插件，模拟路由跳转，断言页面内容，确保路由集成正常。", "parameters": [{"name": "createRouter", "description": "创建路由实例"}, {"name": "push", "description": "路由跳转方法"}], "returnValue": "路由跳转后的页面内容断言结果", "examples": [{"code": "const router = createRouter({ ... })\nconst wrapper = mount(App, { global: { plugins: [router] } })\nawait router.push('/about')\nexpect(wrapper.html()).toContain('About')", "explanation": "断言跳转到/about后页面渲染About内容。"}]}}, {"name": "State Management Testing", "trans": ["状态管理测试"], "usage": {"syntax": "const store = createPinia()\nconst wrapper = mount(App, { global: { plugins: [store] } })\nstore.state.user = { name: '张三' }\nexpect(wrapper.html()).toContain('张三')", "description": "通过挂载状态管理插件，修改store数据，断言组件响应，确保状态管理集成正常。", "parameters": [{"name": "createPinia", "description": "创建Pinia状态管理实例"}, {"name": "state", "description": "全局状态对象"}], "returnValue": "状态变更后的页面内容断言结果", "examples": [{"code": "const store = createPinia()\nconst wrapper = mount(App, { global: { plugins: [store] } })\nstore.state.user = { name: '张三' }\nexpect(wrapper.html()).toContain('张三')", "explanation": "断言store变更后页面渲染新数据。"}]}}, {"name": "API Interaction Testing", "trans": ["API交互测试"], "usage": {"syntax": "jest.spyOn(api, 'getUser').mockResolvedValue({ name: '李四' })\nawait wrapper.vm.fetchUser()\nexpect(wrapper.vm.user.name).toBe('李四')", "description": "通过mock API请求，断言组件与后端交互逻辑，确保API集成正确。", "parameters": [{"name": "jest.spyOn", "description": "mock API方法"}, {"name": "mockResolvedValue", "description": "指定mock返回值"}], "returnValue": "API交互后的断言结果", "examples": [{"code": "jest.spyOn(api, 'getUser').mockResolvedValue({ name: '李四' })\nawait wrapper.vm.fetchUser()\nexpect(wrapper.vm.user.name).toBe('李四')", "explanation": "mock接口后断言组件数据更新。"}]}}, {"name": "User Flow Testing", "trans": ["用户流程测试"], "usage": {"syntax": "await wrapper.find('input').setValue('abc')\nawait wrapper.find('button').trigger('click')\nexpect(wrapper.html()).toContain('abc')", "description": "模拟用户完整操作流程，断言页面状态和内容，确保多步骤集成逻辑正确。", "parameters": [{"name": "setValue", "description": "设置输入值"}, {"name": "trigger", "description": "触发事件"}], "returnValue": "用户流程后的页面内容断言结果", "examples": [{"code": "await wrapper.find('input').setValue('abc')\nawait wrapper.find('button').trigger('click')\nexpect(wrapper.html()).toContain('abc')", "explanation": "模拟输入并提交后页面显示abc。"}]}}, {"name": "Test Coverage", "trans": ["测试覆盖率"], "usage": {"syntax": "npx jest --coverage", "description": "通过--coverage参数生成测试覆盖率报告，分析哪些代码未被测试，提升测试完整性。", "parameters": [{"name": "--coverage", "description": "生成覆盖率报告的参数"}], "returnValue": "测试覆盖率报告", "examples": [{"code": "npx jest --coverage", "explanation": "生成并查看测试覆盖率报告。"}]}}, {"name": "Integration Testing Assignment", "trans": ["集成测试练习"], "usage": {"syntax": "# 集成测试练习", "description": "完成以下练习，巩固集成测试相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 编写路由集成测试。\n2. 编写状态管理集成测试。\n3. mock API进行交互测试。\n4. 编写用户流程集成测试。\n5. 生成并分析测试覆盖率报告。", "explanation": "通过这些练习掌握Vue集成测试方法。"}]}}]}