{"name": "Unit Testing", "trans": ["单元测试"], "methods": [{"name": "Test Environment Setup", "trans": ["测试环境搭建"], "usage": {"syntax": "npm install --save-dev @vue/test-utils jest", "description": "通过安装@vue/test-utils、jest等依赖，配置jest.config.js，完成Vue项目的单元测试环境搭建。", "parameters": [{"name": "@vue/test-utils", "description": "Vue官方测试工具库"}, {"name": "jest", "description": "主流测试运行器"}], "returnValue": "可运行的单元测试环境", "examples": [{"code": "npm install --save-dev @vue/test-utils jest\n// 配置jest.config.js，指定transform和测试目录", "explanation": "完成依赖安装和基本配置。"}]}}, {"name": "Test Library Selection", "trans": ["测试库选择"], "usage": {"syntax": "import { mount } from '@vue/test-utils'\nimport { describe, it, expect } from 'vitest'", "description": "常用Vue测试库有@vue/test-utils、vitest、jest、mocha等，选择合适的库可提升测试效率。", "parameters": [{"name": "@vue/test-utils", "description": "组件测试辅助库"}, {"name": "vitest/jest/mocha", "description": "不同的测试运行器"}], "returnValue": "可用的测试API和断言方法", "examples": [{"code": "import { mount } from '@vue/test-utils'\nimport { describe, it, expect } from 'vitest'", "explanation": "组合使用测试工具和断言库。"}]}}, {"name": "Component Unit Testing", "trans": ["组件单元测试"], "usage": {"syntax": "const wrapper = mount(MyComponent)\nexpect(wrapper.text()).toContain('内容')", "description": "通过mount挂载组件，断言渲染结果、交互行为、props等，验证组件功能正确性。", "parameters": [{"name": "mount", "description": "挂载组件进行测试"}, {"name": "expect", "description": "断言测试结果"}], "returnValue": "测试通过或失败的断言结果", "examples": [{"code": "const wrapper = mount(MyComponent)\nexpect(wrapper.text()).toContain('Hello')", "explanation": "断言组件渲染内容包含Hello。"}]}}, {"name": "Composable Function Testing", "trans": ["组合函数测试"], "usage": {"syntax": "import { useCounter } from './useCounter'\nconst { count, inc } = useCounter()\ninc()\nexpect(count.value).toBe(1)", "description": "对自定义组合函数（composable）进行单元测试，验证其状态和行为。", "parameters": [{"name": "组合函数", "description": "被测试的自定义逻辑"}, {"name": "expect", "description": "断言测试结果"}], "returnValue": "测试通过或失败的断言结果", "examples": [{"code": "import { useCounter } from './useCounter'\nconst { count, inc } = useCounter()\ninc()\nexpect(count.value).toBe(1)", "explanation": "断言inc调用后count为1。"}]}}, {"name": "Async Code Testing", "trans": ["异步代码测试"], "usage": {"syntax": "await wrapper.find('button').trigger('click')\nawait nextTick()\nexpect(wrapper.text()).toContain('完成')", "description": "测试异步操作时需结合async/await、nextTick等，确保断言在异步完成后执行。", "parameters": [{"name": "trigger", "description": "触发异步事件"}, {"name": "nextTick", "description": "等待DOM更新"}], "returnValue": "异步操作后的断言结果", "examples": [{"code": "await wrapper.find('button').trigger('click')\nawait nextTick()\nexpect(wrapper.text()).toContain('完成')", "explanation": "等待异步更新后再断言内容。"}]}}, {"name": "Mock Dependencies", "trans": ["mock依赖"], "usage": {"syntax": "jest.mock('./api')\napi.getData.mockResolvedValue({ data: 1 })", "description": "通过mock依赖，隔离外部请求或模块，保证测试的独立性和可控性。", "parameters": [{"name": "jest.mock", "description": "mock外部模块"}, {"name": "mockResolvedValue", "description": "指定mock返回值"}], "returnValue": "被mock的依赖和可控的测试环境", "examples": [{"code": "jest.mock('./api')\napi.getData.mockResolvedValue({ data: 1 })", "explanation": "mock接口返回固定数据，便于测试。"}]}}, {"name": "Unit Testing Assignment", "trans": ["单元测试练习"], "usage": {"syntax": "# 单元测试练习", "description": "完成以下练习，巩固单元测试相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 配置Vue项目单元测试环境。\n2. 编写组件的基本单元测试。\n3. 测试自定义组合函数。\n4. 编写异步操作的测试。\n5. mock外部依赖进行隔离测试。", "explanation": "通过这些练习掌握Vue单元测试流程。"}]}}]}