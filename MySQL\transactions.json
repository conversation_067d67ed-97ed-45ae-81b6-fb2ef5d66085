{"name": "Transactions", "trans": ["事务基础"], "methods": [{"name": "ACID Properties", "trans": ["事务的ACID特性"], "usage": {"syntax": "-- 事务的ACID特性是MySQL InnoDB等事务型存储引擎的基本特性", "description": "事务的ACID特性是关系型数据库事务正确执行的四个基本要素：原子性(Atomicity)、一致性(Consistency)、隔离性(Isolation)和持久性(Durability)。这些特性确保了数据库在并发操作和系统故障时的数据完整性和可靠性。", "parameters": [], "returnValue": "无返回值，仅为概念说明", "examples": [{"code": "-- 原子性(Atomicity)：事务是不可分割的操作单元，要么全部执行，要么全部不执行\n-- 例如：转账操作必须确保资金从一个账户扣除并正确地添加到另一个账户\nSTART TRANSACTION;\nUPDATE accounts SET balance = balance - 1000 WHERE account_id = 1; -- 从账户1扣款\nUPDATE accounts SET balance = balance + 1000 WHERE account_id = 2; -- 向账户2存款\nCOMMIT; -- 如果任一语句失败，整个事务将回滚\n\n-- 一致性(Consistency)：事务执行前后，数据库必须保持一致状态\n-- 例如：转账前后，总金额应保持不变\nSTART TRANSACTION;\nSELECT SUM(balance) FROM accounts; -- 事务前检查总金额\nUPDATE accounts SET balance = balance - 1000 WHERE account_id = 1;\nUPDATE accounts SET balance = balance + 1000 WHERE account_id = 2;\nSELECT SUM(balance) FROM accounts; -- 事务后检查总金额，应与事务前相同\nCOMMIT;\n\n-- 隔离性(Isolation)：多个事务并发执行时，一个事务的执行不应影响其他事务\n-- MySQL提供了不同的隔离级别：READ UNCOMMITTED, READ COMMITTED, REPEATABLE READ, SERIALIZABLE\n-- 查看当前隔离级别\nSELECT @@transaction_isolation;\n\n-- 设置会话隔离级别为REPEATABLE READ\nSET SESSION TRANSACTION ISOLATION LEVEL REPEATABLE READ;\n\n-- 持久性(Durability)：一旦事务提交，其结果应永久保存在数据库中\n-- 即使系统崩溃，提交的事务也不会丢失\nSTART TRANSACTION;\nUPDATE important_data SET status = 'processed' WHERE id = 100;\nCOMMIT; -- 提交后，即使系统崩溃，更新也会保存", "explanation": "这个例子展示了事务的四个ACID特性。原子性确保转账操作要么完全成功，要么完全失败；一致性确保转账前后总金额不变；隔离性通过设置隔离级别来控制事务间的可见性；持久性确保提交的事务即使在系统崩溃后也不会丢失。"}]}}, {"name": "Start and Commit Transaction", "trans": ["开启与提交事务"], "usage": {"syntax": "START TRANSACTION;\n-- 或\nBEGIN;\n\n-- SQL语句...\n\nCOMMIT;", "description": "在MySQL中，使用START TRANSACTION或BEGIN语句开启一个事务，执行一系列SQL操作后，使用COMMIT语句提交事务，使所有更改永久生效。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "-- 开启事务并执行多个操作\nSTART TRANSACTION;\n\n-- 插入一条订单记录\nINSERT INTO orders (order_no, customer_id, order_date, total_amount)\nVALUES ('ORD-2023-001', 1001, CURDATE(), 1500.00);\n\n-- 获取刚插入的订单ID\nSET @order_id = LAST_INSERT_ID();\n\n-- 插入订单项目\nINSERT INTO order_items (order_id, product_id, quantity, price)\nVALUES (@order_id, 101, 2, 500.00);\n\nINSERT INTO order_items (order_id, product_id, quantity, price)\nVALUES (@order_id, 102, 1, 500.00);\n\n-- 更新产品库存\nUPDATE products SET stock = stock - 2 WHERE product_id = 101;\nUPDATE products SET stock = stock - 1 WHERE product_id = 102;\n\n-- 提交事务\nCOMMIT;\n\n-- 使用BEGIN开启事务的另一个例子\nBEGIN;\n\n-- 更新用户余额\nUPDATE users SET balance = balance - 1500.00 WHERE user_id = 1001;\n\n-- 记录支付历史\nINSERT INTO payment_history (user_id, amount, payment_date, description)\nVALUES (1001, 1500.00, NOW(), '订单支付：ORD-2023-001');\n\n-- 提交事务\nCOMMIT;", "explanation": "第一个例子展示了一个完整的订单处理事务，包括插入订单、订单项目和更新库存；第二个例子展示了一个支付处理事务，包括更新用户余额和记录支付历史。通过将这些操作放在一个事务中，确保了数据的一致性。"}]}}, {"name": "Rollback Transaction", "trans": ["回滚事务"], "usage": {"syntax": "START TRANSACTION;\n\n-- SQL语句...\n\n-- 如果出现错误或需要撤销\nROLLBACK;", "description": "ROLLBACK语句用于撤销当前事务中所做的所有更改，将数据恢复到事务开始前的状态。可以在发现错误或需要取消操作时使用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "-- 使用ROLLBACK手动撤销事务\nSTART TRANSACTION;\n\n-- 尝试更新账户余额\nUPDATE accounts SET balance = balance - 5000 WHERE account_id = 101;\n\n-- 检查余额是否足够\nSELECT balance INTO @current_balance FROM accounts WHERE account_id = 101;\n\nDELIMITER //\n-- 如果余额不足，回滚事务\nIF @current_balance < 0 THEN\n  ROLLBACK;\n  SELECT '余额不足，交易已取消' AS message;\nELSE\n  -- 继续执行其他操作\n  UPDATE accounts SET balance = balance + 5000 WHERE account_id = 102;\n  COMMIT;\n  SELECT '交易成功完成' AS message;\nEND IF;\n//\nDELIMITER ;\n\n-- 使用错误处理和ROLLBACK\nSTART TRANSACTION;\n\nDELIMITER //\nBEGIN\n  DECLARE EXIT HANDLER FOR SQLEXCEPTION\n  BEGIN\n    ROLLBACK;\n    SELECT '发生错误，事务已回滚' AS message;\n  END;\n  \n  -- 执行可能导致错误的操作\n  INSERT INTO orders (order_id, customer_id) VALUES (1, 999); -- 假设999是不存在的客户ID\n  \n  -- 如果上面的语句成功，继续执行\n  INSERT INTO order_items (order_id, product_id, quantity) VALUES (1, 101, 2);\n  \n  -- 提交事务\n  COMMIT;\n  SELECT '事务成功完成' AS message;\nEND;\n//\nDELIMITER ;", "explanation": "第一个例子展示了如何根据业务逻辑条件（账户余额是否足够）决定是提交还是回滚事务；第二个例子展示了如何使用错误处理程序在发生SQL异常时自动回滚事务。这些技术可以确保数据库在出现问题时保持一致状态。"}]}}, {"name": "Savepoint", "trans": ["保存点"], "usage": {"syntax": "START TRANSACTION;\n\n-- SQL语句...\n\nSAVEPOINT 保存点名称;\n\n-- 更多SQL语句...\n\n-- 回滚到保存点\nROLLBACK TO SAVEPOINT 保存点名称;\n\n-- 或提交整个事务\nCOMMIT;", "description": "保存点(Savepoint)允许在事务内创建一个标记点，可以选择回滚到该点而不是回滚整个事务。这对于在复杂事务中处理部分失败非常有用。", "parameters": [{"name": "保存点名称", "description": "保存点的唯一标识符名称"}], "returnValue": "无返回值", "examples": [{"code": "-- 使用保存点管理复杂事务\nSTART TRANSACTION;\n\n-- 第一部分：创建订单\nINSERT INTO orders (order_no, customer_id, order_date, total_amount)\nVALUES ('ORD-2023-002', 1002, CURDATE(), 2000.00);\n\nSET @order_id = LAST_INSERT_ID();\n\n-- 创建第一个保存点\nSAVEPOINT order_created;\n\n-- 第二部分：添加订单项目\nINSERT INTO order_items (order_id, product_id, quantity, price)\nVALUES (@order_id, 201, 1, 1200.00);\n\nINSERT INTO order_items (order_id, product_id, quantity, price)\nVALUES (@order_id, 202, 2, 400.00);\n\n-- 创建第二个保存点\nSAVEPOINT items_added;\n\n-- 第三部分：更新库存\n-- 检查产品201的库存\nSELECT stock INTO @stock_201 FROM products WHERE product_id = 201;\n\n-- 如果库存不足，回滚到添加订单项目之前\nIF @stock_201 < 1 THEN\n  ROLLBACK TO SAVEPOINT order_created;\n  \n  -- 插入不同的订单项目\n  INSERT INTO order_items (order_id, product_id, quantity, price)\n  VALUES (@order_id, 203, 1, 1200.00);\n  \n  -- 更新产品203的库存\n  UPDATE products SET stock = stock - 1 WHERE product_id = 203;\nELSE\n  -- 库存充足，更新产品201和202的库存\n  UPDATE products SET stock = stock - 1 WHERE product_id = 201;\n  UPDATE products SET stock = stock - 2 WHERE product_id = 202;\nEND IF;\n\n-- 提交整个事务\nCOMMIT;", "explanation": "这个例子展示了如何在一个复杂的订单处理事务中使用保存点。它首先创建订单，然后添加订单项目，最后检查库存并更新。如果发现某个产品库存不足，它会回滚到添加订单项目之前的保存点，然后插入替代产品。这样可以在不取消整个订单的情况下处理部分失败。"}]}}, {"name": "Transaction Isolation Levels", "trans": ["事务隔离级别"], "usage": {"syntax": "-- 查看当前隔离级别\nSELECT @@transaction_isolation;\n\n-- 设置会话隔离级别\nSET SESSION TRANSACTION ISOLATION LEVEL 隔离级别;\n\n-- 设置全局隔离级别\nSET GLOBAL TRANSACTION ISOLATION LEVEL 隔离级别;", "description": "MySQL支持四种事务隔离级别：READ UNCOMMITTED、READ COMMITTED、REPEATABLE READ(默认)和SERIALIZABLE。不同的隔离级别提供不同程度的数据一致性和并发性能。", "parameters": [{"name": "隔离级别", "description": "可选值：READ UNCOMMITTED, READ COMMITTED, REPEATABLE READ, SERIALIZABLE"}], "returnValue": "无返回值，仅为配置说明", "examples": [{"code": "-- 查看当前隔离级别\nSELECT @@transaction_isolation;\n\n-- 各隔离级别示例\n\n-- 1. READ UNCOMMITTED（读未提交）：最低隔离级别，允许脏读\nSET SESSION TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;\nSTART TRANSACTION;\n-- 可以读取其他事务未提交的数据\nSELECT * FROM accounts;\nCOMMIT;\n\n-- 2. READ COMMITTED（读已提交）：防止脏读，但允许不可重复读和幻读\nSET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED;\nSTART TRANSACTION;\n-- 只能读取其他事务已提交的数据\n-- 但在同一事务内多次读取可能会得到不同结果\nSELECT * FROM accounts;\n-- 如果此时其他事务提交了更改\nSELECT * FROM accounts; -- 可能与第一次查询结果不同\nCOMMIT;\n\n-- 3. REPEATABLE READ（可重复读）：MySQL默认隔离级别，防止脏读和不可重复读\nSET SESSION TRANSACTION ISOLATION LEVEL REPEATABLE READ;\nSTART TRANSACTION;\n-- 在同一事务内多次读取结果一致\nSELECT * FROM accounts;\n-- 即使其他事务提交了更改\nSELECT * FROM accounts; -- 仍与第一次查询结果相同\nCOMMIT;\n\n-- 4. SERIALIZABLE（串行化）：最高隔离级别，防止所有并发问题，但性能最低\nSET SESSION TRANSACTION ISOLATION LEVEL SERIALIZABLE;\nSTART TRANSACTION;\n-- 完全串行执行，其他事务的写操作会被阻塞\nSELECT * FROM accounts;\nCOMMIT;", "explanation": "这个例子展示了MySQL的四种事务隔离级别及其特点。READ UNCOMMITTED允许读取未提交数据；READ COMMITTED只允许读取已提交数据；REPEATABLE READ(MySQL默认)确保同一事务内多次读取结果一致；SERIALIZABLE提供最高的隔离性但性能最低。选择合适的隔离级别需要在数据一致性和性能之间权衡。"}]}}, {"name": "Transactions Assignment", "trans": ["事务基础练习"], "usage": {"syntax": "# 事务基础练习", "description": "完成以下练习，巩固MySQL事务的基本操作和概念。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n假设有一个简单的银行系统，包含以下表结构：\n\n```sql\n-- 账户表\nCREATE TABLE accounts (\n  account_id INT PRIMARY KEY,\n  account_name VARCHAR(100),\n  balance DECIMAL(15,2),\n  status VARCHAR(20)\n);\n\n-- 交易记录表\nCREATE TABLE transactions (\n  transaction_id INT PRIMARY KEY AUTO_INCREMENT,\n  from_account INT,\n  to_account INT,\n  amount DECIMAL(15,2),\n  transaction_date DATETIME,\n  status VARCHAR(20)\n);\n```\n\n请完成以下任务：\n\n1. 插入测试数据：\n```sql\nINSERT INTO accounts (account_id, account_name, balance, status)\nVALUES \n(1001, '张三', 10000.00, 'active'),\n(1002, '李四', 5000.00, 'active'),\n(1003, '王五', 8000.00, 'active');\n```\n\n2. 编写一个事务，实现从张三账户转账2000元到李四账户，确保：\n   - 转账金额正确扣除和添加\n   - 在交易记录表中记录此次交易\n   - 如果张三账户余额不足，整个交易应回滚\n\n3. 编写一个带有保存点的事务，实现以下操作：\n   - 从王五账户扣除3000元\n   - 创建一个保存点\n   - 将2000元转入张三账户\n   - 检查张三账户状态，如果不是'active'，回滚到保存点\n   - 将剩余1000元转入李四账户\n   - 提交事务\n\n4. 尝试在READ UNCOMMITTED和REPEATABLE READ两种隔离级别下执行相同的查询，观察并解释结果的差异。", "explanation": "通过这个练习，你将学习如何在实际场景中应用事务的ACID特性、开启和提交事务、使用回滚和保存点，以及理解不同隔离级别的影响。"}]}}]}