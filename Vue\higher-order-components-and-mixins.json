{"name": "Higher-Order Components and Mixins", "trans": ["高阶组件与混入"], "methods": [{"name": "Mixin", "trans": ["混入（mixin）"], "usage": {"syntax": "const myMixin = {\n  data() {...},\n  methods: {...},\n  created() {...}\n}\n\nexport default {\n  mixins: [myMixin]\n}", "description": "混入(mixin)是一种代码复用的技术，将组件的选项混合到组件中，包括data、methods、生命周期钩子等。", "parameters": [{"name": "mixins数组", "description": "包含要混入的混入对象"}], "returnValue": "无返回值，直接混合选项到组件中", "examples": [{"code": "// 定义混入对象\nconst myMixin = {\n  data() {\n    return {\n      message: 'hello'\n    }\n  },\n  methods: {\n    sayHello() {\n      console.log(this.message)\n    }\n  },\n  created() {\n    console.log('混入对象的钩子被调用')\n  }\n}\n\n// 使用混入\nexport default {\n  mixins: [myMixin],\n  created() {\n    console.log('组件钩子被调用')\n    this.sayHello() // 可使用混入的方法\n  }\n}", "explanation": "定义并使用混入，组件可访问混入的数据和方法。"}]}}, {"name": "Higher-Order Component", "trans": ["高阶组件（HOC）"], "usage": {"syntax": "function withState(Component) {\n  return {\n    render(h) {\n      return h(Component, {\n        props: { ... }\n      })\n    }\n  }\n}", "description": "高阶组件(HOC)是接收一个组件并返回一个新组件的函数，用于封装和复用组件逻辑，常用于权限控制、数据注入等场景。", "parameters": [{"name": "BaseComponent", "description": "被包装的基础组件"}], "returnValue": "返回增强后的新组件", "examples": [{"code": "// 高阶组件：添加登录检查\nfunction withAuth(Component) {\n  return {\n    props: Component.props,\n    data() {\n      return {\n        isLoggedIn: false\n      }\n    },\n    render(h) {\n      if (!this.isLoggedIn) {\n        return h('div', '请先登录')\n      }\n      return h(Component, {\n        props: this.$props,\n        on: this.$listeners\n      })\n    }\n  }\n}\n\n// 使用高阶组件\nconst AuthedComponent = withAuth(UserProfile)\nexport default AuthedComponent", "explanation": "创建检查登录状态的高阶组件，包装基础组件。"}]}}, {"name": "Extends vs Mixin Difference", "trans": ["extends与mixin区别"], "usage": {"syntax": "// extends\nexport default {\n  extends: BaseComponent\n}\n\n// mixins\nexport default {\n  mixins: [myMixin1, myMixin2]\n}", "description": "extends类似于类继承，继承单个组件；mixins可混入多个对象。extends优先级高于mixins，冲突时extends胜出。", "parameters": [{"name": "extends", "description": "只能继承一个组件"}, {"name": "mixins", "description": "可混入多个对象"}], "returnValue": "无返回值，合并选项到组件中", "examples": [{"code": "// 基础组件\nconst Base = {\n  methods: {\n    show() {\n      console.log('base')\n    }\n  }\n}\n\n// 混入对象\nconst myMixin = {\n  methods: {\n    show() {\n      console.log('mixin')\n    }\n  }\n}\n\n// extends优先级高于mixins\nexport default {\n  extends: Base,\n  mixins: [myMixin],\n  created() {\n    this.show() // 输出'base'\n  }\n}", "explanation": "当extends和mixins冲突时，extends优先级更高。"}]}}, {"name": "Composition API Alternatives", "trans": ["组合式API替代方案"], "usage": {"syntax": "// 不使用mixin，使用组合函数\nfunction useFeature() {\n  const state = reactive({...})\n  function doSomething() {...}\n  return { ...state, doSomething }\n}", "description": "Vue3组合式API通过自定义组合函数(composables)替代了mixins，解决了mixins命名冲突、数据来源不清晰等问题。", "parameters": [{"name": "组合函数", "description": "返回响应式状态和方法的函数"}], "returnValue": "返回可在组件中使用的状态和方法", "examples": [{"code": "// 混入的组合式API替代\nfunction useCounter() {\n  const count = ref(0)\n  function increment() {\n    count.value++\n  }\n  return { count, increment }\n}\n\n// 在组件中使用\nimport { useCounter } from './composables'\n\nexport default {\n  setup() {\n    // 明确数据来源，无命名冲突\n    const { count, increment } = useCounter()\n    return { count, increment }\n  }\n}", "explanation": "用组合函数替代混入，避免命名冲突，数据来源清晰。"}]}}, {"name": "Higher-Order Components and Mixins Assignment", "trans": ["高阶组件与混入练习"], "usage": {"syntax": "# 高阶组件与混入练习", "description": "完成以下练习，巩固高阶组件与混入相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 创建一个混入对象，包含日志功能。\n2. 实现一个添加权限检查的高阶组件。\n3. 比较extends和mixins在冲突时的表现。\n4. 将混入重构为组合式API的组合函数。", "explanation": "通过这些练习掌握代码复用的不同技术及其适用场景。"}]}}]}