{"name": "Datasource and JDBC", "trans": ["数据源与JDBC"], "methods": [{"name": "Datasource Configuration", "trans": ["数据源配置"], "usage": {"syntax": "application.properties中配置spring.datasource.*参数", "description": "Spring Boot通过spring.datasource相关配置自动创建数据源，支持HikariCP、Druid等多种连接池。", "parameters": [{"name": "spring.datasource.url", "description": "数据库连接URL"}, {"name": "spring.datasource.username", "description": "数据库用户名"}, {"name": "spring.datasource.password", "description": "数据库密码"}, {"name": "spring.datasource.driver-class-name", "description": "数据库驱动类名"}], "returnValue": "自动注入的DataSource实例", "examples": [{"code": "# application.properties\nspring.datasource.url=******************************************************************************************************************************************************************", "explanation": "配置好参数后，DataSource会自动注入到Spring容器。"}]}}, {"name": "Multiple Datasource Management", "trans": ["多数据源管理"], "usage": {"syntax": "定义多个DataSource Bean并用@Primary/@Qualifier区分", "description": "可通过自定义配置类注册多个数据源，结合@Primary/@Qualifier实现多数据源切换。", "parameters": [{"name": "@Bean", "description": "定义数据源Bean"}, {"name": "@Primary", "description": "标记主数据源"}, {"name": "@Qualifier", "description": "指定注入的数据源名称"}], "returnValue": "可切换的多个数据源实例", "examples": [{"code": "@Configuration\npublic class DataSourceConfig {\n    @Bean(name = \"ds1\")\n    @Primary\n    @ConfigurationProperties(prefix = \"spring.datasource.ds1\")\n    public DataSource ds1() {\n        return DataSourceBuilder.create().build();\n    }\n    @Bean(name = \"ds2\")\n    @ConfigurationProperties(prefix = \"spring.datasource.ds2\")\n    public DataSource ds2() {\n        return DataSourceBuilder.create().build();\n    }\n}\n// 使用@Qualifier(\"ds2\")注入指定数据源", "explanation": "通过@Primary和@Qualifier灵活切换多数据源。"}]}}, {"name": "JdbcTemplate Usage", "trans": ["JdbcTemplate用法"], "usage": {"syntax": "@Autowired JdbcTemplate，直接操作数据库", "description": "JdbcTemplate是Spring提供的JDBC操作工具类，简化SQL执行、参数绑定和结果映射。", "parameters": [{"name": "JdbcTemplate", "description": "自动注入的数据库操作对象"}, {"name": "SQL语句", "description": "要执行的SQL字符串"}], "returnValue": "SQL执行结果，如更新条数、查询结果集等", "examples": [{"code": "@RestController\npublic class UserController {\n    @Autowired\n    private JdbcTemplate jdbcTemplate;\n    @GetMapping(\"/count\")\n    public int count() {\n        return jdbcTemplate.queryForObject(\"SELECT COUNT(*) FROM user\", Integer.class);\n    }\n}", "explanation": "JdbcTemplate可直接执行SQL并返回结果。"}]}}, {"name": "Transaction Management", "trans": ["事务管理"], "usage": {"syntax": "@Transactional注解实现方法级事务管理", "description": "Spring Boot集成Spring事务管理，@Transactional注解可声明方法或类的事务边界，支持回滚和传播机制。", "parameters": [{"name": "@Transactional", "description": "声明事务范围"}, {"name": "rollbackFor", "description": "指定哪些异常触发回滚"}], "returnValue": "自动提交或回滚的数据库操作", "examples": [{"code": "@Service\npublic class UserService {\n    @Transactional(rollbackFor = Exception.class)\n    public void addUser(User user) {\n        // 插入用户\n        // 发生异常自动回滚\n    }\n}", "explanation": "@Transactional保证方法内数据库操作的原子性。"}]}}, {"name": "Datasource and JDBC Assignment", "trans": ["数据源与JDBC练习"], "usage": {"syntax": "# 数据源与JDBC练习", "description": "完成以下练习，巩固Spring Boot数据源与JDBC相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 配置MySQL数据源并测试连接。\n2. 配置两个数据源并切换。\n3. 用JdbcTemplate实现简单查询。\n4. 用@Transactional实现事务回滚。", "explanation": "通过这些练习掌握Spring Boot数据源与JDBC的核心用法。"}]}}]}