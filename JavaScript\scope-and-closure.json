{"name": "Scope and Closure", "trans": ["作用域与闭包"], "methods": [{"name": "Scope Chain", "trans": ["作用域链"], "usage": {"syntax": "let a = 1;\nfunction outer() {\n  let b = 2;\n  function inner() {\n    let c = 3;\n    console.log(a, b, c);\n  }\n  inner();\n}", "description": "作用域链由当前作用域及其所有父级作用域组成，变量查找会沿作用域链向上查找。", "parameters": [{"name": "变量名", "description": "在作用域链中查找的标识符。"}], "returnValue": "无返回值，影响变量可见性。", "examples": [{"code": "let x = 10;\nfunction foo() {\n  let y = 20;\n  function bar() {\n    let z = 30;\n    console.log(x, y, z); // 10 20 30\n  }\n  bar();\n}\nfoo();", "explanation": "变量x、y、z分别在不同作用域，通过作用域链访问。"}]}}, {"name": "Closure Principle & Usage", "trans": ["闭包原理与应用"], "usage": {"syntax": "function makeCounter() {\n  let count = 0;\n  return function() {\n    count++;\n    return count;\n  };\n}\nconst counter = makeCounter();\ncounter(); // 1\ncounter(); // 2", "description": "闭包是指函数可以访问其外部作用域的变量，常用于数据私有化和工厂函数。", "parameters": [{"name": "外部变量", "description": "被闭包捕获的变量。"}], "returnValue": "返回闭包函数或其执行结果。", "examples": [{"code": "function outer() {\n  let secret = '私有';\n  return function() {\n    return secret;\n  };\n}\nconst getSecret = outer();\nconsole.log(getSecret()); // '私有'", "explanation": "闭包实现数据私有化。"}]}}, {"name": "this Binding", "trans": ["this指向"], "usage": {"syntax": "const obj = {\n  name: '<PERSON>',\n  say() {\n    console.log(this.name);\n  }\n};\nobj.say();", "description": "this指向调用该函数的对象。箭头函数不绑定this，取决于外层作用域。", "parameters": [{"name": "this", "description": "当前执行上下文的对象。"}], "returnValue": "无返回值，影响函数内部this的指向。", "examples": [{"code": "const obj = {\n  name: '<PERSON>',\n  show: function() {\n    console.log(this.name);\n  }\n};\nobj.show(); // 'Alice'\nconst fn = obj.show;\nfn(); // undefined (非严格模式下为window)\nconst arrow = () => console.log(this);\narrow(); // 指向外层作用域", "explanation": "演示普通函数和箭头函数的this指向。"}]}}, {"name": "call/apply/bind", "trans": ["call/apply/bind"], "usage": {"syntax": "func.call(thisArg, arg1, arg2);\nfunc.apply(thisArg, [args]);\nconst bound = func.bind(thisArg);", "description": "call/apply/bind用于改变函数内部this指向。call和apply立即调用，bind返回新函数。", "parameters": [{"name": "thisArg", "description": "指定的this对象。"}, {"name": "arg1, arg2", "description": "传递给函数的参数。"}], "returnValue": "返回函数执行结果或绑定后的新函数。", "examples": [{"code": "function show() { console.log(this.value); }\nconst obj = { value: 42 };\nshow.call(obj); // 42\nshow.apply(obj); // 42\nconst bound = show.bind(obj);\nbound(); // 42", "explanation": "演示call/apply/bind的用法。"}]}}, {"name": "Garbage Collection & Memory Leak", "trans": ["垃圾回收与内存泄漏"], "usage": {"syntax": "let obj = { data: new Array(10000) };\nobj = null; // 解除引用，等待GC回收", "description": "JS采用自动垃圾回收机制，常见内存泄漏有全局变量、闭包未释放、定时器未清理等。", "parameters": [{"name": "对象引用", "description": "被引用的对象不会被回收。"}], "returnValue": "无返回值，影响内存管理。", "examples": [{"code": "let arr = [];\nfunction leak() {\n  arr.push(new Array(10000));\n}\nleak(); // arr未释放导致内存泄漏\narr = null; // 解除引用，GC可回收", "explanation": "演示内存泄漏与手动解除引用。"}]}}, {"name": "作业：作用域与闭包实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 理解作用域链变量查找\n// 2. 用闭包实现数据私有化\n// 3. 理解this指向和call/apply/bind\n// 4. 识别并避免内存泄漏", "description": "通过实践作用域、闭包、this、内存管理等，掌握JS作用域与闭包基础。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 作用域链变量查找\n// 2. 闭包私有化\n// 3. this与call/apply/bind\n// 4. 内存泄漏识别", "explanation": "作业提示，需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nfunction makeCounter() { let c = 0; return () => ++c; }\nconst counter = makeCounter();\nconsole.log(counter());\nconst obj = {v:1, show(){console.log(this.v);}};\nobj.show();\nobj.show.call({v:2});\nlet arr = []; function leak(){ arr.push(new Array(10000)); } leak(); arr = null;", "explanation": "涵盖作用域链、闭包、this、call/apply/bind、内存管理的正确实现。"}]}}]}