{"name": "HyperLogLog, Bitmap, Geo", "trans": ["基数统计、位图、地理位置类型"], "methods": [{"name": "Cardinality Estimation (HyperLogLog)", "trans": ["基数统计"], "usage": {"syntax": "PFADD key element [element ...]\nPFCOUNT key\nPFMERGE destkey sourcekey [sourcekey ...]", "description": "PFADD添加元素，PFCOUNT统计基数（去重后元素数量），PFMERGE合并多个HyperLogLog。适合大规模去重计数。", "parameters": [{"name": "key", "description": "HyperLogLog键名"}, {"name": "element", "description": "要统计的元素"}], "returnValue": "基数估算值", "examples": [{"code": "$ redis-cli pfadd uv user1 user2 user3\n$ redis-cli pfcount uv\n$ redis-cli pfadd uv2 user2 user4\n$ redis-cli pfmerge uv_total uv uv2\n$ redis-cli pfcount uv_total", "explanation": "演示了HyperLogLog的去重计数和合并。"}]}}, {"name": "Bitmap Operations", "trans": ["位图操作"], "usage": {"syntax": "SETBIT key offset value\nGETBIT key offset\nBITCOUNT key\nBITOP op destkey key [key ...]", "description": "SETBIT设置某一位，GETBIT获取某一位，BITCOUNT统计1的个数，BITOP进行位运算。适合签到、活跃统计等场景。", "parameters": [{"name": "key", "description": "位图键名"}, {"name": "offset", "description": "位偏移量"}, {"name": "value", "description": "0或1"}], "returnValue": "位操作结果或统计值", "examples": [{"code": "$ redis-cli setbit sign 1 1\n$ redis-cli setbit sign 2 1\n$ redis-cli getbit sign 1\n$ redis-cli bitcount sign", "explanation": "演示了位图的签到和统计。"}]}}, {"name": "Geospatial Data", "trans": ["地理位置数据"], "usage": {"syntax": "GEOADD key longitude latitude member\nGEOPOS key member [member ...]\nGEODIST key member1 member2 [unit]\nGEORADIUS key longitude latitude radius m|km|ft|mi", "description": "GEOADD添加地理位置，GEOPOS获取坐标，GEODIST计算距离，GEORADIUS查找附近成员。适合LBS、附近的人等场景。", "parameters": [{"name": "key", "description": "地理位置键名"}, {"name": "longitude", "description": "经度"}, {"name": "latitude", "description": "纬度"}, {"name": "member", "description": "成员名"}], "returnValue": "坐标、距离或成员列表", "examples": [{"code": "$ redis-cli geoadd city 116.40 39.90 Beijing 121.47 31.23 Shanghai\n$ redis-cli geopos city Beijing\n$ redis-cli geodist city Beijing Shanghai km\n$ redis-cli georadius city 116.40 39.90 1000 km", "explanation": "演示了地理位置的添加、查询和距离计算。"}]}}, {"name": "Practice: HyperLogLog/Bitmap/Geo类型练习", "trans": ["基数统计/位图/地理类型练习"], "usage": {"syntax": "# Redis基数统计/位图/地理类型练习", "description": "完成以下练习，掌握HyperLogLog、Bitmap、Geo的常用操作和应用场景。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用PFADD/PFCOUNT统计去重用户数。\n2. 用SETBIT/GETBIT/ BITCOUNT实现签到统计。\n3. 用GEOADD/GEORADIUS查找附近成员。", "explanation": "通过这些练习掌握三种特殊类型的常用命令和应用。"}, {"code": "# 正确实现示例\n$ redis-cli pfadd uv user1 user2 user3\n$ redis-cli pfcount uv\n$ redis-cli setbit sign 1 1\n$ redis-cli bitcount sign\n$ redis-cli geoadd city 116.40 39.90 Beijing\n$ redis-cli georadius city 116.40 39.90 1000 km", "explanation": "展示了练习的标准实现过程。"}]}}]}