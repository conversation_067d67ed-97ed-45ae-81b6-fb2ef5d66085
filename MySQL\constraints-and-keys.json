{"name": "Constraints and Keys", "trans": ["约束与主键"], "methods": [{"name": "Primary Key", "trans": ["主键（PRIMARY KEY）"], "usage": {"syntax": "列名 数据类型 PRIMARY KEY", "description": "主键是表中唯一标识每一行的列或列组合，每个表只能有一个主键，且主键值不能为NULL。", "parameters": [{"name": "列名", "description": "作为主键的字段名"}], "returnValue": "无返回值，约束被添加", "examples": [{"code": "CREATE TABLE students (\n  id INT PRIMARY KEY,\n  name VARCHAR(20)\n); -- 创建带主键的表", "explanation": "id字段为主键，不允许重复或为NULL。"}, {"code": "CREATE TABLE orders (\n  order_id INT,\n  product_id INT,\n  PRIMARY KEY (order_id, product_id)\n); -- 创建复合主键", "explanation": "order_id和product_id的组合作为主键，组合值不能重复。"}]}}, {"name": "Unique Constraint", "trans": ["唯一约束（UNIQUE）"], "usage": {"syntax": "列名 数据类型 UNIQUE", "description": "唯一约束确保列中的值不会重复，但允许多个NULL值。一个表可以有多个唯一约束。", "parameters": [{"name": "列名", "description": "要添加唯一约束的字段名"}], "returnValue": "无返回值，约束被添加", "examples": [{"code": "CREATE TABLE users (\n  id INT PRIMARY KEY,\n  email VARCHAR(50) UNIQUE\n); -- 创建带唯一约束的表", "explanation": "email字段不允许重复，但可以为NULL。"}]}}, {"name": "Not Null Constraint", "trans": ["非空约束（NOT NULL）"], "usage": {"syntax": "列名 数据类型 NOT NULL", "description": "非空约束确保列中不能存储NULL值，必须有具体的值。", "parameters": [{"name": "列名", "description": "要添加非空约束的字段名"}], "returnValue": "无返回值，约束被添加", "examples": [{"code": "CREATE TABLE employees (\n  id INT PRIMARY KEY,\n  name VARCHAR(20) NOT NULL,\n  dept VARCHAR(20)\n); -- 创建带非空约束的表", "explanation": "name字段不允许为NULL，必须提供值。"}]}}, {"name": "Default Value", "trans": ["默认值（DEFAULT）"], "usage": {"syntax": "列名 数据类型 DEFAULT 默认值", "description": "默认值约束在没有指定值时，为列提供一个默认值。", "parameters": [{"name": "列名", "description": "要设置默认值的字段名"}, {"name": "默认值", "description": "当没有提供值时使用的默认值"}], "returnValue": "无返回值，约束被添加", "examples": [{"code": "CREATE TABLE products (\n  id INT PRIMARY KEY,\n  name VA<PERSON>HAR(50),\n  price DECIMAL(10,2) DEFAULT 0.00,\n  status ENUM('active','inactive') DEFAULT 'active'\n); -- 创建带默认值的表", "explanation": "price默认为0.00，status默认为'active'。"}]}}, {"name": "Foreign Key Constraint", "trans": ["外键约束（FOREIGN KEY）"], "usage": {"syntax": "FOREIGN KEY (列名) REFERENCES 主表(主表列名)", "description": "外键约束用于关联两个表，确保引用的完整性。子表中的外键值必须在主表中存在或为NULL。", "parameters": [{"name": "列名", "description": "子表中的外键字段名"}, {"name": "主表", "description": "被引用的主表名"}, {"name": "主表列名", "description": "主表中被引用的字段名"}], "returnValue": "无返回值，约束被添加", "examples": [{"code": "CREATE TABLE orders (\n  id INT PRIMARY KEY,\n  customer_id INT,\n  FOREIGN KEY (customer_id) REFERENCES customers(id)\n); -- 创建带外键约束的表", "explanation": "orders表的customer_id必须存在于customers表的id中或为NULL。"}]}}, {"name": "Constraints and Keys Assignment", "trans": ["约束与主键练习"], "usage": {"syntax": "# 约束与主键练习", "description": "完成以下练习，巩固MySQL约束与主键的使用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 创建一个学生表，设置学号为主键，姓名为非空。\n2. 创建一个用户表，设置邮箱为唯一约束，注册时间默认为当前时间。\n3. 创建一个订单表，包含外键引用用户表的用户ID。", "explanation": "通过实际操作掌握主键、唯一约束、非空约束、默认值和外键约束的使用。"}]}}]}