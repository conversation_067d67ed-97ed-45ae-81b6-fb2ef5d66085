{"name": "Compose Basics", "trans": ["Compose基础"], "methods": [{"name": "Compose Installation", "trans": ["Compose安装"], "usage": {"syntax": "# Linux安装\ncurl -L https://github.com/docker/compose/releases/download/v2.27.1/docker-compose-$(uname -s)-$(uname -m) -o /usr/local/bin/docker-compose\nchmod +x /usr/local/bin/docker-compose\n# Windows/Mac推荐用Docker Desktop自带Compose", "description": "Docker Compose可独立安装，也可随Docker Desktop自动集成。Linux需手动下载二进制文件。", "parameters": [{"name": "curl", "description": "下载Compose二进制文件。"}, {"name": "chmod", "description": "赋予执行权限。"}], "returnValue": "无返回值，安装完成后可用docker-compose命令。", "examples": [{"code": "# Linux安装Compose\ncurl -L https://github.com/docker/compose/releases/download/v2.27.1/docker-compose-$(uname -s)-$(uname -m) -o /usr/local/bin/docker-compose\nchmod +x /usr/local/bin/docker-compose", "explanation": "下载并安装Compose到Linux系统。"}]}}, {"name": "docker-compose.yml Syntax", "trans": ["docker-compose.yml语法"], "usage": {"syntax": "version: '3'\nservices:\n  web:\n    image: nginx\n    ports:\n      - \"8080:80\"\n  db:\n    image: mysql\n    environment:\n      MYSQL_ROOT_PASSWORD: example", "description": "docker-compose.yml用于定义多容器服务，包括服务名、镜像、端口、环境变量等。", "parameters": [{"name": "version", "description": "Compose文件版本。"}, {"name": "services", "description": "服务定义块。"}, {"name": "image", "description": "服务使用的镜像。"}, {"name": "ports", "description": "端口映射。"}, {"name": "environment", "description": "环境变量配置。"}], "returnValue": "无返回值，定义多容器编排方案。", "examples": [{"code": "version: '3'\nservices:\n  web:\n    image: nginx\n    ports:\n      - \"8080:80\"\n  db:\n    image: mysql\n    environment:\n      MYSQL_ROOT_PASSWORD: example", "explanation": "定义了nginx和mysql两个服务，分别映射端口和设置环境变量。"}]}}, {"name": "Service Orchestration & Dependency", "trans": ["服务编排与依赖"], "usage": {"syntax": "services:\n  web:\n    depends_on:\n      - db", "description": "通过depends_on指定服务依赖关系，确保按顺序启动。", "parameters": [{"name": "depends_on", "description": "声明服务依赖，保证先后顺序。"}], "returnValue": "无返回值，服务按依赖顺序启动。", "examples": [{"code": "services:\n  web:\n    depends_on:\n      - db", "explanation": "web服务依赖db服务，启动时会先启动db。"}]}}, {"name": "Multi-container Management", "trans": ["多容器应用管理"], "usage": {"syntax": "docker-compose up [-d]\ndocker-compose down", "description": "通过docker-compose up/down一键启动和关闭多容器应用，-d参数后台运行。", "parameters": [{"name": "up", "description": "启动所有服务。"}, {"name": "down", "description": "关闭并清理所有服务。"}, {"name": "-d", "description": "后台运行。"}], "returnValue": "无返回值，批量管理多容器。", "examples": [{"code": "# 启动多容器\ndocker-compose up -d\n# 关闭并清理\ndocker-compose down", "explanation": "一键启动和关闭所有定义的服务。"}]}}, {"name": "Common Compose Commands", "trans": ["Compose常用命令"], "usage": {"syntax": "docker-compose ps\ndocker-compose logs\ndocker-compose restart <服务名>", "description": "常用命令包括查看服务状态、日志、重启等，便于日常运维。", "parameters": [{"name": "ps", "description": "查看服务状态。"}, {"name": "logs", "description": "查看服务日志。"}, {"name": "restart", "description": "重启指定服务。"}], "returnValue": "无返回值，辅助多容器管理。", "examples": [{"code": "# 查看服务状态\ndocker-compose ps\n# 查看日志\ndocker-compose logs\n# 重启web服务\ndocker-compose restart web", "explanation": "常用命令帮助管理和排查多容器服务。"}]}}, {"name": "Assignment: Compose基础实践", "trans": ["作业：Compose基础实践"], "usage": {"syntax": "请完成以下任务：\n1. 安装Compose工具\n2. 编写docker-compose.yml定义nginx和mysql服务\n3. 使用up命令启动服务\n4. 查看服务状态和日志\n5. 关闭并清理所有服务", "description": "通过实际操作掌握Compose安装、yml编写、多容器编排和常用命令。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 1. 安装Compose（Linux）\ncurl -L https://github.com/docker/compose/releases/download/v2.27.1/docker-compose-$(uname -s)-$(uname -m) -o /usr/local/bin/docker-compose\nchmod +x /usr/local/bin/docker-compose\n# 2. 编写yml\n# 3. 启动服务\ndocker-compose up -d\n# 4. 查看状态和日志\ndocker-compose ps\ndocker-compose logs\n# 5. 关闭服务\ndocker-compose down", "explanation": "依次完成Compose基础的核心实践。"}]}}]}