{"name": "Views", "trans": ["视图"], "methods": [{"name": "Create View", "trans": ["创建视图"], "usage": {"syntax": "CREATE VIEW 视图名 AS SELECT语句;", "description": "视图是基于SQL查询的虚拟表，它不存储实际数据，而是存储查询。创建视图可以简化复杂查询、提供安全层和隐藏表的复杂性。", "parameters": [{"name": "视图名", "description": "要创建的视图名称"}, {"name": "SELECT语句", "description": "用于定义视图内容的查询"}], "returnValue": "无返回值，视图被创建", "examples": [{"code": "-- 创建一个显示学生基本信息的视图\nCREATE VIEW student_info AS\nSELECT s.id, s.name, s.gender, c.class_name\nFROM students s\nJOIN classes c ON s.class_id = c.id;\n\n-- 创建一个显示学生成绩统计的视图\nCREATE VIEW student_scores AS\nSELECT s.name, AVG(sc.score) AS avg_score, MAX(sc.score) AS max_score\nFROM students s\nJOIN scores sc ON s.id = sc.student_id\nGROUP BY s.id, s.name;", "explanation": "第一个视图简化了学生和班级表的联合查询；第二个视图提供了学生成绩的统计信息。"}]}}, {"name": "Query View", "trans": ["查询视图"], "usage": {"syntax": "SELECT 列名 FROM 视图名 [WHERE 条件];", "description": "查询视图的方式与查询普通表相同，可以使用SELECT语句和各种子句。", "parameters": [{"name": "列名", "description": "要查询的视图字段"}, {"name": "视图名", "description": "要查询的视图名称"}, {"name": "条件", "description": "可选，筛选条件"}], "returnValue": "查询结果集", "examples": [{"code": "-- 查询所有学生信息\nSELECT * FROM student_info;\n\n-- 查询平均分大于80的学生\nSELECT name, avg_score FROM student_scores WHERE avg_score > 80;", "explanation": "第一个查询返回student_info视图中的所有数据；第二个查询从student_scores视图中筛选出平均分大于80的学生。"}]}}, {"name": "Update View", "trans": ["更新视图"], "usage": {"syntax": "ALTER VIEW 视图名 AS 新SELECT语句;", "description": "更新视图的定义，即修改视图基于的SELECT语句。某些视图也可以通过INSERT、UPDATE和DELETE语句更新基表数据，但需满足特定条件。", "parameters": [{"name": "视图名", "description": "要更新的视图名称"}, {"name": "新SELECT语句", "description": "视图的新定义查询"}], "returnValue": "无返回值，视图被更新", "examples": [{"code": "-- 修改学生信息视图，添加年龄字段\nALTER VIEW student_info AS\nSELECT s.id, s.name, s.gender, s.age, c.class_name\nFROM students s\nJOIN classes c ON s.class_id = c.id;\n\n-- 通过视图更新基表数据（仅适用于某些简单视图）\nUPDATE student_info SET gender = 'F' WHERE id = 1;", "explanation": "第一个例子修改了视图的定义，添加了age字段；第二个例子通过视图更新了基表的数据（仅当视图满足可更新条件时）。"}]}}, {"name": "Delete View", "trans": ["删除视图"], "usage": {"syntax": "DROP VIEW [IF EXISTS] 视图名;", "description": "删除一个已存在的视图。删除视图只会删除视图的定义，不会影响基表的数据。", "parameters": [{"name": "视图名", "description": "要删除的视图名称"}], "returnValue": "无返回值，视图被删除", "examples": [{"code": "-- 删除学生信息视图\nDROP VIEW student_info;\n\n-- 条件删除视图（如果存在）\nDROP VIEW IF EXISTS student_scores;", "explanation": "第一个例子直接删除视图；第二个例子在视图存在时才删除，避免出错。"}]}}, {"name": "View Characteristics", "trans": ["视图特性"], "usage": {"syntax": "CREATE [ALGORITHM = {UNDEFINED | MERGE | TEMPTABLE}] VIEW 视图名 AS SELECT语句 [WITH CHECK OPTION];", "description": "视图可以设置算法和检查选项等特性。ALGORITHM指定视图的处理方式，WITH CHECK OPTION确保通过视图的更新操作不会创建不可见的行。", "parameters": [{"name": "ALGORITHM", "description": "可选，指定视图的处理算法"}, {"name": "WITH CHECK OPTION", "description": "可选，确保通过视图更新的数据符合视图的WHERE条件"}], "returnValue": "无返回值，视图被创建", "examples": [{"code": "-- 创建一个带检查选项的视图\nCREATE VIEW adult_students AS\nSELECT * FROM students WHERE age >= 18\nWITH CHECK OPTION;\n\n-- 尝试通过视图插入不符合条件的数据\nINSERT INTO adult_students (id, name, age) VALUES (10, '小明', 16); -- 会报错，因为不满足age>=18", "explanation": "创建了一个仅显示成年学生的视图，WITH CHECK OPTION确保通过此视图插入或更新的数据必须满足age>=18的条件。"}]}}, {"name": "Views Assignment", "trans": ["视图练习"], "usage": {"syntax": "# 视图练习", "description": "完成以下练习，巩固MySQL视图的使用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n假设有以下表结构：\n\n-- 学生表\nCREATE TABLE students (\n  id INT PRIMARY KEY,\n  name VARCHAR(20),\n  gender CHAR(1),\n  class_id INT,\n  age INT\n);\n\n-- 班级表\nCREATE TABLE classes (\n  id INT PRIMARY KEY,\n  class_name VARCHAR(20)\n);\n\n-- 成绩表\nCREATE TABLE scores (\n  id INT PRIMARY KEY,\n  student_id INT,\n  subject VARCHAR(20),\n  score DECIMAL(5,2)\n);\n\n请完成以下操作：\n1. 创建一个名为class_students的视图，显示每个班级的名称和学生数量。\n2. 创建一个名为student_details的视图，显示学生的姓名、性别、班级名称和平均成绩。\n3. 查询student_details视图中平均成绩大于85的学生。\n4. 修改class_students视图，添加班级的平均年龄字段。\n5. 删除student_details视图。", "explanation": "通过实际操作掌握视图的创建、查询、更新和删除。"}]}}]}