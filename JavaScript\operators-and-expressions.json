{"name": "Operators and Expressions", "trans": ["运算符与表达式"], "methods": [{"name": "Arithmetic Operators", "trans": ["算术运算符"], "usage": {"syntax": "a + b; // 加法\na - b; // 减法\na * b; // 乘法\na / b; // 除法\na % b; // 取余\na ** b; // 幂运算\n++a; // 前置自增\na++; // 后置自增\n--a; // 前置自减\na--; // 后置自减", "description": "算术运算符用于数值计算，包括加、减、乘、除、取余、幂运算和自增自减。", "parameters": [{"name": "+", "description": "加法。"}, {"name": "-", "description": "减法。"}, {"name": "*", "description": "乘法。"}, {"name": "/", "description": "除法。"}, {"name": "%", "description": "取余。"}, {"name": "**", "description": "幂运算。"}, {"name": "++", "description": "自增。"}, {"name": "--", "description": "自减。"}], "returnValue": "返回计算结果。", "examples": [{"code": "let a = 5, b = 2;\nconsole.log(a + b); // 7\nconsole.log(a - b); // 3\nconsole.log(a * b); // 10\nconsole.log(a / b); // 2.5\nconsole.log(a % b); // 1\nconsole.log(a ** b); // 25\na++;\nconsole.log(a); // 6", "explanation": "演示常见算术运算符的用法。"}]}}, {"name": "Assignment Operators", "trans": ["赋值运算符"], "usage": {"syntax": "a = 1;\na += 2;\na -= 1;\na *= 3;\na /= 2;\na %= 2;\na **= 2;", "description": "赋值运算符用于给变量赋值或在原值基础上进行运算。", "parameters": [{"name": "=", "description": "赋值。"}, {"name": "+=", "description": "加后赋值。"}, {"name": "-=", "description": "减后赋值。"}, {"name": "*=", "description": "乘后赋值。"}, {"name": "/=", "description": "除后赋值。"}, {"name": "%=", "description": "取余后赋值。"}, {"name": "**=", "description": "幂后赋值。"}], "returnValue": "返回赋值后的新值。", "examples": [{"code": "let a = 3;\na += 2; // 5\na *= 2; // 10\na -= 4; // 6\na /= 3; // 2\na %= 2; // 0\na **= 3; // 0", "explanation": "演示赋值运算符的链式使用。"}]}}, {"name": "Comparison Operators", "trans": ["比较运算符"], "usage": {"syntax": "a > b;\na >= b;\na < b;\na <= b;\na == b;\na === b;\na != b;\na !== b;", "description": "比较运算符用于判断两个值的大小或相等性，返回布尔值。==和!=为宽松比较，===和!==为严格比较。", "parameters": [{"name": ">", "description": "大于。"}, {"name": ">=", "description": "大于等于。"}, {"name": "<", "description": "小于。"}, {"name": "<=", "description": "小于等于。"}, {"name": "==", "description": "宽松相等。"}, {"name": "!=", "description": "宽松不等。"}, {"name": "===", "description": "严格相等。"}, {"name": "!==", "description": "严格不等。"}], "returnValue": "返回布尔值。", "examples": [{"code": "console.log(5 > 3); // true\nconsole.log(2 == '2'); // true\nconsole.log(2 === '2'); // false\nconsole.log(3 !== 4); // true", "explanation": "演示常见比较运算符的用法。"}]}}, {"name": "Logical Operators", "trans": ["逻辑运算符"], "usage": {"syntax": "a && b; // 与\na || b; // 或\n!a; // 非", "description": "逻辑运算符用于布尔逻辑运算，包括与（&&）、或（||）、非（!）。", "parameters": [{"name": "&&", "description": "与，两个都为真才为真。"}, {"name": "||", "description": "或，有一个为真即为真。"}, {"name": "!", "description": "非，取反。"}], "returnValue": "返回布尔值或原始值。", "examples": [{"code": "console.log(true && false); // false\nconsole.log(false || true); // true\nconsole.log(!false); // true", "explanation": "演示逻辑运算符的基本用法。"}]}}, {"name": "Bitwise Operators", "trans": ["位运算符"], "usage": {"syntax": "a & b; // 按位与\na | b; // 按位或\na ^ b; // 按位异或\n~a; // 按位非\na << b; // 左移\na >> b; // 右移\na >>> b; // 无符号右移", "description": "位运算符对二进制位进行操作，常用于底层运算和性能优化。", "parameters": [{"name": "&", "description": "按位与。"}, {"name": "|", "description": "按位或。"}, {"name": "^", "description": "按位异或。"}, {"name": "~", "description": "按位非。"}, {"name": "<<", "description": "左移。"}, {"name": ">>", "description": "右移。"}, {"name": ">>>", "description": "无符号右移。"}], "returnValue": "返回计算结果。", "examples": [{"code": "console.log(5 & 3); // 1\nconsole.log(5 | 3); // 7\nconsole.log(5 ^ 3); // 6\nconsole.log(~5); // -6\nconsole.log(5 << 1); // 10\nconsole.log(5 >> 1); // 2\nconsole.log(5 >>> 1); // 2", "explanation": "演示常见位运算符的用法。"}]}}, {"name": "Ternary Operator", "trans": ["三元运算符"], "usage": {"syntax": "条件 ? 表达式1 : 表达式2;", "description": "三元运算符根据条件表达式的真假返回不同的值，是if...else的简写。", "parameters": [{"name": "条件", "description": "判断条件。"}, {"name": "表达式1", "description": "条件为真时返回。"}, {"name": "表达式2", "description": "条件为假时返回。"}], "returnValue": "返回表达式1或表达式2的值。", "examples": [{"code": "let age = 20;\nlet type = age >= 18 ? '成人' : '未成年';\nconsole.log(type); // '成人'", "explanation": "根据条件判断返回不同结果。"}]}}, {"name": "作业：运算符与表达式实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 使用各种运算符进行表达式计算\n// 2. 比较不同运算符的结果\n// 3. 用三元运算符实现条件判断", "description": "通过实践各种运算符的使用，掌握表达式计算和条件判断。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 算术、赋值、比较、逻辑、位运算符\n// 2. 三元运算符\n// 3. 输出结果", "explanation": "作业提示，需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nlet a = 5, b = 3;\nconsole.log(a + b, a - b, a * b, a / b, a % b, a ** b);\na += 2;\nconsole.log(a);\nconsole.log(a > b, a === b);\nconsole.log(a && b, a || 0, !b);\nconsole.log(a & b, a | b, a ^ b, ~a, a << 1, a >> 1, a >>> 1);\nlet res = a > b ? '大' : '小';\nconsole.log(res);", "explanation": "涵盖所有运算符和三元表达式的正确实现。"}]}}]}