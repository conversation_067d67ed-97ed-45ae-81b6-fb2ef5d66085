{"name": "useState", "trans": ["状态钩子"], "methods": [{"name": "State Declaration and Initialization", "trans": ["状态声明和初始化"], "usage": {"syntax": "const [state, setState] = useState(initialValue);", "description": "useState是React函数组件中用于声明状态的Hook。调用useState时传入初始值，返回一个由当前状态值和更新函数组成的数组。每次调用useState都会为组件创建一个独立的状态。", "parameters": [{"name": "initialValue", "description": "状态的初始值，可以是任意类型"}, {"name": "state", "description": "当前状态值，等于初始值或最新一次setState的值"}, {"name": "setState", "description": "用于更新状态的函数"}], "returnValue": "[state, setState]，分别为当前状态值和更新函数", "examples": [{"code": "// useState声明和初始化状态\nimport React, { useState } from 'react';\n\nfunction Counter() {\n  // 声明一个名为count的状态，初始值为0\n  const [count, setCount] = useState(0);\n  return (\n    <div>\n      <p>当前计数：{count}</p>\n      <button onClick={() => setCount(count + 1)}>加一</button>\n    </div>\n  );\n}\n\n// 声明多个状态\nfunction Profile() {\n  const [name, setName] = useState('Alice');\n  const [age, setAge] = useState(18);\n  return (\n    <div>\n      <p>姓名：{name}</p>\n      <p>年龄：{age}</p>\n      <button onClick={() => setAge(age + 1)}>长一岁</button>\n    </div>\n  );\n}", "explanation": "第一个例子展示了如何用useState声明和初始化一个计数状态。第二个例子展示了如何在同一个组件中声明多个独立的状态。"}]}}, {"name": "State Update", "trans": ["状态更新"], "usage": {"syntax": "setState(newValue);", "description": "通过setState函数可以更新useState声明的状态。setState会触发组件重新渲染，新的state值会传递到下一次渲染。可以直接传递新值，也可以传递一个函数实现基于前一个状态的更新。", "parameters": [{"name": "newValue", "description": "要设置的新状态值"}], "returnValue": "无返回值，状态更新后组件会自动重新渲染", "examples": [{"code": "// 状态更新示例\nimport React, { useState } from 'react';\n\nfunction Counter() {\n  const [count, setCount] = useState(0);\n  function increment() {\n    setCount(count + 1); // 直接赋新值\n  }\n  function reset() {\n    setCount(0); // 重置为初始值\n  }\n  return (\n    <div>\n      <p>当前计数：{count}</p>\n      <button onClick={increment}>加一</button>\n      <button onClick={reset}>重置</button>\n    </div>\n  );\n}", "explanation": "本例展示了如何通过setCount更新状态，包括递增和重置。每次调用setCount都会触发组件重新渲染。"}]}}, {"name": "Functional Updates", "trans": ["函数式更新"], "usage": {"syntax": "setState(prevState => newState);", "description": "当新状态依赖于前一个状态时，推荐使用函数式更新。setState接收一个函数，参数为前一个状态，返回新状态。这样可以避免异步更新带来的状态不一致问题。", "parameters": [{"name": "prevState", "description": "前一个状态值"}, {"name": "newState", "description": "基于前一个状态计算的新状态值"}], "returnValue": "无返回值，状态更新后组件会自动重新渲染", "examples": [{"code": "// 函数式更新示例\nimport React, { useState } from 'react';\n\nfunction Counter() {\n  const [count, setCount] = useState(0);\n  function increment() {\n    setCount(prev => prev + 1); // 推荐：基于前一个状态更新\n  }\n  function addTen() {\n    // 连续多次更新时，函数式写法可避免bug\n    for (let i = 0; i < 10; i++) {\n      setCount(prev => prev + 1);\n    }\n  }\n  return (\n    <div>\n      <p>当前计数：{count}</p>\n      <button onClick={increment}>加一</button>\n      <button onClick={addTen}>加十</button>\n    </div>\n  );\n}", "explanation": "本例展示了函数式更新的用法，适合依赖前一个状态的场景。addTen函数演示了多次连续更新时，函数式写法能保证每次都基于最新的状态。"}]}}, {"name": "Handling Object and Array State", "trans": ["对象和数组状态处理"], "usage": {"syntax": "// 更新对象\nsetState(prev => ({ ...prev, key: newValue }));\n// 更新数组\nsetState(prev => prev.concat(newItem));", "description": "useState管理对象或数组时，不能直接修改原对象/数组。应通过展开运算符（...）或数组方法创建新副本，再更新状态。这样才能触发React的重新渲染。", "parameters": [{"name": "prev", "description": "前一个对象或数组状态"}, {"name": "newValue/newItem", "description": "要更新的属性值或新增的数组项"}], "returnValue": "无返回值，返回新的对象或数组状态，触发组件渲染", "examples": [{"code": "// 对象状态更新\nimport React, { useState } from 'react';\n\nfunction ProfileEditor() {\n  const [profile, setProfile] = useState({ name: '<PERSON>', age: 18 });\n  function changeName() {\n    setProfile(prev => ({ ...prev, name: '<PERSON>' }));\n  }\n  return (\n    <div>\n      <p>姓名：{profile.name}</p>\n      <button onClick={changeName}>改名</button>\n    </div>\n  );\n}\n\n// 数组状态更新\nfunction TodoList() {\n  const [todos, setTodos] = useState(['学习', '锻炼']);\n  function addTodo() {\n    setTodos(prev => [...prev, '新任务']);\n  }\n  return (\n    <div>\n      <ul>{todos.map((todo, i) => <li key={i}>{todo}</li>)}</ul>\n      <button onClick={addTodo}>添加任务</button>\n    </div>\n  );\n}", "explanation": "本例展示了对象和数组状态的不可变更新写法。对象用展开运算符合并新属性，数组用扩展或concat方法添加新项。"}]}}, {"name": "Lazy Initialization", "trans": ["惰性初始化"], "usage": {"syntax": "const [state, setState] = useState(() => computeInitialValue());", "description": "当初始状态的计算较复杂或依赖外部数据时，可以传入一个函数给useState，实现惰性初始化。该函数只会在组件首次渲染时执行一次，提升性能。", "parameters": [{"name": "computeInitialValue", "description": "返回初始状态值的函数，只在首次渲染时调用一次"}], "returnValue": "[state, setState]，状态值和更新函数，初始值由函数返回", "examples": [{"code": "// 惰性初始化示例\nimport React, { useState } from 'react';\n\nfunction getInitialTodos() {\n  // 假设这里有复杂计算或从本地存储读取\n  return ['学习', '锻炼'];\n}\n\nfunction TodoList() {\n  const [todos, setTodos] = useState(() => getInitialTodos());\n  return (\n    <ul>{todos.map((todo, i) => <li key={i}>{todo}</li>)}</ul>\n  );\n}", "explanation": "本例展示了如何用函数实现useState的惰性初始化，适合初始值计算较复杂或需要读取外部数据的场景。"}]}}, {"name": "State Reset", "trans": ["状态重置"], "usage": {"syntax": "setState(initialValue);", "description": "可以通过setState将状态值重新设置为初始值，实现状态重置。常用于表单清空、计数器归零等场景。初始值可以是常量，也可以是惰性初始化函数的返回值。", "parameters": [{"name": "initialValue", "description": "要重置为的初始状态值"}], "returnValue": "无返回值，状态被重置为初始值", "examples": [{"code": "// 状态重置示例\nimport React, { useState } from 'react';\n\nfunction Counter() {\n  const [count, setCount] = useState(0);\n  function reset() {\n    setCount(0); // 重置为初始值\n  }\n  return (\n    <div>\n      <p>当前计数：{count}</p>\n      <button onClick={() => setCount(count + 1)}>加一</button>\n      <button onClick={reset}>重置</button>\n    </div>\n  );\n}\n\n// 表单重置\nfunction NameForm() {\n  const [name, setName] = useState('');\n  function clear() {\n    setName(''); // 清空输入\n  }\n  return (\n    <div>\n      <input value={name} onChange={e => setName(e.target.value)} />\n      <button onClick={clear}>清空</button>\n    </div>\n  );\n}", "explanation": "本例展示了如何通过setState将状态重置为初始值，适用于计数器归零、表单清空等场景。"}]}}, {"name": "Assignment: useState练习", "trans": ["作业：useState练习"], "usage": {"syntax": "// 作业要求见description", "description": "1. 创建一个计数器组件，支持加一、减一和重置。\n2. 创建一个表单，输入姓名和年龄，要求用一个对象state管理。\n3. 实现一个待办事项列表，支持添加、删除和重置，要求用数组state管理。\n4. （进阶）尝试用惰性初始化优化初始数据加载。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 示例：useState综合练习\nimport React, { useState } from 'react';\n\nfunction Counter() {\n  const [count, setCount] = useState(0);\n  return (\n    <div>\n      <button onClick={() => setCount(count - 1)}>-</button>\n      <span>{count}</span>\n      <button onClick={() => setCount(count + 1)}>+</button>\n      <button onClick={() => setCount(0)}>重置</button>\n    </div>\n  );\n}\n\nfunction ProfileForm() {\n  const [form, setForm] = useState({ name: '', age: '' });\n  function handleChange(e) {\n    setForm({ ...form, [e.target.name]: e.target.value });\n  }\n  return (\n    <div>\n      <input name=\"name\" value={form.name} onChange={handleChange} placeholder=\"姓名\" />\n      <input name=\"age\" value={form.age} onChange={handleChange} placeholder=\"年龄\" />\n      <p>当前：{JSON.stringify(form)}</p>\n    </div>\n  );\n}\n\nfunction TodoList() {\n  const [todos, setTodos] = useState(() => ['学习', '锻炼']);\n  const [input, setInput] = useState('');\n  function addTodo() {\n    if (input) setTodos(prev => [...prev, input]);\n    setInput('');\n  }\n  function removeTodo(index) {\n    setTodos(prev => prev.filter((_, i) => i !== index));\n  }\n  function reset() {\n    setTodos([]);\n  }\n  return (\n    <div>\n      <input value={input} onChange={e => setInput(e.target.value)} />\n      <button onClick={addTodo}>添加</button>\n      <button onClick={reset}>清空</button>\n      <ul>\n        {todos.map((todo, i) => (\n          <li key={i}>\n            {todo} <button onClick={() => removeTodo(i)}>删除</button>\n          </li>\n        ))}\n      </ul>\n    </div>\n  );\n}\n\n// 作业：\n// 1. 尝试将TodoList的初始数据用惰性初始化函数实现\n// 2. 尝试将ProfileForm的state拆分为多个useState实现对比", "explanation": "本作业要求你综合运用useState的声明、更新、对象/数组处理、惰性初始化和重置等知识点。通过动手实践，加深对React状态管理的理解。"}]}}]}