{"name": "Control Flow", "trans": ["流程控制"], "methods": [{"name": "if/else Statement", "trans": ["if/else条件语句"], "usage": {"syntax": "if (条件) {\n  // 代码块\n} else if (条件2) {\n  // 代码块2\n} else {\n  // 其他情况\n}", "description": "if/else用于条件判断，根据条件执行不同代码块。可嵌套使用。", "parameters": [{"name": "条件", "description": "判断条件，结果为true或false。"}, {"name": "代码块", "description": "条件成立时执行的语句。"}], "returnValue": "无返回值，控制程序流程。", "examples": [{"code": "let score = 85;\nif (score >= 90) {\n  console.log('优秀');\n} else if (score >= 60) {\n  console.log('及格');\n} else {\n  console.log('不及格');\n}", "explanation": "根据分数输出不同评价。"}]}}, {"name": "switch Statement", "trans": ["switch语句"], "usage": {"syntax": "switch (表达式) {\n  case 值1:\n    // 代码块1\n    break;\n  case 值2:\n    // 代码块2\n    break;\n  default:\n    // 默认代码块\n}", "description": "switch用于多分支选择，表达式与case值匹配时执行对应代码块。break用于跳出switch。", "parameters": [{"name": "表达式", "description": "用于匹配case的值。"}, {"name": "case", "description": "分支值。"}, {"name": "break", "description": "跳出switch语句。"}, {"name": "default", "description": "所有case不匹配时执行。"}], "returnValue": "无返回值，控制程序流程。", "examples": [{"code": "let day = 3;\nswitch(day) {\n  case 1:\n    console.log('星期一');\n    break;\n  case 2:\n    console.log('星期二');\n    break;\n  case 3:\n    console.log('星期三');\n    break;\n  default:\n    console.log('其他');\n}", "explanation": "根据数字输出星期几。"}]}}, {"name": "Loops (for/while/do...while)", "trans": ["循环(for/while/do...while)"], "usage": {"syntax": "for (初始化; 条件; 更新) {\n  // 代码块\n}\n\nwhile (条件) {\n  // 代码块\n}\n\ndo {\n  // 代码块\n} while (条件);", "description": "for、while、do...while用于重复执行代码块。for适合已知次数，while/do...while适合条件驱动。", "parameters": [{"name": "for", "description": "计数循环结构。"}, {"name": "while", "description": "条件循环结构。"}, {"name": "do...while", "description": "至少执行一次的条件循环。"}], "returnValue": "无返回值，重复执行代码块。", "examples": [{"code": "for (let i = 0; i < 3; i++) {\n  console.log(i);\n}\nlet j = 0;\nwhile (j < 3) {\n  console.log(j);\n  j++;\n}\nlet k = 0;\ndo {\n  console.log(k);\n  k++;\n} while (k < 3);", "explanation": "演示三种循环结构的基本用法。"}]}}, {"name": "break/continue Statement", "trans": ["break/continue语句"], "usage": {"syntax": "for (let i = 0; i < 5; i++) {\n  if (i === 2) break;\n  if (i === 1) continue;\n  // 代码块\n}", "description": "break用于跳出循环，continue用于跳过本次循环。常与for/while等循环配合使用。", "parameters": [{"name": "break", "description": "跳出当前循环。"}, {"name": "continue", "description": "跳过本次循环，继续下一次。"}], "returnValue": "无返回值，控制循环流程。", "examples": [{"code": "for (let i = 0; i < 5; i++) {\n  if (i === 2) break;\n  if (i === 1) continue;\n  console.log(i);\n}", "explanation": "i为1时跳过，i为2时跳出循环。"}]}}, {"name": "return Statement", "trans": ["return语句"], "usage": {"syntax": "function sum(a, b) {\n  return a + b;\n}", "description": "return用于函数内返回值并结束函数执行。后续代码不会被执行。", "parameters": [{"name": "返回值", "description": "函数返回的结果。"}], "returnValue": "返回指定值，结束函数。", "examples": [{"code": "function double(x) {\n  return x * 2;\n  // 下面的代码不会执行\n  console.log('不会输出');\n}\nconsole.log(double(5)); // 10", "explanation": "return返回结果并结束函数。"}]}}, {"name": "作业：流程控制实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 用if/else和switch实现条件分支\n// 2. 用for/while/do...while实现循环\n// 3. 用break/continue控制循环\n// 4. 用return返回函数结果", "description": "通过实践条件分支、循环、流程控制语句，掌握JS流程控制基础。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. if/else和switch\n// 2. for/while/do...while\n// 3. break/continue\n// 4. return", "explanation": "作业提示，需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nfunction judge(num) {\n  if (num > 0) return '正数';\n  else if (num < 0) return '负数';\n  else return '零';\n}\nfor (let i = 0; i < 3; i++) {\n  if (i === 1) continue;\n  if (i === 2) break;\n  console.log(i);\n}\nswitch(2) {\n  case 1: console.log('一'); break;\n  case 2: console.log('二'); break;\n  default: console.log('其他'); }", "explanation": "涵盖条件分支、循环、流程控制的正确实现。"}]}}]}