{"name": "Vuex Basics", "trans": ["Vuex基础"], "methods": [{"name": "Store Concept", "trans": ["Store概念"], "usage": {"syntax": "import { createStore } from 'vuex'\nconst store = createStore({ ... })", "description": "Store是Vuex的核心，集中管理全局状态，所有组件共享同一个store实例。", "parameters": [{"name": "createStore", "description": "创建store实例的方法"}], "returnValue": "全局唯一的store对象", "examples": [{"code": "import { createStore } from 'vuex'\nconst store = createStore({\n  state: { count: 0 }\n})", "explanation": "创建一个包含count状态的全局store。"}]}}, {"name": "State/Getter/Mutation/Action", "trans": ["State/Getter/Mutation/Action"], "usage": {"syntax": "const store = createStore({\n  state: { count: 0 },\n  getters: { double: state => state.count * 2 },\n  mutations: { inc(state) { state.count++ } },\n  actions: { asyncInc({ commit }) { setTimeout(() => commit('inc'), 1000) } }\n})", "description": "state存储数据，getter派生数据，mutation同步修改状态，action处理异步逻辑。", "parameters": [{"name": "state", "description": "全局状态对象"}, {"name": "getters", "description": "派生状态的计算属性"}, {"name": "mutations", "description": "同步修改状态的方法"}, {"name": "actions", "description": "异步操作和业务逻辑"}], "returnValue": "store对象，包含所有状态和方法", "examples": [{"code": "const store = createStore({\n  state: { count: 0 },\n  getters: { double: state => state.count * 2 },\n  mutations: { inc(state) { state.count++ } },\n  actions: { asyncInc({ commit }) { setTimeout(() => commit('inc'), 1000) } }\n})", "explanation": "定义完整的state、getter、mutation、action。"}]}}, {"name": "<PERSON><PERSON><PERSON>", "trans": ["模块化"], "usage": {"syntax": "const store = createStore({\n  modules: {\n    user: { state: { name: '' } },\n    cart: { state: { items: [] } }\n  }\n})", "description": "通过modules将store拆分为多个子模块，便于大型项目状态管理。每个模块有独立的state、mutation等。", "parameters": [{"name": "modules", "description": "包含多个子模块的对象"}], "returnValue": "模块化的store对象", "examples": [{"code": "const store = createStore({\n  modules: {\n    user: { state: { name: '' } },\n    cart: { state: { items: [] } }\n  }\n})", "explanation": "将store拆分为user和cart两个模块。"}]}}, {"name": "Strict Mode", "trans": ["严格模式"], "usage": {"syntax": "const store = createStore({ strict: true })", "description": "开启strict模式后，只有mutation能修改state，直接修改state会报错，便于调试和规范开发。", "parameters": [{"name": "strict", "description": "布尔值，是否启用严格模式"}], "returnValue": "启用严格模式的store对象", "examples": [{"code": "const store = createStore({ state: { count: 0 }, strict: true })", "explanation": "启用严格模式，防止直接修改state。"}]}}, {"name": "Plugin System", "trans": ["插件机制"], "usage": {"syntax": "const myPlugin = store => { store.subscribe((mutation, state) => { ... }) }\nconst store = createStore({ plugins: [myPlugin] })", "description": "插件机制可扩展store功能，如日志、持久化等。插件是接收store实例的函数。", "parameters": [{"name": "plugins", "description": "插件数组，每个插件是函数"}], "returnValue": "扩展功能的store对象", "examples": [{"code": "const logger = store => {\n  store.subscribe((mutation, state) => {\n    console.log(mutation.type)\n  })\n}\nconst store = createStore({ plugins: [logger] })", "explanation": "通过插件机制实现mutation日志记录。"}]}}, {"name": "Async Operation", "trans": ["异步操作"], "usage": {"syntax": "actions: { async fetchData({ commit }) { const data = await api(); commit('setData', data) } }", "description": "action支持异步操作，适合处理接口请求、定时任务等，异步完成后通过mutation修改state。", "parameters": [{"name": "actions", "description": "包含异步逻辑的方法对象"}], "returnValue": "异步操作后的状态更新", "examples": [{"code": "actions: {\n  async fetchData({ commit }) {\n    const data = await fetch('/api').then(res => res.json())\n    commit('setData', data)\n  }\n}", "explanation": "action中异步获取数据并提交mutation。"}]}}, {"name": "State Persistence", "trans": ["状态持久化"], "usage": {"syntax": "const store = createStore({ plugins: [createPersistedState()] })", "description": "通过插件如vuex-persistedstate可将state持久化到localStorage，实现页面刷新数据不丢失。", "parameters": [{"name": "createPersistedState", "description": "持久化插件函数"}], "returnValue": "持久化的store对象", "examples": [{"code": "import createPersistedState from 'vuex-persistedstate'\nconst store = createStore({ plugins: [createPersistedState()] })", "explanation": "使用vuex-persistedstate插件实现状态持久化。"}]}}, {"name": "Vuex Basics Assignment", "trans": ["Vuex基础练习"], "usage": {"syntax": "# Vuex基础练习", "description": "完成以下练习，巩固Vuex基础相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 创建一个包含state、getter、mutation、action的store。\n2. 将store拆分为user和cart模块。\n3. 启用严格模式并测试直接修改state。\n4. 编写插件记录所有mutation。\n5. 实现action中的异步请求。\n6. 用vuex-persistedstate实现状态持久化。", "explanation": "通过这些练习掌握Vuex核心用法和扩展能力。"}]}}]}