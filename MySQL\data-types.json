{"name": "Data Types", "trans": ["数据类型"], "methods": [{"name": "Numeric Types", "trans": ["数值类型"], "usage": {"syntax": "INT, FLOAT, DOUBLE, DECIMAL", "description": "用于存储整数和小数。常见类型有INT（整数）、FLOAT/DOUBLE（浮点数）、DECIMAL（高精度小数）。", "parameters": [{"name": "类型名", "description": "如INT、FLOAT、DECIMAL等"}, {"name": "长度/精度", "description": "可选，指定数值的长度和小数位数"}], "returnValue": "存储数值数据", "examples": [{"code": "CREATE TABLE scores (\n  id INT,\n  score DECIMAL(5,2)\n); -- 创建包含整数和高精度小数的表", "explanation": "id为整数，score为最多5位、2位小数的高精度分数。"}]}}, {"name": "String Types", "trans": ["字符串类型"], "usage": {"syntax": "CHAR(n), VARCHAR(n), TEXT", "description": "用于存储文本数据。CHAR定长，VARCHAR变长，TEXT适合大文本。", "parameters": [{"name": "类型名", "description": "如CHAR、VARCHAR、TEXT等"}, {"name": "长度n", "description": "最大字符数"}], "returnValue": "存储字符串数据", "examples": [{"code": "CREATE TABLE users (\n  name VARCHAR(20),\n  bio TEXT\n); -- 创建包含短文本和长文本的表", "explanation": "name为20字符以内的短文本，bio为长文本。"}]}}, {"name": "Date and Time Types", "trans": ["日期与时间类型"], "usage": {"syntax": "DATE, TIME, DATETIME, TIMESTAMP", "description": "用于存储日期、时间和时间戳。DATE仅日期，TIME仅时间，DATETIME和TIMESTAMP包含日期和时间。", "parameters": [{"name": "类型名", "description": "如DATE、TIME、DATETIME、TIMESTAMP"}], "returnValue": "存储日期和时间数据", "examples": [{"code": "CREATE TABLE events (\n  event_date DATE,\n  event_time TIME,\n  created_at TIMESTAMP\n); -- 创建包含日期、时间和时间戳的表", "explanation": "event_date存储日期，event_time存储时间，created_at自动记录插入时间。"}]}}, {"name": "Enum and Set Types", "trans": ["枚举与集合类型"], "usage": {"syntax": "ENUM('值1','值2',...), SET('值1','值2',...) ", "description": "ENUM用于限定单选值，SET用于多选值。常用于性别、状态等有限选项。", "parameters": [{"name": "类型名", "description": "ENUM或SET"}, {"name": "可选值", "description": "所有允许的取值列表"}], "returnValue": "存储枚举或集合值", "examples": [{"code": "CREATE TABLE products (\n  status ENUM('on','off'),\n  tags SET('hot','new','sale')\n); -- 创建包含枚举和集合的表", "explanation": "status只能取on或off，tags可多选。"}]}}, {"name": "NULL and Default Values", "trans": ["NULL与默认值"], "usage": {"syntax": "字段名 类型 [DEFAULT 默认值] [NULL|NOT NULL]", "description": "NULL表示字段可为空，NOT NULL表示不能为空。DEFAULT设置默认值。", "parameters": [{"name": "NULL/NOT NULL", "description": "是否允许为空"}, {"name": "DEFAULT", "description": "字段的默认值"}], "returnValue": "存储空值或默认值", "examples": [{"code": "CREATE TABLE users (\n  id INT,\n  nickname VARCHAR(20) DEFAULT '匿名',\n  email VARCHAR(50) NULL\n); -- 设置默认值和可空字段", "explanation": "nickname默认值为'匿名'，email字段可为空。"}]}}, {"name": "Data Types Assignment", "trans": ["数据类型练习"], "usage": {"syntax": "# 数据类型练习", "description": "完成以下练习，巩固MySQL常用数据类型的使用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 创建一个包含整数、浮点数、字符串、日期、枚举类型的表。\n2. 设置某字段的默认值和可空属性。\n3. 插入一条包含所有类型的测试数据。", "explanation": "通过实际操作掌握各种数据类型的定义和使用。"}]}}]}