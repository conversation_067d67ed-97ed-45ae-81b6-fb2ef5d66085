{"name": "Jest and RTL Integration", "trans": ["Jest与RTL的结合使用"], "methods": [{"name": "Test Structure", "trans": ["测试结构"], "usage": {"syntax": "describe('模块描述', () => {\n  test('用例描述', () => {\n    // 测试内容\n  });\n});", "description": "推荐使用describe分组测试用例，test/it定义单个测试，结构清晰，便于维护。", "parameters": [{"name": "describe", "description": "分组测试用例的块。"}, {"name": "test/it", "description": "单个测试用例。"}], "returnValue": "无返回值，测试通过即通过。", "examples": [{"code": "describe('按钮组件', () => {\n  test('渲染', () => {\n    // ...\n  });\n  test('点击', () => {\n    // ...\n  });\n});", "explanation": "通过describe分组相关测试用例。"}]}}, {"name": "Mock Functions", "trans": ["模拟函数"], "usage": {"syntax": "const fn = jest.fn();\nfn.mockReturnValue(值);\nexpect(fn).toHaveBeenCalled();", "description": "使用jest.fn()创建mock函数，便于断言调用、参数和返回值，常用于事件、API等依赖。", "parameters": [{"name": "jest.fn", "description": "创建mock函数。"}], "returnValue": "返回mock函数对象，可断言调用和结果。", "examples": [{"code": "const handleClick = jest.fn();\nrender(<button onClick={handleClick}>点我</button>);\nuserEvent.click(screen.getByRole('button'));\nexpect(handleClick).toHaveBeenCalled();", "explanation": "通过jest.fn模拟事件处理函数并断言调用。"}]}}, {"name": "Snapshot Testing", "trans": ["快照测试"], "usage": {"syntax": "import renderer from 'react-test-renderer';\nconst tree = renderer.create(<MyComponent />).toJSON();\nexpect(tree).toMatchSnapshot();", "description": "结合Jest和react-test-renderer生成组件快照，检测UI变更。", "parameters": [{"name": "renderer.create", "description": "生成组件快照。"}], "returnValue": "无返回值，快照一致即测试通过。", "examples": [{"code": "import renderer from 'react-test-renderer';\nfunction Hello() { return <div>Hi</div>; }\ntest('快照', () => {\n  const tree = renderer.create(<Hello />).toJSON();\n  expect(tree).toMatchSnapshot();\n});", "explanation": "生成组件快照并校验渲染输出。"}]}}, {"name": "Hooks Usage", "trans": ["钩子使用"], "usage": {"syntax": "import { renderHook, act } from '@testing-library/react';\nconst { result } = renderHook(() => useMyHook());", "description": "结合renderHook和act测试自定义Hook的状态和副作用。", "parameters": [{"name": "renderHook", "description": "执行Hook并获取结果。"}, {"name": "act", "description": "包裹状态变更操作。"}], "returnValue": "无返回值，断言通过即测试通过。", "examples": [{"code": "import { renderHook, act } from '@testing-library/react';\nfunction useCounter() {\n  const [count, setCount] = useState(0);\n  const inc = () => setCount(c => c + 1);\n  return { count, inc };\n}\ntest('hook递增', () => {\n  const { result } = renderHook(() => useCounter());\n  act(() => { result.current.inc(); });\n  expect(result.current.count).toBe(1);\n});", "explanation": "测试自定义Hook的状态变化。"}]}}, {"name": "Coverage Report", "trans": ["测试覆盖率报告"], "usage": {"syntax": "npm test -- --coverage\n// 查看coverage目录下的报告", "description": "Jest可自动生成测试覆盖率报告，分析测试完整性。", "parameters": [], "returnValue": "无返回值，报告生成在coverage目录。", "examples": [{"code": "npm test -- --coverage\n// 查看coverage/lcov-report/index.html", "explanation": "生成并查看测试覆盖率报告。"}]}}, {"name": "Continuous Integration", "trans": ["持续集成"], "usage": {"syntax": "// 以GitHub Actions为例\nname: CI\non: [push]\njobs:\n  test:\n    runs-on: ubuntu-latest\n    steps:\n      - uses: actions/checkout@v2\n      - name: 安装依赖\n        run: npm install\n      - name: 运行测试\n        run: npm test", "description": "持续集成通过CI工具自动运行测试，保障代码质量。常用GitHub Actions、GitLab CI等。", "parameters": [], "returnValue": "无返回值，CI平台展示测试结果。", "examples": [{"code": "# .github/workflows/ci.yml\nname: CI\non: [push]\njobs:\n  test:\n    runs-on: ubuntu-latest\n    steps:\n      - uses: actions/checkout@v2\n      - name: 安装依赖\n        run: npm install\n      - name: 运行测试\n        run: npm test", "explanation": "GitHub Actions自动化测试配置示例。"}]}}, {"name": "作业：Jest与RTL集成实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 设计合理测试结构\n// 2. 使用mock、快照、hook、覆盖率、CI等功能\n// 3. 综合应用Jest与RTL", "description": "通过实践Jest与RTL的结构、mock、快照、hook、覆盖率和CI，掌握高效测试集成方法。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 设计describe分组结构\n// 2. 使用mock、快照、hook测试\n// 3. 生成覆盖率和CI配置", "explanation": "作业提示，学生需结合本节内容完成实现。"}, {"code": "// 正确实现示例\ndescribe('计数器', () => {\n  test('渲染', () => {\n    // ...\n  });\n  test('点击', () => {\n    // ...\n  });\n});", "explanation": "Jest与RTL集成测试结构的正确实现示例。"}]}}]}