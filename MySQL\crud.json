{"name": "CRUD Operations", "trans": ["基本增删改查"], "methods": [{"name": "Insert Data (INSERT)", "trans": ["插入数据（INSERT）"], "usage": {"syntax": "INSERT INTO 表名 (列1, 列2, ...) VALUES (值1, 值2, ...);", "description": "向指定表中插入一条或多条新数据。", "parameters": [{"name": "表名", "description": "要插入数据的表名称"}, {"name": "列名", "description": "要插入的字段名列表"}, {"name": "值", "description": "对应字段的值"}], "returnValue": "无返回值，数据被插入", "examples": [{"code": "INSERT INTO students (id, name, gender, age) VALUES (1, '张三', 'M', 18); -- 插入一条学生数据", "explanation": "向students表插入一条新记录。"}]}}, {"name": "Select Data (SELECT)", "trans": ["查询数据（SELECT）"], "usage": {"syntax": "SELECT 列1, 列2 FROM 表名 [WHERE 条件];", "description": "从表中查询指定字段的数据，可以加条件筛选。", "parameters": [{"name": "列名", "description": "要查询的字段名列表"}, {"name": "表名", "description": "要查询的表名称"}, {"name": "条件", "description": "可选，筛选数据的条件"}], "returnValue": "查询结果集", "examples": [{"code": "SELECT name, age FROM students WHERE gender = 'M'; -- 查询所有男生的姓名和年龄", "explanation": "只查询gender为'M'的学生的name和age。"}]}}, {"name": "Update Data (UPDATE)", "trans": ["更新数据（UPDATE）"], "usage": {"syntax": "UPDATE 表名 SET 列1=值1, 列2=值2 WHERE 条件;", "description": "更新表中已存在的数据，通常需加WHERE条件限定范围。", "parameters": [{"name": "表名", "description": "要更新的表名称"}, {"name": "列名=值", "description": "要更新的字段及新值"}, {"name": "条件", "description": "筛选要更新的数据"}], "returnValue": "无返回值，数据被更新", "examples": [{"code": "UPDATE students SET age = 19 WHERE id = 1; -- 将id为1的学生年龄改为19", "explanation": "只更新id为1的学生的age字段。"}]}}, {"name": "Delete Data (DELETE)", "trans": ["删除数据（DELETE）"], "usage": {"syntax": "DELETE FROM 表名 WHERE 条件;", "description": "删除表中符合条件的数据，建议加WHERE条件避免全表删除。", "parameters": [{"name": "表名", "description": "要删除数据的表名称"}, {"name": "条件", "description": "筛选要删除的数据"}], "returnValue": "无返回值，数据被删除", "examples": [{"code": "DELETE FROM students WHERE age < 18; -- 删除所有未满18岁的学生", "explanation": "只删除age小于18的学生记录。"}]}}, {"name": "Conditional Query (WHERE)", "trans": ["条件查询（WHERE）"], "usage": {"syntax": "SELECT * FROM 表名 WHERE 条件;", "description": "通过WHERE子句筛选满足条件的数据，可用于SELECT、UPDATE、DELETE等语句。", "parameters": [{"name": "表名", "description": "要查询的表名称"}, {"name": "条件", "description": "筛选数据的表达式"}], "returnValue": "筛选后的结果集", "examples": [{"code": "SELECT * FROM students WHERE age >= 18; -- 查询所有18岁及以上的学生", "explanation": "只返回age大于等于18的学生。"}]}}, {"name": "Order and Limit (ORDER BY, LIMIT)", "trans": ["排序与限制（ORDER BY, LIMIT）"], "usage": {"syntax": "SELECT * FROM 表名 ORDER BY 列 [ASC|DESC] LIMIT 数量;", "description": "ORDER BY用于结果排序，LIMIT用于限制返回的行数。", "parameters": [{"name": "表名", "description": "要查询的表名称"}, {"name": "排序列", "description": "用于排序的字段名"}, {"name": "ASC|DESC", "description": "升序或降序"}, {"name": "LIMIT", "description": "返回的最大行数"}], "returnValue": "排序和限制后的结果集", "examples": [{"code": "SELECT * FROM students ORDER BY age DESC LIMIT 3; -- 查询年龄最大的3位学生", "explanation": "按age降序排列，只返回前3条记录。"}]}}, {"name": "CRUD Assignment", "trans": ["增删改查练习"], "usage": {"syntax": "# 增删改查练习", "description": "完成以下练习，巩固MySQL基本数据操作。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 向students表插入3条学生数据。\n2. 查询所有18岁及以上的学生。\n3. 将某位学生的年龄更新为20。\n4. 删除所有未满18岁的学生。\n5. 查询年龄最大的2位学生的姓名和年龄。", "explanation": "通过实际操作掌握数据的插入、查询、更新、删除、条件筛选和排序限制。"}]}}]}