{"name": "Lazy Loading and Code Splitting", "trans": ["懒加载与代码分割"], "methods": [{"name": "React.lazy()", "trans": ["组件懒加载"], "usage": {"syntax": "const LazyComponent = React.lazy(() => import('./LazyComponent'));", "description": "使用React.lazy实现组件的动态按需加载，减少初始包体积，提高应用性能。", "parameters": [{"name": "import函数", "description": "返回Promise的动态导入函数，需返回默认导出组件"}], "returnValue": "返回一个可用于渲染的懒加载组件。", "examples": [{"code": "// 懒加载组件示例\nimport React, { Suspense } from 'react';\n// 使用React.lazy动态导入组件\nconst LazyPage = React.lazy(() => import('./LazyPage'));\n\nfunction App() {\n  return (\n    <div>\n      {/* Suspense包裹懒加载组件，指定加载时的占位内容 */}\n      <Suspense fallback={<div>加载中...</div>}>\n        <LazyPage />\n      </Suspense>\n    </div>\n  );\n}", "explanation": "通过React.lazy和Suspense实现组件的懒加载，提升首屏加载速度。"}]}}, {"name": "Suspense Integration", "trans": ["Suspense集成"], "usage": {"syntax": "<Suspense fallback={<Loading />}>\n  <LazyComponent />\n</Suspense>", "description": "使用Suspense组件为懒加载组件提供加载状态占位符，支持嵌套和多层Suspense。", "parameters": [{"name": "fallback", "description": "加载时显示的占位内容，可以是任意React元素"}], "returnValue": "无返回值", "examples": [{"code": "// Suspense集成示例\nimport React, { Suspense } from 'react';\nconst LazyChart = React.lazy(() => import('./Chart'));\n\nfunction App() {\n  return (\n    <Suspense fallback={<div>图表加载中...</div>}>\n      <LazyChart />\n    </Suspense>\n  );\n}", "explanation": "通过Suspense的fallback属性优雅处理懒加载组件的加载状态。"}]}}, {"name": "Route-based Code Splitting", "trans": ["路由级代码分割"], "usage": {"syntax": "const Page = React.lazy(() => import('./Page'));\n<Route path=\"/page\" element={\n  <Suspense fallback={<Loading />}> <Page /> </Suspense>\n} />", "description": "结合React Router和React.lazy实现路由级别的代码分割，按需加载页面组件。", "parameters": [{"name": "path", "description": "路由路径"}, {"name": "element", "description": "使用Suspense包裹的懒加载组件"}], "returnValue": "无返回值", "examples": [{"code": "// 路由级代码分割示例\nimport React, { Suspense } from 'react';\nimport { BrowserRouter, Routes, Route } from 'react-router-dom';\nconst Home = React.lazy(() => import('./Home'));\nconst About = React.lazy(() => import('./About'));\n\nfunction App() {\n  return (\n    <BrowserRouter>\n      <Routes>\n        <Route path=\"/\" element={\n          <Suspense fallback={<div>首页加载中...</div>}>\n            <Home />\n          </Suspense>\n        } />\n        <Route path=\"/about\" element={\n          <Suspense fallback={<div>关于页加载中...</div>}>\n            <About />\n          </Suspense>\n        } />\n      </Routes>\n    </BrowserRouter>\n  );\n}", "explanation": "每个路由页面都通过React.lazy和Suspense实现独立的代码分割和加载占位。"}]}}, {"name": "Preloading Strategies", "trans": ["预加载策略"], "usage": {"syntax": "import('./Component') // 预加载组件", "description": "通过提前动态导入组件或资源，实现页面未访问前的预加载，提升用户体验。常见方式有鼠标悬停、提前加载等。", "parameters": [{"name": "import函数", "description": "动态导入目标组件的函数"}], "returnValue": "返回Promise，可用于手动触发加载。", "examples": [{"code": "// 预加载策略示例\n// 鼠标悬停时预加载About组件\nlet aboutPromise;\nfunction preloadAbout() {\n  if (!aboutPromise) {\n    aboutPromise = import('./About');\n  }\n}\n\nfunction Nav() {\n  return (\n    <nav>\n      <a href=\"/about\" onMouseEnter={preloadAbout}>关于我们</a>\n    </nav>\n  );\n}", "explanation": "通过onMouseEnter事件提前动态导入组件，实现页面预加载。"}]}}, {"name": "Loading State Handling", "trans": ["加载状态处理"], "usage": {"syntax": "<Suspense fallback={<Loading />}> <LazyComponent /> </Suspense>", "description": "通过Suspense的fallback属性自定义加载状态，支持骨架屏、动画等多种形式。", "parameters": [{"name": "fallback", "description": "加载时显示的占位内容"}], "returnValue": "无返回值", "examples": [{"code": "// 加载状态处理示例\nimport React, { Suspense } from 'react';\nconst LazyList = React.lazy(() => import('./List'));\n\nfunction App() {\n  return (\n    <Suspense fallback={<div className=\"skeleton\">列表加载中...</div>}>\n      <LazyList />\n    </Suspense>\n  );\n}", "explanation": "通过自定义fallback内容优化加载体验，如骨架屏、动画等。"}]}}, {"name": "Erro<PERSON>", "trans": ["错误处理"], "usage": {"syntax": "<ErrorBoundary> <Suspense fallback={<Loading />}> <LazyComponent /> </Suspense> </ErrorBoundary>", "description": "结合ErrorBoundary和Suspense处理懒加载组件的加载错误，防止页面崩溃。", "parameters": [{"name": "children", "description": "需要包裹的子组件"}], "returnValue": "无返回值", "examples": [{"code": "// 错误处理示例\nimport React, { Suspense } from 'react';\n// 错误边界组件\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { hasError: false };\n  }\n  static getDerivedStateFromError(error) {\n    return { hasError: true };\n  }\n  componentDidCatch(error, info) {\n    // 可以上报错误信息\n  }\n  render() {\n    if (this.state.hasError) {\n      return <div>加载失败，请稍后重试。</div>;\n    }\n    return this.props.children;\n  }\n}\nconst LazyChart = React.lazy(() => import('./Chart'));\n\nfunction App() {\n  return (\n    <ErrorBoundary>\n      <Suspense fallback={<div>图表加载中...</div>}>\n        <LazyChart />\n      </Suspense>\n    </ErrorBoundary>\n  );\n}", "explanation": "通过ErrorBoundary和Suspense组合，优雅处理懒加载组件的加载错误。"}]}}, {"name": "作业：实现懒加载与代码分割", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 使用React.lazy和Suspense实现页面组件的懒加载\n// 2. 路由级代码分割，每个页面独立加载\n// 3. 鼠标悬停时预加载About页面\n// 4. 使用ErrorBoundary处理加载错误", "description": "通过实践懒加载、代码分割、预加载和错误处理，掌握React性能优化关键技术。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 创建Lazy组件和ErrorBoundary\n// 2. 路由配置中使用Suspense包裹\n// 3. 鼠标悬停时动态import预加载\n// 4. 错误边界包裹懒加载组件", "explanation": "作业提示，学生需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nimport React, { Suspense } from 'react';\nimport { BrowserRouter, Routes, Route } from 'react-router-dom';\n// 错误边界组件\nclass ErrorBoundary extends React.Component {\n  constructor(props) { super(props); this.state = { hasError: false }; }\n  static getDerivedStateFromError(error) { return { hasError: true }; }\n  render() { if (this.state.hasError) { return <div>加载失败</div>; } return this.props.children; }\n}\nconst Home = React.lazy(() => import('./Home'));\nconst About = React.lazy(() => import('./About'));\nlet aboutPromise;\nfunction preloadAbout() { if (!aboutPromise) { aboutPromise = import('./About'); } }\nfunction App() {\n  return (\n    <BrowserRouter>\n      <nav>\n        <a href=\"/about\" onMouseEnter={preloadAbout}>关于我们</a>\n      </nav>\n      <ErrorBoundary>\n        <Routes>\n          <Route path=\"/\" element={\n            <Suspense fallback={<div>首页加载中...</div>}>\n              <Home />\n            </Suspense>\n          } />\n          <Route path=\"/about\" element={\n            <Suspense fallback={<div>关于页加载中...</div>}>\n              <About />\n            </Suspense>\n          } />\n        </Routes>\n      </ErrorBoundary>\n    </BrowserRouter>\n  );\n}\nexport default App;", "explanation": "完整实现了页面懒加载、路由级代码分割、鼠标悬停预加载和错误处理。"}]}}]}