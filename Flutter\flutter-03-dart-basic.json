{"name": "Dart Basic Syntax and Introduction", "trans": ["基本语法与Dart入门"], "methods": [{"name": "Main Function Structure", "trans": ["main函数结构"], "usage": {"syntax": "void main() { /* 入口代码 */ }", "description": "main函数是Dart程序的入口，所有Dart应用都从main函数开始执行。", "parameters": [{"name": "无参数", "description": "main函数通常无参数，也可接收List<String> args参数。"}], "returnValue": "无返回值", "examples": [{"code": "// main函数示例\nvoid main() {\n  // 程序入口，打印一行文本\n  print('Hello, Dar<PERSON>!');\n}", "explanation": "main函数是Dart程序的入口，运行时会输出一行文本。"}]}}, {"name": "Variables and Constants", "trans": ["变量与常量"], "usage": {"syntax": "var name = value; final name = value; const name = value;", "description": "Dart通过var、final、const声明变量和常量。var为变量，final和const为常量，const为编译时常量。", "parameters": [{"name": "var", "description": "声明变量，类型可自动推断。"}, {"name": "final", "description": "声明运行时常量，只能赋值一次。"}, {"name": "const", "description": "声明编译时常量，值在编译期确定。"}], "returnValue": "无返回值", "examples": [{"code": "// 变量与常量示例\nvoid main() {\n  var name = 'Dart'; // 变量\n  final age = 10;   // 运行时常量\n  const pi = 3.14;  // 编译时常量\n  print('name: \\${name}, age: \\${age}, pi: \\${pi}');\n}", "explanation": "演示了var、final、const的用法和区别。"}]}}, {"name": "Data Types", "trans": ["数据类型"], "usage": {"syntax": "int, double, String, bool, List, Map, Set, dynamic", "description": "Dart支持多种常用数据类型，包括数字、字符串、布尔、集合、映射等。dynamic可用于动态类型。", "parameters": [{"name": "int", "description": "整数类型"}, {"name": "double", "description": "浮点数类型"}, {"name": "String", "description": "字符串类型"}, {"name": "bool", "description": "布尔类型"}, {"name": "List", "description": "列表/数组类型"}, {"name": "Map", "description": "映射/字典类型"}, {"name": "Set", "description": "集合类型"}, {"name": "dynamic", "description": "动态类型，可赋任意值"}], "returnValue": "无返回值", "examples": [{"code": "// 数据类型示例\nvoid main() {\n  int a = 1;\n  double b = 2.5;\n  String s = 'hello';\n  bool flag = true;\n  List<int> nums = [1, 2, 3];\n  Map<String, int> scores = {'Tom': 90, '<PERSON>': 95};\n  Set<String> tags = {'A', 'B'};\n  dynamic any = '任意类型';\n  print('a: \\${a}, b: \\${b}, s: \\${s}, flag: \\${flag}');\n  print('nums: \\${nums}, scores: \\${scores}, tags: \\${tags}, any: \\${any}');\n}", "explanation": "演示了Dart常用数据类型的声明和使用。"}]}}, {"name": "Functions and Methods", "trans": ["函数与方法"], "usage": {"syntax": "返回类型 函数名(参数) { /* 代码 */ }", "description": "Dart支持顶层函数、匿名函数、箭头函数和类方法。函数可有返回值和参数。", "parameters": [{"name": "参数", "description": "函数的输入参数，可有默认值。"}, {"name": "返回值", "description": "函数的输出结果，可为任意类型。"}], "returnValue": "函数返回值类型，若无返回值用void。", "examples": [{"code": "// 函数与方法示例\nint add(int x, int y) {\n  return x + y;\n}\nvoid main() {\n  var sum = add(3, 5);\n  print('3 + 5 = \\${sum}');\n  // 匿名函数\n  var list = [1, 2, 3];\n  list.forEach((item) { print(item); });\n}", "explanation": "演示了普通函数、返回值、参数和匿名函数的用法。"}]}}, {"name": "Classes and Objects", "trans": ["类与对象"], "usage": {"syntax": "class 类名 { /* 属性和方法 */ }", "description": "Dart是面向对象语言，支持类、对象、继承、构造函数等。类用于封装属性和方法。", "parameters": [{"name": "属性", "description": "类的成员变量。"}, {"name": "方法", "description": "类的成员函数。"}], "returnValue": "无返回值，类通过实例化对象使用。", "examples": [{"code": "// 类与对象示例\nclass Person {\n  String name;\n  int age;\n  // 构造函数\n  Person(this.name, this.age);\n  void sayHello() {\n    print('Hello, my name is \\${name}, I am \\${age} years old.');\n  }\n}\nvoid main() {\n  var p = Person('Tom', 20);\n  p.sayHello();\n}", "explanation": "演示了类的定义、构造函数、属性和方法的使用。"}]}}, {"name": "Asynchronous Programming", "trans": ["异步编程（async/await、Future、Stream）"], "usage": {"syntax": "Future/async/await/Stream的用法", "description": "Dart通过Future和async/await实现异步操作，Stream用于处理异步数据流。", "parameters": [{"name": "Future", "description": "表示一个延迟计算的结果。"}, {"name": "async/await", "description": "用于简化异步代码编写。"}, {"name": "Stream", "description": "用于处理一系列异步事件。"}], "returnValue": "Future或Stream对象，异步结果通过then/await/监听获取。", "examples": [{"code": "// 异步编程示例\nFuture<String> fetchData() async {\n  await Future.delayed(Duration(seconds: 1)); // 模拟耗时操作\n  return '数据加载完成';\n}\nvoid main() async {\n  print('开始加载');\n  var data = await fetchData();\n  print(data);\n  // Stream示例\n  Stream<int> stream = Stream.fromIterable([1, 2, 3]);\n  await for (var value in stream) {\n    print('Stream值: \\${value}');\n  }\n}", "explanation": "演示了Future、async/await和Stream的基本用法。"}]}}, {"name": "Assignment", "trans": ["作业：用Dart实现一个类，包含属性、方法，并演示异步方法的调用。"], "usage": {"syntax": "无", "description": "请用Dart实现一个Person类，包含name和age属性，sayHello方法，以及一个异步方法fetchInfo，模拟获取信息并输出。main函数中创建对象并调用所有方法。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 作业参考实现\nclass Person {\n  String name;\n  int age;\n  Person(this.name, this.age);\n  void sayHello() {\n    print('Hello, my name is \\${name}, I am \\${age} years old.');\n  }\n  Future<void> fetchInfo() async {\n    await Future.delayed(Duration(seconds: 1));\n    print('信息获取完成');\n  }\n}\nvoid main() async {\n  var p = Person('Alice', 18);\n  p.say<PERSON>ello();\n  await p.fetchInfo();\n}", "explanation": "作业要求实现类、属性、方法和异步方法，并在main中调用。"}]}}]}