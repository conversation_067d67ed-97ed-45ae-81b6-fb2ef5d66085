{"name": "CI/CD and Deployment", "trans": ["持续集成与部署"], "methods": [{"name": "Git and Version Control", "trans": ["Git与版本管理"], "usage": {"syntax": "git init\ngit add .\ngit commit -m 'msg'\ngit push", "description": "Git是主流的版本控制工具，支持代码管理、协作开发、分支合并等。", "parameters": [{"name": "git init", "description": "初始化仓库"}, {"name": "git add", "description": "添加文件到暂存区"}, {"name": "git commit", "description": "提交更改"}, {"name": "git push", "description": "推送到远程仓库"}], "returnValue": "操作结果或日志", "examples": [{"code": "git init\ngit add .\ngit commit -m 'init'\ngit push origin main", "explanation": "演示了Git的基本操作流程。"}]}}, {"name": "Automated Testing", "trans": ["自动化测试"], "usage": {"syntax": "pytest\nunittest\npytest --cov", "description": "自动化测试可通过pytest、unittest等工具批量执行测试用例，保障代码质量。", "parameters": [{"name": "pytest", "description": "第三方测试框架"}, {"name": "unittest", "description": "内置测试框架"}], "returnValue": "测试结果报告", "examples": [{"code": "pytest\npytest --cov", "explanation": "演示了pytest自动化测试和覆盖率统计。"}]}}, {"name": "CI/CD Workflow (GitHub Actions, Travis CI, etc.)", "trans": ["CI/CD流程（GitHub Actions、Travis CI等）"], "usage": {"syntax": ".github/workflows/ci.yml\n.travis.yml", "description": "CI/CD工具可自动化测试、构建、部署流程，常用GitHub Actions、Travis CI等。", "parameters": [{"name": "ci.yml", "description": "GitHub Actions工作流配置文件"}, {"name": ".travis.yml", "description": "Travis CI配置文件"}], "returnValue": "自动化执行结果", "examples": [{"code": "# .github/workflows/ci.yml\nname: CI\non: [push]\njobs:\n  build:\n    runs-on: ubuntu-latest\n    steps:\n      - uses: actions/checkout@v2\n      - name: Set up Python\n        uses: actions/setup-python@v2\n        with:\n          python-version: '3.8'\n      - name: Install dependencies\n        run: pip install -r requirements.txt\n      - name: Run tests\n        run: pytest", "explanation": "演示了GitHub Actions的CI配置。"}]}}, {"name": "Containerization and Docker", "trans": ["容器化与Docker"], "usage": {"syntax": "Dockerfile\ndocker build -t image .\ndocker run image", "description": "Docker可将应用及其依赖打包为容器，便于部署和迁移。", "parameters": [{"name": "Dockerfile", "description": "容器构建配置文件"}, {"name": "docker build", "description": "构建镜像命令"}, {"name": "docker run", "description": "运行容器命令"}], "returnValue": "镜像或容器运行结果", "examples": [{"code": "# Dockerfile\nFROM python:3.9\nCOPY . /app\nWORKDIR /app\nRUN pip install -r requirements.txt\nCMD [\"python\", \"main.py\"]\n# 构建与运行\ndocker build -t myapp .\ndocker run myapp", "explanation": "演示了Docker容器化的基本流程。"}]}}, {"name": "Cloud Deployment and Server Ops", "trans": ["云部署与服务器运维"], "usage": {"syntax": "scp file user@host:/path\nssh user@host\nsystemctl restart 服务", "description": "云服务器部署常用scp上传、ssh远程、systemctl管理服务等命令，适用于多种云平台。", "parameters": [{"name": "scp", "description": "文件上传命令"}, {"name": "ssh", "description": "远程登录命令"}, {"name": "systemctl", "description": "服务管理命令"}], "returnValue": "操作结果或日志", "examples": [{"code": "scp main.py user@server:/app\nssh user@server\ncd /app\nsystemctl restart myapp", "explanation": "演示了云部署与服务器运维的常用命令。"}]}}, {"name": "CI/CD and Deployment Assignment", "trans": ["持续集成与部署练习"], "usage": {"syntax": "# 持续集成与部署练习", "description": "完成以下练习，巩固CI/CD与部署相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用git管理项目代码。\n2. 配置GitHub Actions自动化测试。\n3. 编写Dockerfile并运行容器。\n4. 用scp和ssh部署到云服务器。", "explanation": "练习要求动手实践git、CI/CD、Docker、云部署等。"}]}}]}