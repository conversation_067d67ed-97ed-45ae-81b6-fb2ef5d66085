{"name": "Stream", "trans": ["流"], "methods": [{"name": "Stream Definition and Listening", "trans": ["Stream定义与监听"], "usage": {"syntax": "// 创建Stream\nStream<类型> 变量名 = Stream<类型>.periodic(Duration(seconds: 间隔), (计数器) => 值);\nStream<类型> 变量名 = Stream<类型>.fromIterable(可迭代对象);\nStreamController<类型> 控制器 = StreamController<类型>();\nStream<类型> 变量名 = 控制器.stream;\n\n// 监听Stream\n变量名.listen((数据) {\n  // 处理数据\n}, onError: (错误) {\n  // 处理错误\n}, onDone: () {\n  // 流结束时的处理\n}, cancelOnError: false);\n\n// 使用await for监听Stream（仅在异步函数中）\nawait for (var 数据 in 变量名) {\n  // 处理数据\n}", "description": "Stream是Dart中表示异步数据序列的对象，用于处理连续的异步事件，如用户输入、网络响应、文件读取等。与Future不同，Future只能提供单个异步结果，而Stream可以提供多个异步结果。\n\nDart提供了多种创建Stream的方式：\n- 使用Stream.periodic创建周期性生成数据的流\n- 使用Stream.fromIterable从可迭代对象创建流\n- 使用StreamController手动控制流的数据发射\n- 使用async*函数和yield语句创建流\n\nStream有两种基本类型：\n- 单订阅流（Single-subscription）：只能被监听一次，适合表示连续的事件序列，如文件读取\n- 广播流（Broadcast）：可以被多次监听，适合表示独立的事件，如按钮点击\n\n监听Stream有两种主要方式：\n- 使用listen方法注册回调函数，处理数据、错误和流结束事件\n- 在异步函数中使用await for循环，这种方式更加直观，但要注意流的关闭\n\nStream提供了丰富的转换和组合操作方法，如map、where、take、skip等，这些方法返回新的流，不会修改原始流。这使得可以构建复杂的异步数据处理管道。\n\n在使用Stream时需要注意流的关闭，特别是使用StreamController时，要确保在不再需要时调用close()方法，以避免内存泄漏。对于单订阅流，一旦流被取消或完成，就不能再次监听。", "parameters": [], "returnValue": "不同方法返回值不同", "examples": [{"code": "import 'dart:async';\n\nvoid main() async {\n  print('开始Stream示例');\n  \n  // 1. 使用Stream.fromIterable创建Stream\n  print('\\n使用Stream.fromIterable创建Stream:');\n  var iterableStream = Stream.fromIterable([1, 2, 3, 4, 5]);\n  \n  // 使用listen监听\n  iterableStream.listen(\n    (data) => print('  接收到数据: $data'),\n    onError: (error) => print('  发生错误: $error'),\n    onDone: () => print('  流已关闭'),\n  );\n  \n  // 等待上面的Stream完成\n  await Future.delayed(Duration(seconds: 1));\n  \n  // 2. 使用Stream.periodic创建周期性Stream\n  print('\\n使用Stream.periodic创建周期性Stream:');\n  var periodicStream = Stream.periodic(Duration(milliseconds: 500), (count) => count);\n  \n  // 使用take限制数据数量\n  var subscription = periodicStream.take(5).listen(\n    (data) => print('  接收到周期数据: $data'),\n    onDone: () => print('  周期流已关闭'),\n  );\n  \n  // 等待上面的Stream完成\n  await Future.delayed(Duration(seconds: 3));\n  \n  // 3. 使用StreamController手动控制\n  print('\\n使用StreamController手动控制Stream:');\n  var controller = StreamController<String>();\n  \n  // 监听控制器的stream\n  controller.stream.listen(\n    (data) => print('  控制器接收到数据: $data'),\n    onError: (error) => print('  控制器发生错误: $error'),\n    onDone: () => print('  控制器流已关闭'),\n  );\n  \n  // 向流中添加数据\n  controller.add('第一条消息');\n  controller.add('第二条消息');\n  \n  // 添加错误\n  controller.addError('发生了一个错误');\n  \n  // 添加更多数据\n  controller.add('第三条消息');\n  \n  // 关闭流\n  controller.close();\n  \n  // 等待上面的操作完成\n  await Future.delayed(Duration(milliseconds: 500));\n  \n  // 4. 使用async* 和 yield创建Stream\n  print('\\n使用async*和yield创建Stream:');\n  var generatedStream = countStream(5);\n  \n  generatedStream.listen(\n    (data) => print('  生成的流数据: $data'),\n    onDone: () => print('  生成的流已关闭'),\n  );\n  \n  // 等待上面的Stream完成\n  await Future.delayed(Duration(seconds: 1));\n  \n  // 5. 使用await for监听Stream\n  print('\\n使用await for监听Stream:');\n  await processStream();\n  \n  // 6. 取消Stream订阅\n  print('\\n取消Stream订阅:');\n  var cancelableStream = Stream.periodic(Duration(milliseconds: 300), (count) => count).take(10);\n  \n  var cancelableSubscription = cancelableStream.listen(\n    (data) => print('  接收到数据: $data'),\n    onDone: () => print('  流已关闭（不应该看到这个消息）'),\n  );\n  \n  // 3秒后取消订阅\n  await Future.delayed(Duration(milliseconds: 1000));\n  print('  取消订阅');\n  await cancelableSubscription.cancel();\n  print('  订阅已取消');\n  \n  // 7. 处理Stream中的错误\n  print('\\n处理Stream中的错误:');\n  var errorStream = Stream<int>.periodic(Duration(milliseconds: 300), (count) {\n    if (count == 2) throw Exception('故意制造的错误');\n    return count;\n  }).take(5);\n  \n  // 默认情况下，错误会导致订阅取消\n  print('  默认错误处理（cancelOnError: true）:');\n  errorStream.listen(\n    (data) => print('  接收到数据: $data'),\n    onError: (error) => print('  发生错误: $error'),\n    onDone: () => print('  流已关闭'),\n    cancelOnError: true, // 默认值\n  );\n  \n  await Future.delayed(Duration(seconds: 2));\n  \n  // 创建一个新的错误流，设置错误不取消订阅\n  print('\\n  错误后继续（cancelOnError: false）:');\n  var errorStream2 = Stream<int>.periodic(Duration(milliseconds: 300), (count) {\n    if (count == 2) throw Exception('故意制造的错误');\n    return count;\n  }).take(5);\n  \n  errorStream2.listen(\n    (data) => print('  接收到数据: $data'),\n    onError: (error) => print('  发生错误: $error'),\n    onDone: () => print('  流已关闭'),\n    cancelOnError: false, // 错误后继续\n  );\n  \n  await Future.delayed(Duration(seconds: 2));\n  \n  // 8. 单订阅流的特性\n  print('\\n单订阅流的特性:');\n  var singleSubscriptionController = StreamController<int>();\n  var singleSubscriptionStream = singleSubscriptionController.stream;\n  \n  // 第一次监听\n  singleSubscriptionStream.listen(\n    (data) => print('  第一个监听者接收到数据: $data'),\n    onDone: () => print('  对第一个监听者流已关闭'),\n  );\n  \n  // 添加数据\n  singleSubscriptionController.add(1);\n  singleSubscriptionController.add(2);\n  \n  // 尝试再次监听（这会导致错误）\n  try {\n    singleSubscriptionStream.listen(\n      (data) => print('  第二个监听者接收到数据: $data'),\n    );\n  } catch (e) {\n    print('  尝试多次监听单订阅流时出错: $e');\n  }\n  \n  singleSubscriptionController.close();\n  \n  print('\\nStream定义与监听示例完成');\n}\n\n// 使用async*和yield创建一个Stream\nStream<int> countStream(int max) async* {\n  for (int i = 0; i < max; i++) {\n    // 模拟异步操作\n    await Future.delayed(Duration(milliseconds: 200));\n    yield i; // 产生一个值到流中\n  }\n}\n\n// 使用await for监听Stream\nFuture<void> processStream() async {\n  var stream = Stream.fromIterable([10, 20, 30, 40, 50]);\n  \n  print('  使用await for开始处理流:');\n  await for (var value in stream) {\n    print('  处理值: $value');\n  }\n  print('  流处理完成');\n}", "explanation": "这个示例全面展示了Dart中Stream的定义和监听方法。首先展示了使用Stream.fromIterable从可迭代对象创建Stream，然后展示了使用Stream.periodic创建周期性生成数据的Stream。接着展示了如何使用StreamController手动控制流的数据发射，包括添加数据、错误和关闭流。然后演示了如何使用async*和yield创建自定义Stream。示例还展示了两种监听Stream的方式：使用listen方法和使用await for循环。此外，示例还演示了如何取消Stream订阅，以及如何处理Stream中的错误（包括错误后取消订阅和错误后继续）。最后，演示了单订阅流的特性，即单订阅流只能被监听一次，尝试多次监听会导致错误。"}]}}, {"name": "Single-subscription and Broadcast Streams", "trans": ["单订阅与广播流"], "usage": {"syntax": "// 创建单订阅流\nStreamController<类型> controller = StreamController<类型>();\nStream<类型> singleSubscriptionStream = controller.stream;\n\n// 创建广播流\nStreamController<类型> controller = StreamController<类型>.broadcast();\nStream<类型> broadcastStream = controller.stream;\n\n// 将单订阅流转换为广播流\nStream<类型> broadcastStream = singleSubscriptionStream.asBroadcastStream();", "description": "Dart中的Stream有两种基本类型：单订阅流（Single-subscription Stream）和广播流（Broadcast Stream）。它们的区别在于可以被监听的次数和行为特性。\n\n单订阅流（Single-subscription Stream）:\n- 默认的Stream类型，只能被监听一次\n- 适合表示连续的事件序列，如文件读取、网络请求等\n- 保证事件按顺序传递给监听者，不会丢失事件\n- 如果尝试多次监听，会抛出异常\n- 一旦订阅被取消或完成，就不能再次订阅\n- 创建方式：默认的StreamController创建的就是单订阅流\n\n广播流（Broadcast Stream）:\n- 可以被多次监听，适合表示独立的事件，如用户界面事件、通知等\n- 不保证事件传递的完整性，新的监听者只会收到监听后产生的事件\n- 即使没有监听者，也不会缓存事件，无人监听时的事件会被丢弃\n- 允许动态添加和移除监听者\n- 创建方式：使用StreamController.broadcast()构造函数或调用单订阅流的asBroadcastStream()方法\n\n选择使用哪种类型的流取决于你的用例：\n- 如果你需要处理一个连续的、完整的数据序列（如文件读取），应该使用单订阅流\n- 如果你需要处理可以被多个部分独立监听的事件（如UI事件），应该使用广播流\n\n在处理流时，可以使用isBroadcast属性检查一个流是否是广播流。对于广播流，你还可以使用hasListener检查是否有活跃的监听者。", "parameters": [], "returnValue": "不同方法返回值不同", "examples": [{"code": "import 'dart:async';\n\nvoid main() async {\n  print('开始单订阅与广播流示例');\n  \n  // 单订阅流示例\n  print('\\n1. 单订阅流示例:');\n  final singleSubController = StreamController<int>();\n  final singleSubStream = singleSubController.stream;\n  \n  // 检查是否是广播流\n  print('  是广播流: ${singleSubStream.isBroadcast}');\n  \n  // 添加第一个监听者\n  final subscription1 = singleSubStream.listen(\n    (data) => print('  监听者1收到数据: $data'),\n    onDone: () => print('  监听者1: 流已关闭'),\n  );\n  \n  // 添加数据\n  singleSubController.add(1);\n  singleSubController.add(2);\n  \n  // 尝试添加第二个监听者（这会导致错误）\n  try {\n    singleSubStream.listen(\n      (data) => print('  监听者2收到数据: $data'),\n    );\n    print('  成功添加第二个监听者（不应该看到这条消息）');\n  } catch (e) {\n    print('  尝试多次监听单订阅流时出错: $e');\n  }\n  \n  // 继续添加数据\n  singleSubController.add(3);\n  \n  // 关闭流\n  await singleSubController.close();\n  await Future.delayed(Duration(milliseconds: 300));\n  \n  // 广播流示例\n  print('\\n2. 广播流示例:');\n  final broadcastController = StreamController<String>.broadcast();\n  final broadcastStream = broadcastController.stream;\n  \n  // 检查是否是广播流\n  print('  是广播流: ${broadcastStream.isBroadcast}');\n  \n  // 添加第一个监听者\n  final broadcastSub1 = broadcastStream.listen(\n    (data) => print('  广播监听者1收到数据: $data'),\n    onDone: () => print('  广播监听者1: 流已关闭'),\n  );\n  \n  // 添加一些数据\n  broadcastController.add('A');\n  broadcastController.add('B');\n  \n  // 添加第二个监听者（广播流可以多次监听）\n  final broadcastSub2 = broadcastStream.listen(\n    (data) => print('  广播监听者2收到数据: $data'),\n    onDone: () => print('  广播监听者2: 流已关闭'),\n  );\n  \n  // 注意第二个监听者不会收到之前的A和B\n  broadcastController.add('C');\n  broadcastController.add('D');\n  \n  // 取消第一个监听者的订阅\n  await broadcastSub1.cancel();\n  print('  广播监听者1已取消订阅');\n  \n  // 添加更多数据，只有监听者2会收到\n  broadcastController.add('E');\n  \n  // 关闭流\n  await broadcastController.close();\n  await Future.delayed(Duration(milliseconds: 300));\n  \n  // 将单订阅流转换为广播流\n  print('\\n3. 将单订阅流转换为广播流:');\n  final singleController = StreamController<int>();\n  \n  // 转换为广播流\n  final convertedBroadcastStream = singleController.stream.asBroadcastStream();\n  \n  // 检查是否是广播流\n  print('  原始流是广播流: ${singleController.stream.isBroadcast}');\n  print('  转换后的流是广播流: ${convertedBroadcastStream.isBroadcast}');\n  \n  // 添加多个监听者到转换后的广播流\n  convertedBroadcastStream.listen(\n    (data) => print('  转换后的监听者1收到数据: $data'),\n    onDone: () => print('  转换后的监听者1: 流已关闭'),\n  );\n  \n  convertedBroadcastStream.listen(\n    (data) => print('  转换后的监听者2收到数据: $data'),\n    onDone: () => print('  转换后的监听者2: 流已关闭'),\n  );\n  \n  // 添加数据到原始控制器\n  singleController.add(100);\n  singleController.add(200);\n  singleController.add(300);\n  \n  // 关闭流\n  await singleController.close();\n  await Future.delayed(Duration(milliseconds: 300));\n  \n  // 广播流的行为特性\n  print('\\n4. 广播流的行为特性:');\n  final behaviorController = StreamController<int>.broadcast();\n  \n  // 添加数据（没有监听者，这些数据会被丢弃）\n  print('  添加数据但没有监听者（这些数据会被丢弃）');\n  behaviorController.add(1000);\n  behaviorController.add(2000);\n  \n  // 添加监听者\n  print('  添加监听者');\n  behaviorController.stream.listen(\n    (data) => print('  行为监听者收到数据: $data'),\n    onDone: () => print('  行为监听者: 流已关闭'),\n  );\n  \n  // 添加更多数据（现在有监听者了）\n  behaviorController.add(3000);\n  behaviorController.add(4000);\n  \n  // 关闭流\n  await behaviorController.close();\n  \n  // 检查流是否有监听者\n  print('\\n5. 检查流是否有监听者:');\n  final checkController = StreamController<int>.broadcast();\n  \n  // 检查是否有监听者\n  print('  有监听者: ${checkController.hasListener}');\n  \n  // 添加监听者\n  final checkSub = checkController.stream.listen(\n    (data) => print('  检查监听者收到数据: $data'),\n  );\n  \n  // 再次检查\n  print('  有监听者: ${checkController.hasListener}');\n  \n  // 取消订阅\n  await checkSub.cancel();\n  \n  // 再次检查\n  print('  有监听者: ${checkController.hasListener}');\n  \n  // 关闭控制器\n  await checkController.close();\n  \n  print('\\n单订阅与广播流示例完成');\n}", "explanation": "这个示例详细展示了Dart中单订阅流和广播流的区别。首先，示例创建了一个单订阅流并演示了它只能被监听一次的特性，尝试添加第二个监听者会导致错误。然后，示例创建了一个广播流并展示了它可以被多次监听的特性，但新的监听者只会收到在它开始监听后产生的事件。接着，示例展示了如何使用asBroadcastStream()方法将单订阅流转换为广播流。示例还展示了广播流的一些行为特性，比如在没有监听者时发送的事件会被丢弃。最后，示例展示了如何使用hasListener属性检查广播流是否有活跃的监听者。"}, {"code": "import 'dart:async';\n\nvoid main() async {\n  print('开始单订阅与广播流的使用场景示例');\n  \n  // 模拟文件读取 - 使用单订阅流\n  print('\\n1. 文件读取场景 - 使用单订阅流:');\n  var fileStream = simulateFileReading();\n  \n  print('  开始读取文件...');\n  await for (var chunk in fileStream) {\n    print('  读取数据块: $chunk');\n  }\n  print('  文件读取完成');\n  \n  // 模拟UI事件 - 使用广播流\n  print('\\n2. UI事件场景 - 使用广播流:');\n  var buttonClickStream = simulateButtonClicks();\n  \n  // 添加第一个监听者 - 计数器组件\n  print('  添加计数器组件监听');\n  var counterSubscription = buttonClickStream.listen(\n    (event) => print('  计数器组件: 按钮被点击 ${event.clickCount} 次'),\n  );\n  \n  // 模拟点击\n  simulateClick();\n  simulateClick();\n  \n  // 添加第二个监听者 - 日志组件\n  print('\\n  添加日志组件监听');\n  var logSubscription = buttonClickStream.listen(\n    (event) => print('  日志组件: 按钮点击于 ${event.timestamp}'),\n  );\n  \n  // 再次模拟点击，两个监听者都会收到\n  simulateClick();\n  simulateClick();\n  \n  // 移除计数器组件监听\n  print('\\n  移除计数器组件监听');\n  await counterSubscription.cancel();\n  \n  // 再次模拟点击，只有日志组件会收到\n  simulateClick();\n  \n  // 清理\n  await logSubscription.cancel();\n  closeButtonSimulation();\n  \n  print('\\n单订阅与广播流的使用场景示例完成');\n}\n\n// 模拟文件读取的单订阅流\nStream<String> simulateFileReading() async* {\n  // 假设这是一个大文件被分成了多个块\n  List<String> fileChunks = [\n    '文件头部内容',\n    '文件中间内容第1部分',\n    '文件中间内容第2部分',\n    '文件尾部内容'\n  ];\n  \n  // 逐块产生数据\n  for (var chunk in fileChunks) {\n    // 模拟读取延迟\n    await Future.delayed(Duration(milliseconds: 500));\n    yield chunk;\n  }\n}\n\n// 按钮点击事件类\nclass ClickEvent {\n  final DateTime timestamp;\n  final int clickCount;\n  \n  ClickEvent(this.timestamp, this.clickCount);\n  \n  @override\n  String toString() => 'ClickEvent(time: $timestamp, count: $clickCount)';\n}\n\n// 全局变量用于模拟\nStreamController<ClickEvent> _buttonController;\nint _clickCount = 0;\n\n// 模拟按钮点击的广播流\nStream<ClickEvent> simulateButtonClicks() {\n  // 创建广播控制器\n  _buttonController = StreamController<ClickEvent>.broadcast();\n  return _buttonController.stream;\n}\n\n// 模拟点击按钮\nvoid simulateClick() {\n  _clickCount++;\n  if (_buttonController != null && !_buttonController.isClosed) {\n    _buttonController.add(ClickEvent(DateTime.now(), _clickCount));\n  }\n}\n\n// 关闭模拟\nvoid closeButtonSimulation() {\n  if (_buttonController != null && !_buttonController.isClosed) {\n    _buttonController.close();\n  }\n}", "explanation": "这个示例展示了单订阅流和广播流的实际使用场景。首先，示例模拟了文件读取场景，使用单订阅流按顺序读取文件数据块，这种场景需要保证数据的完整性和顺序性，非常适合使用单订阅流。然后，示例模拟了UI事件场景，使用广播流处理按钮点击事件，并允许多个组件（计数器组件和日志组件）同时监听这些事件，展示了广播流适合处理独立事件并支持多个监听者的特性。示例还展示了如何动态添加和移除监听者，以及当某个监听者取消订阅后，其他监听者仍然可以继续接收事件的特性。"}]}}, {"name": "Stream Operations", "trans": ["Stream常用操作"], "usage": {"syntax": "// 转换操作\nStream<R> mappedStream = stream.map<R>((T data) => 转换后的数据);\nStream<T> filteredStream = stream.where((T data) => 条件);\nStream<T> limitedStream = stream.take(数量);\nStream<T> skippedStream = stream.skip(数量);\n\n// 组合操作\nStream<T> combinedStream = stream1.followedBy(stream2);\n\n// 缓冲和窗口操作\nStream<List<T>> bufferedStream = stream.bufferCount(大小);\n\n// 测试操作\nFuture<bool> containsTest = stream.contains(值);\nFuture<bool> anyTest = stream.any((T data) => 条件);\nFuture<bool> everyTest = stream.every((T data) => 条件);\n\n// 收集操作\nFuture<List<T>> asList = stream.toList();\nFuture<Set<T>> asSet = stream.toSet();\nFuture<String> asString = stream.join(分隔符);\n\n// 异步操作\nStream<T> asyncMappedStream = stream.asyncMap((T data) async => 异步转换后的数据);\nStream<S> asyncExpandedStream = stream.asyncExpand((T data) => 返回一个Stream<S>);", "description": "Stream提供了丰富的操作方法，可以用于转换、过滤、组合和收集流中的数据。这些操作可以分为几类：\n\n1. 转换操作：\n- map：将流中的每个元素转换为新的元素\n- expand：将流中的每个元素展开为多个元素\n- where：根据条件过滤流中的元素\n- take：只获取流中的前n个元素\n- skip：跳过流中的前n个元素\n- distinct：去除流中的重复元素\n\n2. 组合操作：\n- followedBy：将两个流按顺序组合成一个新流\n\n3. 缓冲和窗口操作：\n- bufferCount：将流元素按指定大小分组成批次\n\n4. 测试操作：\n- contains：检查流中是否包含指定的元素\n- any：检查流中是否有元素满足条件\n- every：检查流中的所有元素是否都满足条件\n\n5. 收集操作：\n- toList：将流中的所有元素收集为列表\n- toSet：将流中的所有元素收集为集合\n- join：将流中的所有元素连接为字符串\n- drain：丢弃流中的所有元素，只关注流的完成\n\n6. 异步操作：\n- asyncMap：对流中的每个元素执行异步转换\n- asyncExpand：对流中的每个元素执行异步展开\n\n这些操作都是非破坏性的，它们不会修改原始流，而是返回一个新的流。通过组合这些操作，可以构建复杂的异步数据处理管道。\n\n大多数Stream操作都支持链式调用，可以连续应用多个操作。例如：stream.where(条件).map(转换).take(数量)\n\n使用这些操作时，要注意一些操作（如toList、contains）会触发对整个流的消费，完成后流就不能再被使用。而转换操作（如map、where）则允许后续的操作继续处理流。", "parameters": [{"name": "条件函数", "description": "传递给方法的函数，用于过滤、映射或测试流元素"}, {"name": "数量", "description": "用于限制操作的元素数量"}, {"name": "值", "description": "用于比较或测试的值"}], "returnValue": "大多数操作返回一个新的Stream，测试操作返回Future<bool>，收集操作返回相应的集合类型", "examples": [{"code": "import 'dart:async';\n\nvoid main() async {\n  print('开始Stream常用操作示例');\n  \n  // 创建一个基础的数字流\n  final numbers = Stream.fromIterable([1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);\n  \n  // 1. 转换操作\n  print('\\n1. 转换操作:');\n  \n  // map - 将每个数字乘以2\n  print('\\n  map操作:');\n  final doubledNumbers = numbers.map((n) => n * 2);\n  await printStream(doubledNumbers, '  映射后的数字（×2）');\n  \n  // 创建一个新的流来演示其他操作\n  final moreNumbers = Stream.fromIterable([1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);\n  \n  // where - 只保留偶数\n  print('\\n  where操作:');\n  final evenNumbers = moreNumbers.where((n) => n % 2 == 0);\n  await printStream(evenNumbers, '  过滤后的偶数');\n  \n  // 创建一个新的流\n  final rangeNumbers = Stream.fromIterable([1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);\n  \n  // take - 只取前3个元素\n  print('\\n  take操作:');\n  final firstThree = rangeNumbers.take(3);\n  await printStream(firstThree, '  前3个数字');\n  \n  // 创建一个新的流\n  final skipNumbers = Stream.fromIterable([1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);\n  \n  // skip - 跳过前7个元素\n  print('\\n  skip操作:');\n  final lastThree = skipNumbers.skip(7);\n  await printStream(lastThree, '  跳过7个后的数字');\n  \n  // 创建一个有重复元素的流\n  final duplicateNumbers = Stream.fromIterable([1, 2, 2, 3, 3, 3, 4, 4, 5]);\n  \n  // distinct - 去除重复元素\n  print('\\n  distinct操作:');\n  final uniqueNumbers = duplicateNumbers.distinct();\n  await printStream(uniqueNumbers, '  去重后的数字');\n  \n  // expand - 将每个元素展开为多个元素\n  print('\\n  expand操作:');\n  final baseNumbers = Stream.fromIterable([1, 2, 3]);\n  final expandedNumbers = baseNumbers.expand((n) => [n, n * 10, n * 100]);\n  await printStream(expandedNumbers, '  展开后的数字');\n  \n  // 2. 组合操作\n  print('\\n2. 组合操作:');\n  \n  // followedBy - 按顺序组合两个流\n  print('\\n  followedBy操作:');\n  final firstStream = Stream.fromIterable(['a', 'b', 'c']);\n  final secondStream = Stream.fromIterable(['d', 'e', 'f']);\n  final combinedStream = firstStream.followedBy(secondStream);\n  await printStream(combinedStream, '  组合后的字符');\n  \n  // 3. 测试操作\n  print('\\n3. 测试操作:');\n  \n  // contains - 检查流是否包含特定元素\n  print('\\n  contains操作:');\n  final containsTestStream = Stream.fromIterable([10, 20, 30, 40, 50]);\n  final containsResult = await containsTestStream.contains(30);\n  print('  流是否包含30: $containsResult');\n  \n  // any - 检查是否有元素满足条件\n  print('\\n  any操作:');\n  final anyTestStream = Stream.fromIterable([1, 3, 5, 7, 9]);\n  final anyResult = await anyTestStream.any((n) => n > 5);\n  print('  是否有大于5的元素: $anyResult');\n  \n  // every - 检查是否所有元素都满足条件\n  print('\\n  every操作:');\n  final everyTestStream = Stream.fromIterable([2, 4, 6, 8, 10]);\n  final everyResult = await everyTestStream.every((n) => n % 2 == 0);\n  print('  是否所有元素都是偶数: $everyResult');\n  \n  // 4. 收集操作\n  print('\\n4. 收集操作:');\n  \n  // toList - 将流元素收集为列表\n  print('\\n  toList操作:');\n  final listCollectStream = Stream.fromIterable(['苹果', '香蕉', '橙子', '葡萄']);\n  final fruitsList = await listCollectStream.toList();\n  print('  收集的水果列表: $fruitsList');\n  \n  // toSet - 将流元素收集为集合（自动去重）\n  print('\\n  toSet操作:');\n  final setCollectStream = Stream.fromIterable(['红色', '蓝色', '红色', '绿色', '蓝色']);\n  final colorsSet = await setCollectStream.toSet();\n  print('  收集的颜色集合（自动去重）: $colorsSet');\n  \n  // join - 将流元素连接为字符串\n  print('\\n  join操作:');\n  final joinCollectStream = Stream.fromIterable(['你好', '世界', '!']);\n  final joinedString = await joinCollectStream.join(' ');\n  print('  连接后的字符串: \"$joinedString\"');\n  \n  // 5. 异步操作\n  print('\\n5. 异步操作:');\n  \n  // asyncMap - 异步转换每个元素\n  print('\\n  asyncMap操作:');\n  final asyncMapStream = Stream.fromIterable([100, 200, 300]);\n  final asyncMappedStream = asyncMapStream.asyncMap((n) async {\n    // 模拟异步操作，如网络请求\n    await Future.delayed(Duration(milliseconds: 100));\n    return '处理后: $n';\n  });\n  await printStream(asyncMappedStream, '  异步映射后');\n  \n  // asyncExpand - 异步展开每个元素为流\n  print('\\n  asyncExpand操作:');\n  final asyncExpandStream = Stream.fromIterable(['A', 'B', 'C']);\n  final asyncExpandedStream = asyncExpandStream.asyncExpand((letter) {\n    // 为每个字母创建一个包含数字的流\n    return Stream.fromIterable(\n        [1, 2].map((n) => '$letter$n')).asStream();\n  });\n  await printStream(asyncExpandedStream, '  异步展开后');\n  \n  // 6. 链式操作组合\n  print('\\n6. 链式操作组合:');\n  final chainSource = Stream.fromIterable([1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);\n  \n  final chainResult = chainSource\n      .where((n) => n % 2 == 1) // 过滤，只保留奇数\n      .map((n) => n * n) // 映射，计算平方\n      .take(3); // 只取前3个结果\n  \n  await printStream(chainResult, '  链式操作结果（奇数的平方，取前3个）');\n  \n  // 7. 实际应用：处理用户输入\n  print('\\n7. 实际应用：处理用户输入:');\n  // 模拟用户输入一系列命令\n  final userInputs = Stream.fromIterable([\n    'HELP',\n    'list',\n    'invalid!',\n    'open file.txt',\n    'quit'\n  ]);\n  \n  // 处理这些命令\n  await processUserCommands(userInputs);\n  \n  print('\\nStream常用操作示例完成');\n}\n\n// 辅助函数：打印流内容\nFuture<void> printStream(Stream stream, String prefix) async {\n  await for (var item in stream) {\n    print('$prefix: $item');\n  }\n}\n\n// 模拟用户命令处理器\nFuture<void> processUserCommands(Stream<String> commands) async {\n  print('  开始处理用户命令:');\n  \n  // 预处理：转小写并去除额外空白\n  final normalizedCommands = commands\n      .map((cmd) => cmd.toLowerCase().trim())\n      .where((cmd) => cmd.isNotEmpty); // 过滤掉空命令\n  \n  // 处理特定命令\n  await for (var cmd in normalizedCommands) {\n    print('  收到命令: \"$cmd\"');\n    \n    if (cmd == 'help') {\n      print('  > 显示帮助信息');\n    } else if (cmd == 'list') {\n      print('  > 列出文件');\n    } else if (cmd.startsWith('open ')) {\n      final filename = cmd.substring(5);\n      print('  > 打开文件: $filename');\n    } else if (cmd == 'quit') {\n      print('  > 退出应用');\n      break; // 遇到退出命令就停止处理\n    } else {\n      print('  > 未知命令，请输入\"help\"获取帮助');\n    }\n  }\n  \n  print('  命令处理完成');\n}", "explanation": "这个示例全面展示了Dart中Stream的常用操作。首先，展示了转换操作，如map（将流中的每个元素转换为新的元素）、where（根据条件过滤流中的元素）、take（只获取流中的前n个元素）、skip（跳过流中的前n个元素）、distinct（去除流中的重复元素）和expand（将流中的每个元素展开为多个元素）。然后，展示了组合操作followedBy，用于按顺序组合两个流。接着，展示了测试操作，如contains（检查流中是否包含指定的元素）、any（检查流中是否有元素满足条件）和every（检查流中的所有元素是否都满足条件）。然后，展示了收集操作，如toList（将流中的所有元素收集为列表）、toSet（将流中的所有元素收集为集合）和join（将流中的所有元素连接为字符串）。接着，展示了异步操作，如asyncMap（对流中的每个元素执行异步转换）和asyncExpand（对流中的每个元素执行异步展开）。最后，展示了如何组合多个操作，以及如何将这些操作应用于实际场景，如处理用户输入命令。"}, {"code": "import 'dart:async';\n\nvoid main() async {\n  print('开始Stream常用操作实战示例');\n  \n  print('\\n1. 数据过滤与转换实战:');\n  // 模拟传感器数据流\n  final sensorData = [\n    {'time': 1, 'value': 22.5, 'unit': 'C', 'valid': true},\n    {'time': 2, 'value': null, 'unit': 'C', 'valid': false},\n    {'time': 3, 'value': 23.1, 'unit': 'C', 'valid': true},\n    {'time': 4, 'value': 22.8, 'unit': 'C', 'valid': true},\n    {'time': 5, 'value': 0, 'unit': 'C', 'valid': false},\n    {'time': 6, 'value': 23.3, 'unit': 'C', 'valid': true},\n  ];\n  \n  print('  原始传感器数据:');\n  sensorData.forEach((data) => print('  $data'));\n  \n  // 创建数据流并处理\n  final dataStream = Stream.fromIterable(sensorData);\n  \n  // 过滤掉无效数据，提取温度值，转换为摄氏度字符串\n  final processedData = dataStream\n      .where((data) => data['valid'] == true && data['value'] != null)\n      .map((data) => {\n            'time': data['time'],\n            'temperature': '${data['value']}${data['unit']}'\n          });\n  \n  print('\\n  处理后的温度数据:');\n  await printStream(processedData, '  数据');\n  \n  print('\\n2. 事件处理与统计实战:');\n  // 模拟应用点击事件\n  final clickEvents = [\n    {'type': 'button', 'id': 'save', 'timestamp': 1000},\n    {'type': 'link', 'id': 'help', 'timestamp': 1050},\n    {'type': 'button', 'id': 'cancel', 'timestamp': 1100},\n    {'type': 'button', 'id': 'save', 'timestamp': 1200},\n    {'type': 'image', 'id': 'logo', 'timestamp': 1250},\n    {'type': 'button', 'id': 'save', 'timestamp': 1300},\n  ];\n  \n  final eventsStream = Stream.fromIterable(clickEvents);\n  \n  // 按类型分组并计数\n  print('  按类型统计点击次数:');\n  final counts = <String, int>{};\n  \n  await for (var event in eventsStream) {\n    final type = event['type'] as String;\n    counts[type] = (counts[type] ?? 0) + 1;\n  }\n  \n  counts.forEach((type, count) {\n    print('  $type: $count 次点击');\n  });\n  \n  // 创建新的流来进行更多分析\n  final eventsForFiltering = Stream.fromIterable(clickEvents);\n  \n  // 过滤出所有\"save\"按钮的点击\n  print('\\n  过滤出所有Save按钮点击:');\n  final saveClicks = eventsForFiltering\n      .where((event) => event['type'] == 'button' && event['id'] == 'save');\n  \n  await printStream(saveClicks, '  Save点击');\n  \n  print('\\n3. 异步数据处理实战:');\n  // 模拟一个API请求流程\n  \n  // 1. 用户ID列表\n  final userIds = ['user1', 'user2', 'user3'];\n  \n  // 2. 为每个用户ID获取用户详情\n  print('  异步获取并处理用户数据:');\n  final results = await Stream.fromIterable(userIds)\n      .asyncMap((id) async {\n        // 模拟API请求延迟\n        print('  请求用户数据: $id');\n        await Future.delayed(Duration(milliseconds: 200));\n        // 模拟API响应\n        return {'id': id, 'name': '用户_$id', 'active': id != 'user2'};\n      })\n      // 只保留活跃用户\n      .where((user) => user['active'] == true)\n      // 提取用户名\n      .map((user) => user['name'])\n      .toList();\n  \n  print('  活跃用户列表: $results');\n  \n  print('\\n4. 错误处理实战:');\n  // 模拟可能出错的数据处理流程\n  final dataWithErrors = [\n    '100',\n    'error',\n    '200',\n    '300',\n    'another_error',\n    '400'\n  ];\n  \n  final errorStream = Stream.fromIterable(dataWithErrors);\n  \n  // 尝试将字符串转换为数字，处理可能的错误\n  print('  错误处理演示:');\n  try {\n    await for (var item in errorStream) {\n      try {\n        // 尝试转换为整数\n        final number = int.parse(item);\n        print('  成功处理: $item -> $number');\n      } catch (e) {\n        // 处理单个元素的错误，但继续处理流\n        print('  无法转换「$item」: $e');\n      }\n    }\n    print('  所有数据处理完成');\n  } catch (e) {\n    // 处理整个流的错误（如果有）\n    print('  流处理中断: $e');\n  }\n  \n  print('\\n5. 流的限流与缓冲实战:');\n  \n  // 模拟高频数据流\n  final highFrequencyData = List.generate(20, (i) => i);\n  \n  // 创建高频数据流并以批次方式处理\n  print('  以批次方式处理数据:');\n  \n  // 创建原始数据流\n  final highFreqStream = Stream.fromIterable(highFrequencyData);\n  \n  // 使用scan操作按批次收集数据\n  final batches = await highFreqStream\n      .scan<List<int>>([], (accumulated, value, index) {\n        // 添加当前值到累加器\n        accumulated.add(value);\n        \n        // 当累加器大小为5或到达最后一个元素时返回当前批次\n        if (accumulated.length == 5 || index == highFrequencyData.length - 1) {\n          final batch = List<int>.from(accumulated);\n          accumulated.clear();\n          return batch;\n        }\n        return accumulated;\n      })\n      .where((batch) => batch.isNotEmpty) // 过滤掉空批次\n      .toList();\n  \n  // 打印批次\n  for (var i = 0; i < batches.length; i++) {\n    print('  批次 ${i + 1}: ${batches[i]}');\n  }\n  \n  print('\\nStream常用操作实战示例完成');\n}\n\n// 辅助方法：打印流内容\nFuture<void> printStream(Stream stream, String prefix) async {\n  await for (var item in stream) {\n    print('$prefix: $item');\n  }\n}\n\n// 扩展Stream类以添加scan方法（Dart核心库中不包含此方法）\nextension ScanExtension<T> on Stream<T> {\n  Stream<R> scan<R>(R seed, R Function(R accumulated, T value, int index) combine) {\n    var index = 0;\n    return fold<_ScanState<R, T>>(\n      _ScanState<R, T>(seed, []),\n      (state, element) {\n        final newValue = combine(state.accumulated, element, index++);\n        state.values.add(newValue);\n        state.accumulated = newValue;\n        return state;\n      },\n    ).expand((state) => state.values);\n  }\n}\n\n// 用于scan方法的辅助类\nclass _ScanState<R, T> {\n  R accumulated;\n  final List<R> values;\n  \n  _ScanState(this.accumulated, this.values);\n}", "explanation": "这个示例展示了Stream操作在实际应用场景中的用法。首先，示例演示了如何处理传感器数据流，使用where过滤出有效的数据，并使用map转换数据格式。然后，示例展示了如何处理应用点击事件，统计不同类型事件的数量，并过滤出特定的事件。接着，示例展示了如何使用asyncMap进行异步数据处理，模拟了从API获取用户数据并进行处理的流程。然后，示例展示了如何在Stream处理过程中处理错误，展示了如何捕获和处理单个元素的错误，同时继续处理流中的其他元素。最后，示例展示了如何使用自定义的scan方法实现数据的批处理，将高频数据流按批次进行处理，这在处理大量数据时非常有用。这个示例展示了Stream操作在数据过滤、转换、异步处理、错误处理和批处理等实际应用场景中的强大功能。"}, {"code": "import 'dart:async';\n\nvoid main() async {\n  print('Stream常用操作练习：');  \n  // 使用Stream.fromIterable创建一个包含1到10数字的流\n  // 1. 使用where筛选出偶数\n  // 2. 使用map将每个数字翻倍\n  // 3. 使用take(3)只获取前3个结果\n  // 4. 使用toList()将结果收集到列表中\n  // 预期结果: [4, 8, 12]\n  \n  // 在下方编写你的代码\n  final numbers = Stream.fromIterable([1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);\n  final result = await numbers\n      .where((n) => n % 2 == 0) // 筛选偶数\n      .map((n) => n * 2) // 每个数字翻倍\n      .take(3) // 只取前3个\n      .toList(); // 收集到列表\n  \n  print('练习结果: $result');\n}", "explanation": "这是一个简单的Stream操作练习，要求学生创建一个包含1到10的数字流，然后应用多个Stream操作：首先使用where筛选出偶数（2、4、6、8、10），然后使用map将每个数字翻倍（4、8、12），接着使用take(3)只获取前3个结果（4、8、12），最后使用toList()将结果收集到列表中。这个练习帮助学生理解和掌握Stream的链式操作，以及如何组合多个操作来处理数据流。"}]}}]}