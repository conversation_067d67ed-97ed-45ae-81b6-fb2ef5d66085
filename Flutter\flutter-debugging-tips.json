{"name": "Debugging Tips", "trans": ["调试技巧"], "methods": [{"name": "Logging and Breakpoint Debugging", "trans": ["日志与断点调试"], "usage": {"syntax": "print、debugPrint、IDE断点", "description": "通过日志输出和断点调试，可以快速定位代码执行流程和变量状态，是Flutter开发中最常用的调试手段。", "parameters": [{"name": "print", "description": "基础日志输出，适合简单调试"}, {"name": "debugPrint", "description": "长文本日志输出，自动分段"}, {"name": "断点", "description": "在IDE中设置断点，逐步调试代码"}], "returnValue": "无返回值", "examples": [{"code": "// 日志输出示例\nprint('当前计数：$count');\ndebugPrint('长文本日志：' + longString);\n\n// 断点调试：\n// 1. 在IDE（如VSCode/Android Studio）点击行号设置断点\n// 2. 启动调试模式，程序运行到断点会自动暂停\n// 3. 可查看变量、单步执行、观察调用栈", "explanation": "演示如何用print、debugPrint输出日志，以及在IDE中设置断点进行逐步调试。"}]}}, {"name": "Hot Reload and Hot Restart", "trans": ["热重载与热重启"], "usage": {"syntax": "flutter hot reload / hot restart", "description": "热重载（Hot Reload）可在不丢失应用状态的情况下快速刷新UI，热重启（Hot Restart）则会重置应用状态，适合大幅度代码修改后调试。", "parameters": [{"name": "热重载", "description": "保存UI状态，刷新界面"}, {"name": "热重启", "description": "重置应用状态，彻底刷新"}], "returnValue": "无返回值", "examples": [{"code": "// 热重载：\n// 1. 修改Dart代码后，按'r'（终端）或点击IDE的Hot Reload按钮\n// 2. UI会立即刷新，状态不丢失\n\n// 热重启：\n// 1. 按'R'（终端）或点击IDE的Hot Restart按钮\n// 2. 应用状态会被重置，适合结构性修改后调试", "explanation": "演示热重载和热重启的使用场景和操作方法。"}]}}, {"name": "DevTools Debugging", "trans": ["DevTools调试"], "usage": {"syntax": "Flutter DevTools", "description": "Flutter DevTools是官方提供的可视化调试工具，支持性能分析、内存监控、Widget树查看、断点调试等功能。", "parameters": [{"name": "性能分析", "description": "查看帧率、CPU、内存等性能指标"}, {"name": "Widget树", "description": "可视化查看当前Widget结构"}, {"name": "断点与变量", "description": "调试代码、查看变量值"}], "returnValue": "无返回值", "examples": [{"code": "// 启动DevTools：\n// 1. 运行flutter pub global activate devtools\n// 2. 在终端输入flutter pub global run devtools\n// 3. 在浏览器打开提示的地址，连接正在运行的Flutter应用\n// 4. 使用Widget Inspector、Performance、Memory等面板进行调试", "explanation": "演示如何启动和使用Flutter DevTools进行可视化调试和性能分析。"}]}}, {"name": "Assignment", "trans": ["作业：调试技巧实践"], "usage": {"syntax": "无", "description": "请在自己的Flutter项目中，分别实践日志输出、断点调试、热重载/热重启和DevTools调试，写下你的调试心得。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 作业参考实现\n// 1. 用print输出变量值，观察日志\n// 2. 在IDE设置断点，单步调试\n// 3. 修改UI后用热重载刷新界面\n// 4. 启动DevTools，分析性能和Widget树\n// 写下每一步的操作过程和收获。", "explanation": "通过实际操作掌握Flutter调试技巧，提升开发效率和排查问题能力。"}]}}]}