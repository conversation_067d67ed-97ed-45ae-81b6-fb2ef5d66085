{"name": "Compose Advanced", "trans": ["Compose进阶"], "methods": [{"name": "Environment Variables & Config", "trans": ["环境变量与配置"], "usage": {"syntax": "environment:\n  - VAR=value\nenv_file:\n  - .env", "description": "通过environment和env_file在docker-compose.yml中注入环境变量，支持集中管理配置。", "parameters": [{"name": "environment", "description": "直接在yml中定义环境变量。"}, {"name": "env_file", "description": "引用外部.env文件批量注入变量。"}], "returnValue": "无返回值，服务启动时自动加载环境变量。", "examples": [{"code": "services:\n  web:\n    image: nginx\n    environment:\n      - NGINX_PORT=80\n    env_file:\n      - .env", "explanation": "web服务通过environment和.env文件注入环境变量。"}]}}, {"name": "Multiple Compose Files", "trans": ["多环境配置文件"], "usage": {"syntax": "docker-compose -f docker-compose.yml -f docker-compose.prod.yml up", "description": "通过多个compose文件实现多环境配置，后面的文件会覆盖前面的配置。", "parameters": [{"name": "-f", "description": "指定compose文件，可叠加多个。"}], "returnValue": "无返回值，支持灵活环境切换。", "examples": [{"code": "docker-compose -f docker-compose.yml -f docker-compose.prod.yml up", "explanation": "先加载基础配置，再用prod配置覆盖，实现生产环境部署。"}]}}, {"name": "Service Scaling", "trans": ["服务扩缩容"], "usage": {"syntax": "docker-compose up --scale <服务名>=<数量>", "description": "通过--scale参数快速扩展或缩减服务实例数量，适用于负载均衡和高可用。", "parameters": [{"name": "--scale", "description": "指定服务实例数量。"}, {"name": "<服务名>", "description": "要扩缩容的服务名称。"}, {"name": "<数量>", "description": "目标实例数量。"}], "returnValue": "无返回值，服务实例自动扩缩容。", "examples": [{"code": "docker-compose up -d --scale web=3", "explanation": "将web服务扩展为3个实例。"}]}}, {"name": "Compose & Swarm Integration", "trans": ["Compose与Swarm集成"], "usage": {"syntax": "docker stack deploy -c docker-compose.yml <栈名>", "description": "使用docker stack命令将Compose文件部署到Swarm集群，实现多主机编排。", "parameters": [{"name": "docker stack deploy", "description": "将Compose文件部署为Swarm栈。"}, {"name": "-c", "description": "指定Compose文件。"}, {"name": "<栈名>", "description": "Swarm栈的名称。"}], "returnValue": "无返回值，支持集群级多容器编排。", "examples": [{"code": "docker stack deploy -c docker-compose.yml mystack", "explanation": "将Compose定义的服务部署到Swarm集群。"}]}}, {"name": "Compose Debug & Logs", "trans": ["Compose调试与日志"], "usage": {"syntax": "docker-compose logs [-f] [服务名]\ndocker-compose config\ndocker-compose --verbose <命令>", "description": "通过logs、config、--verbose等命令调试和排查Compose服务问题。", "parameters": [{"name": "logs", "description": "查看服务日志。"}, {"name": "config", "description": "校验并展示最终配置。"}, {"name": "--verbose", "description": "输出详细调试信息。"}], "returnValue": "无返回值，辅助定位和解决问题。", "examples": [{"code": "# 查看web服务日志\ndocker-compose logs -f web\n# 校验配置\ndocker-compose config\n# 调试模式运行\ndocker-compose --verbose up", "explanation": "通过日志、配置校验和详细输出调试Compose服务。"}]}}, {"name": "Assignment: Compose进阶实践", "trans": ["作业：Compose进阶实践"], "usage": {"syntax": "请完成以下任务：\n1. 使用.env文件为服务注入环境变量\n2. 通过多个compose文件实现dev/prod环境切换\n3. 扩容web服务到3个实例\n4. 用stack命令将服务部署到Swarm\n5. 查看并调试服务日志和配置", "description": "通过实际操作掌握Compose环境变量、多环境配置、扩缩容、Swarm集成和调试。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 1. 使用.env文件\nNGINX_PORT=80\n# 2. 多compose文件\ndocker-compose -f docker-compose.yml -f docker-compose.prod.yml up\n# 3. 扩容\ndocker-compose up -d --scale web=3\n# 4. 部署到Swarm\ndocker stack deploy -c docker-compose.yml mystack\n# 5. 查看日志和调试\ndocker-compose logs -f web\ndocker-compose config", "explanation": "依次完成Compose进阶的核心实践。"}]}}]}