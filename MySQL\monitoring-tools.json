{"name": "Monitoring Tools", "trans": ["监控工具"], "methods": [{"name": "SHOW STATUS", "trans": ["SHOW STATUS"], "usage": {"syntax": "SHOW GLOBAL STATUS;\nSHOW SESSION STATUS;", "description": "用于查看MySQL服务器的全局或会话级运行状态，包括连接数、查询数、缓存命中率等关键指标。", "parameters": [], "returnValue": "返回各类状态变量及其当前值", "examples": [{"code": "-- 查看全局状态\nSHOW GLOBAL STATUS;\n-- 查看当前会话状态\nSHOW SESSION STATUS;\n-- 查询特定指标\nSHOW GLOBAL STATUS LIKE 'Threads_connected';", "explanation": "本例展示了如何查看MySQL的全局和会话状态，以及如何筛选特定指标。"}]}}, {"name": "SHOW PROCESSLIST", "trans": ["SHOW PROCESSLIST"], "usage": {"syntax": "SHOW PROCESSLIST;", "description": "用于查看当前MySQL服务器的所有连接和正在执行的线程，便于分析慢查询、死锁和连接瓶颈。", "parameters": [], "returnValue": "返回当前所有连接的线程信息", "examples": [{"code": "-- 查看所有连接和线程\nSHOW PROCESSLIST;", "explanation": "本例展示了如何查看当前MySQL的所有连接和线程状态。"}]}}, {"name": "information_schema", "trans": ["information_schema"], "usage": {"syntax": "SELECT * FROM information_schema.表名 WHERE 条件;", "description": "information_schema是MySQL的元数据数据库，包含所有数据库、表、列、索引、权限等信息，适合做系统级监控和分析。", "parameters": [{"name": "表名", "description": "如TABLES、COLUMNS、STATISTICS等"}, {"name": "条件", "description": "筛选元数据的条件"}], "returnValue": "返回元数据信息", "examples": [{"code": "-- 查询所有表信息\nSELECT TABLE_NAME, TABLE_ROWS FROM information_schema.TABLES WHERE TABLE_SCHEMA = 'testdb';\n-- 查询所有索引信息\nSELECT * FROM information_schema.STATISTICS WHERE TABLE_SCHEMA = 'testdb';", "explanation": "本例展示了如何通过information_schema查询数据库表和索引等元数据信息。"}]}}, {"name": "Monitoring Tools Assignment", "trans": ["监控工具练习"], "usage": {"syntax": "# 监控工具练习", "description": "完成以下练习，巩固MySQL监控与诊断的常用方法。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 使用SHOW STATUS查看当前连接数和查询数。\n2. 使用SHOW PROCESSLIST分析当前活跃线程和慢查询。\n3. 通过information_schema查询testdb数据库的所有表和索引信息。", "explanation": "通过这些练习，你将掌握MySQL常用监控工具的实际用法。"}]}}]}