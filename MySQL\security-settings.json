{"name": "Security Settings", "trans": ["安全设置"], "methods": [{"name": "Password Policy", "trans": ["密码策略"], "usage": {"syntax": "-- 查看密码策略参数\nSHOW VARIABLES LIKE 'validate_password%';\n\n-- 设置密码长度要求\nSET GLOBAL validate_password.length = 8;\n\n-- 设置密码复杂度等级\nSET GLOBAL validate_password.policy = 2;", "description": "MySQL支持密码强度策略，可以设置密码最小长度、复杂度等级等，提升账户安全性。常用参数有validate_password.length（最小长度）、validate_password.policy（复杂度等级，0=LOW, 1=MEDIUM, 2=STRONG）。", "parameters": [{"name": "validate_password.length", "description": "密码最小长度"}, {"name": "validate_password.policy", "description": "密码复杂度等级"}], "returnValue": "无返回值", "examples": [{"code": "-- 查看当前密码策略\nSHOW VARIABLES LIKE 'validate_password%';\n\n-- 设置密码最小长度为10\nSET GLOBAL validate_password.length = 10;\n\n-- 设置密码复杂度为STRONG\nSET GLOBAL validate_password.policy = 2;", "explanation": "本例展示了如何查看和设置MySQL的密码策略，包括最小长度和复杂度等级。"}]}}, {"name": "Prevent SQL Injection", "trans": ["防SQL注入"], "usage": {"syntax": "-- 使用参数化查询（以Python为例）\ncursor.execute('SELECT * FROM users WHERE username = %s AND password = %s', (username, password))\n\n-- 使用预处理语句（以MySQL原生为例）\nPREPARE stmt FROM 'SELECT * FROM users WHERE username = ? AND password = ?';\nSET @u = 'admin', @p = '123456';\nEXECUTE stmt USING @u, @p;", "description": "SQL注入是数据库常见安全威胁。防护措施包括：1. 始终使用参数化查询或预处理语句，避免拼接SQL字符串；2. 限制数据库用户权限；3. 对输入进行校验和过滤。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "-- Python参数化查询示例\ncursor.execute('SELECT * FROM users WHERE username = %s AND password = %s', (username, password))\n\n-- MySQL预处理语句示例\nPREPARE stmt FROM 'SELECT * FROM users WHERE username = ? AND password = ?';\nSET @u = 'admin', @p = '123456';\nEXECUTE stmt USING @u, @p;", "explanation": "本例展示了如何通过参数化查询和预处理语句防止SQL注入攻击。"}]}}, {"name": "Backup and Restore", "trans": ["数据备份与恢复"], "usage": {"syntax": "-- 备份数据库\nmysqldump -u 用户名 -p 数据库名 > backup.sql\n\n-- 恢复数据库\nmysql -u 用户名 -p 数据库名 < backup.sql", "description": "定期备份数据库是保障数据安全的重要措施。MySQL常用mysqldump工具进行逻辑备份，也可用物理备份。恢复时可直接导入SQL文件。", "parameters": [{"name": "mysqldump", "description": "MySQL逻辑备份工具"}, {"name": "数据库名", "description": "要备份或恢复的数据库名称"}, {"name": "backup.sql", "description": "备份文件名"}], "returnValue": "无返回值", "examples": [{"code": "-- 备份testdb数据库\nmysqldump -u root -p testdb > testdb_backup.sql\n\n-- 恢复testdb数据库\nmysql -u root -p testdb < testdb_backup.sql", "explanation": "本例展示了如何使用mysqldump备份和恢复MySQL数据库。"}]}}, {"name": "Security Settings Assignment", "trans": ["安全设置练习"], "usage": {"syntax": "# 安全设置练习", "description": "完成以下练习，巩固MySQL安全设置相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 查看当前MySQL密码策略配置。\n2. 修改密码最小长度为12，复杂度为MEDIUM。\n3. 编写一段Python代码，安全地查询users表中的数据，防止SQL注入。\n4. 备份testdb数据库并恢复到新数据库testdb_copy。", "explanation": "通过这些练习，你将掌握MySQL密码策略配置、防SQL注入编程和数据库备份恢复等安全管理技能。"}]}}]}