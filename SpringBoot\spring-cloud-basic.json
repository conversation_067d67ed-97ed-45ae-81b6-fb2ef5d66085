{"name": "Spring Cloud Basic", "trans": ["Spring Cloud基础"], "methods": [{"name": "Service Registration and Discovery", "trans": ["服务注册与发现"], "usage": {"syntax": "引入spring-cloud-starter-eureka或spring-cloud-starter-alibaba-nacos依赖，@EnableEurekaServer/@EnableDiscoveryClient注解", "description": "服务注册与发现是微服务架构的基础，Eureka和Nacos是主流实现。服务注册中心负责维护服务实例，客户端通过服务名发现并调用服务。", "parameters": [{"name": "spring-cloud-starter-eureka", "description": "Eureka注册中心依赖"}, {"name": "spring-cloud-starter-alibaba-nacos", "description": "Nacos注册中心依赖"}, {"name": "@EnableEurekaServer", "description": "启动Eureka注册中心"}, {"name": "@EnableDiscoveryClient", "description": "服务注册与发现注解"}], "returnValue": "服务注册与发现功能", "examples": [{"code": "// 1. Eureka注册中心主类\n@SpringBootApplication\n@EnableEurekaServer\npublic class EurekaServerApplication {\n    public static void main(String[] args) {\n        SpringApplication.run(EurekaServerApplication.class, args);\n    }\n}\n\n// 2. 服务注册到Eureka\n@SpringBootApplication\n@EnableDiscoveryClient\npublic class UserServiceApplication {\n    public static void main(String[] args) {\n        SpringApplication.run(UserServiceApplication.class, args);\n    }\n}", "explanation": "演示了Eureka注册中心和服务注册的基本用法。"}]}}, {"name": "<PERSON><PERSON>", "trans": ["负载均衡"], "usage": {"syntax": "引入spring-cloud-starter-loadbalancer或spring-cloud-starter-netflix-ribbon依赖，@LoadBalanced注解RestTemplate/WebClient", "description": "负载均衡用于将请求分发到多个服务实例，提升系统可用性。Spring Cloud支持Ribbon和LoadBalancer两种实现。", "parameters": [{"name": "spring-cloud-starter-loadbalancer", "description": "Spring Cloud官方负载均衡依赖"}, {"name": "spring-cloud-starter-netflix-ribbon", "description": "Ribbon负载均衡依赖（已过时）"}, {"name": "@LoadBalanced", "description": "注解RestTemplate/WebClient实现负载均衡"}], "returnValue": "具备负载均衡能力的服务调用", "examples": [{"code": "// 1. 配置负载均衡RestTemplate\n@Bean\n@LoadBalanced\npublic RestTemplate restTemplate() {\n    return new RestTemplate();\n}\n\n// 2. 服务名调用\nUser user = restTemplate.getForObject(\"http://user-service/users/{id}\", User.class, 1);", "explanation": "演示了@LoadBalanced注解实现服务间负载均衡调用。"}]}}, {"name": "Config Center", "trans": ["配置中心"], "usage": {"syntax": "引入spring-cloud-starter-config依赖，@RefreshScope注解动态刷新配置", "description": "配置中心用于集中管理微服务配置，支持动态刷新。Spring Cloud Config支持本地、Git、Nacos等多种存储。", "parameters": [{"name": "spring-cloud-starter-config", "description": "配置中心依赖"}, {"name": "@RefreshScope", "description": "支持配置动态刷新的注解"}], "returnValue": "集中式和动态刷新的配置管理能力", "examples": [{"code": "// 1. 配置中心客户端\n@SpringBootApplication\n@RefreshScope\npublic class ConfigClientApplication {\n    @Value(\"${user.name}\")\n    private String userName;\n}\n\n// 2. application.properties\nspring.cloud.config.uri=http://localhost:8888\nspring.application.name=config-client", "explanation": "演示了配置中心客户端和动态刷新配置的用法。"}]}}, {"name": "Circuit Breaker and Fallback", "trans": ["服务熔断与降级"], "usage": {"syntax": "引入spring-cloud-starter-circuitbreaker-resilience4j或spring-cloud-starter-netflix-hystrix依赖，@CircuitBreaker/@HystrixCommand注解", "description": "熔断器用于防止服务雪崩，降级用于在服务不可用时返回默认结果。Resilience4j和Hystrix是常用实现。", "parameters": [{"name": "spring-cloud-starter-circuitbreaker-resilience4j", "description": "Resilience4j熔断降级依赖"}, {"name": "spring-cloud-starter-netflix-hystrix", "description": "Hystrix熔断降级依赖（已过时）"}, {"name": "@CircuitBreaker/@HystrixCommand", "description": "熔断与降级注解"}], "returnValue": "具备熔断与降级能力的服务调用", "examples": [{"code": "// 1. Resilience4j熔断降级\n@CircuitBreaker(name = \"userService\", fallbackMethod = \"fallback\")\npublic User getUser(Long id) {\n    // 远程调用\n}\npublic User fallback(Long id, Throwable t) {\n    return new User(-1L, \"默认用户\");\n}\n\n// 2. Hystrix熔断降级\n@HystrixCommand(fallbackMethod = \"fallback\")\npublic User getUser(Long id) {\n    // 远程调用\n}\npublic User fallback(Long id) {\n    return new User(-1L, \"默认用户\");\n}", "explanation": "演示了Resilience4j和Hystrix的熔断与降级用法。"}]}}, {"name": "Gateway", "trans": ["网关"], "usage": {"syntax": "引入spring-cloud-starter-gateway依赖，配置路由规则，实现统一入口", "description": "网关是微服务的统一入口，负责路由、鉴权、限流等。Spring Cloud Gateway支持动态路由和过滤器扩展。", "parameters": [{"name": "spring-cloud-starter-gateway", "description": "网关依赖"}, {"name": "路由规则", "description": "配置文件或Java代码定义路由"}], "returnValue": "统一入口和路由转发能力", "examples": [{"code": "# application.yml\nspring:\n  cloud:\n    gateway:\n      routes:\n        - id: user-service\n          uri: lb://user-service\n          predicates:\n            - Path=/users/**", "explanation": "演示了Spring Cloud Gateway的基本路由配置。"}]}}, {"name": "Distributed Tracing", "trans": ["分布式链路追踪"], "usage": {"syntax": "引入spring-cloud-starter-sleuth和spring-cloud-starter-zipkin依赖，实现链路追踪", "description": "分布式链路追踪用于监控微服务调用链路，定位性能瓶颈。Sleuth负责埋点，Zipkin负责收集和展示追踪数据。", "parameters": [{"name": "spring-cloud-starter-sleuth", "description": "链路追踪埋点依赖"}, {"name": "spring-cloud-starter-zipkin", "description": "链路追踪数据收集与展示依赖"}], "returnValue": "分布式调用链路追踪能力", "examples": [{"code": "# application.properties\nspring.zipkin.base-url=http://localhost:9411\nspring.sleuth.sampler.probability=1.0", "explanation": "演示了Sleuth和Z<PERSON>kin的链路追踪配置。"}]}}, {"name": "Spring Cloud Basic Assignment", "trans": ["Spring Cloud基础练习"], "usage": {"syntax": "# Spring Cloud基础练习", "description": "完成以下练习，巩固Spring Cloud基础相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 搭建Eureka或Nacos注册中心，实现服务注册与发现。\n2. 配置@LoadBalanced实现服务间负载均衡调用。\n3. 集成Config配置中心，实现动态刷新。\n4. 实现服务熔断与降级，返回默认结果。\n5. 配置Gateway实现统一入口路由。\n6. 集成Sleuth和Zipkin，实现分布式链路追踪。", "explanation": "通过这些练习掌握Spring Cloud基础的核心用法。"}]}}]}