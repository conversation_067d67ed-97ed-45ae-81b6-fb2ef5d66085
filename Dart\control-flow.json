{"name": "Control Flow", "trans": ["流程控制"], "methods": [{"name": "If-Else Statements", "trans": ["if/else语句"], "usage": {"syntax": "// 基本if语句\nif (condition) {\n  // 条件为true时执行的代码\n}\n\n// if-else语句\nif (condition) {\n  // 条件为true时执行的代码\n} else {\n  // 条件为false时执行的代码\n}\n\n// if-else if-else语句\nif (condition1) {\n  // condition1为true时执行的代码\n} else if (condition2) {\n  // condition1为false且condition2为true时执行的代码\n} else {\n  // 所有条件都为false时执行的代码\n}", "description": "if/else语句用于根据布尔条件执行不同的代码块。Dart中的if语句与大多数编程语言类似，使用括号包围条件表达式，使用花括号包围要执行的代码块。条件必须是一个布尔表达式（true或false），Dart不会将非布尔值自动转换为布尔值。if语句可以单独使用，也可以与else分支组合使用，或者使用else if创建多重条件分支。if语句支持嵌套，即在一个if或else代码块内部再使用if语句。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "void main() {\n  // 基本if语句\n  int number = 42;\n  \n  if (number > 0) {\n    print('数字是正数');\n  }\n  \n  // if-else语句\n  bool isRaining = true;\n  \n  if (isRaining) {\n    print('带上雨伞');\n  } else {\n    print('天气晴朗');\n  }\n  \n  // if-else if-else语句\n  int score = 85;\n  \n  if (score >= 90) {\n    print('优秀');\n  } else if (score >= 80) {\n    print('良好');\n  } else if (score >= 60) {\n    print('及格');\n  } else {\n    print('不及格');\n  }\n  \n  // 复合条件\n  int age = 25;\n  bool hasPermission = true;\n  \n  if (age >= 18 && hasPermission) {\n    print('允许访问');\n  } else {\n    print('拒绝访问');\n  }\n  \n  // 嵌套if语句\n  int a = 10;\n  int b = 20;\n  \n  if (a > 0) {\n    if (b > 0) {\n      print('a和b都是正数');\n    } else {\n      print('a是正数，b不是正数');\n    }\n  } else {\n    print('a不是正数');\n  }\n  \n  // 单行if语句（不推荐，可读性较低）\n  bool isWeekend = true;\n  if (isWeekend) print('今天是周末');\n  \n  // 三元运算符替代简单的if-else\n  int x = 10;\n  int y = 20;\n  int max = (x > y) ? x : y;\n  print('较大的数是: $max');\n  \n  // 空安全相关的条件判断\n  String? nullableName;\n  \n  if (nullableName != null) {\n    print('名字的长度: ${nullableName.length}');\n  } else {\n    print('名字为空');\n  }\n  \n  // 字符串和集合的条件判断\n  String text = \"\";\n  List<int> numbers = [];\n  \n  // 检查字符串是否为空\n  if (text.isEmpty) {\n    print('字符串为空');\n  }\n  \n  // 检查列表是否为空\n  if (numbers.isEmpty) {\n    print('列表为空');\n  }\n  \n  // 检查列表是否包含元素\n  List<String> fruits = ['apple', 'banana', 'orange'];\n  if (fruits.contains('apple')) {\n    print('列表包含apple');\n  }\n  \n  // 检查值是否在范围内\n  int value = 15;\n  if (value >= 10 && value <= 20) {\n    print('值在10到20之间');\n  }\n  \n  // 注意：Dart中的条件必须是布尔值\n  // 以下代码在Dart中会出错\n  // if (value) { ... }  // 错误：条件必须是布尔类型\n  // if (\"text\") { ... } // 错误：条件必须是布尔类型\n  \n  // 使用显式条件\n  String name = \"John\";\n  if (name.isNotEmpty) { // 正确\n    print('名字不为空');\n  }\n}", "explanation": "这个示例展示了Dart中if/else语句的多种用法。首先演示了基本的if语句、if-else语句和if-else if-else语句的结构。然后展示了如何使用复合条件（使用逻辑运算符）和嵌套if语句来处理更复杂的条件逻辑。示例还展示了单行if语句（不推荐）和使用三元运算符作为简单if-else语句的替代。最后展示了一些常见的条件判断场景，如空安全检查、字符串和集合操作等。特别强调了Dart中条件表达式必须是布尔值，不会自动将非布尔值转换为布尔值，这与一些其他语言不同。"}]}}, {"name": "Switch-Case Statements", "trans": ["switch/case语句"], "usage": {"syntax": "switch (expression) {\n  case value1:\n    // expression等于value1时执行的代码\n    break; // 防止继续执行下一个case\n\n  case value2:\n    // expression等于value2时执行的代码\n    break;\n\n  default:\n    // expression不匹配任何case时执行的代码\n    break;\n}\n\n// Dart 3.0中的增强switch表达式\nvar result = switch (expression) {\n  pattern1 => value1,\n  pattern2 => value2,\n  _ => defaultValue // 默认值\n};", "description": "switch语句用于根据表达式的值选择执行不同的代码块。Dart中的switch语句支持比较以下类型：整数、字符串、编译时常量和枚举值。每个case后面必须有一个break、continue、return或throw语句，或者以`case`或`default`结束，以防止出现贯穿（fall-through）行为，除非明确使用`fallthrough`注释表示这是有意的。从Dart 2.19开始，switch语句支持模式匹配；Dart 3.0引入了switch表达式，可以直接返回值，语法更简洁。switch语句在需要处理多个条件分支时比if-else if结构更清晰、更高效。", "parameters": [], "returnValue": "switch语句本身没有返回值，switch表达式返回匹配模式对应的值", "examples": [{"code": "void main() {\n  // 基本switch语句与整数\n  int day = 3;\n  \n  switch (day) {\n    case 1:\n      print('星期一');\n      break;\n    case 2:\n      print('星期二');\n      break;\n    case 3:\n      print('星期三');\n      break;\n    case 4:\n      print('星期四');\n      break;\n    case 5:\n      print('星期五');\n      break;\n    case 6:\n    case 7:\n      print('周末');\n      break;\n    default:\n      print('无效的日期');\n      break;\n  }\n  \n  // switch语句与字符串\n  String fruit = 'apple';\n  \n  switch (fruit) {\n    case 'apple':\n      print('这是一个苹果');\n      break;\n    case 'banana':\n      print('这是一个香蕉');\n      break;\n    case 'orange':\n      print('这是一个橙子');\n      break;\n    default:\n      print('未知水果');\n      break;\n  }\n  \n  // switch语句与枚举\n  Color color = Color.green;\n  \n  switch (color) {\n    case Color.red:\n      print('红色表示停止');\n      break;\n    case Color.yellow:\n      print('黄色表示等待');\n      break;\n    case Color.green:\n      print('绿色表示通行');\n      break;\n  }\n  \n  // 使用continue跳转到另一个case\n  int month = 2;\n  int year = 2024;\n  int days;\n  \n  switch (month) {\n    case 1:\n    case 3:\n    case 5:\n    case 7:\n    case 8:\n    case 10:\n    case 12:\n      days = 31;\n      break;\n    case 4:\n    case 6:\n    case 9:\n    case 11:\n      days = 30;\n      break;\n    case 2:\n      // 闰年判断\n      if ((year % 4 == 0 && year % 100 != 0) || year % 400 == 0) {\n        days = 29;\n      } else {\n        days = 28;\n      }\n      break;\n    default:\n      days = 0;\n      print('无效月份');\n      break;\n  }\n  \n  print('$year年$month月有$days天');\n  \n  // 使用return提前结束函数\n  print('weekdayName返回: ${weekdayName(3)}');\n  \n  // Dart 2.19+ 支持的模式匹配\n  Object obj = 42;\n  \n  switch (obj) {\n    case int i when i > 0:\n      print('正整数: $i');\n      break;\n    case int i when i < 0:\n      print('负整数: $i');\n      break;\n    case int i when i == 0:\n      print('零');\n      break;\n    case String s:\n      print('字符串: $s');\n      break;\n    default:\n      print('其他类型');\n      break;\n  }\n  \n  // Dart 3.0+ 支持的switch表达式\n  try {\n    var number = 2;\n    var description = switch (number) {\n      1 => '一',\n      2 => '二',\n      3 => '三',\n      _ => '其他'\n    };\n    print('数字描述: $description');\n    \n    // 使用when进行守卫条件\n    int value = 15;\n    String category = switch (value) {\n      < 0 => '负数',\n      > 0 && < 10 => '个位数',\n      >= 10 && < 100 => '两位数',\n      _ => '大数'\n    };\n    print('数字类别: $category');\n    \n  } catch (e) {\n    // 在Dart 3.0之前的版本可能会抛出异常\n    print('当前Dart版本可能不支持switch表达式: $e');\n  }\n}\n\n// 枚举定义\nenum Color { red, yellow, green }\n\n// 在函数中使用switch\nString weekdayName(int day) {\n  switch (day) {\n    case 1:\n      return '星期一';\n    case 2:\n      return '星期二';\n    case 3:\n      return '星期三';\n    case 4:\n      return '星期四';\n    case 5:\n      return '星期五';\n    case 6:\n      return '星期六';\n    case 7:\n      return '星期日';\n    default:\n      return '无效日期';\n  }\n}", "explanation": "这个示例展示了Dart中switch语句的多种用法。首先演示了基本的switch语句用于整数、字符串和枚举值的匹配。然后展示了如何使用case空语句（没有break）来为多个case执行相同的代码，以及如何使用continue跳转到另一个case标签。示例还展示了在函数中使用switch语句并通过return提前结束函数。对于Dart 2.19及以上版本，展示了如何使用模式匹配和守卫条件（when）来增强switch语句的功能。最后，对于Dart 3.0及以上版本，展示了如何使用新的switch表达式语法直接返回值，这种语法更简洁，不需要break语句。"}]}}, {"name": "Nested Conditionals", "trans": ["嵌套条件判断"], "usage": {"syntax": "// 嵌套if语句\nif (condition1) {\n  if (condition2) {\n    // 两个条件都为true时执行的代码\n  } else {\n    // condition1为true，condition2为false时执行的代码\n  }\n} else {\n  if (condition3) {\n    // condition1为false，condition3为true时执行的代码\n  } else {\n    // condition1和condition3都为false时执行的代码\n  }\n}\n\n// 嵌套switch语句\nswitch (expr1) {\n  case value1:\n    switch (expr2) {\n      case nestedValue1:\n        // 执行代码\n        break;\n      default:\n        // 执行代码\n        break;\n    }\n    break;\n  default:\n    // 执行代码\n    break;\n}", "description": "嵌套条件判断是指在一个条件结构内部包含另一个条件结构，允许处理更复杂的逻辑分支。Dart支持嵌套if语句、嵌套switch语句，以及if和switch的相互嵌套。虽然嵌套条件能够处理复杂的逻辑，但过深的嵌套会降低代码的可读性和可维护性。为了提高代码质量，应当尽量限制嵌套的深度，考虑使用提前返回、逻辑运算符组合条件，或者将复杂逻辑提取为单独的函数。Dart 2.19及以上版本的模式匹配功能也可以用来简化某些复杂的嵌套条件。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "void main() {\n  // 基本嵌套if语句\n  int a = 10;\n  int b = 5;\n  \n  if (a > 0) {\n    if (b > 0) {\n      print('a和b都是正数');\n    } else {\n      print('a是正数，b不是正数');\n    }\n  } else {\n    if (b > 0) {\n      print('a不是正数，b是正数');\n    } else {\n      print('a和b都不是正数');\n    }\n  }\n  \n  // 多重嵌套if语句 - 不推荐，但展示目的\n  int score = 85;\n  bool isExam = true;\n  bool hasBonus = true;\n  \n  if (isExam) {\n    if (score >= 60) {\n      print('考试及格');\n      if (score >= 90) {\n        print('成绩优秀');\n        if (hasBonus) {\n          print('获得额外奖励');\n        }\n      }\n    } else {\n      print('考试不及格');\n    }\n  } else {\n    print('不是考试成绩');\n  }\n  \n  // 重构上面的多重嵌套 - 更好的实践\n  if (!isExam) {\n    print('不是考试成绩');\n    return;\n  }\n  \n  if (score < 60) {\n    print('考试不及格');\n    return;\n  }\n  \n  print('考试及格');\n  \n  if (score >= 90) {\n    print('成绩优秀');\n    if (hasBonus) {\n      print('获得额外奖励');\n    }\n  }\n  \n  // 嵌套switch语句\n  String fruit = 'apple';\n  String color = 'red';\n  \n  switch (fruit) {\n    case 'apple':\n      switch (color) {\n        case 'red':\n          print('这是一个红苹果');\n          break;\n        case 'green':\n          print('这是一个青苹果');\n          break;\n        default:\n          print('这是一个其他颜色的苹果');\n          break;\n      }\n      break;\n    case 'banana':\n      print('香蕉通常是黄色的');\n      break;\n    default:\n      print('其他水果');\n      break;\n  }\n  \n  // if中嵌套switch\n  int day = 3;\n  bool isHoliday = false;\n  \n  if (isHoliday) {\n    print('今天是假日');\n  } else {\n    switch (day) {\n      case 1:\n      case 2:\n      case 3:\n      case 4:\n      case 5:\n        print('今天是工作日');\n        break;\n      case 6:\n      case 7:\n        print('今天是周末');\n        break;\n      default:\n        print('无效日期');\n        break;\n    }\n  }\n  \n  // switch中嵌套if\n  int month = 3;\n  \n  switch (month) {\n    case 12:\n    case 1:\n    case 2:\n      if (month == 12) {\n        print('冬季 - 年末');\n      } else {\n        print('冬季 - 年初');\n      }\n      break;\n    case 3:\n    case 4:\n    case 5:\n      print('春季');\n      break;\n    case 6:\n    case 7:\n    case 8:\n      print('夏季');\n      break;\n    case 9:\n    case 10:\n    case 11:\n      print('秋季');\n      break;\n    default:\n      print('无效月份');\n      break;\n  }\n  \n  // 使用逻辑运算符替代部分嵌套\n  bool hasPermission = true;\n  int age = 20;\n  \n  // 嵌套if写法\n  if (age >= 18) {\n    if (hasPermission) {\n      print('成年人且有权限');\n    }\n  }\n  \n  // 使用&&运算符简化\n  if (age >= 18 && hasPermission) {\n    print('成年人且有权限');\n  }\n  \n  // 使用函数提取复杂逻辑\n  processUser('Alice', 25, true);\n  \n  // 使用Dart 2.19+的模式匹配简化嵌套条件\n  try {\n    Object obj = [1, 2, 3];\n    \n    switch (obj) {\n      case int i when i > 0:\n        print('正整数');\n        break;\n      case String s when s.isNotEmpty:\n        print('非空字符串');\n        break;\n      case List<int> list when list.length > 2:\n        print('包含超过2个元素的整数列表');\n        break;\n      default:\n        print('其他情况');\n        break;\n    }\n  } catch (e) {\n    print('当前Dart版本可能不支持高级模式匹配');\n  }\n}\n\n// 提取复杂逻辑到函数中\nvoid processUser(String name, int age, bool isPremium) {\n  if (name.isEmpty) {\n    print('用户名不能为空');\n    return;\n  }\n  \n  if (age < 18) {\n    print('未成年用户');\n    return;\n  }\n  \n  print('成年用户: $name');\n  \n  if (isPremium) {\n    print('高级会员');\n  } else {\n    print('普通会员');\n  }\n}", "explanation": "这个示例展示了Dart中嵌套条件判断的多种用法和最佳实践。首先演示了基本的嵌套if语句和多重嵌套if语句（不推荐但作为示例），然后展示了如何通过提前返回和扁平化代码结构重构深度嵌套，提高代码可读性。示例还展示了嵌套switch语句、if中嵌套switch以及switch中嵌套if的用法。接着展示了如何使用逻辑运算符（如&&、||）替代某些简单的嵌套条件，以及如何通过提取函数来处理复杂的嵌套逻辑。最后，对于Dart 2.19及以上版本，展示了如何使用模式匹配功能简化复杂的条件判断。这些示例强调了在处理复杂条件逻辑时应当关注代码的可读性和可维护性。"}]}}, {"name": "Assignment", "trans": ["实践作业"], "usage": {"syntax": "// 条件语句实践", "description": "完成以下任务，练习Dart中条件语句的使用：1) 使用if/else语句实现一个函数，根据学生成绩返回对应的等级（A/B/C/D/F）；2) 使用switch语句实现一个简单的计算器，支持加、减、乘、除四种基本运算；3) 实现一个函数，使用嵌套条件判断一个年份是否为闰年，并根据月份返回该月的天数；4) 挑战：使用Dart 3.0的switch表达式（如果你的Dart版本支持）实现一个HTTP状态码解释器，为常见HTTP状态码（如200、404、500等）返回对应的描述。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "void main() {\n  // 1. 使用if/else语句获取成绩等级\n  print('85分的等级是: ${getGrade(85)}'); // B\n  print('95分的等级是: ${getGrade(95)}'); // A\n  print('55分的等级是: ${getGrade(55)}'); // F\n  \n  // 2. 使用switch语句实现计算器\n  print('5 + 3 = ${calculate(5, '+', 3)}'); // 8\n  print('10 - 4 = ${calculate(10, '-', 4)}'); // 6\n  print('3 * 6 = ${calculate(3, '*', 6)}'); // 18\n  print('20 / 4 = ${calculate(20, '/', 4)}'); // 5.0\n  print('无效运算: ${calculate(5, '%', 2)}'); // 无效操作符\n  \n  // 3. 获取指定月份的天数\n  print('2023年2月有 ${getDaysInMonth(2023, 2)} 天'); // 28\n  print('2024年2月有 ${getDaysInMonth(2024, 2)} 天'); // 29\n  print('2024年4月有 ${getDaysInMonth(2024, 4)} 天'); // 30\n  print('2024年7月有 ${getDaysInMonth(2024, 7)} 天'); // 31\n  \n  // 4. HTTP状态码解释器\n  try {\n    print('HTTP 200: ${getHttpStatusDescription(200)}'); // OK\n    print('HTTP 404: ${getHttpStatusDescription(404)}'); // Not Found\n    print('HTTP 500: ${getHttpStatusDescription(500)}'); // Internal Server Error\n  } catch (e) {\n    print('当前Dart版本可能不支持switch表达式: $e');\n    // 提供传统switch语句实现作为替代\n    print('HTTP 200: ${getHttpStatusDescriptionTraditional(200)}');\n    print('HTTP 404: ${getHttpStatusDescriptionTraditional(404)}');\n    print('HTTP 500: ${getHttpStatusDescriptionTraditional(500)}');\n  }\n}\n\n// 1. 根据分数返回等级\nString getGrade(int score) {\n  if (score >= 90) {\n    return 'A';\n  } else if (score >= 80) {\n    return 'B';\n  } else if (score >= 70) {\n    return 'C';\n  } else if (score >= 60) {\n    return 'D';\n  } else {\n    return 'F';\n  }\n}\n\n// 2. 使用switch实现简单计算器\ndynamic calculate(num a, String operator, num b) {\n  switch (operator) {\n    case '+':\n      return a + b;\n    case '-':\n      return a - b;\n    case '*':\n      return a * b;\n    case '/':\n      return a / b;\n    default:\n      return '无效操作符';\n  }\n}\n\n// 3. 判断闰年并返回月份天数\nint getDaysInMonth(int year, int month) {\n  // 检查月份有效性\n  if (month < 1 || month > 12) {\n    return 0; // 无效月份\n  }\n  \n  // 判断特定月份的天数\n  switch (month) {\n    case 1: case 3: case 5: case 7: case 8: case 10: case 12:\n      return 31;\n    case 4: case 6: case 9: case 11:\n      return 30;\n    case 2:\n      // 闰年判断\n      if (isLeapYear(year)) {\n        return 29;\n      } else {\n        return 28;\n      }\n    default:\n      return 0; // 不应该到达这里\n  }\n}\n\n// 判断是否是闰年\nbool isLeapYear(int year) {\n  // 闰年规则: 能被4整除但不能被100整除，或者能被400整除\n  if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {\n    return true;\n  }\n  return false;\n}\n\n// 4. 使用Dart 3.0 switch表达式的HTTP状态码解释器\nString getHttpStatusDescription(int statusCode) {\n  return switch (statusCode) {\n    200 => 'OK',\n    201 => 'Created',\n    204 => 'No Content',\n    400 => 'Bad Request',\n    401 => 'Unauthorized',\n    403 => 'Forbidden',\n    404 => 'Not Found',\n    500 => 'Internal Server Error',\n    502 => 'Bad Gateway',\n    503 => 'Service Unavailable',\n    _ => 'Unknown Status Code'\n  };\n}\n\n// 传统switch语句实现的HTTP状态码解释器(兼容早期Dart版本)\nString getHttpStatusDescriptionTraditional(int statusCode) {\n  switch (statusCode) {\n    case 200:\n      return 'OK';\n    case 201:\n      return 'Created';\n    case 204:\n      return 'No Content';\n    case 400:\n      return 'Bad Request';\n    case 401:\n      return 'Unauthorized';\n    case 403:\n      return 'Forbidden';\n    case 404:\n      return 'Not Found';\n    case 500:\n      return 'Internal Server Error';\n    case 502:\n      return 'Bad Gateway';\n    case 503:\n      return 'Service Unavailable';\n    default:\n      return 'Unknown Status Code';\n  }\n}", "explanation": "这个参考实现完成了四个条件语句练习任务。第一个任务使用if/else语句实现了一个根据学生成绩返回等级的函数。第二个任务使用switch语句实现了一个简单的计算器，支持四种基本运算。第三个任务实现了一个函数，使用嵌套条件（switch中包含if）判断一个年份是否为闰年，并根据月份返回该月的天数。第四个任务提供了两种实现：一种使用Dart 3.0的新switch表达式语法，另一种使用传统switch语句，以兼容早期Dart版本。这些任务涵盖了Dart中条件语句的主要用法和实际应用场景。"}]}}]}