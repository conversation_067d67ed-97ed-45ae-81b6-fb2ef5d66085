{"name": "Objects", "trans": ["对象基础"], "methods": [{"name": "Object Literals & Properties", "trans": ["对象字面量与属性"], "usage": {"syntax": "const obj = { key: 'value', num: 1 };", "description": "对象字面量用{}定义，属性为键值对。属性名可为字符串、Symbol。", "parameters": [{"name": "key", "description": "属性名。"}, {"name": "value", "description": "属性值。"}], "returnValue": "返回对象。", "examples": [{"code": "const person = { name: '张三', age: 18 };\nconsole.log(person.name); // '张三'", "explanation": "用字面量创建对象并访问属性。"}]}}, {"name": "Property Access & Manipulation", "trans": ["属性访问与操作"], "usage": {"syntax": "obj.key;\nobj['key'];\nobj.newProp = 123;\ndelete obj.key;", "description": "点语法和中括号语法访问属性。可动态添加、修改、删除属性。", "parameters": [{"name": "obj.key", "description": "点语法访问属性。"}, {"name": "obj['key']", "description": "中括号语法访问属性。"}, {"name": "delete", "description": "删除属性。"}], "returnValue": "返回属性值或undefined。", "examples": [{"code": "const obj = {};\nobj.a = 1;\nobj['b'] = 2;\ndelete obj.a;\nconsole.log(obj); // { b: 2 }", "explanation": "动态添加、访问和删除属性。"}]}}, {"name": "Property Descriptors", "trans": ["属性描述符"], "usage": {"syntax": "Object.defineProperty(obj, 'key', { value: 1, writable: false });\nObject.getOwnPropertyDescriptor(obj, 'key');", "description": "属性描述符用于控制属性的特性，如可写、可枚举、可配置。", "parameters": [{"name": "writable", "description": "是否可写。"}, {"name": "enumerable", "description": "是否可枚举。"}, {"name": "configurable", "description": "是否可配置。"}], "returnValue": "返回属性描述符对象。", "examples": [{"code": "const obj = {};\nObject.defineProperty(obj, 'x', { value: 10, writable: false });\nconsole.log(obj.x); // 10\nobj.x = 20;\nconsole.log(obj.x); // 10 (不可写)\nconsole.log(Object.getOwnPropertyDescriptor(obj, 'x'));", "explanation": "定义只读属性并查看描述符。"}]}}, {"name": "Object Methods & this", "trans": ["对象方法与this"], "usage": {"syntax": "const obj = {\n  value: 1,\n  show() {\n    console.log(this.value);\n  }\n};\nobj.show();", "description": "对象方法是作为属性的函数，this指向调用该方法的对象。", "parameters": [{"name": "this", "description": "指向当前对象。"}], "returnValue": "无返回值，方法内部可操作对象属性。", "examples": [{"code": "const obj = { value: 5, get() { return this.value; } };\nconsole.log(obj.get()); // 5", "explanation": "对象方法通过this访问属性。"}]}}, {"name": "Constructor & Prototype", "trans": ["构造函数与原型"], "usage": {"syntax": "function Person(name) { this.name = name; }\nPerson.prototype.say = function() { console.log(this.name); };\nconst p = new Person('Tom');\np.say();", "description": "构造函数用new创建实例，原型对象共享方法和属性。", "parameters": [{"name": "构造函数", "description": "用于创建对象实例的函数。"}, {"name": "prototype", "description": "原型对象，实例共享属性和方法。"}], "returnValue": "返回新对象实例。", "examples": [{"code": "function Animal(type) { this.type = type; }\nAnimal.prototype.say = function() { console.log(this.type); };\nconst dog = new Animal('dog');\ndog.say(); // 'dog'", "explanation": "构造函数和原型方法的用法。"}]}}, {"name": "Prototype Chain & Inheritance", "trans": ["原型链与继承"], "usage": {"syntax": "function Parent() { this.x = 1; }\nParent.prototype.say = function() { console.log('parent'); };\nfunction Child() { Parent.call(this); this.y = 2; }\nChild.prototype = Object.create(Parent.prototype);\nChild.prototype.constructor = Child;\nconst c = new Child();\nc.say();", "description": "原型链实现对象继承，子类型可访问父类型属性和方法。", "parameters": [{"name": "Parent", "description": "父构造函数。"}, {"name": "Child", "description": "子构造函数。"}], "returnValue": "返回继承后的子对象实例。", "examples": [{"code": "function A() { this.a = 1; }\nA.prototype.say = function() { console.log('A'); };\nfunction B() { A.call(this); this.b = 2; }\nB.prototype = Object.create(A.prototype);\nB.prototype.constructor = B;\nconst b = new B();\nb.say(); // 'A'", "explanation": "原型链继承的实现方式。"}]}}, {"name": "作业：对象基础实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 用字面量创建对象并操作属性\n// 2. 使用属性描述符控制属性\n// 3. 定义对象方法和this\n// 4. 构造函数与原型继承", "description": "通过实践对象创建、属性操作、原型继承等，掌握JS对象基础。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 字面量/属性操作\n// 2. 属性描述符\n// 3. 方法与this\n// 4. 构造函数/原型/继承", "explanation": "作业提示，需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nconst obj = {a:1}; obj.b=2; delete obj.a;\nObject.defineProperty(obj,'c',{value:3,writable:false});\nconst user = {name:'<PERSON>',get(){return this.name;}};\nfunction P(n){this.n=n;} P.prototype.say=function(){return this.n;};\nfunction C(n){P.call(this,n);} C.prototype=Object.create(P.prototype);C.prototype.constructor=C;\nconst c = new C('child');\nconsole.log(obj,user.get(),c.say());", "explanation": "涵盖对象创建、属性操作、描述符、方法、原型继承的正确实现。"}]}}]}