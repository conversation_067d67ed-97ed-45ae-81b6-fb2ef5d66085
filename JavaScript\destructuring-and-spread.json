{"name": "Destructuring and Spread", "trans": ["变量与解构"], "methods": [{"name": "let/const Declaration", "trans": ["let/const"], "usage": {"syntax": "let a = 1;\nconst b = 2;", "description": "let声明块级作用域变量，const声明常量（不可重新赋值）。", "parameters": [{"name": "let", "description": "声明可变变量。"}, {"name": "const", "description": "声明常量。"}], "returnValue": "无返回值，声明变量。", "examples": [{"code": "let x = 10;\nconst PI = 3.14;\nx = 20; // 允许\n// PI = 3.15; // 报错", "explanation": "let声明变量可变，const声明常量不可变。"}]}}, {"name": "Destructuring Assignment", "trans": ["解构赋值"], "usage": {"syntax": "const [a, b] = [1, 2];\nconst {x, y} = {x: 10, y: 20};", "description": "解构赋值可从数组或对象中快速提取变量。支持默认值和嵌套结构。", "parameters": [{"name": "[a, b]", "description": "数组解构。"}, {"name": "{x, y}", "description": "对象解构。"}], "returnValue": "返回解构后的变量。", "examples": [{"code": "const arr = [1,2];\nconst [a,b=0] = arr;\nconst obj = {x:1};\nconst {x,y=2} = obj;\nconsole.log(a,b,x,y); // 1 2 1 2", "explanation": "数组和对象的解构赋值及默认值。"}]}}, {"name": "Spread Operator", "trans": ["扩展运算符"], "usage": {"syntax": "const arr = [1,2];\nconst arr2 = [...arr, 3];\nconst obj = {a:1};\nconst obj2 = {...obj, b:2};", "description": "...扩展运算符用于展开数组或对象，常用于合并、复制、传参等。", "parameters": [{"name": "...arr", "description": "展开数组。"}, {"name": "...obj", "description": "展开对象。"}], "returnValue": "返回新数组或新对象。", "examples": [{"code": "const a = [1,2];\nconst b = [...a,3];\nconst o = {x:1};\nconst p = {...o,y:2};\nconsole.log(b,p);", "explanation": "数组和对象的扩展运算符用法。"}]}}, {"name": "作业：变量与解构实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 用let/const声明变量\n// 2. 用解构赋值提取数据\n// 3. 用扩展运算符合并数组和对象", "description": "通过实践let/const、解构赋值、扩展运算符，掌握ES6变量与解构基础。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. let/const声明\n// 2. 解构赋值/扩展运算符", "explanation": "作业提示，需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nlet a = 1; const b = 2;\nconst arr = [a,b];\nconst [x,y] = arr;\nconst obj = {x,y};\nconst obj2 = {...obj,z:3};\nconsole.log(x,y,obj2);", "explanation": "涵盖let/const、解构赋值、扩展运算符的正确实现。"}]}}]}