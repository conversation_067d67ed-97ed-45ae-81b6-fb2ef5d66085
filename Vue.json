{"topics": [{"id": "template-syntax", "name": "模板语法", "file": "Vue/template-syntax.json", "description": "详细介绍Vue模板语法，包括插值表达式、常用指令、事件与属性绑定、条件与列表渲染、过滤器、模板引用等，帮助掌握Vue视图层声明式开发的基础。"}, {"id": "component-basics", "name": "组件基础", "file": "Vue/component-basics.json", "description": "系统讲解Vue组件的定义与注册、单文件组件、父子通信、事件派发、插槽、动态组件、生命周期与样式作用域，帮助掌握组件化开发的核心能力。"}, {"id": "reactivity-principle", "name": "响应式原理", "file": "Vue/reactivity-principle.json", "description": "系统讲解Vue响应式数据声明、底层原理、计算属性、侦听器、深度监听及常见陷阱，帮助深入理解Vue响应式机制。"}, {"id": "form-handling", "name": "表单处理", "file": "Vue/form-handling.json", "description": "系统讲解Vue表单双向绑定、表单元素类型、修饰符、校验、动态表单、受控与非受控表单等，帮助掌握表单开发的核心技能。"}, {"id": "conditional-and-list-rendering", "name": "条件与列表渲染", "file": "Vue/conditional-and-list-rendering.json", "description": "系统讲解Vue条件渲染、列表渲染、key用法、列表项组件提取与性能优化，帮助高效开发动态视图。"}, {"id": "event-handling", "name": "事件处理", "file": "Vue/event-handling.json", "description": "系统讲解Vue事件绑定、修饰符、事件对象、自定义事件、冒泡与捕获、解绑与委托，帮助掌握事件驱动开发。"}, {"id": "setup-function", "name": "setup函数", "file": "Vue/setup-function.json", "description": "系统讲解Vue3 setup函数的参数、返回值、生命周期钩子及与选项式API的对比，帮助掌握组合式API的核心入口。"}, {"id": "ref-and-reactive", "name": "ref与reactive", "file": "Vue/ref-and-reactive.json", "description": "系统讲解Vue3 ref与reactive的用法、区别、toRefs、shallowRef、readonly等，帮助掌握响应式核心API。"}, {"id": "computed-and-watch", "name": "计算属性与侦听器", "file": "Vue/computed-and-watch.json", "description": "系统讲解Vue3 computed、watch、watchEffect、侦听多个源、深度与立即侦听等，帮助掌握响应式派生与副作用管理。"}, {"id": "lifecycle-hooks", "name": "生命周期钩子", "file": "Vue/lifecycle-hooks.json", "description": "系统讲解Vue3 onMounted、onUpdated等生命周期钩子及与选项式API的对照，帮助掌握组件生命周期管理。"}, {"id": "provide-inject", "name": "provide/inject", "file": "Vue/provide-inject.json", "description": "系统讲解Vue provide/inject的跨层级依赖注入、用法、响应式注入、适用场景与注意事项，帮助掌握组件间高效解耦通信。"}, {"id": "custom-composables", "name": "自定义组合函数", "file": "Vue/custom-composables.json", "description": "系统讲解Vue自定义组合函数（composable）的创建、逻辑复用、状态隔离、参数与返回、实际应用等，帮助掌握组合式API的高级用法。"}, {"id": "slots-and-scoped-slots", "name": "插槽与作用域插槽", "file": "Vue/slots-and-scoped-slots.json", "description": "系统讲解Vue插槽的默认插槽、具名插槽、作用域插槽、动态插槽及内容传递，帮助掌握组件内容分发与灵活复用。"}, {"id": "dynamic-and-async-components", "name": "动态组件与异步组件", "file": "Vue/dynamic-and-async-components.json", "description": "系统讲解Vue动态组件的is属性切换、keep-alive缓存及异步组件的加载、错误处理等，帮助掌握组件动态渲染与性能优化。"}, {"id": "higher-order-components-and-mixins", "name": "高阶组件与混入", "file": "Vue/higher-order-components-and-mixins.json", "description": "系统讲解Vue混入(mixin)、高阶组件(HOC)、extends与mixin区别及组合式API替代方案，帮助掌握Vue不同代码复用技术的适用场景和最佳实践。"}, {"id": "plugins-and-injection", "name": "依赖注入与插件", "file": "Vue/plugins-and-injection.json", "description": "系统讲解Vue插件开发、全局属性与方法、全局组件注册、插件生命周期等，帮助掌握Vue生态扩展与全局能力注入。"}, {"id": "component-communication", "name": "组件通信模式", "file": "Vue/component-communication.json", "description": "系统讲解Vue组件通信的props/emit、provide/inject、eventBus、Vuex/Pinia、$attrs/$listeners、v-model等多种模式，帮助掌握不同场景下的高效通信方案。"}, {"id": "built-in-state-management", "name": "Vue内置状态管理", "file": "Vue/built-in-state-management.json", "description": "系统讲解Vue组件本地状态、props向下传递、事件向上传递、状态提升、跨组件通信等内置状态管理方式，帮助掌握Vue数据流与状态共享。"}, {"id": "vuex-basics", "name": "Vuex基础", "file": "Vue/vuex-basics.json", "description": "系统讲解Vuex Store概念、State/Getter/Mutation/Action、模块化、严格模式、插件机制、异步操作、状态持久化等，帮助掌握Vuex核心用法与扩展能力。"}, {"id": "pinia-basics", "name": "Pinia", "file": "Vue/pinia-basics.json", "description": "系统讲解Pinia Store定义、State/Getter/Action、组合式API集成、模块拆分、插件与中间件、状态持久化、与Vuex对比等，帮助掌握新一代Vue状态管理方案。"}, {"id": "other-state-management", "name": "其他状态管理方案", "file": "Vue/other-state-management.json", "description": "系统讲解Vue的inject/provide、eventBus、组合式API等多种状态管理方案及适用场景，帮助灵活选择最优状态管理方式。"}, {"id": "vue-router-basics", "name": "Vue Router基础", "file": "Vue/vue-router-basics.json", "description": "系统讲解Vue Router的安装与配置、路由模式、路由表与嵌套路由、动态路由、路由参数、编程式导航、路由守卫、路由元信息等，帮助掌握前端路由核心用法。"}, {"id": "vue-router-advanced", "name": "路由进阶", "file": "Vue/vue-router-advanced.json", "description": "系统讲解Vue Router的懒加载与代码分割、路由懒加载、组件缓存与keep-alive、滚动行为控制、动态添加/移除路由、404与重定向等进阶用法，帮助掌握大型项目路由优化。"}, {"id": "reactivity-optimization", "name": "响应式优化", "file": "Vue/reactivity-optimization.json", "description": "系统讲解Vue响应式数据粒度、computed与watch优化、避免不必要的渲染、v-once/v-memo、事件与数据分离等性能优化技巧，帮助提升大型项目性能。"}, {"id": "virtual-list-and-bigdata", "name": "虚拟列表与大数据渲染", "file": "Vue/virtual-list-and-bigdata.json", "description": "系统讲解虚拟滚动原理、常用虚拟列表库、动态高度处理、性能测试与调优等大数据渲染优化方案，帮助高效应对大数据场景。"}, {"id": "async-and-lazyload", "name": "异步组件与懒加载", "file": "Vue/async-and-lazyload.json", "description": "系统讲解异步组件加载、路由懒加载、图片懒加载、资源预加载等前端性能优化方案，帮助提升页面加载速度和用户体验。"}, {"id": "cache-and-persistence", "name": "缓存与持久化", "file": "Vue/cache-and-persistence.json", "description": "系统讲解keep-alive、localStorage/sessionStorage、IndexedDB、数据缓存策略等前端缓存与持久化方案，帮助提升数据持久性与用户体验。"}, {"id": "performance-tools", "name": "性能分析工具", "file": "Vue/performance-tools.json", "description": "系统讲解Vue Devtools、Chrome Performance、Lighthouse、性能瓶颈排查等前端性能分析工具，帮助定位和优化项目性能。"}, {"id": "unit-testing", "name": "单元测试", "file": "Vue/unit-testing.json", "description": "系统讲解测试环境搭建、测试库选择、组件单元测试、组合函数测试、异步代码测试、mock依赖等，帮助掌握Vue项目单元测试全流程。"}, {"id": "component-testing", "name": "组件测试", "file": "Vue/component-testing.json", "description": "系统讲解组件渲染测试、交互测试、快照测试、事件测试、状态变化测试、属性验证等，帮助掌握Vue组件测试全流程。"}, {"id": "integration-testing", "name": "集成测试", "file": "Vue/integration-testing.json", "description": "系统讲解路由测试、状态管理测试、API交互测试、用户流程测试、测试覆盖率等，帮助掌握Vue项目集成测试全流程。"}, {"id": "vue-test-utils", "name": "Vue Test Utils", "file": "Vue/vue-test-utils.json", "description": "系统讲解渲染组件、查询元素、触发事件、断言结果、异步测试、最佳实践等，帮助掌握Vue Test Utils的核心用法和测试规范。"}, {"id": "jest-vtu-integration", "name": "Jest与VTU结合", "file": "Vue/jest-vtu-integration.json", "description": "系统讲解测试结构、mock函数、快照测试、钩子测试、覆盖率报告、持续集成等，帮助掌握Jest与VTU结合的测试全流程和自动化。"}, {"id": "nuxtjs", "name": "Nuxt.js", "file": "Vue/nuxtjs.json", "description": "系统讲解服务端渲染、静态站点生成、路由自动生成、API集成、布局与页面、插件机制、性能优化等，帮助掌握Nuxt.js核心用法与最佳实践。"}, {"id": "typescript-and-vue", "name": "TypeScript与Vue", "file": "Vue/typescript-and-vue.json", "description": "系统讲解类型定义、组件类型、Props类型、组合式API类型、事件类型、通用组件、类型断言等，帮助掌握TypeScript与Vue结合的类型体系和最佳实践。"}, {"id": "vue-query-apollo", "name": "Vue Query/Vue Apollo", "file": "Vue/vue-query-apollo.json", "description": "系统讲解数据获取、缓存管理、请求状态、乐观更新、无限加载、轮询和重试、预取数据等，帮助掌握现代Vue数据管理与优化。"}, {"id": "css-in-js-and-style", "name": "CSS-in-JS与样式方案", "file": "Vue/css-in-js-and-style.json", "description": "系统讲解scoped样式、CSS Modules、动态样式、全局样式、样式复用、CSS-in-JS、SSR样式处理等，帮助掌握Vue项目的多样化样式解决方案。"}, {"id": "animation-and-interaction", "name": "动画与交互", "file": "Vue/animation-and-interaction.json", "description": "系统讲解Transition组件、动画钩子、页面转场、手势交互、微交互、性能优化、可访问性等，帮助掌握Vue动画与交互开发。"}, {"id": "vue-devtools", "name": "Vue Devtools", "file": "Vue/vue-devtools.json", "description": "系统讲解组件检查、Props和State查看、性能分析、调试组合式API、组件过滤、时间旅行调试、网络环境模拟等，帮助掌握Vue Devtools调试与分析能力。"}]}