{"name": "Context + useReducer", "trans": ["Context + useReducer"], "methods": [{"name": "Global State Management", "trans": ["全局状态管理"], "usage": {"syntax": "const [state, dispatch] = useReducer(reducer, initialState);\n<MyContext.Provider value={{ state, dispatch }}>...</MyContext.Provider>", "description": "结合Context和useReducer可实现全局状态管理，适合中大型应用。Context提供跨组件访问，useReducer集中管理状态和逻辑。", "parameters": [{"name": "reducer", "description": "状态更新函数"}, {"name": "initialState", "description": "初始状态"}], "returnValue": "全局共享的state和dispatch函数", "examples": [{"code": "import React, { createContext, useReducer, useContext } from 'react';\n// 1. 定义初始状态\nconst initialState = { count: 0 };\n// 2. 定义reducer函数\nfunction reducer(state, action) {\n  switch (action.type) {\n    case 'increment':\n      return { ...state, count: state.count + 1 };\n    default:\n      return state;\n  }\n}\n// 3. 创建Context对象\nconst CounterContext = createContext();\n// 4. 提供全局状态的Provider组件\nfunction CounterProvider({ children }) {\n  const [state, dispatch] = useReducer(reducer, initialState);\n  return (\n    <CounterContext.Provider value={{ state, dispatch }}>\n      {children}\n    </CounterContext.Provider>\n  );\n}\n// 5. 消费全局状态的组件\nfunction Counter() {\n  const { state, dispatch } = useContext(CounterContext);\n  return (\n    <div>\n      <span>全局计数: {state.count}</span>\n      <button onClick={() => dispatch({ type: 'increment' })}>加一</button>\n    </div>\n  );\n}\n// 6. 用法示例\nfunction App() {\n  return (\n    <CounterProvider>\n      <Counter />\n    </CounterProvider>\n  );\n}", "explanation": "通过Context和useReducer实现全局计数器。"}]}}, {"name": "Reducer Design", "trans": ["reducer设计"], "usage": {"syntax": "function reducer(state, action) { ... }", "description": "reducer函数根据action类型和参数返回新的状态，需保证纯函数、不可变性和可预测性。", "parameters": [{"name": "state", "description": "当前状态"}, {"name": "action", "description": "描述操作的对象"}], "returnValue": "新的状态对象", "examples": [{"code": "// 计数器reducer示例\nfunction reducer(state, action) {\n  switch (action.type) {\n    case 'increment':\n      return { ...state, count: state.count + 1 };\n    case 'decrement':\n      return { ...state, count: state.count - 1 };\n    default:\n      return state;\n  }\n}", "explanation": "reducer根据action类型返回新状态，保持不可变性。"}]}}, {"name": "Action Type Definition", "trans": ["action类型定义"], "usage": {"syntax": "dispatch({ type: 'actionType', payload })", "description": "action对象通常包含type字段（必需）和payload字段（可选），type用于区分操作类型，payload传递额外数据。", "parameters": [{"name": "type", "description": "操作类型标识符"}, {"name": "payload", "description": "附加数据"}], "returnValue": "无返回值（通过reducer更新状态）", "examples": [{"code": "// 派发带payload的action\ndispatch({ type: 'setUser', payload: { name: '小明' } });\n// reducer处理\nfunction reducer(state, action) {\n  switch (action.type) {\n    case 'setUser':\n      return { ...state, user: action.payload };\n    default:\n      return state;\n  }\n}", "explanation": "action类型和payload的标准用法。"}]}}, {"name": "State Splitting", "trans": ["状态分割"], "usage": {"syntax": "// 将不同业务状态拆分到不同reducer或Context", "description": "大型应用可将状态按业务模块拆分，分别用不同reducer和Context管理，提升可维护性和性能。", "parameters": [], "returnValue": "分割后的独立状态模块", "examples": [{"code": "// 用户状态reducer\nfunction userReducer(state, action) { ... }\n// 主题状态reducer\nfunction themeReducer(state, action) { ... }\n// 分别用不同Context和Provider包裹\n<UserContext.Provider value={...}>\n  <ThemeContext.Provider value={...}>\n    <App />\n  </ThemeContext.Provider>\n</UserContext.Provider>", "explanation": "将状态按业务拆分，分别管理。"}]}}, {"name": "Middleware Pattern", "trans": ["中间件模式"], "usage": {"syntax": "// 在dispatch前后插入中间处理逻辑", "description": "可通过自定义dispatch或封装Provider，在dispatch前后插入日志、异步、权限等中间件逻辑。", "parameters": [], "returnValue": "增强后的dispatch函数", "examples": [{"code": "import React, { createContext, useReducer } from 'react';\n// 日志中间件dispatch\nfunction useLoggerReducer(reducer, initialState) {\n  const [state, dispatch] = useReducer(reducer, initialState);\n  // 封装dispatch，打印日志\n  function loggerDispatch(action) {\n    console.log('action:', action);\n    dispatch(action);\n  }\n  return [state, loggerDispatch];\n}\n// 用法\nconst MyContext = createContext();\nfunction Provider({ children }) {\n  const [state, dispatch] = useLoggerReducer(reducer, initialState);\n  return <MyContext.Provider value={{ state, dispatch }}>{children}</MyContext.Provider>;\n}", "explanation": "通过自定义dispatch实现中间件功能。"}]}}, {"name": "Async Operation Handling", "trans": ["异步操作处理"], "usage": {"syntax": "// 在dispatch中处理异步逻辑或结合useEffect", "description": "useReducer本身不支持异步，可在组件中用useEffect处理副作用，或用中间件封装dispatch支持异步。", "parameters": [], "returnValue": "异步操作后的状态更新", "examples": [{"code": "import React, { useReducer, useEffect } from 'react';\n// 异步action示例\nfunction reducer(state, action) {\n  switch (action.type) {\n    case 'fetchSuccess':\n      return { ...state, data: action.payload };\n    default:\n      return state;\n  }\n}\nfunction DataProvider() {\n  const [state, dispatch] = useReducer(reducer, { data: null });\n  useEffect(() => {\n    // 异步获取数据\n    fetch('/api/data').then(res => res.json()).then(data => {\n      dispatch({ type: 'fetchSuccess', payload: data });\n    });\n  }, []);\n  return <div>数据: {JSON.stringify(state.data)}</div>;\n}", "explanation": "结合useEffect和dispatch处理异步操作。"}]}}, {"name": "作业：实现一个全局待办事项管理", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 用Context+useReducer实现全局待办管理。\n// 2. 支持添加、删除、切换完成。\n// 3. 代码结构清晰，注释完整。", "description": "请实现上述全局待办管理，提交时请附上完整代码和注释。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 示例代码略，请学生自行实现\n// 提示：用Context+useReducer实现全局状态。", "explanation": "作业要求学生掌握Context+useReducer的全局管理。"}, {"code": "import React, { createContext, useReducer, useContext, useState } from 'react';\n// 1. 定义初始状态\nconst initialState = { todos: [] };\n// 2. 定义reducer\nfunction reducer(state, action) {\n  switch (action.type) {\n    case 'add':\n      // 添加新待办，done初始为false\n      return { ...state, todos: [...state.todos, { text: action.text, done: false }] };\n    case 'toggle':\n      // 切换待办完成状态\n      return { ...state, todos: state.todos.map((t, i) => i === action.index ? { ...t, done: !t.done } : t) };\n    case 'remove':\n      // 删除指定下标的待办\n      return { ...state, todos: state.todos.filter((_, i) => i !== action.index) };\n    default:\n      return state;\n  }\n}\n// 3. 创建Context\nconst TodoContext = createContext();\n// 4. Provider组件\nfunction TodoProvider({ children }) {\n  const [state, dispatch] = useReducer(reducer, initialState);\n  return <TodoContext.Provider value={{ state, dispatch }}>{children}</TodoContext.Provider>;\n}\n// 5. 消费组件\nfunction TodoList() {\n  const { state, dispatch } = useContext(TodoContext);\n  const [text, setText] = useState('');\n  return (\n    <div>\n      {/* 输入框和添加按钮 */}\n      <input value={text} onChange={e => setText(e.target.value)} placeholder=\"添加待办\" />\n      <button onClick={() => { dispatch({ type: 'add', text }); setText(''); }}>添加</button>\n      {/* 待办列表 */}\n      <ul>\n        {state.todos.map((todo, i) => (\n          <li key={i} style={{ textDecoration: todo.done ? 'line-through' : 'none' }}>\n            {todo.text}\n            {/* 切换完成状态按钮 */}\n            <button onClick={() => dispatch({ type: 'toggle', index: i })}>切换</button>\n            {/* 删除按钮 */}\n            <button onClick={() => dispatch({ type: 'remove', index: i })}>删除</button>\n          </li>\n        ))}\n      </ul>\n    </div>\n  );\n}\n// 6. 用法\nfunction App() {\n  return (\n    <TodoProvider>\n      <TodoList />\n    </TodoProvider>\n  );\n}", "explanation": "完整实现全局待办管理，支持增删改查。"}]}}]}