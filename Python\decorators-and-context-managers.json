{"name": "Decorators and Context Managers", "trans": ["装饰器与上下文管理器"], "methods": [{"name": "Function Decorators", "trans": ["函数装饰器"], "usage": {"syntax": "@decorator\ndef func(): ...", "description": "函数装饰器用于在不修改原函数代码的情况下，动态增加功能。常用于日志、权限、缓存等。", "parameters": [{"name": "decorator", "description": "装饰器函数，接收被装饰函数作为参数"}, {"name": "func", "description": "被装饰的函数"}], "returnValue": "增强后的函数对象", "examples": [{"code": "def log(func):\n    def wrapper(*args, **kwargs):\n        print('调用', func.__name__)\n        return func(*args, **kwargs)\n    return wrapper\n@log\ndef hello():\n    print('hello')\nhello()", "explanation": "演示了函数装饰器的基本用法。"}]}}, {"name": "Class Decorators", "trans": ["类装饰器"], "usage": {"syntax": "@decorator\nclass MyClass: ...", "description": "类装饰器用于动态修改类的行为，常用于注册、单例等场景。", "parameters": [{"name": "decorator", "description": "装饰器函数，接收被装饰类作为参数"}, {"name": "MyClass", "description": "被装饰的类"}], "returnValue": "增强后的类对象", "examples": [{"code": "def singleton(cls):\n    instances = {}\n    def get_instance(*args, **kwargs):\n        if cls not in instances:\n            instances[cls] = cls(*args, **kwargs)\n        return instances[cls]\n    return get_instance\n@singleton\nclass A: pass\na1 = A()\na2 = A()\nprint(a1 is a2)", "explanation": "演示了类装饰器实现单例模式。"}]}}, {"name": "<PERSON><PERSON><PERSON>", "trans": ["contextlib模块"], "usage": {"syntax": "from contextlib import contextmanager\n@contextmanager\ndef my_ctx(): ...", "description": "contextlib模块简化了上下文管理器的实现，@contextmanager装饰器可用生成器快速实现with语句。", "parameters": [{"name": "@contextmanager", "description": "装饰生成器函数为上下文管理器"}], "returnValue": "上下文管理器对象", "examples": [{"code": "from contextlib import contextmanager\n@contextmanager\ndef myctx():\n    print('进入')\n    yield\n    print('退出')\nwith myctx():\n    print('处理中')", "explanation": "演示了contextlib模块的用法。"}]}}, {"name": "Custom Context Manager", "trans": ["自定义上下文管理器"], "usage": {"syntax": "class MyCtx:\n    def __enter__(self): ...\n    def __exit__(self, exc_type, exc_val, exc_tb): ...", "description": "自定义类实现__enter__和__exit__方法即可作为上下文管理器，配合with语句自动管理资源。", "parameters": [{"name": "__enter__", "description": "进入上下文时执行，返回资源对象"}, {"name": "__exit__", "description": "退出上下文时执行，处理清理或异常"}], "returnValue": "上下文管理器对象", "examples": [{"code": "class MyCtx:\n    def __enter__(self):\n        print('进入')\n        return self\n    def __exit__(self, exc_type, exc_val, exc_tb):\n        print('退出')\nwith MyCtx():\n    print('处理中')", "explanation": "演示了自定义上下文管理器的用法。"}]}}, {"name": "Decorators and Context Managers Assignment", "trans": ["装饰器与上下文管理器练习"], "usage": {"syntax": "# 装饰器与上下文管理器练习", "description": "完成以下练习，巩固装饰器与上下文管理器相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 编写一个函数装饰器统计函数调用次数。\n2. 用类装饰器实现单例。\n3. 用contextlib实现一个计时器上下文。\n4. 自定义一个文件操作上下文管理器。", "explanation": "练习要求动手实践函数装饰器、类装饰器、contextlib、自定义上下文管理器等。"}]}}]}