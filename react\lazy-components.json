{"name": "Lazy Components", "trans": ["懒加载组件"], "methods": [{"name": "Dynamic Import", "trans": ["动态导入"], "usage": {"syntax": "import('./Component').then(module => {\n  const Component = module.default;\n  // 使用组件\n});", "description": "使用JavaScript动态导入语法import()实现组件的按需加载，返回Promise对象，可在运行时决定是否加载组件。", "parameters": [{"name": "路径", "description": "要导入的组件路径，支持相对路径和绝对路径"}], "returnValue": "返回Promise，解析为包含组件默认导出和命名导出的模块对象。", "examples": [{"code": "// 动态导入示例\nimport React, { useState } from 'react';\n\nfunction App() {\n  const [Component, setComponent] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  \n  const loadComponent = () => {\n    setIsLoading(true);\n    // 动态导入组件\n    import('./HeavyComponent')\n      .then(module => {\n        // 获取默认导出的组件\n        const Component = module.default;\n        setComponent(() => Component); // 保存组件构造函数\n        setIsLoading(false);\n      })\n      .catch(error => {\n        console.error('加载组件失败:', error);\n        setIsLoading(false);\n      });\n  };\n  \n  return (\n    <div>\n      <button onClick={loadComponent} disabled={isLoading}>\n        {isLoading ? '加载中...' : '加载复杂组件'}\n      </button>\n      \n      {Component && <Component />}\n    </div>\n  );\n}", "explanation": "使用动态import()语法实现组件的按需加载，仅在用户点击按钮时才加载HeavyComponent。"}]}}, {"name": "Conditional Loading", "trans": ["条件加载"], "usage": {"syntax": "const OtherComponent = React.lazy(() => \n  condition \n    ? import('./ComponentA') \n    : import('./ComponentB')\n);", "description": "基于条件动态决定加载哪个组件，可以根据用户权限、设备类型、功能开关等条件懒加载不同组件。", "parameters": [{"name": "条件表达式", "description": "决定加载哪个组件的条件"}], "returnValue": "返回基于条件动态加载的懒加载组件。", "examples": [{"code": "// 条件加载示例\nimport React, { Suspense, useState } from 'react';\n\nfunction FeatureSwitch() {\n  const [isAdmin, setIsAdmin] = useState(false);\n  \n  // 基于用户角色条件加载不同组件\n  const Dashboard = React.lazy(() => \n    isAdmin \n      ? import('./AdminDashboard') // 管理员仪表盘\n      : import('./UserDashboard')  // 普通用户仪表盘\n  );\n  \n  return (\n    <div>\n      <label>\n        <input \n          type=\"checkbox\" \n          checked={isAdmin} \n          onChange={() => setIsAdmin(!isAdmin)} \n        />\n        管理员模式\n      </label>\n      \n      <Suspense fallback={<div>加载仪表盘...</div>}>\n        <Dashboard />\n      </Suspense>\n    </div>\n  );\n}\n\n// 特性开关条件加载\nfunction FeatureFlagExample() {\n  const features = {\n    newUI: true,\n    betaFeature: false\n  };\n  \n  // 基于特性开关加载不同版本组件\n  const UserProfile = React.lazy(() => \n    features.newUI \n      ? import('./NewUserProfile')\n      : import('./OldUserProfile')\n  );\n  \n  const BetaFeature = React.lazy(() => \n    features.betaFeature \n      ? import('./BetaFeature')\n      : Promise.resolve({ default: () => null }) // 空组件\n  );\n  \n  return (\n    <Suspense fallback={<div>加载中...</div>}>\n      <UserProfile />\n      <BetaFeature />\n    </Suspense>\n  );\n}", "explanation": "展示了如何基于用户角色和特性开关条件加载不同组件，实现灵活的功能配置。"}]}}, {"name": "Preloading Techniques", "trans": ["预加载技术"], "usage": {"syntax": "// 鼠标悬停预加载\nconst preloadComponent = () => import('./Component');\n\n// 视口预加载\nconst observer = new IntersectionObserver(entries => {\n  if (entries[0].isIntersecting) {\n    import('./Component');\n  }\n});", "description": "通过用户行为(悬停、滚动)、空闲时间或优先级预测提前加载组件，减少感知延迟，提升用户体验。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 预加载技术示例\nimport React, { useState, useEffect } from 'react';\n\n// 1. 鼠标悬停预加载\nfunction HoverPreload() {\n  const [showDetails, setShowDetails] = useState(false);\n  const [ProductDetails, setProductDetails] = useState(null);\n  \n  // 鼠标悬停时预加载\n  const handleMouseEnter = () => {\n    if (!ProductDetails) {\n      import('./ProductDetails')\n        .then(module => setProductDetails(() => module.default))\n        .catch(err => console.error('预加载失败:', err));\n    }\n  };\n  \n  return (\n    <div>\n      <div \n        className=\"product-card\" \n        onMouseEnter={handleMouseEnter}\n        onClick={() => setShowDetails(true)}\n      >\n        产品信息 (悬停预加载详情)\n      </div>\n      \n      {showDetails && ProductDetails && <ProductDetails />}\n    </div>\n  );\n}\n\n// 2. 路由切换预加载\nfunction RouterPreload() {\n  // 导航链接预加载\n  const preloadAbout = () => import('./About');\n  const preloadContact = () => import('./Contact');\n  \n  return (\n    <nav>\n      <a href=\"/about\" onMouseEnter={preloadAbout}>关于我们</a>\n      <a href=\"/contact\" onMouseEnter={preloadContact}>联系我们</a>\n    </nav>\n  );\n}\n\n// 3. 视口预加载 (Intersection Observer)\nfunction ViewportPreload() {\n  const containerRef = React.useRef(null);\n  const [Comments, setComments] = useState(null);\n  \n  useEffect(() => {\n    // 创建交叉观察器\n    const observer = new IntersectionObserver(entries => {\n      if (entries[0].isIntersecting) {\n        // 进入视口时加载评论组件\n        import('./Comments')\n          .then(module => setComments(() => module.default))\n          .catch(err => console.error('预加载失败:', err));\n        \n        // 加载后停止观察\n        observer.disconnect();\n      }\n    }, { rootMargin: '200px' }); // 提前200px触发\n    \n    if (containerRef.current) {\n      observer.observe(containerRef.current);\n    }\n    \n    return () => observer.disconnect();\n  }, []);\n  \n  return (\n    <div>\n      <div className=\"article-content\">文章内容...</div>\n      \n      {/* 当用户滚动接近这个div时，会预加载Comments组件 */}\n      <div ref={containerRef} className=\"comments-section\">\n        <h3>评论区</h3>\n        {Comments ? <Comments /> : <div>加载评论...</div>}\n      </div>\n    </div>\n  );\n}", "explanation": "展示了三种预加载技术：鼠标悬停预加载、路由链接预加载和基于视口的预加载，提前加载可能需要的组件。"}]}}, {"name": "Loading Boundaries", "trans": ["加载边界"], "usage": {"syntax": "<Suspense fallback={<LoadingComponent />}>\n  <LazyComponent />\n</Suspense>", "description": "使用React.Suspense定义加载边界，优雅处理懒加载组件的加载状态，支持嵌套和并行加载多个组件。", "parameters": [{"name": "fallback", "description": "组件加载期间显示的内容"}], "returnValue": "无返回值", "examples": [{"code": "// 加载边界示例\nimport React, { Suspense } from 'react';\n\n// 懒加载多个组件\nconst Header = React.lazy(() => import('./Header'));\nconst Content = React.lazy(() => import('./Content'));\nconst Sidebar = React.lazy(() => import('./Sidebar'));\nconst Footer = React.lazy(() => import('./Footer'));\n\n// 加载状态组件\nfunction LoadingSpinner() {\n  return <div className=\"spinner\">加载中...</div>;\n}\n\nfunction SkeletonHeader() {\n  return <div className=\"skeleton-header\"></div>;\n}\n\nfunction SkeletonContent() {\n  return <div className=\"skeleton-content\"></div>;\n}\n\n// 嵌套的Suspense边界\nfunction App() {\n  return (\n    <div className=\"app\">\n      {/* 整个应用的顶级Suspense */}\n      <Suspense fallback={<div>应用加载中...</div>}>\n        {/* 页眉的Suspense */}\n        <Suspense fallback={<SkeletonHeader />}>\n          <Header />\n        </Suspense>\n        \n        <div className=\"main\">\n          {/* 内容区的Suspense */}\n          <Suspense fallback={<SkeletonContent />}>\n            <Content />\n          </Suspense>\n          \n          {/* 侧边栏Suspense */}\n          <Suspense fallback={<LoadingSpinner />}>\n            <Sidebar />\n          </Suspense>\n        </div>\n        \n        {/* 页脚Suspense */}\n        <Suspense fallback={<LoadingSpinner />}>\n          <Footer />\n        </Suspense>\n      </Suspense>\n    </div>\n  );\n}\n\n// 并行加载多个组件\nfunction ParallelLoading() {\n  return (\n    <Suspense fallback={<LoadingSpinner />}>\n      {/* 这些组件会并行加载 */}\n      <Header />\n      <Content />\n      <Sidebar />\n    </Suspense>\n  );\n}", "explanation": "通过嵌套和并行的Suspense加载边界，优化用户体验，可为不同区域设置不同加载状态。"}]}}, {"name": "Erro<PERSON>", "trans": ["失败处理"], "usage": {"syntax": "<ErrorBoundary fallback={<ErrorComponent />}>\n  <Suspense fallback={<LoadingComponent />}>\n    <LazyComponent />\n  </Suspense>\n</ErrorBoundary>", "description": "使用错误边界(Error Boundary)捕获懒加载失败的错误，提供友好的错误反馈和恢复机制。", "parameters": [{"name": "fallback", "description": "错误发生时显示的内容"}], "returnValue": "无返回值", "examples": [{"code": "// 失败处理示例\nimport React, { Suspense, useState } from 'react';\n\n// 错误边界组件\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { hasError: false, error: null };\n  }\n  \n  static getDerivedStateFromError(error) {\n    return { hasError: true, error };\n  }\n  \n  componentDidCatch(error, info) {\n    // 记录错误到日志服务\n    console.error('组件加载失败:', error, info);\n  }\n  \n  retry = () => {\n    this.setState({ hasError: false, error: null });\n  };\n  \n  render() {\n    if (this.state.hasError) {\n      // 自定义错误UI\n      return (\n        <div className=\"error-container\">\n          <h3>加载失败</h3>\n          <p>{this.state.error?.message || '组件加载出错'}</p>\n          <button onClick={this.retry}>重试</button>\n          {this.props.fallback}\n        </div>\n      );\n    }\n    \n    return this.props.children;\n  }\n}\n\n// 使用ErrorBoundary包裹懒加载组件\nfunction App() {\n  const LazyComponent = React.lazy(() => import('./MayFailComponent'));\n  \n  return (\n    <div>\n      <h1>我的应用</h1>\n      \n      <ErrorBoundary fallback={<div>请检查网络连接后重试</div>}>\n        <Suspense fallback={<div>加载中...</div>}>\n          <LazyComponent />\n        </Suspense>\n      </ErrorBoundary>\n    </div>\n  );\n}\n\n// 更多错误处理策略\nfunction AdvancedErrorHandling() {\n  const [key, setKey] = useState(0);\n  const retryLoading = () => setKey(k => k + 1);\n  \n  return (\n    <div>\n      {/* 使用key重置组件状态 */}\n      <ErrorBoundary key={key} fallback={\n        <div>\n          <p>加载失败</p>\n          <button onClick={retryLoading}>重新加载</button>\n        </div>\n      }>\n        <Suspense fallback={<div>加载中...</div>}>\n          <React.lazy(() => {\n            // 模拟可能失败的组件加载\n            return new Promise((resolve, reject) => {\n              setTimeout(() => {\n                // 随机成功或失败\n                if (Math.random() > 0.5) {\n                  resolve({ default: () => <div>成功加载的组件</div> });\n                } else {\n                  reject(new Error('网络错误'));\n                }\n              }, 1000);\n            });\n          })()\n        </Suspense>\n      </ErrorBoundary>\n    </div>\n  );\n}", "explanation": "通过ErrorBoundary包裹Suspense和懒加载组件，优雅处理加载失败的情况，提供重试机制。"}]}}, {"name": "User Experience Considerations", "trans": ["用户体验考虑"], "usage": {"syntax": "// 用户体验优化策略：\n// 1. 合理设计加载状态UI\n// 2. 实现渐进式加载\n// 3. 最小化加载时间", "description": "通过精心设计的加载状态、骨架屏、进度指示、预加载和优先级控制，提升懒加载组件的用户体验。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 用户体验优化示例\nimport React, { Suspense, useState, useEffect } from 'react';\n\n// 1. 渐进式骨架屏\nfunction SkeletonCard() {\n  return (\n    <div className=\"skeleton-card\">\n      <div className=\"skeleton-image pulse\"></div>\n      <div className=\"skeleton-title pulse\"></div>\n      <div className=\"skeleton-text pulse\"></div>\n    </div>\n  );\n}\n\n// 2. 延迟显示加载状态\nfunction DelayedFallback({ delay = 500 }) {\n  const [show, setShow] = useState(false);\n  \n  useEffect(() => {\n    const timer = setTimeout(() => setShow(true), delay);\n    return () => clearTimeout(timer);\n  }, [delay]);\n  \n  return show ? <div className=\"loading-spinner\">加载中...</div> : null;\n}\n\n// 3. 带进度条的加载\nfunction ProgressLoading() {\n  const [progress, setProgress] = useState(0);\n  \n  useEffect(() => {\n    const timer = setInterval(() => {\n      setProgress(p => {\n        const next = p + 10;\n        if (next >= 100) clearInterval(timer);\n        return next > 100 ? 100 : next;\n      });\n    }, 200);\n    \n    return () => clearInterval(timer);\n  }, []);\n  \n  return (\n    <div className=\"loading-container\">\n      <div className=\"progress-bar\">\n        <div className=\"progress\" style={{ width: `${progress}%` }}></div>\n      </div>\n      <div className=\"progress-text\">{progress}% 已加载</div>\n    </div>\n  );\n}\n\n// 使用这些优化策略\nfunction App() {\n  const LazyChart = React.lazy(() => {\n    // 模拟网络延迟\n    return new Promise(resolve => {\n      setTimeout(() => {\n        resolve(import('./Chart'));\n      }, 2000);\n    });\n  });\n  \n  return (\n    <div className=\"dashboard\">\n      <h1>数据仪表盘</h1>\n      \n      {/* 1. 骨架屏 */}\n      <div className=\"chart-container\">\n        <Suspense fallback={<SkeletonCard />}>\n          <LazyChart />\n        </Suspense>\n      </div>\n      \n      {/* 2. 延迟显示加载状态 */}\n      <div className=\"table-container\">\n        <Suspense fallback={<DelayedFallback delay={800} />}>\n          <React.lazy(() => import('./DataTable'))() />\n        </Suspense>\n      </div>\n      \n      {/* 3. 进度条加载 */}\n      <div className=\"analytics-container\">\n        <Suspense fallback={<ProgressLoading />}>\n          <React.lazy(() => import('./Analytics'))() />\n        </Suspense>\n      </div>\n    </div>\n  );\n}", "explanation": "展示了多种用户体验优化策略：骨架屏、延迟显示加载状态和进度条加载，提供更友好的加载体验。"}]}}, {"name": "作业：实现高级懒加载组件", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 实现一个多级Tab页面，每个Tab懒加载\n// 2. 鼠标悬停时预加载即将切换的Tab\n// 3. 提供优雅的加载状态和错误处理\n// 4. 针对不同设备优化加载策略", "description": "通过实践懒加载组件的动态导入、条件加载、预加载、加载边界、错误处理和用户体验优化，掌握高级组件懒加载技术。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 使用React.lazy加载Tab组件\n// 2. 实现onMouseEnter预加载\n// 3. 自定义ErrorBoundary和加载状态\n// 4. 使用媒体查询适配不同设备", "explanation": "作业提示，学生需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nimport React, { Suspense, useState, useEffect } from 'react';\n\nclass ErrorBoundary extends React.Component {\n  state = { hasError: false };\n  static getDerivedStateFromError() { return { hasError: true }; }\n  retry = () => this.setState({ hasError: false });\n  render() {\n    if (this.state.hasError) {\n      return <div className=\"error\"><button onClick={this.retry}>重试</button></div>;\n    }\n    return this.props.children;\n  }\n}\n\nfunction SkeletonTab() {\n  return <div className=\"skeleton-tab\"></div>;\n}\n\n// 定义Tab组件和预加载函数\nconst tabs = [\n  { id: 'home', label: '首页', component: React.lazy(() => import('./HomeTab')) },\n  { id: 'products', label: '产品', component: React.lazy(() => import('./ProductsTab')) },\n  { id: 'about', label: '关于', component: React.lazy(() => import('./AboutTab')) }\n];\n\n// 预加载缓存\nconst preloadCache = {};\nfunction preloadTab(id) {\n  if (!preloadCache[id]) {\n    const tab = tabs.find(tab => tab.id === id);\n    if (tab) {\n      // 开始预加载并缓存Promise\n      preloadCache[id] = import(`./tabs/${id}Tab.js`);\n    }\n  }\n  return preloadCache[id];\n}\n\nfunction App() {\n  const [activeTab, setActiveTab] = useState('home');\n  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);\n  \n  // 响应式检测\n  useEffect(() => {\n    const handleResize = () => setIsMobile(window.innerWidth < 768);\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n  \n  // 获取当前Tab组件\n  const ActiveTabComponent = tabs.find(tab => tab.id === activeTab)?.component;\n  \n  return (\n    <div className=\"app\">\n      <nav className=\"tabs\">\n        {tabs.map(tab => (\n          <button\n            key={tab.id}\n            className={activeTab === tab.id ? 'active' : ''}\n            onClick={() => setActiveTab(tab.id)}\n            onMouseEnter={() => !isMobile && preloadTab(tab.id)}\n          >\n            {tab.label}\n          </button>\n        ))}\n      </nav>\n      \n      <main className=\"tab-content\">\n        <ErrorBoundary>\n          <Suspense fallback={<SkeletonTab />}>\n            {ActiveTabComponent && <ActiveTabComponent />}\n          </Suspense>\n        </ErrorBoundary>\n      </main>\n    </div>\n  );\n}\n\nexport default App;", "explanation": "完整实现了多级Tab页面的懒加载、鼠标悬停预加载、响应式加载策略和错误处理。"}]}}]}