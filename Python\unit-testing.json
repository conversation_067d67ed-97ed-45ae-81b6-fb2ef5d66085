{"name": "Unit Testing", "trans": ["单元测试"], "methods": [{"name": "unittest Framework", "trans": ["unittest框架"], "usage": {"syntax": "import unittest\nclass MyTest(unittest.TestCase): ...", "description": "unittest是Python内置的单元测试框架，支持测试用例、断言、测试套件等。", "parameters": [{"name": "TestCase", "description": "测试用例基类"}, {"name": "setUp/tearDown", "description": "测试前后准备与清理方法"}], "returnValue": "无返回值", "examples": [{"code": "import unittest\nclass MyTest(unittest.TestCase):\n    def test_add(self):\n        self.assertEqual(1+1, 2)\nif __name__ == '__main__':\n    unittest.main()", "explanation": "演示了unittest框架的基本用法。"}]}}, {"name": "pytest Basics", "trans": ["pytest基础"], "usage": {"syntax": "def test_func(): ...\npytest.main()", "description": "pytest是流行的第三方测试框架，语法简洁，支持自动发现测试、丰富插件。", "parameters": [{"name": "test_函数", "description": "以test_开头的测试函数"}], "returnValue": "无返回值", "examples": [{"code": "def add(x, y):\n    return x + y\ndef test_add():\n    assert add(1, 2) == 3", "explanation": "演示了pytest的基本用法。"}]}}, {"name": "Assertions and Test Cases", "trans": ["断言与测试用例"], "usage": {"syntax": "self.assertEqual(a, b)\nassert expr", "description": "断言用于判断测试结果是否符合预期，unittest和pytest均支持多种断言方式。", "parameters": [{"name": "a, b", "description": "要比较的值"}, {"name": "expr", "description": "布尔表达式"}], "returnValue": "无返回值，断言失败抛出异常", "examples": [{"code": "self.assertTrue(1 < 2)\nassert 2 > 1", "explanation": "演示了unittest和pytest的断言用法。"}]}}, {"name": "Test Fixtures", "trans": ["测试夹具fixture"], "usage": {"syntax": "def setup_function(): ...\****************\ndef my_fixture(): ...", "description": "测试夹具用于测试前的准备和测试后的清理，pytest支持fixture装饰器。", "parameters": [{"name": "setup/teardown", "description": "测试前后执行的函数"}, {"name": "@pytest.fixture", "description": "声明fixture"}], "returnValue": "无返回值或fixture对象", "examples": [{"code": "import pytest\****************\ndef data():\n    return [1, 2, 3]\ndef test_len(data):\n    assert len(data) == 3", "explanation": "演示了pytest fixture的用法。"}]}}, {"name": "Mock and patch", "trans": ["Mock与patch"], "usage": {"syntax": "from unittest.mock import Mock, patch", "description": "Mock和patch用于模拟对象和方法，隔离外部依赖，便于单元测试。", "parameters": [{"name": "<PERSON><PERSON>", "description": "创建模拟对象"}, {"name": "patch", "description": "临时替换对象或方法"}], "returnValue": "Mock对象或patch上下文管理器", "examples": [{"code": "from unittest.mock import Mock, patch\nm = Mock(return_value=123)\nprint(m())\nwith patch('os.getcwd', return_value='/tmp'):\n    import os\n    print(os.getcwd())", "explanation": "演示了Mock和patch的用法。"}]}}, {"name": "Coverage Report", "trans": ["覆盖率报告"], "usage": {"syntax": "coverage run -m pytest\ncoverage report", "description": "coverage工具可统计测试代码覆盖率，结合pytest等使用。", "parameters": [{"name": "coverage run", "description": "运行测试并收集覆盖率"}, {"name": "coverage report", "description": "生成覆盖率报告"}], "returnValue": "覆盖率统计结果", "examples": [{"code": "# 命令行执行\ncoverage run -m pytest\ncoverage report", "explanation": "演示了如何生成测试覆盖率报告。"}]}}, {"name": "Unit Testing Assignment", "trans": ["单元测试练习"], "usage": {"syntax": "# 单元测试练习", "description": "完成以下练习，巩固单元测试相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用unittest编写一个加法函数的测试。\n2. 用pytest断言字符串包含关系。\n3. 用fixture提供测试数据。\n4. 用Mock模拟os模块方法。\n5. 生成测试覆盖率报告。", "explanation": "练习要求动手实践unittest、pytest、fixture、Mock、覆盖率等。"}]}}]}