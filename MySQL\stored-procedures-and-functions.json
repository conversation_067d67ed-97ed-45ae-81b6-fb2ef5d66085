{"name": "Stored Procedures and Functions", "trans": ["存储过程与函数"], "methods": [{"name": "Create Stored Procedure", "trans": ["创建存储过程"], "usage": {"syntax": "DELIMITER //\nCREATE PROCEDURE proc_name([参数列表])\nBEGIN\n  -- 过程体\nEND //\nDELIMITER ;", "description": "用于在MySQL中创建存储过程。存储过程是一组预编译的SQL语句，可以带参数，便于批量处理和复用。", "parameters": [{"name": "proc_name", "description": "存储过程名称"}, {"name": "参数列表", "description": "输入、输出或输入输出参数"}], "returnValue": "无返回值（可通过OUT参数返回结果）", "examples": [{"code": "-- 创建一个简单的存储过程，查询学生表总人数\nDELIMITER //\nCREATE PROCEDURE count_students(OUT total INT)\nBEGIN\n  SELECT COUNT(*) INTO total FROM student;\nEND //\nDELIMITER ;", "explanation": "本例创建了一个名为count_students的存储过程，通过OUT参数返回学生表的总人数。"}]}}, {"name": "Call Stored Procedure", "trans": ["调用存储过程"], "usage": {"syntax": "CALL proc_name([参数]);", "description": "用于调用已创建的存储过程，可以传递参数并获取OUT参数结果。", "parameters": [{"name": "proc_name", "description": "存储过程名称"}, {"name": "参数", "description": "调用时传递的参数"}], "returnValue": "无返回值（OUT参数可获取结果）", "examples": [{"code": "-- 调用count_students过程并获取结果\nSET @total = 0;\nCALL count_students(@total);\nSELECT @total;", "explanation": "本例演示了如何调用带OUT参数的存储过程，并通过用户变量获取结果。"}]}}, {"name": "Create Function", "trans": ["创建函数"], "usage": {"syntax": "DELIMITER //\nCREATE FUNCTION func_name([参数列表]) RETURNS 数据类型\nBEGIN\n  -- 函数体\n  RETURN 返回值;\nEND //\nDELIMITER ;", "description": "用于在MySQL中创建自定义函数。函数可在SQL语句中直接调用，返回单一值。", "parameters": [{"name": "func_name", "description": "函数名称"}, {"name": "参数列表", "description": "函数参数"}, {"name": "数据类型", "description": "函数返回值类型"}], "returnValue": "函数返回值（单一值）", "examples": [{"code": "-- 创建一个返回学生数量的函数\nDELIMITER //\nCREATE FUNCTION get_student_count() RETURNS INT\nBEGIN\n  DECLARE total INT;\n  SELECT COUNT(*) INTO total FROM student;\n  RETURN total;\nEND //\nDELIMITER ;", "explanation": "本例创建了一个名为get_student_count的函数，返回学生表的总人数。"}]}}, {"name": "Use Function", "trans": ["使用函数"], "usage": {"syntax": "SELECT func_name([参数]);", "description": "在SQL语句中直接调用自定义函数，获取返回值。", "parameters": [{"name": "func_name", "description": "函数名称"}, {"name": "参数", "description": "调用时传递的参数"}], "returnValue": "函数返回值（单一值）", "examples": [{"code": "-- 查询学生总人数\nSELECT get_student_count();", "explanation": "本例演示了如何在SELECT语句中直接调用自定义函数。"}]}}, {"name": "Stored Procedures and Functions Assignment", "trans": ["存储过程与函数练习"], "usage": {"syntax": "# 存储过程与函数练习", "description": "完成以下练习，巩固MySQL存储过程与函数的基本用法。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 创建一个存储过程，统计student表中成绩大于80分的学生人数，并通过OUT参数返回。\n2. 创建一个函数，返回指定学生的平均成绩。\n3. 调用上述过程和函数，验证结果。", "explanation": "通过这些练习，你将掌握MySQL存储过程与函数的创建、调用和实际应用。"}]}}]}