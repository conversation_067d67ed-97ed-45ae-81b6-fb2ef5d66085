{"name": "Compound Components", "trans": ["复合组件"], "methods": [{"name": "Compound Component Pattern", "trans": ["复合组件模式"], "usage": {"syntax": "<Toggle>\n  <Toggle.On>开</Toggle.On>\n  <Toggle.Off>关</Toggle.Off>\n  <Toggle.Button />\n</Toggle>", "description": "复合组件是一组协同工作的组件，通常由一个父组件和多个子组件组成。父组件负责状态管理，子组件通过约定的API访问和响应状态，实现灵活组合和可读性。", "parameters": [{"name": "children", "description": "复合组件的子组件"}], "returnValue": "渲染复合组件结构和内容", "examples": [{"code": "// Toggle复合组件模式\nfunction Toggle({ children }) {\n  const [on, setOn] = React.useState(false);\n  return React.Children.map(children, child => {\n    if (child.type === Toggle.On) return on ? child : null;\n    if (child.type === Toggle.Off) return !on ? child : null;\n    if (child.type === Toggle.Button) return React.cloneElement(child, { on, setOn });\n    return child;\n  });\n}\nToggle.On = ({ children }) => children;\nToggle.Off = ({ children }) => children;\nToggle.Button = ({ on, setOn }) => (\n  <button onClick={() => setOn(o => !o)}>{on ? '关' : '开'}</button>\n);\n// 用法\n<Toggle>\n  <Toggle.On>开</Toggle.On>\n  <Toggle.Off>关</Toggle.Off>\n  <Toggle.Button />\n</Toggle>", "explanation": "Toggle复合组件通过子组件类型约定，实现灵活的状态共享和组合。"}]}}, {"name": "Shared Internal State", "trans": ["内部状态共享"], "usage": {"syntax": "// 父组件通过Context或props向子组件共享状态", "description": "复合组件通过父组件集中管理状态，并通过props或Context向所有子组件共享，实现状态同步和响应。", "parameters": [], "returnValue": "子组件可访问和响应的共享状态", "examples": [{"code": "// 使用Context实现状态共享\nconst ToggleContext = React.createContext();\nfunction Toggle({ children }) {\n  const [on, setOn] = React.useState(false);\n  return (\n    <ToggleContext.Provider value={{ on, setOn }}>\n      {children}\n    </ToggleContext.Provider>\n  );\n}\nfunction On({ children }) {\n  const { on } = React.useContext(ToggleContext);\n  return on ? children : null;\n}\nfunction Off({ children }) {\n  const { on } = React.useContext(ToggleContext);\n  return !on ? children : null;\n}\nfunction Button() {\n  const { on, setOn } = React.useContext(ToggleContext);\n  return <button onClick={() => setOn(o => !o)}>{on ? '关' : '开'}</button>;\n}\n// 用法\n<Toggle>\n  <On>开</On>\n  <Off>关</Off>\n  <Button />\n</Toggle>", "explanation": "通过Context实现父子组件间的状态共享，子组件可随时访问和响应状态。"}]}}, {"name": "Component API Design", "trans": ["组件API设计"], "usage": {"syntax": "// 通过静态属性、Context、props等设计API", "description": "复合组件API设计应简洁、直观，支持灵活组合和扩展。常用方式有静态属性、Context、props约定等。", "parameters": [], "returnValue": "易用且可扩展的组件API", "examples": [{"code": "// 静态属性API\nfunction Tabs({ children }) {\n  // ...\n}\nTabs.Tab = function Tab(props) { return <div>{props.children}</div>; };\n// 用法\n<Tabs>\n  <Tabs.Tab>内容</Tabs.Tab>\n</Tabs>", "explanation": "通过静态属性暴露子组件，提升API一致性和可读性。"}]}}, {"name": "Combining with Context", "trans": ["上下文结合"], "usage": {"syntax": "// 复合组件结合Context实现深层状态共享", "description": "Context可用于复合组件深层嵌套时的状态共享，避免多层props传递。", "parameters": [], "returnValue": "深层组件可访问的共享状态", "examples": [{"code": "const TabsContext = React.createContext();\nfunction Tabs({ children }) {\n  const [active, setActive] = React.useState(0);\n  return (\n    <TabsContext.Provider value={{ active, setActive }}>\n      {children}\n    </TabsContext.Provider>\n  );\n}\nfunction TabList({ children }) {\n  return <div>{children}</div>;\n}\nfunction Tab({ index, children }) {\n  const { active, setActive } = React.useContext(TabsContext);\n  return (\n    <button\n      style={{ fontWeight: index === active ? 'bold' : 'normal' }}\n      onClick={() => setActive(index)}\n    >\n      {children}\n    </button>\n  );\n}\nfunction TabPanels({ children }) {\n  const { active } = React.useContext(TabsContext);\n  return <div>{React.Children.toArray(children)[active]}</div>;\n}\n// 用法\n<Tabs>\n  <TabList>\n    <Tab index={0}>Tab1</Tab>\n    <Tab index={1}>Tab2</Tab>\n  </TabList>\n  <TabPanels>\n    <div>内容1</div>\n    <div>内容2</div>\n  </TabPanels>\n</Tabs>", "explanation": "通过Context实现多层复合组件的状态共享和交互。"}]}}, {"name": "Enabling Customization", "trans": ["实现可定制性"], "usage": {"syntax": "// 通过props、render props、Context等实现定制", "description": "复合组件可通过props、render props、Context等方式暴露定制点，支持样式、行为、内容等灵活扩展。", "parameters": [], "returnValue": "可定制的复合组件", "examples": [{"code": "function Modal({ children, footer }) {\n  return (\n    <div className=\"modal\">\n      <div className=\"content\">{children}</div>\n      {footer && <div className=\"footer\">{footer}</div>}\n    </div>\n  );\n}\n// 用法\n<Modal footer={<button>关闭</button>}>内容</Modal>", "explanation": "通过props暴露footer插槽，实现内容和行为的定制。"}]}}, {"name": "Practical Example", "trans": ["实际应用示例"], "usage": {"syntax": "// 见下方Tabs复合组件示例", "description": "Tabs、Accordion、Modal等都是典型的复合组件，支持灵活组合和状态共享。", "parameters": [], "returnValue": "完整的复合组件实现示例", "examples": [{"code": "import React, { useState, createContext, useContext } from 'react';\nconst TabsContext = createContext();\nfunction Tabs({ children }) {\n  const [active, setActive] = useState(0);\n  return (\n    <TabsContext.Provider value={{ active, setActive }}>\n      {children}\n    </TabsContext.Provider>\n  );\n}\nfunction TabList({ children }) {\n  return <div style={{ display: 'flex' }}>{children}</div>;\n}\nfunction Tab({ index, children }) {\n  const { active, setActive } = useContext(TabsContext);\n  return (\n    <button\n      style={{ fontWeight: index === active ? 'bold' : 'normal', marginRight: 8 }}\n      onClick={() => setActive(index)}\n    >\n      {children}\n    </button>\n  );\n}\nfunction TabPanels({ children }) {\n  const { active } = useContext(TabsContext);\n  return <div style={{ marginTop: 16 }}>{React.Children.toArray(children)[active]}</div>;\n}\n// 用法\nfunction Demo() {\n  return (\n    <Tabs>\n      <TabList>\n        <Tab index={0}>Tab1</Tab>\n        <Tab index={1}>Tab2</Tab>\n        <Tab index={2}>Tab3</Tab>\n      </TabList>\n      <TabPanels>\n        <div>内容1</div>\n        <div>内容2</div>\n        <div>内容3</div>\n      </TabPanels>\n    </Tabs>\n  );\n}", "explanation": "这是一个完整的Tabs复合组件实现，支持多Tab、状态共享和灵活组合。"}]}}, {"name": "作业：实现一个可定制的Accordion复合组件", "trans": ["作业"], "usage": {"syntax": "// 需求：实现一个Accordion复合组件\n// 1. 支持多个面板展开/收起。\n// 2. 通过Context共享状态。\n// 3. 支持自定义标题和内容。\n// 4. 代码结构清晰，注释完整。", "description": "请实现一个Accordion复合组件，要求如上。提交时请附上完整代码和注释。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 示例代码略，请学生自行实现\n// 提示：用Context和静态属性实现。", "explanation": "作业要求学生掌握复合组件的状态共享和定制。"}, {"code": "import React, { useState, createContext, useContext } from 'react';\nconst AccordionContext = createContext();\nfunction Accordion({ children }) {\n  const [open, setOpen] = useState(null);\n  return (\n    <AccordionContext.Provider value={{ open, setOpen }}>\n      {children}\n    </AccordionContext.Provider>\n  );\n}\nfunction AccordionItem({ index, title, children }) {\n  const { open, setOpen } = useContext(AccordionContext);\n  const isOpen = open === index;\n  return (\n    <div>\n      <div\n        style={{ cursor: 'pointer', fontWeight: isOpen ? 'bold' : 'normal' }}\n        onClick={() => setOpen(isOpen ? null : index)}\n      >\n        {title}\n      </div>\n      {isOpen && <div style={{ padding: 8 }}>{children}</div>}\n    </div>\n  );\n}\n// 用法示例\nfunction Demo() {\n  return (\n    <Accordion>\n      <AccordionItem index={0} title=\"标题1\">内容1</AccordionItem>\n      <AccordionItem index={1} title=\"标题2\">内容2</AccordionItem>\n      <AccordionItem index={2} title=\"标题3\">内容3</AccordionItem>\n    </Accordion>\n  );\n}", "explanation": "这是一个完整的Accordion复合组件实现，支持多面板、状态共享和自定义内容。"}]}}]}