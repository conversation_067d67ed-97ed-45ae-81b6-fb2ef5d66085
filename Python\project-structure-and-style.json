{"name": "Project Structure and Style", "trans": ["项目结构与规范"], "methods": [{"name": "Code Organization Structure", "trans": ["代码组织结构"], "usage": {"syntax": "project/\n  module/\n    __init__.py\n    ...\n  tests/\n    ...\n  main.py", "description": "推荐采用分层结构：主程序、模块、测试、资源等分目录管理，便于维护和扩展。", "parameters": [{"name": "project", "description": "项目根目录"}, {"name": "module", "description": "功能模块目录"}, {"name": "tests", "description": "测试代码目录"}], "returnValue": "无返回值", "examples": [{"code": "myproject/\n  app/\n    __init__.py\n    core.py\n  tests/\n    test_core.py\n  main.py", "explanation": "演示了典型的Python项目结构。"}]}}, {"name": "Naming Conventions and PEP8", "trans": ["命名规范与PEP8"], "usage": {"syntax": "变量名: lower_case\n类名: CamelCase\n常量: UPPER_CASE", "description": "PEP8是Python官方编码规范，涵盖命名、缩进、空格、注释等，提升代码可读性。", "parameters": [{"name": "变量名", "description": "小写字母+下划线"}, {"name": "类名", "description": "首字母大写驼峰"}, {"name": "常量", "description": "全大写+下划线"}], "returnValue": "无返回值", "examples": [{"code": "user_name = 'Tom'\nclass UserProfile: pass\nMAX_SIZE = 100", "explanation": "演示了PEP8命名规范。"}]}}, {"name": "Documentation and Comments", "trans": ["文档与注释"], "usage": {"syntax": "# 单行注释\n'''模块/函数说明文档'''", "description": "良好的注释和文档字符串有助于理解和维护代码，推荐为模块、函数、类编写docstring。", "parameters": [{"name": "#", "description": "单行注释"}, {"name": "'''...'''", "description": "文档字符串"}], "returnValue": "无返回值", "examples": [{"code": "# 这是一个注释\ndef add(a, b):\n    '''返回a和b之和'''\n    return a + b", "explanation": "演示了注释和文档字符串的写法。"}]}}, {"name": "Code Style Check with flake8", "trans": ["代码风格检查flake8"], "usage": {"syntax": "flake8 文件或目录", "description": "flake8是常用的Python代码风格检查工具，可检测PEP8违规、未使用变量等。", "parameters": [{"name": "文件或目录", "description": "要检查的目标文件或目录"}], "returnValue": "风格检查报告", "examples": [{"code": "# 命令行执行\nflake8 app/", "explanation": "演示了flake8检查代码风格。"}]}}, {"name": "Auto-formatting with black/isort", "trans": ["自动格式化black/isort"], "usage": {"syntax": "black 文件或目录\nisort 文件或目录", "description": "black可自动格式化Python代码，isort用于自动排序import语句，提升代码一致性。", "parameters": [{"name": "black", "description": "自动格式化工具"}, {"name": "isort", "description": "import排序工具"}], "returnValue": "格式化后的代码或报告", "examples": [{"code": "# 命令行执行\nblack app/\nisort app/", "explanation": "演示了black和isort的用法。"}]}}, {"name": "Project Structure and Style Assignment", "trans": ["项目结构与规范练习"], "usage": {"syntax": "# 项目结构与规范练习", "description": "完成以下练习，巩固项目结构与规范相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 设计一个合理的Python项目结构。\n2. 按PEP8规范命名变量和类。\n3. 为函数添加文档字符串。\n4. 用flake8检查代码风格。\n5. 用black和isort自动格式化代码。", "explanation": "练习要求动手实践项目结构设计、命名规范、注释、flake8、black、isort等。"}]}}]}