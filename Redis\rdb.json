{"name": "RDB Persistence", "trans": ["RDB持久化"], "methods": [{"name": "Snapshot Principle", "trans": ["快照原理"], "usage": {"syntax": "RDB通过定期快照将内存数据保存到磁盘（dump.rdb文件）", "description": "RDB持久化会在指定条件下生成数据快照，保存到dump.rdb文件，实现数据的定期备份和恢复。", "parameters": [], "returnValue": "生成的RDB快照文件", "examples": [{"code": "$ redis-cli save # 立即生成快照\n$ redis-cli bgsave # 后台异步生成快照", "explanation": "展示了RDB快照的生成方式。"}]}}, {"name": "Configuration and Trigger Timing", "trans": ["配置与触发时机"], "usage": {"syntax": "save <seconds> <changes>\ndir <path>\ndbfilename <filename>", "description": "通过save配置设置快照触发条件，dir指定保存目录，dbfilename指定文件名。", "parameters": [{"name": "save", "description": "快照触发条件，如save 900 1"}, {"name": "dir", "description": "快照文件保存目录"}, {"name": "db<PERSON><PERSON><PERSON>", "description": "快照文件名，默认dump.rdb"}], "returnValue": "RDB快照的保存路径和文件名", "examples": [{"code": "# redis.conf配置示例\nsave 900 1\nsave 300 10\ndir /var/lib/redis\ndbfilename dump.rdb", "explanation": "展示了RDB快照的常用配置。"}]}}, {"name": "RDB File Management", "trans": ["RDB文件管理"], "usage": {"syntax": "cp/mv/rm dump.rdb\n重启Redis自动加载RDB文件", "description": "RDB文件可手动备份、迁移、恢复。重启Redis时会自动加载RDB文件恢复数据。", "parameters": [{"name": "dump.rdb", "description": "RDB快照文件"}], "returnValue": "数据恢复或备份结果", "examples": [{"code": "$ cp dump.rdb /backup/\n$ mv dump.rdb /data/\n$ rm dump.rdb # 删除快照\n# 重启Redis自动加载RDB文件", "explanation": "展示了RDB文件的备份、迁移和恢复。"}]}}, {"name": "Practice: RDB持久化练习", "trans": ["RDB持久化练习"], "usage": {"syntax": "# Redis RDB持久化练习", "description": "完成以下练习，掌握RDB快照的生成、配置和恢复。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 手动生成RDB快照。\n2. 修改save配置实现定期快照。\n3. 备份和恢复dump.rdb文件。", "explanation": "通过这些练习掌握RDB持久化的常用操作。"}, {"code": "# 正确实现示例\n$ redis-cli save\n$ redis-cli config set save '60 1'\n$ cp dump.rdb /tmp/\n$ redis-server --dir /tmp/ --dbfilename dump.rdb", "explanation": "展示了练习的标准实现过程。"}]}}]}