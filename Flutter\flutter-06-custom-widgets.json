{"name": "Custom Widgets", "trans": ["自定义组件"], "methods": [{"name": "StatelessWidget", "trans": ["StatelessWidget（无状态组件）"], "usage": {"syntax": "class MyWidget extends StatelessWidget { ... }", "description": "StatelessWidget用于构建无状态的自定义组件，组件内容不会随状态变化而改变。", "parameters": [{"name": "build", "description": "必须实现的构建方法，返回Widget树"}], "returnValue": "无返回值，直接渲染在界面上。", "examples": [{"code": "// 自定义StatelessWidget示例\nimport 'package:flutter/material.dart';\n\nclass MyText extends StatelessWidget {\n  final String text;\n  MyText(this.text);\n  @override\n  Widget build(BuildContext context) {\n    return Text(text, style: TextStyle(fontSize: 20, color: Colors.blue));\n  }\n}\n\nvoid main() => runApp(MaterialApp(home: Scaffold(body: Center(child: MyText('自定义无状态组件')))));\n", "explanation": "演示了如何自定义一个无状态组件MyText，并在页面中使用。"}]}}, {"name": "StatefulWidget", "trans": ["StatefulWidget（有状态组件）"], "usage": {"syntax": "class MyWidget extends StatefulWidget { ... } class _MyWidgetState extends State<MyWidget> { ... }", "description": "StatefulWidget用于构建有状态的自定义组件，组件内容可随状态变化而更新。", "parameters": [{"name": "createState", "description": "必须实现，返回State对象"}, {"name": "setState", "description": "用于更新组件状态并触发重建"}], "returnValue": "无返回值，状态变化时自动刷新界面。", "examples": [{"code": "// 自定义StatefulWidget示例\nimport 'package:flutter/material.dart';\n\nclass Counter extends StatefulWidget {\n  @override\n  _CounterState createState() => _CounterState();\n}\n\nclass _CounterState extends State<Counter> {\n  int _count = 0;\n  @override\n  Widget build(BuildContext context) {\n    return Column(\n      mainAxisAlignment: MainAxisAlignment.center,\n      children: [\n        Text('计数: \\$_count', style: TextStyle(fontSize: 24)),\n        ElevatedButton(\n          onPressed: () {\n            setState(() { _count++; });\n          },\n          child: Text('加一'),\n        ),\n      ],\n    );\n  }\n}\n\nvoid main() => runApp(MaterialApp(home: Scaffold(body: Center(child: Counter()))));\n", "explanation": "演示了如何自定义一个有状态组件Counter，并通过setState更新界面。"}]}}, {"name": "Widget Composition and Reuse", "trans": ["组件组合与复用"], "usage": {"syntax": "自定义组件可作为其他组件的子组件进行组合和复用。", "description": "通过将自定义组件嵌套、组合，实现复杂界面和逻辑的复用，提高开发效率。", "parameters": [{"name": "props", "description": "通过构造函数传递参数，实现灵活复用"}], "returnValue": "无返回值，组合后形成新的Widget树。", "examples": [{"code": "// 组件组合与复用示例\nimport 'package:flutter/material.dart';\n\nclass MyButton extends StatelessWidget {\n  final String label;\n  final VoidCallback onPressed;\n  MyButton({required this.label, required this.onPressed});\n  @override\n  Widget build(BuildContext context) {\n    return ElevatedButton(onPressed: onPressed, child: Text(label));\n  }\n}\n\nclass MyPanel extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return Column(\n      children: [\n        MyButton(label: '按钮1', onPressed: () { print('点击按钮1'); }),\n        MyButton(label: '按钮2', onPressed: () { print('点击按钮2'); }),\n      ],\n    );\n  }\n}\n\nvoid main() => runApp(MaterialApp(home: Scaffold(body: Center(child: MyPanel()))));\n", "explanation": "演示了自定义组件的组合与复用，将MyButton组件多次复用在MyPanel中。"}]}}, {"name": "Widget Lifecycle", "trans": ["组件生命周期"], "usage": {"syntax": "StatefulWidget的生命周期方法：initState、didUpdateWidget、dispose等", "description": "StatefulWidget拥有完整的生命周期，可在不同阶段执行特定逻辑，如初始化、更新、销毁等。", "parameters": [{"name": "initState", "description": "初始化状态，只调用一次"}, {"name": "didUpdateWidget", "description": "组件更新时调用"}, {"name": "dispose", "description": "组件销毁时调用，释放资源"}], "returnValue": "无返回值，生命周期方法自动调用。", "examples": [{"code": "// 组件生命周期示例\nimport 'package:flutter/material.dart';\n\nclass LifeDemo extends StatefulWidget {\n  @override\n  _LifeDemoState createState() => _LifeDemoState();\n}\n\nclass _LifeDemoState extends State<LifeDemo> {\n  @override\n  void initState() {\n    super.initState();\n    print('initState: 初始化');\n  }\n  @override\n  void didUpdateWidget(LifeDemo oldWidget) {\n    super.didUpdateWidget(oldWidget);\n    print('didUpdateWidget: 组件更新');\n  }\n  @override\n  void dispose() {\n    print('dispose: 组件销毁');\n    super.dispose();\n  }\n  @override\n  Widget build(BuildContext context) {\n    return Text('生命周期演示', style: TextStyle(fontSize: 20));\n  }\n}\n\nvoid main() => runApp(MaterialApp(home: Scaffold(body: Center(child: LifeDemo()))));\n", "explanation": "演示了StatefulWidget的生命周期方法及其调用时机。"}]}}, {"name": "Assignment", "trans": ["作业：自定义一个有状态组件，包含计数功能和生命周期日志输出。"], "usage": {"syntax": "无", "description": "请自定义一个StatefulWidget，实现计数器功能，并在生命周期方法中输出日志。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 作业参考实现\nimport 'package:flutter/material.dart';\n\nclass CounterDemo extends StatefulWidget {\n  @override\n  _CounterDemoState createState() => _CounterDemoState();\n}\n\nclass _CounterDemoState extends State<CounterDemo> {\n  int _count = 0;\n  @override\n  void initState() {\n    super.initState();\n    print('initState: 初始化');\n  }\n  @override\n  void dispose() {\n    print('dispose: 组件销毁');\n    super.dispose();\n  }\n  @override\n  Widget build(BuildContext context) {\n    return Column(\n      mainAxisAlignment: MainAxisAlignment.center,\n      children: [\n        Text('计数: \\$_count', style: TextStyle(fontSize: 24)),\n        ElevatedButton(\n          onPressed: () {\n            setState(() { _count++; });\n          },\n          child: Text('加一'),\n        ),\n      ],\n    );\n  }\n}\n\nvoid main() => runApp(MaterialApp(home: Scaffold(body: Center(child: CounterDemo()))));\n", "explanation": "作业要求自定义有状态组件，包含计数功能和生命周期日志输出。"}]}}]}