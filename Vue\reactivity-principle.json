{"name": "Reactivity Principle", "trans": ["响应式原理"], "methods": [{"name": "Reactive Data Declaration", "trans": ["响应式数据声明"], "usage": {"syntax": "data() { return { count: 0 } }\n// Vue3: reactive({ count: 0 }) 或 ref(0)", "description": "在Vue2中通过data选项声明响应式数据，Vue3中可用reactive或ref创建响应式变量。数据变化会自动驱动视图更新。", "parameters": [{"name": "data/reactive/ref", "description": "声明响应式数据的方式"}], "returnValue": "返回响应式对象或基本类型，数据变更自动响应视图", "examples": [{"code": "// Vue2\ndata() { return { count: 0 } }\n// Vue3\nimport { ref } from 'vue'\nsetup() {\n  const count = ref(0)\n  return { count }\n}", "explanation": "Vue2用data声明响应式，Vue3用ref或reactive。"}]}}, {"name": "Data Proxy and Hijacking", "trans": ["数据代理与劫持"], "usage": {"syntax": "// Vue2: Object.defineProperty\n// Vue3: Proxy", "description": "Vue2通过Object.defineProperty劫持对象属性实现响应式，Vue3通过Proxy代理整个对象，支持更多数据类型和操作。", "parameters": [{"name": "Object.defineProperty/Proxy", "description": "实现响应式的底层技术"}], "returnValue": "返回被代理的响应式对象", "examples": [{"code": "// Vue2原理\nObject.defineProperty(obj, 'key', {\n  get() { /* 依赖收集 */ },\n  set(val) { /* 派发更新 */ }\n})\n// Vue3原理\nconst proxy = new Proxy(obj, handler)", "explanation": "Vue2劫持属性，Vue3代理整个对象，提升性能和灵活性。"}]}}, {"name": "Computed Property", "trans": ["计算属性（computed）"], "usage": {"syntax": "computed: { double() { return this.count * 2 } }\n// Vue3: import { computed } from 'vue'\nconst double = computed(() => count.value * 2)", "description": "计算属性用于声明依赖其他响应式数据的派生值，具有缓存特性，只有依赖变化时才重新计算。", "parameters": [{"name": "computed", "description": "计算属性的声明方式"}], "returnValue": "返回计算后的派生值，具备缓存", "examples": [{"code": "// Vue2\ncomputed: {\n  double() { return this.count * 2 }\n}\n// Vue3\nimport { computed } from 'vue'\nconst double = computed(() => count.value * 2)", "explanation": "computed声明派生值，只有依赖变化时才重新计算。"}]}}, {"name": "Watcher", "trans": ["侦听器（watch）"], "usage": {"syntax": "watch: { count(newVal, oldVal) { ... } }\n// Vue3: import { watch } from 'vue'\nwatch(count, (newVal, oldVal) => { ... })", "description": "侦听器用于观察响应式数据的变化并执行副作用操作，适合异步请求、手动操作等场景。", "parameters": [{"name": "watch", "description": "侦听目标及回调函数"}], "returnValue": "无返回值，数据变化时自动执行回调", "examples": [{"code": "// Vue2\nwatch: {\n  count(newVal, oldVal) {\n    console.log(newVal, oldVal)\n  }\n}\n// Vue3\nimport { watch } from 'vue'\nwatch(count, (newVal, oldVal) => {\n  console.log(newVal, oldVal)\n})", "explanation": "watch侦听数据变化，适合处理副作用。"}]}}, {"name": "Deep Watcher", "trans": ["深度监听"], "usage": {"syntax": "// Vue2\nwatch: {\n  obj: { handler(val) { ... }, deep: true }\n}\n// Vue3\nwatch(obj, callback, { deep: true })", "description": "深度监听用于侦听对象或数组内部属性的变化，需设置deep: true。", "parameters": [{"name": "deep", "description": "是否开启深度监听"}], "returnValue": "无返回值，任意属性变化时回调触发", "examples": [{"code": "// Vue2\nwatch: {\n  user: {\n    handler(val) { console.log(val) },\n    deep: true\n  }\n}\n// Vue3\nwatch(user, (val) => { console.log(val) }, { deep: true })", "explanation": "deep: true可监听对象内部所有属性变化。"}]}}, {"name": "Reactivity Pitfalls and Notes", "trans": ["响应式陷阱与注意事项"], "usage": {"syntax": "// Vue2: this.$set(obj, key, value)\n// Vue3: reactive/readonly限制", "description": "Vue2对新增属性、数组变更等有响应式陷阱，需用$set等API。Vue3通过Proxy解决大部分问题，但readonly等有只读限制。", "parameters": [{"name": "$set/readonly", "description": "常见陷阱及解决方法"}], "returnValue": "返回响应式对象或只读对象，注意API限制", "examples": [{"code": "// Vue2\nthis.$set(user, 'age', 18)\n// Vue3\nimport { readonly } from 'vue'\nconst state = reactive({ name: 'A' })\nconst ro = readonly(state)", "explanation": "Vue2需用$set新增属性，Vue3可用readonly创建只读对象。"}]}}, {"name": "Reactivity Principle Assignment", "trans": ["响应式原理练习"], "usage": {"syntax": "# 响应式原理练习", "description": "完成以下练习，巩固响应式原理的理解和应用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用data/ref声明响应式数据。\n2. 用computed实现一个派生属性。\n3. 用watch监听数据变化。\n4. 用deep监听对象内部属性。\n5. 尝试触发响应式陷阱并解决。", "explanation": "通过这些练习掌握响应式数据声明、计算属性、侦听器和常见陷阱。"}]}}]}