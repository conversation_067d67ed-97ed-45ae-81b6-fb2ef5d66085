{"name": "TypeScript and React", "trans": ["TypeScript与React"], "methods": [{"name": "Type Definitions", "trans": ["类型定义"], "usage": {"syntax": "type User = { id: number; name: string };\ninterface Props { title: string; }", "description": "通过type和interface定义对象、组件、函数等类型，提升类型安全和开发体验。", "parameters": [{"name": "type", "description": "类型别名定义。"}, {"name": "interface", "description": "接口类型定义。"}], "returnValue": "无返回值，类型用于类型检查。", "examples": [{"code": "type User = { id: number; name: string };\ninterface Props { title: string; }", "explanation": "定义用户对象和组件Props类型。"}]}}, {"name": "Component Types", "trans": ["组件类型"], "usage": {"syntax": "const MyComponent: React.FC<Props> = (props) => { ... }", "description": "通过React.FC或函数类型定义组件类型，明确props类型和children支持。", "parameters": [{"name": "Props", "description": "组件属性类型。"}], "returnValue": "返回React组件。", "examples": [{"code": "type Props = { title: string };\nconst MyComponent: React.FC<Props> = ({ title }) => <h1>{title}</h1>;", "explanation": "定义带类型的函数组件。"}]}}, {"name": "Props Types", "trans": ["Props类型"], "usage": {"syntax": "function Hello(props: { name: string }) { ... }", "description": "为props参数单独定义类型，提升组件可读性和类型安全。", "parameters": [{"name": "props", "description": "组件属性对象。"}], "returnValue": "无返回值，类型用于props检查。", "examples": [{"code": "type Props = { name: string };\nfunction Hello(props: Props) { return <div>Hello, {props.name}</div>; }", "explanation": "为props定义类型并应用于组件。"}]}}, {"name": "Hooks Types", "trans": ["Hooks类型"], "usage": {"syntax": "const [count, setCount] = useState<number>(0);", "description": "为useState、useRef等Hooks指定泛型类型，确保状态和引用类型安全。", "parameters": [{"name": "useState<T>", "description": "指定状态类型。"}, {"name": "useRef<T>", "description": "指定引用类型。"}], "returnValue": "返回带类型的状态或引用。", "examples": [{"code": "const [count, setCount] = useState<number>(0);\nconst inputRef = useRef<HTMLInputElement>(null);", "explanation": "为状态和ref指定类型。"}]}}, {"name": "Event Types", "trans": ["事件类型"], "usage": {"syntax": "function handleClick(e: React.MouseEvent<HTMLButtonElement>) { ... }", "description": "为事件处理函数参数指定React事件类型，提升类型推断和代码提示。", "parameters": [{"name": "e", "description": "事件对象，指定具体事件类型。"}], "returnValue": "无返回值，类型用于事件参数检查。", "examples": [{"code": "function handleClick(e: React.MouseEvent<HTMLButtonElement>) {\n  alert(e.currentTarget.textContent);\n}\n<button onClick={handleClick}>点我</button>;", "explanation": "为点击事件处理函数指定类型。"}]}}, {"name": "Generic Components", "trans": ["通用组件"], "usage": {"syntax": "function List<T>({ items }: { items: T[] }) { ... }", "description": "通过泛型定义通用组件，支持多种数据类型，提高复用性。", "parameters": [{"name": "T", "description": "泛型参数，代表任意类型。"}, {"name": "items", "description": "数据项数组。"}], "returnValue": "返回渲染后的通用组件。", "examples": [{"code": "function List<T>({ items }: { items: T[] }) {\n  return <ul>{items.map((item, i) => <li key={i}>{String(item)}</li>)}</ul>;\n}\n// 使用：<List items={[1,2,3]} /> 或 <List items={[\"a\", \"b\"]} />;", "explanation": "定义和使用支持多类型的通用列表组件。"}]}}, {"name": "Type Assertions", "trans": ["类型断言"], "usage": {"syntax": "const el = document.getElementById('root') as HTMLDivElement;", "description": "通过as语法进行类型断言，明确指定变量或对象的类型，解决类型推断不准确的问题。", "parameters": [{"name": "as Type", "description": "断言目标类型。"}], "returnValue": "返回断言后的指定类型对象。", "examples": [{"code": "const el = document.getElementById('root') as HTMLDivElement;", "explanation": "将DOM元素断言为HTMLDivElement类型。"}]}}, {"name": "作业：TypeScript与React实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 定义类型和接口\n// 2. 编写带类型的组件、props、hook、事件\n// 3. 实现通用组件和类型断言", "description": "通过实践类型定义、组件类型、props、hook、事件、通用组件和类型断言，掌握TypeScript与React集成开发。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 定义User类型和组件Props\n// 2. 编写带类型的组件和hook\n// 3. 实现通用组件和类型断言", "explanation": "作业提示，学生需结合本节内容完成实现。"}, {"code": "// 正确实现示例\ntype User = { id: number; name: string };\nfunction UserCard({ user }: { user: User }) { return <div>{user.name}</div>; }\nconst [count, setCount] = useState<number>(0);\nconst el = document.getElementById('root') as HTMLDivElement;", "explanation": "TypeScript与React集成开发的正确实现示例。"}]}}]}