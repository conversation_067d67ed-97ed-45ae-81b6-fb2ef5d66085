{"name": "Performance Optimization", "trans": ["性能优化"], "methods": [{"name": "Memory Leak Detection", "trans": ["内存泄漏排查"], "usage": {"syntax": "使用浏览器DevTools、WeakMap、手动释放引用等", "description": "内存泄漏是指不再使用的对象依然被引用，导致内存无法释放。常见排查方法包括：\n- 使用Chrome DevTools的Memory面板快照和堆分析\n- 使用WeakMap/WeakSet存储临时数据\n- 手动解除不再需要的引用", "parameters": [{"name": "object", "description": "需要监控或释放的对象"}], "returnValue": "无返回值", "examples": [{"code": "let cache = new WeakMap();\nfunction setCache(key, value) {\n  cache.set(key, value);\n}\nlet obj = { name: 'test' };\nsetCache(obj, 'data');\nobj = null;\n", "explanation": "示例展示了如何用WeakMap避免内存泄漏，以及如何用DevTools分析内存使用情况。"}]}}, {"name": "Code Splitting and Lazy Loading", "trans": ["代码分割与懒加载"], "usage": {"syntax": "import() 动态加载 / Webpack/Vite等工具配置", "description": "代码分割和懒加载可以减少首屏加载体积，提高页面加载速度。常用方式：\n- 使用import()动态加载模块\n- Webpack的SplitChunksPlugin\n- React.lazy、Vue异步组件等框架特性", "parameters": [{"name": "module", "description": "需要按需加载的模块或组件"}], "returnValue": "返回Promise，异步加载模块", "examples": [{"code": "document.getElementById('btn').onclick = async function() {\n  const module = await import('./math.js');\n  console.log('动态加载结果:', module.add(1, 2));\n};\nconst LazyComponent = React.lazy(() => import('./MyComponent'));", "explanation": "示例展示了原生、React和Webpack中代码分割与懒加载的常见用法。"}]}}, {"name": "Asset Compression and Caching", "trans": ["资源压缩与缓存"], "usage": {"syntax": "Gzip/Brotli压缩、Cache-Control/Etag、Service Worker等", "description": "资源压缩和缓存可以减少网络传输体积和请求次数，提高页面加载速度。常用方式：\n- 启用Gzip/Brotli压缩\n- 设置HTTP缓存头（Cache-Control、Etag等）\n- 使用Service Worker做离线缓存", "parameters": [{"name": "asset", "description": "需要压缩或缓存的静态资源"}], "returnValue": "无返回值", "examples": [{"code": "self.addEventListener('install', event => {\n  event.waitUntil(\n    caches.open('v1').then(cache => {\n      return cache.addAll(['/index.html', '/main.js', '/style.css']);\n    })\n  );\n});", "explanation": "示例展示了服务器压缩、HTTP缓存和前端Service Worker缓存的常见配置。"}]}}, {"name": "DOM Optimization", "trans": ["DOM优化"], "usage": {"syntax": "批量操作、文档片段、虚拟DOM等", "description": "DOM操作性能低下是前端常见瓶颈。优化方法包括：\n- 批量插入节点时使用DocumentFragment\n- 减少重排和重绘\n- 使用虚拟DOM技术（如React、Vue）", "parameters": [{"name": "element", "description": "需要优化操作的DOM元素"}], "returnValue": "无返回值", "examples": [{"code": "const fragment = document.createDocumentFragment();\nfor (let i = 0; i < 1000; i++) {\n  const li = document.createElement('li');\n  li.textContent = 'Item ' + i;\n  fragment.appendChild(li);\n}\ndocument.getElementById('list').appendChild(fragment);\n", "explanation": "示例展示了如何用DocumentFragment批量插入节点，减少DOM操作次数。"}]}}, {"name": "Event Optimization", "trans": ["事件优化"], "usage": {"syntax": "事件委托、防抖/节流、解绑无用事件等", "description": "事件过多会影响性能。优化方法包括：\n- 使用事件委托减少事件绑定数量\n- 对高频事件使用防抖/节流\n- 离开页面时解绑无用事件", "parameters": [{"name": "event", "description": "需要优化的事件类型或处理函数"}], "returnValue": "无返回值", "examples": [{"code": "const list = document.getElementById('list');\nlist.addEventListener('click', function(e) {\n  if (e.target.tagName === 'LI') {\n    console.log('点击了', e.target.textContent);\n  }\n});\nwindow.addEventListener('unload', function() {\n  list.removeEventListener('click', handler);\n});", "explanation": "示例展示了事件委托和解绑事件的常见做法，提升事件处理性能。"}]}}, {"name": "Async Optimization", "trans": ["异步优化"], "usage": {"syntax": "异步加载、并发控制、Web Worker等", "description": "合理利用异步可以提升页面响应速度和流畅度。常见优化方式：\n- 异步加载资源和数据\n- 控制并发请求数量\n- 使用Web Worker处理计算密集型任务", "parameters": [{"name": "task", "description": "需要异步优化的任务或操作"}], "returnValue": "无返回值", "examples": [{"code": "async function fetchWithLimit(urls, limit = 3) {\n  const results = [];\n  const executing = [];\n  for (const url of urls) {\n    const p = fetch(url).then(res => res.json());\n    results.push(p);\n    if (executing.length >= limit) {\n      await Promise.race(executing);\n      executing.splice(executing.findIndex(e => e === p), 1);\n    }\n    executing.push(p);\n  }\n  return Promise.all(results);\n}\nconst worker = new Worker('worker.js');\nworker.postMessage(largeData);\nworker.onmessage = function(e) {\n  console.log('处理结果:', e.data);\n};", "explanation": "示例展示了并发请求控制和Web Worker的异步优化方法。"}]}}, {"name": "Performance Optimization Assignment", "trans": ["性能优化练习"], "usage": {"syntax": "// 性能优化练习", "description": "完成以下练习，巩固性能优化技巧。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "const fragment = document.createDocumentFragment();\nfor (let i = 0; i < 10000; i++) {\n  const li = document.createElement('li');\n  li.textContent = 'Item ' + i;\n  fragment.appendChild(li);\n}\ndocument.getElementById('list').appendChild(fragment);", "explanation": "练习要求优化批量插入DOM节点的性能，建议结合文档片段和其他优化手段。"}]}}]}