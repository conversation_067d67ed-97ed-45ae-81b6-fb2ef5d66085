{"name": "Animation and Interaction", "trans": ["动画与交互"], "methods": [{"name": "Transition Component", "trans": ["Transition组件"], "usage": {"syntax": "<transition name=\"fade\">\n  <div v-if=\"show\">内容</div>\n</transition>\n<style>\n.fade-enter-active, .fade-leave-active { transition: opacity .5s; }\n.fade-enter, .fade-leave-to { opacity: 0; }\n</style>", "description": "通过<transition>包裹元素，实现进入/离开动画，支持自定义动画名和过渡时长。", "parameters": [{"name": "name", "description": "动画类名前缀"}, {"name": "appear", "description": "首次渲染时是否应用动画"}], "returnValue": "带动画效果的DOM元素", "examples": [{"code": "<transition name=\"fade\">\n  <div v-if=\"show\">内容</div>\n</transition>\n<style>\n.fade-enter-active, .fade-leave-active { transition: opacity .5s; }\n.fade-enter, .fade-leave-to { opacity: 0; }\n</style>", "explanation": "淡入淡出动画效果。"}]}}, {"name": "Animation Hooks", "trans": ["动画钩子"], "usage": {"syntax": "<transition\n  @before-enter=\"onBeforeEnter\"\n  @enter=\"onEnter\"\n  @after-enter=\"onAfterEnter\"\n>\n  <div v-if=\"show\">内容</div>\n</transition>", "description": "通过transition的钩子事件自定义动画过程，如动画前、动画中、动画后执行JS逻辑。", "parameters": [{"name": "@before-enter/@enter/@after-enter", "description": "动画生命周期钩子"}], "returnValue": "动画过程中的自定义行为", "examples": [{"code": "<transition\n  @before-enter=\"onBeforeEnter\"\n  @enter=\"onEnter\"\n  @after-enter=\"onAfterEnter\"\n>\n  <div v-if=\"show\">内容</div>\n</transition>", "explanation": "在动画各阶段执行自定义逻辑。"}]}}, {"name": "Page Transition", "trans": ["页面转场"], "usage": {"syntax": "<router-view v-slot=\"{ Component }\">\n  <transition name=\"slide\">\n    <component :is=\"Component\" />\n  </transition>\n</router-view>\n<style>\n.slide-enter-active, .slide-leave-active { transition: transform .5s; }\n.slide-enter, .slide-leave-to { transform: translateX(100%); }\n</style>", "description": "通过在router-view外包裹transition，实现页面切换时的转场动画。", "parameters": [{"name": "router-view", "description": "路由视图组件"}, {"name": "transition", "description": "转场动画组件"}], "returnValue": "页面切换时的转场动画效果", "examples": [{"code": "<router-view v-slot=\"{ Component }\">\n  <transition name=\"slide\">\n    <component :is=\"Component\" />\n  </transition>\n</router-view>\n<style>\n.slide-enter-active, .slide-leave-active { transition: transform .5s; }\n.slide-enter, .slide-leave-to { transform: translateX(100%); }\n</style>", "explanation": "页面切换时滑动转场动画。"}]}}, {"name": "Gesture Interaction", "trans": ["手势交互"], "usage": {"syntax": "<div v-touch:swipe=\"onSwipe\"></div>", "description": "通过第三方库（如vue3-touch-events）实现手势识别，支持滑动、长按等交互。", "parameters": [{"name": "v-touch", "description": "手势指令"}, {"name": "onSwipe", "description": "手势回调函数"}], "returnValue": "手势触发的交互行为", "examples": [{"code": "<div v-touch:swipe=\"onSwipe\"></div>", "explanation": "监听滑动手势并执行回调。"}]}}, {"name": "Micro Interaction", "trans": ["微交互"], "usage": {"syntax": "<button @click=\"active = !active\" :class=\"{ bounce: active }\">点击</button>\n<style>\n.bounce { animation: bounce .3s; }\n@keyframes bounce { 0% { transform: scale(1); } 50% { transform: scale(1.2); } 100% { transform: scale(1); } }\n</style>", "description": "通过动画类名和事件结合，实现按钮等元素的微交互动画，提升用户体验。", "parameters": [{"name": ":class", "description": "动态绑定动画类名"}, {"name": "@keyframes", "description": "定义动画关键帧"}], "returnValue": "微交互动画效果", "examples": [{"code": "<button @click=\"active = !active\" :class=\"{ bounce: active }\">点击</button>\n<style>\n.bounce { animation: bounce .3s; }\n@keyframes bounce { 0% { transform: scale(1); } 50% { transform: scale(1.2); } 100% { transform: scale(1); } }\n</style>", "explanation": "点击按钮时弹跳动画。"}]}}, {"name": "Performance Optimization", "trans": ["性能优化"], "usage": {"syntax": "<transition appear>...</transition>\nv-once、v-memo、will-change等", "description": "通过appear、v-once、will-change等优化动画性能，减少重绘和回流。", "parameters": [{"name": "appear", "description": "首次渲染时应用动画"}, {"name": "v-once/v-memo", "description": "减少不必要的渲染"}, {"name": "will-change", "description": "提示浏览器优化动画"}], "returnValue": "更流畅高效的动画体验", "examples": [{"code": "<transition appear>...</transition>\n<div v-once>静态内容</div>\n<style>\n.animated { will-change: transform; }\n</style>", "explanation": "首次渲染动画、静态内容优化和will-change提升性能。"}]}}, {"name": "Accessibility", "trans": ["可访问性"], "usage": {"syntax": "<button aria-label=\"关闭\">×</button>\n动画元素加aria属性和焦点管理", "description": "为动画和交互元素添加aria属性、管理焦点，保证无障碍访问体验。", "parameters": [{"name": "aria-*", "description": "无障碍属性"}, {"name": "tabindex", "description": "焦点管理"}], "returnValue": "兼容辅助技术的动画与交互", "examples": [{"code": "<button aria-label=\"关闭\">×</button>\n<div tabindex=\"0\">可聚焦元素</div>", "explanation": "为动画按钮添加无障碍属性和焦点。"}]}}, {"name": "Animation and Interaction Assignment", "trans": ["动画与交互练习"], "usage": {"syntax": "# 动画与交互练习", "description": "完成以下练习，巩固动画与交互相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用transition实现元素动画。\n2. 用钩子自定义动画过程。\n3. 实现页面转场和手势交互。\n4. 设计微交互动画。\n5. 优化动画性能和可访问性。", "explanation": "通过这些练习掌握Vue动画与交互。"}]}}]}