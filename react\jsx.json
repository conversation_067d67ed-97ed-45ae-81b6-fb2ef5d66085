{"name": "JSX Syntax", "trans": ["JSX语法"], "methods": [{"name": "JSX Basics", "trans": ["JSX基础"], "usage": {"syntax": "<TagName attribute={value}>content</TagName>", "description": "JSX是JavaScript的语法扩展，允许在JavaScript中编写类似HTML的代码。React使用JSX来描述UI的外观。", "parameters": [], "returnValue": "React元素", "examples": [{"code": "// 基本JSX元素\nconst element = <h1>Hello, world!</h1>;\n\n// 包含JavaScript表达式的JSX\nconst name = '<PERSON>';\nconst element = <h1>Hello, {name}</h1>;\n\n// 多行JSX需要用括号包裹\nconst element = (\n  <div>\n    <h1>Hello!</h1>\n    <p>Good to see you here.</p>\n  </div>\n);\n\n// JSX也是表达式\nfunction getGreeting(user) {\n  if (user) {\n    return <h1>Hello, {user.name}!</h1>;\n  }\n  return <h1>Hello, Stranger.</h1>;\n}", "explanation": "这些例子展示了JSX的基本用法：创建简单元素、在JSX中嵌入JavaScript表达式、编写多行JSX以及在条件语句中使用JSX。"}]}}, {"name": "JSX Attributes", "trans": ["JSX属性"], "usage": {"syntax": "<TagName attribute=\"value\" anotherAttribute={expression} />", "description": "JSX元素可以使用属性，类似于HTML属性。可以使用引号传递字符串字面量，或使用大括号传递JavaScript表达式。", "parameters": [], "returnValue": "带有属性的React元素", "examples": [{"code": "// 字符串字面量属性\nconst element = <a href=\"https://reactjs.org\">React官网</a>;\n\n// JavaScript表达式作为属性值\nconst element = <img src={user.avatarUrl} alt={user.name} />;\n\n// 使用扩展运算符传递所有props\nfunction Button(props) {\n  return <button {...props} className=\"btn\" />;\n}\n\n// 布尔属性\nconst element = <input type=\"text\" disabled={false} />;\n\n// className代替class（HTML与JavaScript保留字的区别）\nconst element = <div className=\"container\">内容</div>;\n\n// style属性使用对象\nconst styles = {\n  color: 'white',\n  backgroundColor: 'black',\n  fontSize: '16px'\n};\nconst element = <div style={styles}>Styled content</div>;\n\n// 内联style对象\nconst element = <div style={{ color: 'blue', fontWeight: 'bold' }}>蓝色粗体文本</div>;", "explanation": "这些例子展示了在JSX中使用属性的各种方式：字符串属性、表达式属性、扩展属性、布尔属性、className属性和style属性。在React中，HTML属性通常使用camelCase命名（如className代替class）。"}]}}, {"name": "JSX Children", "trans": ["JSX子元素"], "usage": {"syntax": "<Parent>children</Parent>", "description": "JSX标签可以包含子元素，包括其他JSX元素、JavaScript表达式、字符串和数组。", "parameters": [], "returnValue": "包含子元素的React元素", "examples": [{"code": "// 包含字符串的JSX\nconst element = <h1>Hello, world!</h1>;\n\n// 包含其他JSX元素的JSX\nconst element = (\n  <div>\n    <h1>标题</h1>\n    <p>段落</p>\n  </div>\n);\n\n// 包含JavaScript表达式的JSX\nconst element = <li>{props.message}</li>;\n\n// 包含数组的JSX\nconst items = ['苹果', '香蕉', '橙子'];\nconst element = (\n  <ul>\n    {items.map((item, index) => (\n      <li key={index}>{item}</li>\n    ))}\n  </ul>\n);\n\n// 空元素\nconst element = <img src=\"image.jpg\" alt=\"描述\" />;\n\n// 条件渲染子元素\nconst element = (\n  <div>\n    {isLoggedIn ? (\n      <LogoutButton />\n    ) : (\n      <LoginButton />\n    )}\n  </div>\n);\n\n// 使用&&运算符条件渲染\nconst element = (\n  <div>\n    {unreadMessages.length > 0 &&\n      <h2>\n        您有{unreadMessages.length}条未读消息\n      </h2>\n    }\n  </div>\n);", "explanation": "这些例子展示了在JSX中使用各种类型的子元素：字符串、其他JSX元素、JavaScript表达式、数组、条件渲染等。JSX可以嵌套，就像HTML一样。"}]}}, {"name": "JSX Prevention of Injection Attacks", "trans": ["JSX防注入攻击"], "usage": {"syntax": "const element = <div>{userProvidedContent}</div>;", "description": "React DOM在渲染所有输入内容之前，默认会进行转义，从而防止XSS（跨站脚本）攻击。所有内容在渲染前都会被转换为字符串，这有助于防止应用中的注入攻击。", "parameters": [], "returnValue": "安全的React元素", "examples": [{"code": "// 用户输入的内容（可能包含恶意代码）\nconst userInput = '<script>alert(\"恶意代码\")</script>';\n\n// 安全地渲染用户输入\nconst element = <div>{userInput}</div>;\n\n// 渲染结果会是：\n// <div>&lt;script&gt;alert(&quot;恶意代码&quot;)&lt;/script&gt;</div>\n\n// 如果确实需要渲染HTML（谨慎使用）\nconst trustedHtml = '<p>安全的HTML</p>';\nconst element = <div dangerouslySetInnerHTML={{ __html: trustedHtml }} />;", "explanation": "这个例子展示了React如何自动防止XSS攻击。当在JSX中嵌入用户提供的内容时，React会自动对其进行转义，确保其作为文本而非HTML被渲染。如果确实需要渲染HTML，可以使用dangerouslySetInnerHTML属性，但应该谨慎使用，并确保内容是可信的。"}]}}, {"name": "JSX Compilation", "trans": ["JSX编译转换"], "usage": {"syntax": "// JSX:\nconst element = <h1>Hello, world!</h1>;\n\n// 编译后的JavaScript:\nconst element = React.createElement('h1', null, 'Hello, world!');", "description": "JSX会被Babel等编译器转换为React.createElement()函数调用。这个转换过程发生在构建阶段，浏览器实际运行的是编译后的JavaScript代码。", "parameters": [], "returnValue": "JavaScript函数调用", "examples": [{"code": "// JSX代码\nconst element = (\n  <div className=\"container\">\n    <h1>Hello!</h1>\n    <p>Welcome to React</p>\n  </div>\n);\n\n// 编译后的JavaScript代码\nconst element = React.createElement(\n  'div',\n  { className: 'container' },\n  React.createElement('h1', null, 'Hello!'),\n  React.createElement('p', null, 'Welcome to React')\n);\n\n// React.createElement的返回值（简化）\n{\n  type: 'div',\n  props: {\n    className: 'container',\n    children: [\n      {\n        type: 'h1',\n        props: { children: 'Hello!' }\n      },\n      {\n        type: 'p',\n        props: { children: 'Welcome to React' }\n      }\n    ]\n  }\n}", "explanation": "这个例子展示了JSX如何被转换为JavaScript代码。JSX代码会被编译为React.createElement()函数调用，最终创建一个描述UI的JavaScript对象（React元素）。这个过程使我们可以使用类似HTML的语法编写UI，同时享受JavaScript的全部功能。"}]}}]}