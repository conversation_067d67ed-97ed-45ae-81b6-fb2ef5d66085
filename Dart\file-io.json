{"name": "File I/O", "trans": ["文件读写"], "methods": [{"name": "File Basics", "trans": ["文件基础"], "usage": {"syntax": "import 'dart:io';\n\n// 创建File对象\nFile file = File('path/to/file.txt');\n\n// 检查文件是否存在\nFuture<bool> exists = file.exists();\n\n// 获取文件信息\nFuture<FileStat> stat = file.stat();", "description": "在Dart中，文件操作主要通过dart:io库中的File类实现。要使用文件操作功能，首先需要导入dart:io包。\n\nFile类提供了创建、读取、写入和删除文件的功能。使用File类时，需要提供文件路径作为构造函数的参数。文件路径可以是绝对路径或相对路径。\n\n文件操作在Dart中通常是异步的，返回Future对象，这意味着这些操作不会立即完成，而是在未来某个时刻完成。这种异步特性使应用程序可以在等待文件操作完成的同时继续执行其他任务，从而提高效率。\n\n在开始读写文件之前，通常需要检查文件是否存在，这可以通过File对象的exists()方法实现。此外，stat()方法可以获取文件的详细信息，如大小、修改时间等。", "parameters": [], "returnValue": "不同方法返回不同的值，通常是Future对象", "examples": [{"code": "import 'dart:io';\n\nvoid main() async {\n  print('文件基础操作示例');\n  \n  // 创建File对象\n  File file = File('example.txt');\n  \n  // 检查文件是否存在\n  bool fileExists = await file.exists();\n  print('文件存在: $fileExists');\n  \n  if (fileExists) {\n    // 获取文件信息\n    FileStat stats = await file.stat();\n    print('文件大小: ${stats.size} 字节');\n    print('修改时间: ${stats.modified}');\n    print('访问时间: ${stats.accessed}');\n    print('更改时间: ${stats.changed}');\n    print('文件模式: ${stats.mode}');\n    print('文件类型: ${_getFileType(stats.type)}');\n  } else {\n    print('文件不存在，将创建新文件');\n    \n    // 创建文件并写入内容\n    await file.writeAsString('这是一个示例文件。\\n创建于 ${DateTime.now()}');\n    print('文件已创建');\n    \n    // 验证文件现在存在\n    fileExists = await file.exists();\n    print('文件存在: $fileExists');\n    \n    // 获取新文件信息\n    if (fileExists) {\n      FileStat stats = await file.stat();\n      print('新文件大小: ${stats.size} 字节');\n    }\n  }\n}\n\n// 辅助函数：将文件类型枚举转换为字符串\nString _getFileType(FileSystemEntityType type) {\n  switch (type) {\n    case FileSystemEntityType.file:\n      return '文件';\n    case FileSystemEntityType.directory:\n      return '目录';\n    case FileSystemEntityType.link:\n      return '链接';\n    case FileSystemEntityType.notFound:\n      return '不存在';\n    default:\n      return '未知';\n  }\n}", "explanation": "这个示例展示了如何使用Dart中的File类进行基本操作。首先，创建了一个指向'example.txt'的File对象，然后检查该文件是否存在。如果文件存在，程序会获取并显示文件的详细信息，包括大小、修改时间等。如果文件不存在，程序会创建该文件并写入一些文本内容，然后验证文件已被创建并显示新文件的大小。这个示例演示了File类的基本使用方法，包括文件存在性检查、文件信息获取以及简单的文件创建操作。"}]}}, {"name": "Reading Files", "trans": ["读取文件"], "usage": {"syntax": "// 读取整个文件为字符串\nFuture<String> readAsString() async {\n  File file = File('path/to/file.txt');\n  return await file.readAsString();\n}\n\n// 读取文件为字节列表\nFuture<List<int>> readAsBytes() async {\n  File file = File('path/to/file.txt');\n  return await file.readAsBytes();\n}\n\n// 按行读取文件\nFuture<List<String>> readAsLines() async {\n  File file = File('path/to/file.txt');\n  return await file.readAsLines();\n}\n\n// 使用Stream读取文件\nStream<List<int>> readAsStream() {\n  File file = File('path/to/file.txt');\n  return file.openRead();\n}", "description": "Dart提供了多种读取文件内容的方法，可以根据需要选择合适的方式：\n\n1. readAsString()：将整个文件内容读取为一个字符串。这适用于处理文本文件，如配置文件、日志或简单的文本数据。\n\n2. readAsBytes()：将整个文件内容读取为字节列表。这适用于处理二进制文件，如图像、音频或视频文件。\n\n3. readAsLines()：将文件内容按行读取，返回字符串列表。这适用于需要逐行处理的文本文件，如CSV文件或日志文件。\n\n4. openRead()：以流的形式读取文件内容。这适用于处理大文件，因为它不会一次性将整个文件加载到内存中，而是逐块读取，从而减少内存使用。\n\n所有这些方法都是异步的，返回Future或Stream对象。使用这些方法时，通常需要结合async/await或then()方法来处理异步结果。\n\n读取文件时，如果指定的文件不存在或无法访问，这些方法会抛出FileSystemException异常。因此，建议在读取文件之前检查文件是否存在，或使用try/catch块来捕获可能的异常。", "parameters": [], "returnValue": "不同方法返回不同类型的Future或Stream对象", "examples": [{"code": "import 'dart:io';\nimport 'dart:convert';\n\nvoid main() async {\n  print('文件读取示例');\n  \n  // 确保示例文件存在\n  File file = File('sample.txt');\n  if (!await file.exists()) {\n    // 创建示例文件\n    await file.writeAsString(\n      '这是第一行\\n'\n      '这是第二行\\n'\n      '这是第三行，包含特殊字符：äöüß\\n'\n      '这是最后一行'\n    );\n    print('创建了示例文件');\n  }\n  \n  // 1. 读取整个文件为字符串\n  print('\\n1. 读取整个文件为字符串:');\n  try {\n    String content = await file.readAsString();\n    print('文件内容:\\n$content');\n    print('字符数: ${content.length}');\n  } catch (e) {\n    print('读取文件时出错: $e');\n  }\n  \n  // 2. 读取文件为字节列表\n  print('\\n2. 读取文件为字节列表:');\n  try {\n    List<int> bytes = await file.readAsBytes();\n    print('字节数: ${bytes.length}');\n    print('前10个字节: ${bytes.take(10).toList()}');\n    \n    // 将字节转换回字符串\n    String content = utf8.decode(bytes);\n    print('转换回字符串后的内容:\\n$content');\n  } catch (e) {\n    print('读取文件时出错: $e');\n  }\n  \n  // 3. 按行读取文件\n  print('\\n3. 按行读取文件:');\n  try {\n    List<String> lines = await file.readAsLines();\n    print('总行数: ${lines.length}');\n    \n    // 遍历每一行\n    for (int i = 0; i < lines.length; i++) {\n      print('第${i+1}行: ${lines[i]}');\n    }\n  } catch (e) {\n    print('读取文件时出错: $e');\n  }\n  \n  // 4. 使用Stream读取文件\n  print('\\n4. 使用Stream读取文件:');\n  try {\n    // 获取文件流\n    Stream<List<int>> fileStream = file.openRead();\n    \n    // 将字节转换为字符串并按行处理\n    int lineCount = 0;\n    await for (String line in fileStream\n        .transform(utf8.decoder) // 解码UTF-8字节\n        .transform(LineSplitter())) { // 按行分割\n      lineCount++;\n      print('流处理 - 第$lineCount行: $line');\n    }\n    \n    print('通过流处理了$lineCount行');\n  } catch (e) {\n    print('读取文件时出错: $e');\n  }\n  \n  // 5. 读取大文件的一部分\n  print('\\n5. 使用RandomAccessFile读取文件的一部分:');\n  RandomAccessFile randomAccessFile;\n  try {\n    randomAccessFile = await file.open(mode: FileMode.read);\n    \n    // 跳过前5个字节\n    await randomAccessFile.setPosition(5);\n    \n    // 读取10个字节\n    List<int> buffer = await randomAccessFile.read(10);\n    print('读取的字节: $buffer');\n    print('转换为字符串: ${utf8.decode(buffer)}');\n    \n    // 关闭文件\n    await randomAccessFile.close();\n  } catch (e) {\n    print('随机访问文件时出错: $e');\n  }\n  \n  print('\\n文件读取示例完成');\n}", "explanation": "这个示例展示了在Dart中读取文件的各种方法。首先确保示例文件存在，如果不存在则创建一个。然后演示了四种不同的文件读取方式：将整个文件读取为字符串(readAsString)、将文件读取为字节列表(readAsBytes)、按行读取文件(readAsLines)以及使用流的方式读取文件(openRead)。对于字节列表方法，示例展示了如何使用utf8.decode将字节转换回字符串。对于流式读取，示例展示了如何使用转换器将字节流转换为文本行。最后，示例演示了如何使用RandomAccessFile来读取文件的特定部分，这对于处理大型文件特别有用。所有操作都使用了try/catch块来处理可能出现的异常。"}]}}, {"name": "Writing Files", "trans": ["写入文件"], "usage": {"syntax": "// 将字符串写入文件\nFuture<File> writeAsString(String contents) async {\n  File file = File('path/to/file.txt');\n  return await file.writeAsString(contents);\n}\n\n// 将字节列表写入文件\nFuture<File> writeAsBytes(List<int> bytes) async {\n  File file = File('path/to/file.txt');\n  return await file.writeAsBytes(bytes);\n}\n\n// 追加内容到文件\nFuture<File> appendString(String contents) async {\n  File file = File('path/to/file.txt');\n  return await file.writeAsString(contents, mode: FileMode.append);\n}\n\n// 使用IOSink流式写入文件\nFuture<void> writeWithSink() async {\n  File file = File('path/to/file.txt');\n  IOSink sink = file.openWrite(mode: FileMode.append);\n  sink.write('内容1');\n  sink.writeln('内容2');\n  await sink.flush();\n  await sink.close();\n}", "description": "Dart提供了多种向文件写入内容的方法，可以根据需要选择合适的方式：\n\n1. writeAsString()：将字符串内容写入文件。可以通过mode参数指定写入模式：\n   - FileMode.write（默认）：覆盖现有文件内容\n   - FileMode.append：追加到文件末尾\n\n2. writeAsBytes()：将字节列表写入文件。同样支持write和append模式。\n\n3. openWrite()：返回一个IOSink对象，可以用于流式写入文件。这对于需要逐步写入大量数据的情况特别有用。使用完IOSink后，必须调用close()方法关闭它。\n\n所有这些方法都是异步的，返回Future对象。写入操作完成后，Future会完成并返回File对象（对于writeAsString和writeAsBytes）或void（对于IOSink的close方法）。\n\n写入文件时，如果指定的目录不存在，这些方法会抛出FileSystemException异常。如果文件不存在，这些方法会创建新文件。如果文件已存在，write模式会覆盖现有内容，append模式会追加新内容。\n\n此外，可以通过encoding参数指定字符编码（默认为utf8），对于包含非ASCII字符的文本尤其重要。", "parameters": [], "returnValue": "通常返回Future<File>，表示操作完成后的文件对象", "examples": [{"code": "import 'dart:io';\nimport 'dart:convert';\n\nvoid main() async {\n  print('文件写入示例');\n  \n  // 1. 基本的字符串写入\n  print('\\n1. 基本的字符串写入:');\n  try {\n    File file = File('output.txt');\n    await file.writeAsString('这是使用writeAsString写入的内容。\\n');\n    print('写入成功，文件大小: ${await file.length()} 字节');\n    \n    // 读取内容以验证\n    String content = await file.readAsString();\n    print('文件内容:\\n$content');\n  } catch (e) {\n    print('写入文件时出错: $e');\n  }\n  \n  // 2. 追加内容到文件\n  print('\\n2. 追加内容到文件:');\n  try {\n    File file = File('output.txt');\n    await file.writeAsString('这是追加的第一行\\n', mode: FileMode.append);\n    await file.writeAsString('这是追加的第二行\\n', mode: FileMode.append);\n    print('追加成功，更新后的文件大小: ${await file.length()} 字节');\n    \n    // 读取内容以验证\n    String content = await file.readAsString();\n    print('更新后的文件内容:\\n$content');\n  } catch (e) {\n    print('追加内容时出错: $e');\n  }\n  \n  // 3. 写入字节\n  print('\\n3. 写入字节:');\n  try {\n    File file = File('binary_output.bin');\n    \n    // 创建一些二进制数据\n    List<int> bytes = List.generate(10, (index) => index * 10);\n    await file.writeAsBytes(bytes);\n    print('写入二进制数据成功，文件大小: ${await file.length()} 字节');\n    \n    // 读取字节以验证\n    List<int> readBytes = await file.readAsBytes();\n    print('读取的字节: $readBytes');\n  } catch (e) {\n    print('写入二进制数据时出错: $e');\n  }\n  \n  // 4. 使用IOSink流式写入\n  print('\\n4. 使用IOSink流式写入:');\n  try {\n    File file = File('stream_output.txt');\n    IOSink sink = file.openWrite(mode: FileMode.write);\n    \n    // 写入多条内容\n    sink.write('这是第一部分内容');\n    sink.write('，这是紧接着的第二部分内容');\n    sink.writeln('，这部分内容后会换行。');\n    sink.writeln('这是新的一行。');\n    \n    for (int i = 1; i <= 5; i++) {\n      sink.writeln('这是写入的第$i个条目');\n    }\n    \n    // 一定要关闭sink\n    await sink.flush(); // 刷新缓冲区\n    await sink.close(); // 关闭sink\n    \n    print('流式写入成功，文件大小: ${await file.length()} 字节');\n    \n    // 读取内容以验证\n    String content = await file.readAsString();\n    print('流式写入的文件内容:\\n$content');\n  } catch (e) {\n    print('流式写入时出错: $e');\n  }\n  \n  // 5. 写入JSON数据\n  print('\\n5. 写入JSON数据:');\n  try {\n    // 准备JSON数据\n    Map<String, dynamic> userData = {\n      'name': '张三',\n      'age': 30,\n      'email': '<EMAIL>',\n      'isActive': true,\n      'tags': ['用户', '客户', 'VIP'],\n      'address': {\n        'city': '北京',\n        'street': '长安街',\n        'number': 100\n      }\n    };\n    \n    // 转换为JSON字符串\n    String jsonString = jsonEncode(userData);\n    \n    // 写入文件\n    File jsonFile = File('user_data.json');\n    await jsonFile.writeAsString(jsonString, encoding: utf8);\n    print('JSON数据写入成功，文件大小: ${await jsonFile.length()} 字节');\n    \n    // 格式化并重新写入，使JSON更易读\n    String prettyJson = JsonEncoder.withIndent('  ').convert(userData);\n    await jsonFile.writeAsString(prettyJson, encoding: utf8);\n    print('格式化的JSON数据写入成功');\n    \n    // 读取内容以验证\n    String content = await jsonFile.readAsString();\n    print('JSON文件内容:\\n$content');\n  } catch (e) {\n    print('写入JSON数据时出错: $e');\n  }\n  \n  print('\\n文件写入示例完成');\n}", "explanation": "这个示例展示了在Dart中写入文件的多种方法。首先演示了基本的字符串写入，使用writeAsString方法将文本内容写入文件，并读取内容进行验证。然后展示了如何使用FileMode.append模式追加内容到现有文件。接着，示例演示了如何使用writeAsBytes方法写入二进制数据。之后，示例展示了如何使用IOSink进行流式写入，这允许程序在不一次性加载所有内容的情况下逐步写入数据。最后，示例展示了如何将Dart对象转换为JSON格式并写入文件，包括如何创建格式化的、易于阅读的JSON输出。所有操作都使用了try/catch块来处理可能出现的异常。"}, {"code": "import 'dart:io';\n\nvoid main() async {\n  print('文件操作练习：');\n  \n  // 创建一个程序，实现以下功能：\n  // 1. 创建一个文件，写入几行文本\n  // 2. 读取该文件的内容并显示\n  // 3. 向文件追加一行新内容\n  // 4. 再次读取并显示更新后的内容\n  \n  // 提示：使用File类的方法，如writeAsString(), readAsString(), 以及FileMode.append\n  \n  // 在这里实现您的解决方案\n  String filePath = 'practice_file.txt';\n  \n  try {\n    // 1. 创建文件并写入内容\n    File file = File(filePath);\n    await file.writeAsString(\n      '这是第一行内容\\n'\n      '这是第二行内容\\n'\n      '这是第三行内容'\n    );\n    print('文件创建成功，已写入初始内容');\n    \n    // 2. 读取并显示文件内容\n    String content = await file.readAsString();\n    print('\\n文件内容：\\n$content');\n    \n    // 3. 追加新内容\n    await file.writeAsString('\\n这是新追加的第四行内容', mode: FileMode.append);\n    print('\\n已追加新内容');\n    \n    // 4. 再次读取并显示\n    content = await file.readAsString();\n    print('\\n更新后的文件内容：\\n$content');\n    \n  } catch (e) {\n    print('操作过程中出错: $e');\n  }\n}", "explanation": "这是一个文件操作的练习示例，要求学生实现一个简单的文件读写程序。在这个解决方案中，首先创建了一个文件并写入三行初始内容，然后读取并显示这些内容。接着，向文件追加一行新内容，最后再次读取并显示更新后的文件内容。这个练习涵盖了基本的文件创建、写入、读取和追加操作，帮助学生熟悉Dart中的文件操作API。整个过程使用了try/catch块来捕获可能的异常，确保程序的健壮性。"}]}}]}