{"name": "Higher-Order Components", "trans": ["高阶组件(HOC)"], "methods": [{"name": "HOC Concept and Usage", "trans": ["HOC概念和用途"], "usage": {"syntax": "const EnhancedComponent = withSomething(BaseComponent);", "description": "高阶组件（HOC）是一个函数，接收一个组件并返回一个新组件。HOC用于复用组件逻辑、增强功能、抽离副作用等。常见用途包括权限控制、数据注入、日志、主题等。", "parameters": [{"name": "BaseComponent", "description": "被增强的基础组件"}], "returnValue": "返回增强后的新组件", "examples": [{"code": "function withLogger(Component) {\n  return function Wrapper(props) {\n    console.log('渲染', Component.name);\n    return <Component {...props} />;\n  };\n}\nconst Enhanced = withLogger(Button);", "explanation": "with<PERSON>ogger是一个简单的高阶组件，为被包装组件增加了日志功能。"}]}}, {"name": "Creating HOC", "trans": ["创建HOC"], "usage": {"syntax": "function withFeature(Component) {\n  return function Enhanced(props) {\n    // ...\n    return <Component {...props} />;\n  };\n}", "description": "创建HOC时，通常返回一个新函数组件，内部可以添加副作用、注入props、包裹UI等。HOC应以with或wrap开头，便于识别。", "parameters": [{"name": "Component", "description": "被包装的组件"}], "returnValue": "返回增强后的新组件", "examples": [{"code": "function withTheme(Component) {\n  return function Enhanced(props) {\n    const theme = 'dark';\n    return <Component {...props} theme={theme} />;\n  };\n}\n// 用法\nconst ThemedButton = withTheme(Button);", "explanation": "withTheme HOC为被包装组件注入了theme属性。"}]}}, {"name": "Props Passing", "trans": ["属性传递"], "usage": {"syntax": "function withProps(Component) {\n  return function Wrapper(props) {\n    return <Component {...props} extra=\"value\" />;\n  };\n}", "description": "HOC应完整传递原组件的props，避免丢失属性。可以通过...props展开所有属性，也可以注入额外属性。", "parameters": [{"name": "props", "description": "原组件的所有属性"}], "returnValue": "返回带有全部原始和新增属性的新组件", "examples": [{"code": "function withUser(Component) {\n  return function Wrapper(props) {\n    const user = { name: '张三' };\n    return <Component {...props} user={user} />;\n  };\n}\n// 用法\nconst UserProfile = withUser(Profile);", "explanation": "withUser HOC为被包装组件注入了user属性，并保留了原有props。"}]}}, {"name": "Display Name Handling", "trans": ["显示名称处理"], "usage": {"syntax": "Wrapper.displayName = `withFeature(${Component.displayName || Component.name || 'Component'})`;", "description": "为调试和开发友好，HOC应设置返回组件的displayName，便于在React DevTools中识别。", "parameters": [{"name": "Component", "description": "被包装的组件"}], "returnValue": "无返回值", "examples": [{"code": "function withLogger(Component) {\n  function Wrapper(props) {\n    return <Component {...props} />;\n  }\n  Wrapper.displayName = `withLogger(${Component.displayName || Component.name || 'Component'})`;\n  return Wrapper;\n}", "explanation": "通过设置displayName，方便在调试工具中识别HOC包装后的组件。"}]}}, {"name": "Composing Multiple HOCs", "trans": ["组合多个HOC"], "usage": {"syntax": "const Enhanced = withA(withB(BaseComponent));", "description": "可以通过嵌套调用实现多个HOC的组合。也可以用compose等工具函数简化组合。", "parameters": [{"name": "withA", "description": "第一个高阶组件"}, {"name": "withB", "description": "第二个高阶组件"}, {"name": "BaseComponent", "description": "基础组件"}], "returnValue": "返回多重增强后的新组件", "examples": [{"code": "const Enhanced = with<PERSON>uth(withTheme(Button));", "explanation": "先用withTheme增强Button，再用withAuth增强，最终得到多重增强组件。"}, {"code": "// 使用compose工具\nimport { compose } from 'redux';\nconst Enhanced = compose(withAuth, withTheme)(Button);", "explanation": "compose可以让多个HOC从右到左依次应用。"}]}}, {"name": "Common HOC Examples", "trans": ["常见HOC示例"], "usage": {"syntax": "// 见下方示例", "description": "常见HOC包括：权限控制、数据注入、主题切换、错误边界、性能监控等。", "parameters": [], "returnValue": "返回增强功能的组件", "examples": [{"code": "// 权限控制HOC\nfunction withAuth(Component) {\n  return function Wrapper(props) {\n    const isLogin = true;\n    if (!isLogin) return <div>请先登录</div>;\n    return <Component {...props} />;\n  };\n}", "explanation": "withAuth HOC实现了简单的权限控制。"}, {"code": "// 错误边界HOC\nfunction withErrorBoundary(Component) {\n  return class ErrorBoundary extends React.Component {\n    constructor(props) {\n      super(props);\n      this.state = { hasError: false }\n    }\n    static getDerivedStateFromError() {\n      return { hasError: true }\n    }\n    componentDidCatch(error, info) {\n      // 记录错误\n    }\n    render() {\n      if (this.state.hasError) return <div>出错了</div>\n      return <Component {...this.props} />\n    }\n  }\n}", "explanation": "withErrorBoundary HOC为组件增加了错误边界功能。"}]}}, {"name": "HOC Caveats", "trans": ["HOC注意事项"], "usage": {"syntax": "// 注意：不要在render中调用HOC\n// 避免props冲突和ref传递问题", "description": "HOC使用时要注意：不要在render中动态调用HOC，避免props冲突，正确处理ref转发，避免静态属性丢失。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 错误用法\nfunction App(props) {\n  // 不要在render中调用HOC\n  const Enhanced = withLogger(Button);\n  return <Enhanced />;\n}", "explanation": "HOC应在组件外部定义，避免每次渲染都创建新组件。"}, {"code": "// ref转发\nconst Forwarded = React.forwardRef((props, ref) => <Button ref={ref} {...props} />);\nfunction withRef(Component) {\n  return React.forwardRef((props, ref) => <Component ref={ref} {...props} />);\n}", "explanation": "通过React.forwardRef可以让HOC正确转发ref。"}]}}, {"name": "作业：实现一个withLoading高阶组件", "trans": ["作业"], "usage": {"syntax": "// 需求：实现一个withLoading HOC\n// 1. 包装组件时，传入loading为true时显示加载中。\n// 2. 其余props正常传递。\n// 3. 代码结构清晰，注释完整。", "description": "请实现一个withLoading高阶组件，要求如上。提交时请附上完整代码和注释。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 示例代码略，请学生自行实现\n// 提示：可以用props.loading判断。", "explanation": "作业要求学生掌握HOC的包装、属性传递和条件渲染。"}, {"code": "function withLoading(Component) {\n  return function Wrapper({ loading, ...rest }) {\n    if (loading) return <div>加载中...</div>;\n    return <Component {...rest} />;\n  };\n}\n// 用法示例\nconst UserListWithLoading = withLoading(UserList);\n// <UserListWithLoading loading={true} />", "explanation": "这是一个完整的withLoading HOC实现，支持loading状态和属性透传。"}]}}]}