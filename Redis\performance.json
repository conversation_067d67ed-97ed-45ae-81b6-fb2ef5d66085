{"name": "Performance Optimization", "trans": ["性能优化"], "methods": [{"name": "Memory Optimization", "trans": ["内存优化"], "usage": {"syntax": "通过合理配置maxmemory、内存淘汰策略等参数，提升内存利用率。", "description": "Redis支持多种内存优化手段，包括设置最大内存、选择合适的淘汰策略、使用压缩等，防止内存溢出并提升性能。", "parameters": [{"name": "maxmemory", "description": "设置Redis可用最大内存。"}, {"name": "maxmemory-policy", "description": "内存淘汰策略，如volatile-lru、allkeys-lru等。"}], "returnValue": "无返回值", "examples": [{"code": "# redis.conf配置内存限制\nmaxmemory 512mb\nmaxmemory-policy allkeys-lru\n", "explanation": "通过配置最大内存和淘汰策略优化内存使用。"}]}}, {"name": "Slow Query Analysis", "trans": ["慢查询分析"], "usage": {"syntax": "通过慢查询日志和监控命令分析性能瓶颈。", "description": "Redis可记录执行时间超过阈值的慢查询，便于定位性能问题。可通过SLOWLOG命令查看慢查询详情。", "parameters": [{"name": "slowlog-log-slower-than", "description": "慢查询阈值，单位微秒。"}, {"name": "SLOWLOG", "description": "慢查询日志相关命令。"}], "returnValue": "无返回值", "examples": [{"code": "# 配置慢查询阈值\nslowlog-log-slower-than 10000\n# 查看慢查询\nSLOWLOG get\n", "explanation": "通过配置和命令分析慢查询，定位性能瓶颈。"}]}}, {"name": "Hot Key and Big Key Handling", "trans": ["热点key与大key处理"], "usage": {"syntax": "通过监控和分片等手段，避免热点key和大key影响性能。", "description": "热点key频繁访问会导致性能瓶颈，大key占用大量内存和带宽。可通过分片、拆分、监控等方式优化。", "parameters": [{"name": "热点key监控", "description": "通过MONITOR、LATENCY等命令发现热点key。"}, {"name": "大key分析", "description": "使用MEMORY USAGE、SCAN等命令定位大key。"}], "returnValue": "无返回值", "examples": [{"code": "# 监控热点key\nMONITOR\nLATENCY latest\n# 分析大key\nMEMORY USAGE mykey\nSCAN 0 COUNT 100\n", "explanation": "通过命令监控和分析热点key与大key，优化性能。"}]}}, {"name": "Network and IO Optimization", "trans": ["网络与IO优化"], "usage": {"syntax": "通过合理配置网络参数和IO线程，提升并发性能。", "description": "Redis可通过调整网络缓冲区、开启IO线程等方式提升高并发场景下的网络和IO性能。", "parameters": [{"name": "tcp-backlog", "description": "监听队列长度，提升高并发接入能力。"}, {"name": "io-threads", "description": "开启多IO线程，提高IO处理能力。"}], "returnValue": "无返回值", "examples": [{"code": "# redis.conf配置网络与IO\ntcp-backlog 1024\nio-threads 4\n", "explanation": "通过配置网络参数和IO线程提升并发性能。"}]}}, {"name": "性能优化作业", "trans": ["作业：性能优化"], "usage": {"syntax": "请完成以下任务：\n1. 配置maxmemory和淘汰策略。\n2. 分析慢查询日志。\n3. 监控并优化热点key和大key。\n4. 调整网络与IO参数。", "description": "通过实际操作理解性能优化的各项措施和效果。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 作业示例\n# 1. 配置maxmemory和maxmemory-policy\n# 2. 使用SLOWLOG分析慢查询\n# 3. 监控热点key和大key\n# 4. 配置tcp-backlog和io-threads\n", "explanation": "通过动手实验掌握性能优化的关键措施。"}]}}, {"name": "性能优化正确实现示例", "trans": ["正确实现卡片"], "usage": {"syntax": "性能优化的标准配置与监控流程。", "description": "展示如何标准配置和监控性能优化参数，确保Redis高效运行。", "parameters": [{"name": "maxmemory", "description": "设置最大内存。"}, {"name": "SLOWLOG", "description": "慢查询日志分析。"}, {"name": "MONITOR", "description": "监控热点key。"}, {"name": "io-threads", "description": "配置IO线程。"}], "returnValue": "无返回值", "examples": [{"code": "# redis.conf标准配置\nmaxmemory 1gb\nmaxmemory-policy allkeys-lru\nio-threads 4\n\n# 监控流程\n# 1. 使用SLOWLOG、MONITOR命令监控性能\n# 2. 定期分析大key和热点key\n", "explanation": "标准性能优化配置和监控流程，保证Redis高效稳定运行。"}]}}]}