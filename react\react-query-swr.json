{"name": "React Query/SWR", "trans": ["React Query/SWR"], "methods": [{"name": "Data Fetching", "trans": ["数据获取"], "usage": {"syntax": "import { useQuery } from 'react-query';\nconst { data, isLoading } = useQuery('key', fetchFn);", "description": "通过useQuery或useSWR钩子进行数据获取，自动处理缓存、状态和请求。", "parameters": [{"name": "key", "description": "唯一标识请求的key。"}, {"name": "fetchFn", "description": "获取数据的异步函数。"}], "returnValue": "返回数据、加载状态、错误等。", "examples": [{"code": "import { useQuery } from 'react-query';\nfunction fetchUser() { return fetch('/api/user').then(res => res.json()); }\nfunction User() {\n  const { data, isLoading } = useQuery('user', fetchUser);\n  if (isLoading) return <div>加载中...</div>;\n  return <div>{data.name}</div>;\n}", "explanation": "用useQuery获取用户数据并渲染。"}]}}, {"name": "Cache Management", "trans": ["缓存管理"], "usage": {"syntax": "import { useQueryClient } from 'react-query';\nconst queryClient = useQueryClient();\nqueryClient.invalidateQueries('key');", "description": "通过QueryClient或mutate等方法管理缓存，支持失效、更新、清除等操作。", "parameters": [{"name": "queryClient", "description": "全局缓存管理对象。"}, {"name": "invalidateQueries", "description": "使指定key的缓存失效。"}], "returnValue": "无返回值，缓存状态被更新。", "examples": [{"code": "import { useQueryClient } from 'react-query';\nconst queryClient = useQueryClient();\nqueryClient.invalidateQueries('user');", "explanation": "使user相关缓存失效，触发重新请求。"}]}}, {"name": "Request Status", "trans": ["请求状态"], "usage": {"syntax": "const { isLoading, isError, error } = useQuery(...);", "description": "通过isLoading、isError、error等状态判断请求进度和异常，优化用户体验。", "parameters": [{"name": "isLoading", "description": "是否正在加载。"}, {"name": "isError", "description": "是否请求出错。"}, {"name": "error", "description": "错误对象。"}], "returnValue": "返回请求的各种状态。", "examples": [{"code": "const { isLoading, isError, error } = useQuery('user', fetchUser);\nif (isLoading) return <div>加载中...</div>;\nif (isError) return <div>错误: {error.message}</div>;", "explanation": "根据请求状态渲染不同UI。"}]}}, {"name": "Optimistic Update", "trans": ["乐观更新"], "usage": {"syntax": "import { useMutation, useQueryClient } from 'react-query';\nconst mutation = useMutation(updateFn, {\n  onMutate: async (newData) => {\n    // 乐观更新\n  },\n  onError: (err, newData, context) => {\n    // 回滚\n  },\n  onSettled: () => {\n    // 重新获取\n  }\n});", "description": "通过useMutation的onMutate等回调实现乐观UI更新，提升交互体验。", "parameters": [{"name": "useMutation", "description": "用于数据变更的钩子。"}, {"name": "onMutate", "description": "乐观更新回调。"}], "returnValue": "返回mutation对象和状态。", "examples": [{"code": "import { useMutation, useQueryClient } from 'react-query';\nconst queryClient = useQueryClient();\nconst mutation = useMutation(updateUser, {\n  onMutate: async (newUser) => {\n    await queryClient.cancelQueries('user');\n    const prev = queryClient.getQueryData('user');\n    queryClient.setQueryData('user', newUser);\n    return { prev };\n  },\n  onError: (err, newUser, context) => {\n    queryClient.setQueryData('user', context.prev);\n  },\n  onSettled: () => {\n    queryClient.invalidateQueries('user');\n  }\n});", "explanation": "实现乐观更新和失败回滚。"}]}}, {"name": "Infinite Loading", "trans": ["无限加载"], "usage": {"syntax": "import { useInfiniteQuery } from 'react-query';\nconst { data, fetchNextPage, hasNextPage } = useInfiniteQuery('key', fetchFn, {\n  getNextPageParam: (lastPage, pages) => ...\n});", "description": "通过useInfiniteQuery或useSWRInfinite实现分页和无限滚动加载。", "parameters": [{"name": "useInfiniteQuery", "description": "分页加载钩子。"}, {"name": "getNextPageParam", "description": "获取下一页参数的方法。"}], "returnValue": "返回分页数据和加载方法。", "examples": [{"code": "import { useInfiniteQuery } from 'react-query';\nconst { data, fetchNextPage, hasNextPage } = useInfiniteQuery('list', fetchList, {\n  getNextPageParam: (lastPage) => lastPage.nextCursor\n});", "explanation": "实现列表的无限加载。"}]}}, {"name": "Polling and Retry", "trans": ["轮询和重试"], "usage": {"syntax": "useQuery('key', fetchFn, {\n  refetchInterval: 5000,\n  retry: 3\n});", "description": "通过refetchInterval设置自动轮询，通过retry设置失败重试次数，提升数据实时性和健壮性。", "parameters": [{"name": "refetchInterval", "description": "轮询间隔毫秒数。"}, {"name": "retry", "description": "失败重试次数。"}], "returnValue": "返回数据和自动轮询、重试机制。", "examples": [{"code": "useQuery('user', fetchUser, { refetchInterval: 10000, retry: 2 });", "explanation": "每10秒自动请求，失败最多重试2次。"}]}}, {"name": "Prefetch Data", "trans": ["预取数据"], "usage": {"syntax": "queryClient.prefetchQuery('key', fetchFn);", "description": "通过prefetchQuery提前请求并缓存数据，提升页面切换体验。", "parameters": [{"name": "prefetch<PERSON><PERSON>y", "description": "预取数据方法。"}], "returnValue": "无返回值，数据被缓存。", "examples": [{"code": "import { useQueryClient } from 'react-query';\nconst queryClient = useQueryClient();\nqueryClient.prefetchQuery('user', fetchUser);", "explanation": "预取用户数据，页面切换时可直接使用。"}]}}, {"name": "作业：React Query/SWR实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 实现数据获取、缓存、状态、乐观更新、无限加载\n// 2. 实现轮询、重试和预取数据", "description": "通过实践数据获取、缓存管理、请求状态、乐观更新、无限加载、轮询重试和预取，掌握高效数据管理。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 用useQuery/useSWR实现数据获取和缓存\n// 2. 实现乐观更新、无限加载、轮询和预取", "explanation": "作业提示，学生需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nimport { useQuery } from 'react-query';\nfunction fetchUser() { return fetch('/api/user').then(res => res.json()); }\nfunction User() {\n  const { data, isLoading } = useQuery('user', fetchUser);\n  if (isLoading) return <div>加载中...</div>;\n  return <div>{data.name}</div>;\n}", "explanation": "React Query数据获取与渲染的正确实现示例。"}]}}]}