{"name": "Recoil", "trans": ["Recoil"], "methods": [{"name": "Atom", "trans": ["原子(Atom)"], "usage": {"syntax": "import { atom } from 'recoil';\nconst countState = atom({ key, default });", "description": "Atom是Recoil的最小状态单元，每个atom代表一份独立的可读写状态。", "parameters": [{"name": "key", "description": "全局唯一的字符串标识"}, {"name": "default", "description": "初始值"}], "returnValue": "atom对象，可用于useRecoilState等hook", "examples": [{"code": "import { atom } from 'recoil';\n// 定义一个计数器atom\nconst countState = atom({\n  key: 'countState', // 全局唯一key\n  default: 0 // 初始值\n});", "explanation": "通过atom定义独立的状态单元。"}]}}, {"name": "Selector", "trans": ["选择器(Selector)"], "usage": {"syntax": "import { selector } from 'recoil';\nconst doubleCount = selector({ key, get, set });", "description": "Selector用于派生/计算状态，可依赖一个或多个atom或其他selector。支持只读和可写。", "parameters": [{"name": "key", "description": "全局唯一的字符串标识"}, {"name": "get", "description": "读取函数，返回派生值"}, {"name": "set", "description": "可选，写入函数"}], "returnValue": "selector对象，可用于useRecoilValue等hook", "examples": [{"code": "import { selector } from 'recoil';\nimport { countState } from './atoms';\n// 定义一个派生状态selector\nconst doubleCount = selector({\n  key: 'doubleCount',\n  get: ({ get }) => get(countState) * 2\n});", "explanation": "通过selector实现状态的派生和计算。"}]}}, {"name": "State Sharing", "trans": ["状态共享"], "usage": {"syntax": "const [value, setValue] = useRecoilState(atom);", "description": "通过useRecoilState、useRecoilValue等hook在多个组件间共享和同步atom状态。", "parameters": [{"name": "atom", "description": "要共享的atom对象"}], "returnValue": "当前atom的值和更新函数", "examples": [{"code": "import { useRecoilState } from 'recoil';\nimport { countState } from './atoms';\n// 在多个组件中使用同一个atom\nfunction Counter() {\n  const [count, setCount] = useRecoilState(countState);\n  return (\n    <div>\n      <span>计数: {count}</span>\n      <button onClick={() => setCount(count + 1)}>加一</button>\n    </div>\n  );\n}", "explanation": "多个组件通过同一个atom实现状态共享。"}]}}, {"name": "Async Data Handling", "trans": ["异步数据处理"], "usage": {"syntax": "const data = useRecoilValue(asyncSelector);", "description": "Selector的get函数可返回Promise，实现异步数据请求和自动缓存。", "parameters": [{"name": "asyncSelector", "description": "返回Promise的selector对象"}], "returnValue": "异步加载的数据值", "examples": [{"code": "import { selector, useRecoilValue } from 'recoil';\n// 定义异步selector\nconst userSelector = selector({\n  key: 'userSelector',\n  get: async () => {\n    const res = await fetch('/api/user');\n    return await res.json();\n  }\n});\n// 组件中使用\nfunction UserInfo() {\n  const user = useRecoilValue(userSelector);\n  return <div>用户名: {user.name}</div>;\n}", "explanation": "通过异步selector实现数据请求和缓存。"}]}}, {"name": "State Persistence", "trans": ["状态持久化"], "usage": {"syntax": "// 结合recoil-persist等库实现持久化", "description": "Recoil本身不带持久化，可结合recoil-persist等库将atom状态同步到localStorage。", "parameters": [], "returnValue": "持久化的atom状态", "examples": [{"code": "import { atom } from 'recoil';\nimport { recoilPersist } from 'recoil-persist';\n// 配置持久化\nconst { persistAtom } = recoilPersist();\nconst countState = atom({\n  key: 'countState',\n  default: 0,\n  effects_UNSTABLE: [persistAtom]\n});", "explanation": "通过recoil-persist实现atom持久化。"}]}}, {"name": "State Synchronization", "trans": ["状态同步"], "usage": {"syntax": "// 通过selector、effects等实现多源状态同步", "description": "可通过selector、effects等机制实现Recoil与外部状态或多端同步。", "parameters": [], "returnValue": "同步后的状态值", "examples": [{"code": "import { atom } from 'recoil';\n// 通过effects同步本地存储\nconst countState = atom({\n  key: 'countState',\n  default: 0,\n  effects_UNSTABLE: [({ setSelf }) => {\n    const saved = localStorage.getItem('count');\n    if (saved) setSelf(Number(saved));\n  }]\n});", "explanation": "通过effects实现本地存储与Recoil状态同步。"}]}}, {"name": "Comparison with Context", "trans": ["与Context对比"], "usage": {"syntax": "// Recoil无需手动传递Provider，API更灵活", "description": "Recoil无需手动传递Provider，状态粒度更细，适合复杂依赖和大规模状态管理。Context适合简单全局数据。", "parameters": [], "returnValue": "Recoil与Context的主要区别", "examples": [{"code": "// Context需手动传递Provider，Recoil自动管理依赖关系\n// Recoil适合复杂依赖，Context适合简单全局数据", "explanation": "对比两者API和适用场景。"}]}}, {"name": "作业：实现一个Recoil计数器", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 用Recoil实现计数器。\n// 2. 支持加一、减一。\n// 3. 代码结构清晰，注释完整。", "description": "请实现上述Recoil计数器，提交时请附上完整代码和注释。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 示例代码略，请学生自行实现\n// 提示：用atom、useRecoilState实现。", "explanation": "作业要求学生掌握Recoil全流程。"}, {"code": "import React from 'react';\nimport { atom, useRecoilState, RecoilRoot } from 'recoil';\n// 1. 定义计数器atom\nconst countState = atom({\n  key: 'countState',\n  default: 0\n});\n// 2. 组件中使用\nfunction Counter() {\n  const [count, setCount] = useRecoilState(countState);\n  return (\n    <div>\n      <span>计数: {count}</span>\n      <button onClick={() => setCount(count + 1)}>加一</button>\n      <button onClick={() => setCount(count - 1)}>减一</button>\n    </div>\n  );\n}\n// 3. 用法\nfunction App() {\n  return (\n    <RecoilRoot>\n      <Counter />\n    </RecoilRoot>\n  );\n}", "explanation": "完整实现Recoil计数器，支持加一减一。"}]}}]}