{"name": "Built-in Modules", "trans": ["内置模块"], "methods": [{"name": "sys and os", "trans": ["sys与os模块"], "usage": {"syntax": "import sys, os", "description": "sys用于与Python解释器交互，os用于操作系统相关功能，如文件、路径、环境变量等。", "parameters": [{"name": "sys.argv", "description": "命令行参数列表"}, {"name": "os.getcwd", "description": "获取当前工作目录"}, {"name": "os.listdir", "description": "列出目录下文件"}], "returnValue": "依赖具体函数，通常为字符串、列表等", "examples": [{"code": "import sys, os\nprint(sys.argv)  # 输出命令行参数\nprint(os.getcwd())  # 当前目录\nprint(os.listdir('.'))  # 当前目录下文件列表", "explanation": "演示了sys和os模块的常用用法。"}]}}, {"name": "math and random", "trans": ["math与random模块"], "usage": {"syntax": "import math, random", "description": "math提供数学运算函数，random用于生成随机数和随机操作。", "parameters": [{"name": "math.sqrt", "description": "平方根"}, {"name": "random.randint", "description": "生成指定范围的随机整数"}], "returnValue": "依赖具体函数，通常为数值", "examples": [{"code": "import math, random\nprint(math.sqrt(16))  # 4.0\nprint(random.randint(1, 10))  # 1~10之间的随机整数", "explanation": "演示了math和random模块的常用用法。"}]}}, {"name": "datetime and time", "trans": ["datetime与time模块"], "usage": {"syntax": "import datetime, time", "description": "datetime和time用于处理日期和时间，包括获取当前时间、格式化、延时等。", "parameters": [{"name": "datetime.datetime.now", "description": "当前日期时间"}, {"name": "time.sleep", "description": "休眠指定秒数"}], "returnValue": "依赖具体函数，通常为日期时间对象或None", "examples": [{"code": "import datetime, time\nprint(datetime.datetime.now())  # 当前时间\ntime.sleep(1)  # 暂停1秒", "explanation": "演示了datetime和time模块的常用用法。"}]}}, {"name": "collections", "trans": ["collections模块"], "usage": {"syntax": "import collections", "description": "collections提供了多种容器数据类型，如Counter、deque、defaultdict等。", "parameters": [{"name": "Counter", "description": "计数器，统计元素出现次数"}, {"name": "deque", "description": "双端队列，支持高效插入和删除"}], "returnValue": "依赖具体类型，通常为容器对象", "examples": [{"code": "from collections import Counter, deque\nc = Counter('hello')\nprint(c)  # 统计每个字母出现次数\nd = deque([1,2,3])\nd.appendleft(0)\nprint(d)", "explanation": "演示了Counter和deque的用法。"}]}}, {"name": "itertools and functools", "trans": ["itertools与functools模块"], "usage": {"syntax": "import itertools, functools", "description": "itertools提供高效迭代器工具，functools用于函数式编程，如偏函数、缓存等。", "parameters": [{"name": "itertools.count", "description": "生成无限递增序列"}, {"name": "functools.reduce", "description": "对序列进行累积计算"}], "returnValue": "依赖具体函数，通常为迭代器或计算结果", "examples": [{"code": "import itertools, functools\nfor i in itertools.islice(itertools.count(10), 3):\n    print(i)  # 10, 11, 12\nfrom functools import reduce\nprint(reduce(lambda x, y: x + y, [1,2,3]))  # 6", "explanation": "演示了itertools和functools的用法。"}]}}, {"name": "re Regular Expressions", "trans": ["re正则表达式模块"], "usage": {"syntax": "import re", "description": "re模块用于正则表达式匹配、查找、替换等操作。", "parameters": [{"name": "re.match", "description": "从字符串开头匹配"}, {"name": "re.findall", "description": "查找所有匹配"}], "returnValue": "依赖具体函数，通常为匹配对象或列表", "examples": [{"code": "import re\nprint(re.match(r'\\d+', '123abc'))  # 匹配数字\nprint(re.findall(r'\\w+', 'a1 b2'))  # 匹配所有单词", "explanation": "演示了re模块的常用用法。"}]}}, {"name": "json and pickle", "trans": ["json与pickle模块"], "usage": {"syntax": "import json, pickle", "description": "json用于处理JSON数据，pickle用于Python对象的序列化和反序列化。", "parameters": [{"name": "json.dumps", "description": "对象转JSON字符串"}, {"name": "pickle.dumps", "description": "对象转字节流"}], "returnValue": "依赖具体函数，通常为字符串或字节流", "examples": [{"code": "import json, pickle\ndata = {'a': 1}\ns = json.dumps(data)\nprint(s)  # '{\"a\": 1}'\nb = pickle.dumps(data)\nprint(b)", "explanation": "演示了json和pickle的用法。"}]}}, {"name": "logging", "trans": ["logging日志模块"], "usage": {"syntax": "import logging", "description": "logging模块用于记录日志，支持多级别和多种输出方式。", "parameters": [{"name": "logging.basicConfig", "description": "配置日志格式和级别"}, {"name": "logging.info", "description": "输出信息级别日志"}], "returnValue": "无返回值", "examples": [{"code": "import logging\nlogging.basicConfig(level=logging.INFO)\nlogging.info('信息日志')", "explanation": "演示了logging模块的基本用法。"}]}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "trans": ["argparse命令行参数模块"], "usage": {"syntax": "import argparse", "description": "argparse用于解析命令行参数，适合编写命令行工具。", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "参数解析器对象"}, {"name": "add_argument", "description": "添加参数定义"}], "returnValue": "解析后的参数对象", "examples": [{"code": "import argparse\nparser = argparse.ArgumentParser()\nparser.add_argument('--name')\nargs = parser.parse_args(['--name', 'Tom'])\nprint(args.name)", "explanation": "演示了argparse模块的用法。"}]}}, {"name": "Built-in Modules Assignment", "trans": ["内置模块练习"], "usage": {"syntax": "# 内置模块练习", "description": "完成以下练习，巩固内置模块相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用os列出当前目录所有文件。\n2. 用random生成1~100的随机数。\n3. 用datetime获取当前日期。\n4. 用re提取字符串中的所有数字。\n5. 用json序列化一个字典。\n6. 用logging输出一条警告日志。", "explanation": "练习要求动手实践各内置模块的常用操作。"}]}}]}