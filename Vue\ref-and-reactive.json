{"name": "ref and reactive", "trans": ["ref与reactive"], "methods": [{"name": "ref Basic Usage", "trans": ["ref基本用法"], "usage": {"syntax": "import { ref } from 'vue'\nconst count = ref(0)", "description": "ref用于创建基本类型或对象的响应式引用，访问和赋值需通过.value属性。适合基本类型和单一响应式变量。", "parameters": [{"name": "ref(初始值)", "description": "要响应的数据"}], "returnValue": "返回一个响应式引用对象，值在.value属性上", "examples": [{"code": "import { ref } from 'vue'\nconst msg = ref('hello')\nmsg.value = 'world'", "explanation": "msg是响应式引用，赋值和读取都用.value。"}]}}, {"name": "reactive Basic Usage", "trans": ["reactive基本用法"], "usage": {"syntax": "import { reactive } from 'vue'\nconst state = reactive({ count: 0 })", "description": "reactive用于将对象变为响应式，适合管理多个属性的响应式状态。直接操作属性即可自动响应。", "parameters": [{"name": "reactive(对象)", "description": "要响应的对象"}], "returnValue": "返回一个响应式对象，属性可直接访问和赋值", "examples": [{"code": "import { reactive } from 'vue'\nconst state = reactive({ count: 0 })\nstate.count++", "explanation": "state是响应式对象，属性可直接操作。"}]}}, {"name": "Reactive Object vs Primitive", "trans": ["响应式对象与基本类型"], "usage": {"syntax": "ref(基本类型)\nreactive(对象)", "description": "ref适合基本类型和单一变量，reactive适合对象和数组。ref的.value是响应式，reactive返回整个对象的响应式代理。", "parameters": [{"name": "ref/reative", "description": "两种响应式API的适用场景"}], "returnValue": "ref返回响应式引用，reactive返回响应式对象", "examples": [{"code": "const a = ref(1)\nconst obj = reactive({ b: 2 })", "explanation": "a适合基本类型，obj适合对象。"}]}}, {"name": "toRefs and toRef", "trans": ["toRefs与toRef"], "usage": {"syntax": "import { toRefs, toRef } from 'vue'\nconst state = reactive({ a: 1 })\nconst { a } = toRefs(state)\nconst b = toRef(state, 'a')", "description": "toRefs将响应式对象的每个属性转为ref，适合解构和传递。toRef将对象的单个属性转为ref。", "parameters": [{"name": "toRefs(对象)", "description": "批量转为ref"}, {"name": "toRef(对象, 属性名)", "description": "单个属性转为ref"}], "returnValue": "返回ref对象，保持与原对象属性响应式同步", "examples": [{"code": "const state = reactive({ a: 1 })\nconst { a } = toRefs(state)\nconst b = toRef(state, 'a')", "explanation": "toRefs适合解构，toRef适合单独引用属性。"}]}}, {"name": "shallowRef and shallowReactive", "trans": ["shallowRef/shallowReactive"], "usage": {"syntax": "import { shallowRef, shallowReactive } from 'vue'\nconst s1 = shallowRef({ a: 1 })\nconst s2 = shallowReactive({ a: 1 })", "description": "shallowRef和shallowReactive只做一层响应式，内部对象属性不是响应式，适合性能优化或特殊场景。", "parameters": [{"name": "shallowRef/shallowReactive", "description": "浅层响应式API"}], "returnValue": "返回浅层响应式引用或对象，内部属性非响应式", "examples": [{"code": "const s1 = shallowRef({ a: 1 })\ns1.value.a = 2 // 不会触发响应式\nconst s2 = shallowReactive({ a: 1 })\ns2.a = 2 // 响应式，s2.a变更会触发视图更新，但s2.a的内部属性变更不会响应", "explanation": "shallowRef/shallowReactive只追踪第一层属性。"}]}}, {"name": "readonly and markRaw", "trans": ["readonly/markRaw"], "usage": {"syntax": "import { readonly, markRaw } from 'vue'\nconst ro = readonly(obj)\nconst raw = markRaw(obj)", "description": "readonly创建只读响应式对象，禁止修改。markRaw标记对象为非响应式，适合第三方库数据等。", "parameters": [{"name": "readonly(obj)", "description": "只读响应式对象"}, {"name": "mark<PERSON><PERSON>(obj)", "description": "非响应式对象"}], "returnValue": "返回只读或非响应式对象，防止误操作或性能浪费", "examples": [{"code": "const ro = readonly({ a: 1 })\nro.a = 2 // 报错\nconst raw = markRaw({ b: 2 })", "explanation": "readonly禁止修改，markRaw不做响应式处理。"}]}}, {"name": "ref and reactive Assignment", "trans": ["ref与reactive练习"], "usage": {"syntax": "# ref与reactive练习", "description": "完成以下练习，巩固ref与reactive相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用ref声明基本类型响应式变量。\n2. 用reactive声明对象响应式。\n3. 用toRefs解构响应式对象。\n4. 用shallowRef和readonly体验不同响应式行为。", "explanation": "通过这些练习掌握ref、reactive、toRefs、shallowRef、readonly等用法。"}]}}]}