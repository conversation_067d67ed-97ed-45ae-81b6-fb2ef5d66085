{"name": "Modern Python Ecosystem", "trans": ["现代Python生态"], "methods": [{"name": "Scientific Computing (NumPy, Pandas, Matplotlib)", "trans": ["科学计算（NumPy、Pandas、Matplotlib）"], "usage": {"syntax": "import numpy as np\nimport pandas as pd\nimport matplotlib.pyplot as plt", "description": "NumPy用于高效数值计算，Pandas用于数据分析与处理，Matplotlib用于数据可视化。", "parameters": [{"name": "np.array", "description": "创建数组"}, {"name": "pd.DataFrame", "description": "创建数据表"}, {"name": "plt.plot", "description": "绘制图表"}], "returnValue": "数组、数据表、图形对象等", "examples": [{"code": "import numpy as np\nimport pandas as pd\nimport matplotlib.pyplot as plt\na = np.array([1,2,3])\ndf = pd.DataFrame({'a': a})\ndf.plot()\nplt.show()", "explanation": "演示了NumPy、Pandas、Matplotlib的基础用法。"}]}}, {"name": "Web Development (Django, Flask, FastAPI)", "trans": ["Web开发（Django、Flask、FastAPI）"], "usage": {"syntax": "import flask\nfrom django.http import HttpResponse\nfrom fastapi import FastAPI", "description": "Django适合大型Web项目，Flask轻量灵活，FastAPI支持异步和类型注解，适合现代API开发。", "parameters": [{"name": "Flask", "description": "轻量Web框架"}, {"name": "Django", "description": "全功能Web框架"}, {"name": "FastAPI", "description": "高性能API框架"}], "returnValue": "Web应用对象或响应", "examples": [{"code": "from flask import Flask\napp = Flask(__name__)\***********('/')\ndef hello():\n    return 'Hello, Flask!'", "explanation": "演示了Flask的最简用法。"}, {"code": "from fastapi import FastAPI\napp = FastAPI()\*********('/')\ndef read_root():\n    return {'msg': 'Hello, FastAPI!'}", "explanation": "演示了FastAPI的最简用法。"}]}}, {"name": "Data Analysis and Machine Learning (scikit-learn, TensorFlow, PyTorch)", "trans": ["数据分析与机器学习（scikit-learn、TensorFlow、PyTorch）"], "usage": {"syntax": "import sklearn\nimport tensorflow as tf\nimport torch", "description": "scikit-learn用于传统机器学习，TensorFlow和PyTorch用于深度学习和神经网络。", "parameters": [{"name": "sklearn.model", "description": "机器学习模型"}, {"name": "tf.keras.Model", "description": "TensorFlow模型"}, {"name": "torch.nn.<PERSON><PERSON><PERSON>", "description": "PyTorch模型"}], "returnValue": "模型对象、训练结果等", "examples": [{"code": "from sklearn.linear_model import LinearRegression\nmodel = LinearRegression()\nmodel.fit([[1],[2]], [1,2])", "explanation": "演示了scikit-learn的基本用法。"}, {"code": "import torch\nimport torch.nn as nn\nmodel = nn.Linear(1, 1)", "explanation": "演示了PyTorch的基本用法。"}]}}, {"name": "Web Crawling and Automation (Scrapy, Selenium)", "trans": ["爬虫与自动化（Scrapy、Selenium）"], "usage": {"syntax": "import scrapy\nfrom selenium import webdriver", "description": "Scrapy适合大规模爬虫，Selenium可自动化浏览器操作，常用于数据采集和自动化测试。", "parameters": [{"name": "scrapy.<PERSON>", "description": "爬虫类"}, {"name": "webdriver", "description": "浏览器驱动对象"}], "returnValue": "爬取结果、自动化操作结果等", "examples": [{"code": "from selenium import webdriver\ndriver = webdriver.Chrome()\ndriver.get('https://www.example.com')\nprint(driver.title)\ndriver.quit()", "explanation": "演示了Selenium自动化浏览器操作。"}]}}, {"name": "Scripting and Automation Ops", "trans": ["脚本与自动化运维"], "usage": {"syntax": "import os\nos.system('命令')", "description": "Python可用于批量脚本、自动化运维、定时任务等，提升开发和运维效率。", "parameters": [{"name": "os.system", "description": "执行系统命令"}], "returnValue": "命令执行结果", "examples": [{"code": "import os\nos.system('echo hello')", "explanation": "演示了os.system执行自动化脚本。"}]}}, {"name": "Modern Python Ecosystem Assignment", "trans": ["现代Python生态练习"], "usage": {"syntax": "# 现代Python生态练习", "description": "完成以下练习，巩固现代Python生态相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用NumPy和Pandas处理数据并画图。\n2. 用Flask或FastAPI写一个简单Web接口。\n3. 用scikit-learn训练一个回归模型。\n4. 用Selenium自动打开网页并截图。\n5. 用os.system批量执行命令。", "explanation": "练习要求动手实践科学计算、Web开发、机器学习、爬虫、自动化等。"}]}}]}