{"name": "String", "trans": ["字符串类型"], "methods": [{"name": "Basic String Commands", "trans": ["基本操作命令"], "usage": {"syntax": "SET key value [EX seconds] [NX|XX]\nGET key\nDEL key", "description": "SET用于设置字符串键值，GET获取，DEL删除。支持过期时间（EX）、仅在不存在时设置（NX）等选项。", "parameters": [{"name": "key", "description": "字符串键名"}, {"name": "value", "description": "字符串值"}, {"name": "EX", "description": "可选，设置过期时间（秒）"}, {"name": "NX|XX", "description": "可选，NX仅在不存在时设置，XX仅在已存在时设置"}], "returnValue": "OK 或实际值，DEL返回删除数量", "examples": [{"code": "$ redis-cli set name Tom\n$ redis-cli get name\n$ redis-cli del name", "explanation": "演示了字符串的基本增删查操作。"}]}}, {"name": "Common String Applications", "trans": ["字符串常用应用"], "usage": {"syntax": "INCR key\nDECR key\nAPPEND key value\nGETRANGE key start end", "description": "INCR/DECR用于数值自增自减，APPEND追加字符串，GETRANGE获取子串。常用于计数器、拼接、截取等场景。", "parameters": [{"name": "key", "description": "目标键名"}, {"name": "value", "description": "追加内容"}, {"name": "start/end", "description": "子串起止下标"}], "returnValue": "操作后的新值或子串", "examples": [{"code": "$ redis-cli set counter 10\n$ redis-cli incr counter # 变为11\n$ redis-cli append name ' <PERSON>'\n$ redis-cli get name\n$ redis-cli getrange name 0 2", "explanation": "演示了计数器、字符串追加和截取。"}]}}, {"name": "Counting and Rate Limiting", "trans": ["计数与限流"], "usage": {"syntax": "INCR key\nEXPIRE key seconds\nSET key value EX seconds NX", "description": "通过INCR和EXPIRE组合实现计数器和限流，如接口访问频率限制。SET NX EX可实现分布式锁和限流。", "parameters": [{"name": "key", "description": "计数器或限流键名"}, {"name": "seconds", "description": "过期时间"}], "returnValue": "计数值或操作结果", "examples": [{"code": "# 限流示例\n$ redis-cli incr api:count\n$ redis-cli expire api:count 60 # 60秒内只计数一次\n# 分布式锁\n$ redis-cli set lock:api 1 NX EX 10", "explanation": "演示了计数器和限流的实现方法。"}]}}, {"name": "Practice: String类型练习", "trans": ["字符串类型练习"], "usage": {"syntax": "# Redis字符串类型练习", "description": "完成以下练习，掌握字符串类型的常用操作和应用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 设置一个字符串键并获取其值。\n2. 实现一个自增计数器。\n3. 追加内容到已有字符串。\n4. 用INCR+EXPIRE实现一分钟内的访问计数。", "explanation": "通过这些练习掌握字符串类型的常用命令和应用。"}, {"code": "# 正确实现示例\n$ redis-cli set msg 'hello'\n$ redis-cli get msg\n$ redis-cli incr count\n$ redis-cli append msg ' world'\n$ redis-cli get msg\n$ redis-cli incr api:visit\n$ redis-cli expire api:visit 60", "explanation": "展示了练习的标准实现过程。"}]}}]}