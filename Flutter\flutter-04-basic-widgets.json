{"name": "Common Basic Widgets", "trans": ["常用基础组件"], "methods": [{"name": "Text, Image, Icon", "trans": ["Text、Image、Icon"], "usage": {"syntax": "Text('内容'), Image.asset('路径'), Icon(Icons.xxx)", "description": "Text用于显示文本，Image用于显示图片，Icon用于显示图标，是最常用的基础组件。", "parameters": [{"name": "Text", "description": "文本内容及样式"}, {"name": "Image", "description": "图片路径、网络地址、样式"}, {"name": "Icon", "description": "图标类型、颜色、大小"}], "returnValue": "无返回值，直接渲染在界面上。", "examples": [{"code": "// Text、Image、Icon组件示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      home: Scaffold(\n        appBar: AppBar(title: Text('基础组件示例')),\n        body: Column(\n          children: [\n            Text('Hello Flutter!', style: TextStyle(fontSize: 24, color: Colors.blue)), // 显示文本\n            Image.network('https://flutter.dev/images/flutter-logo-sharing.png', width: 100), // 显示网络图片\n            Icon(Icons.favorite, color: Colors.red, size: 32), // 显示图标\n          ],\n        ),\n      ),\n    );\n  }\n}", "explanation": "演示了Text、Image、Icon的基本用法，分别显示文本、图片和图标。"}]}}, {"name": "<PERSON><PERSON> Widgets", "trans": ["Button（ElevatedButton、TextButton等）"], "usage": {"syntax": "Elevated<PERSON><PERSON><PERSON>(onPressed: ..., child: ...), Text<PERSON><PERSON><PERSON>(onPressed: ..., child: ...)", "description": "Flutter提供多种按钮组件，如ElevatedButton（凸起按钮）、TextButton（文本按钮）等，常用于交互。", "parameters": [{"name": "onPressed", "description": "点击事件回调函数"}, {"name": "child", "description": "按钮显示内容"}], "returnValue": "无返回值，点击时触发回调。", "examples": [{"code": "// 按钮组件示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      home: Scaffold(\n        appBar: AppBar(title: Text('按钮组件示例')),\n        body: Center(\n          child: Row(\n            mainAxisAlignment: MainAxisAlignment.center,\n            children: [\n              ElevatedButton(\n                onPressed: () { print('ElevatedButton点击'); },\n                child: Text('ElevatedButton'),\n              ),\n              SizedBox(width: 16),\n              TextButton(\n                onPressed: () { print('TextButton点击'); },\n                child: Text('TextButton'),\n              ),\n            ],\n          ),\n        ),\n      ),\n    );\n  }\n}", "explanation": "演示了ElevatedButton和TextButton的基本用法，点击按钮会触发回调。"}]}}, {"name": "TextField (Input Box)", "trans": ["输入框（TextField）"], "usage": {"syntax": "<PERSON><PERSON><PERSON>(controller: ..., decoration: ...)", "description": "TextField是Flutter中用于文本输入的组件，支持多种装饰和事件。", "parameters": [{"name": "controller", "description": "用于获取和控制输入内容的TextEditingController对象"}, {"name": "decoration", "description": "输入框的装饰，如提示文本、边框等"}], "returnValue": "无返回值，输入内容通过controller获取。", "examples": [{"code": "// TextField输入框示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatefulWidget {\n  @override\n  _MyAppState createState() => _MyAppState();\n}\n\nclass _MyAppState extends State<MyApp> {\n  final TextEditingController _controller = TextEditingController();\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      home: Scaffold(\n        appBar: AppBar(title: Text('输入框示例')),\n        body: Padding(\n          padding: EdgeInsets.all(16),\n          child: Column(\n            children: [\n              TextField(\n                controller: _controller,\n                decoration: InputDecoration(labelText: '请输入内容'),\n              ),\n              SizedBox(height: 16),\n              ElevatedButton(\n                onPressed: () {\n                  print('输入内容: \\${_controller.text}');\n                },\n                child: Text('提交'),\n              ),\n            ],\n          ),\n        ),\n      ),\n    );\n  }\n}", "explanation": "演示了TextField输入框的基本用法，配合controller获取输入内容。"}]}}, {"name": "ListView, GridView", "trans": ["列表（ListView、GridView）"], "usage": {"syntax": "<PERSON><PERSON><PERSON><PERSON>(children: [...]), <PERSON><PERSON><PERSON><PERSON><PERSON>.count(crossAxisCount: ..., children: [...])", "description": "ListView用于垂直或水平滚动的列表，GridView用于网格布局，常用于展示大量数据。", "parameters": [{"name": "children", "description": "列表或网格的子组件数组"}, {"name": "crossAxisCount", "description": "GridView的列数"}], "returnValue": "无返回值，直接渲染在界面上。", "examples": [{"code": "// ListView和GridView示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      home: Scaffold(\n        appBar: AppBar(title: Text('列表组件示例')),\n        body: Column(\n          children: [\n            Expanded(\n              child: ListView(\n                children: List.generate(5, (i) => ListTile(title: Text('Item \\${i+1}'))),\n              ),\n            ),\n            Divider(),\n            Expanded(\n              child: GridView.count(\n                crossAxisCount: 3,\n                children: List.generate(6, (i) => Container(\n                  margin: EdgeInsets.all(8),\n                  color: Colors.blue[100 * ((i % 8) + 1)],\n                  child: Center(child: Text('Grid \\${i+1}')),\n                )),\n              ),\n            ),\n          ],\n        ),\n      ),\n    );\n  }\n}", "explanation": "演示了ListView和GridView的基本用法，分别实现列表和网格布局。"}]}}, {"name": "Container, Card, SizedBox", "trans": ["容器（Container、Card、SizedBox）"], "usage": {"syntax": "Container(width: ..., height: ..., color: ...), <PERSON>(child: ...), <PERSON><PERSON><PERSON><PERSON>(height: ...)", "description": "Container是最常用的容器组件，Card用于卡片式展示，SizedBox用于占位和间距。", "parameters": [{"name": "width/height", "description": "宽高设置"}, {"name": "color", "description": "背景色"}, {"name": "child", "description": "子组件"}], "returnValue": "无返回值，直接渲染在界面上。", "examples": [{"code": "// Container、Card、SizedBox示例\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      home: Scaffold(\n        appBar: AppBar(title: Text('容器组件示例')),\n        body: Center(\n          child: Column(\n            mainAxisAlignment: MainAxisAlignment.center,\n            children: [\n              Container(\n                width: 100,\n                height: 50,\n                color: Colors.amber,\n                child: Center(child: Text('Container')),\n              ),\n              SizedBox(height: 16),\n              Card(\n                elevation: 4,\n                child: Padding(\n                  padding: EdgeInsets.all(16),\n                  child: Text('Card内容'),\n                ),\n              ),\n            ],\n          ),\n        ),\n      ),\n    );\n  }\n}", "explanation": "演示了Container、Card、SizedBox的基本用法，分别用于布局、卡片和间距。"}]}}, {"name": "Assignment", "trans": ["作业：实现一个包含文本、图片、按钮和输入框的简单页面。"], "usage": {"syntax": "无", "description": "请用Flutter实现一个页面，包含Text、Image、Icon、ElevatedButton、TextField等组件，点击按钮后弹出输入框内容。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 作业参考实现\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatefulWidget {\n  @override\n  _MyAppState createState() => _MyAppState();\n}\n\nclass _MyAppState extends State<MyApp> {\n  final TextEditingController _controller = TextEditingController();\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      home: Scaffold(\n        appBar: AppBar(title: Text('作业实现')),\n        body: Padding(\n          padding: EdgeInsets.all(16),\n          child: Column(\n            children: [\n              Text('作业演示', style: TextStyle(fontSize: 24)),\n              Image.network('https://flutter.dev/images/flutter-logo-sharing.png', width: 80),\n              Icon(Icons.star, color: Colors.orange),\n              TextField(\n                controller: _controller,\n                decoration: InputDecoration(labelText: '请输入内容'),\n              ),\n              <PERSON><PERSON><PERSON><PERSON>(height: 16),\n              Elevated<PERSON>utton(\n                onPressed: () {\n                  showDialog(\n                    context: context,\n                    builder: (_) => AlertDialog(content: Text('你输入了: \\${_controller.text}')),\n                  );\n                },\n                child: Text('提交'),\n              ),\n            ],\n          ),\n        ),\n      ),\n    );\n  }\n}", "explanation": "作业要求实现一个包含文本、图片、按钮和输入框的页面，点击按钮弹窗显示输入内容。"}]}}]}