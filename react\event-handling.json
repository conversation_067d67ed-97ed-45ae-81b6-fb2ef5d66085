{"name": "Event Handling", "trans": ["事件处理"], "methods": [{"name": "React Event System", "trans": ["React事件系统"], "usage": {"syntax": "<element onClick={handleClick} />", "description": "React实现了一套跨浏览器的合成事件系统，它模拟了DOM事件的行为，但提供了一致的API和性能优化。React事件使用驼峰命名法（如onClick而非onclick）。", "parameters": [{"name": "element", "description": "React元素，可以是原生DOM元素或自定义组件"}, {"name": "onEventName", "description": "事件处理器属性，如onClick、onSubmit等，使用驼峰命名法"}, {"name": "handleEvent", "description": "事件处理函数，当事件触发时会被调用，接收事件对象作为参数"}], "returnValue": "", "examples": [{"code": "// 基本事件处理\nimport React from 'react';\n\nfunction Button() {\n  function handleClick() {\n    console.log('Button was clicked!');\n  }\n\n  return (\n    <button onClick={handleClick}>\n      Click me\n    </button>\n  );\n}\n\n// React事件与原生DOM事件的区别\n/*\n1. 命名使用驼峰式（onClick vs onclick）\n2. 传递函数作为事件处理器，而不是字符串\n3. 阻止默认行为必须显式调用preventDefault\n4. 事件对象是合成事件(SyntheticEvent)，不是原生事件\n*/\n\nfunction Link() {\n  function handleClick(e) {\n    e.preventDefault(); // 阻止默认行为\n    console.log('The link was clicked.');\n  }\n\n  return (\n    <a href=\"https://reactjs.org\" onClick={handleClick}>\n      React官网\n    </a>\n  );\n}\n\n// 事件冒泡和捕获\nfunction NestedButtons() {\n  function handleOuterClick() {\n    console.log('Outer button clicked');\n  }\n\n  function handleInnerClick(e) {\n    e.stopPropagation(); // 阻止事件冒泡\n    console.log('Inner button clicked');\n  }\n\n  return (\n    <button onClick={handleOuterClick}>\n      Outer\n      <button onClick={handleInnerClick}>Inner</button>\n    </button>\n  );\n}", "explanation": "这个例子展示了React事件系统的基本用法。React的合成事件系统提供了跨浏览器一致性，并实现了性能优化。事件处理器接收一个合成事件对象作为参数，可以用来阻止默认行为和事件传播。React事件使用驼峰命名法，并且需要传递函数引用而非字符串。"}]}}, {"name": "Event Handler Binding", "trans": ["事件处理函数绑定"], "usage": {"syntax": "// 类组件中的绑定方法\nthis.handleClick = this.handleClick.bind(this);\n\n// 或使用箭头函数\nhandleClick = () => { /* ... */ };\n\n// 函数组件不需要绑定", "description": "在React中，事件处理函数需要正确绑定this上下文（在类组件中）。有多种方法可以确保事件处理函数中的this指向正确的组件实例。", "parameters": [{"name": "event", "description": "React合成事件对象，包含事件相关的属性和方法"}, {"name": "this", "description": "在类组件中，指向组件实例，需要正确绑定才能在事件处理函数中访问组件的props、state和方法"}, {"name": "bind方法", "description": "JavaScript的Function.prototype.bind()方法，用于创建一个新函数，将this值绑定到指定对象"}, {"name": "箭头函数", "description": "ES6引入的语法，自动绑定this到定义时的上下文，而不是执行时的上下文"}], "returnValue": "bind方法返回一个新的绑定函数；箭头函数定义返回一个自动绑定this的函数。这些函数本身在事件触发时通常不需要返回值。", "examples": [{"code": "// 1. 在构造函数中绑定\nimport React from 'react';\n\nclass ToggleButton extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { isOn: false };\n    // 在构造函数中绑定\n    this.handleClick = this.handleClick.bind(this);\n  }\n\n  handleClick() {\n    this.setState(prevState => ({\n      isOn: !prevState.isOn\n    }));\n  }\n\n  render() {\n    return (\n      <button onClick={this.handleClick}>\n        {this.state.isOn ? 'ON' : 'OFF'}\n      </button>\n    );\n  }\n}\n\n// 2. 使用类属性和箭头函数（需要Babel支持）\nclass CounterButton extends React.Component {\n  state = { count: 0 };\n\n  // 使用箭头函数自动绑定this\n  handleClick = () => {\n    this.setState(prevState => ({\n      count: prevState.count + 1\n    }));\n  };\n\n  render() {\n    return (\n      <button onClick={this.handleClick}>\n        Clicked {this.state.count} times\n      </button>\n    );\n  }\n}\n\n// 3. 在render方法中使用箭头函数（可能影响性能）\nclass ColorButton extends React.Component {\n  state = { color: 'red' };\n\n  changeColor(color) {\n    this.setState({ color });\n  }\n\n  render() {\n    return (\n      <div>\n        <button onClick={() => this.changeColor('red')}>Red</button>\n        <button onClick={() => this.changeColor('green')}>Green</button>\n        <button onClick={() => this.changeColor('blue')}>Blue</button>\n        <div style={{ backgroundColor: this.state.color, height: '50px', width: '50px' }} />\n      </div>\n    );\n  }\n}\n\n// 4. 函数组件中的事件处理（使用useState钩子）\nimport React, { useState } from 'react';\n\nfunction ToggleSwitch() {\n  const [isOn, setIsOn] = useState(false);\n\n  // 函数组件不需要担心this绑定问题\n  function handleToggle() {\n    setIsOn(!isOn);\n  }\n\n  return (\n    <button onClick={handleToggle}>\n      {isOn ? 'ON' : 'OFF'}\n    </button>\n  );\n}", "explanation": "这个例子展示了在React中绑定事件处理函数的四种主要方法：在构造函数中使用bind方法、使用类属性和箭头函数、在render方法中使用内联箭头函数，以及在函数组件中定义处理函数。每种方法都有其优缺点：构造函数绑定是最传统的方法；类属性和箭头函数语法简洁但需要Babel支持；内联箭头函数可能导致不必要的重新渲染；函数组件则完全避免了this绑定问题。"}]}}, {"name": "Passing Arguments to Event Handlers", "trans": ["向事件处理程序传递参数"], "usage": {"syntax": "// 方法1：使用箭头函数\n<button onClick={(e) => handleClick(id, e)}>Click</button>\n\n// 方法2：使用bind\n<button onClick={handleClick.bind(this, id)}>Click</button>", "description": "在React中，有时需要向事件处理函数传递额外的参数。可以使用箭头函数或bind方法来实现这一点。", "parameters": [{"name": "event", "description": "React合成事件对象，使用箭头函数时需要显式传递，使用bind时会自动作为最后一个参数传入"}, {"name": "id", "description": "传递给事件处理函数的自定义参数，可以是任何类型的数据"}, {"name": "this", "description": "使用bind方法时，第一个参数指定函数执行时的this值"}, {"name": "其他参数", "description": "可以传递多个自定义参数给事件处理函数"}], "returnValue": "箭头函数或bind方法会返回一个新的函数，该函数在事件触发时被调用。事件处理函数本身可以返回任何值，但在React事件系统中这些返回值通常不会被使用。", "examples": [{"code": "// 使用箭头函数传递参数\nimport React from 'react';\n\nclass ItemList extends React.Component {\n  deleteItem(itemId, event) {\n    console.log('Deleting item:', itemId);\n    console.log('Event:', event); // 事件对象作为第二个参数\n  }\n\n  render() {\n    const items = [\n      { id: 1, name: 'Item 1' },\n      { id: 2, name: 'Item 2' },\n      { id: 3, name: 'Item 3' }\n    ];\n\n    return (\n      <ul>\n        {items.map(item => (\n          <li key={item.id}>\n            {item.name}\n            <button onClick={(e) => this.deleteItem(item.id, e)}>\n              Delete\n            </button>\n          </li>\n        ))}\n      </ul>\n    );\n  }\n}\n\n// 使用bind传递参数\nclass ProductList extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      products: [\n        { id: 101, name: 'Product A', price: 9.99 },\n        { id: 102, name: 'Product B', price: 19.99 },\n        { id: 103, name: 'Product C', price: 29.99 }\n      ],\n      cart: []\n    };\n  }\n\n  addToCart(product, event) {\n    console.log('Adding to cart:', product.name);\n    this.setState(prevState => ({\n      cart: [...prevState.cart, product]\n    }));\n  }\n\n  render() {\n    return (\n      <div>\n        <h2>Products</h2>\n        <ul>\n          {this.state.products.map(product => (\n            <li key={product.id}>\n              {product.name} - ${product.price}\n              <button onClick={this.addToCart.bind(this, product)}>\n                Add to Cart\n              </button>\n            </li>\n          ))}\n        </ul>\n        <h2>Cart ({this.state.cart.length} items)</h2>\n      </div>\n    );\n  }\n}\n\n// 在函数组件中传递参数\nimport React, { useState } from 'react';\n\nfunction TabSelector() {\n  const [activeTab, setActiveTab] = useState('home');\n\n  function selectTab(tabName, event) {\n    event.preventDefault();\n    setActiveTab(tabName);\n  }\n\n  const tabs = ['home', 'profile', 'settings', 'contact'];\n\n  return (\n    <div>\n      <nav>\n        {tabs.map(tab => (\n          <a\n            key={tab}\n            href={`#${tab}`}\n            onClick={(e) => selectTab(tab, e)}\n            className={activeTab === tab ? 'active' : ''}\n          >\n            {tab.charAt(0).toUpperCase() + tab.slice(1)}\n          </a>\n        ))}\n      </nav>\n      <div className=\"content\">\n        <h2>{activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Tab</h2>\n      </div>\n    </div>\n  );\n}", "explanation": "这个例子展示了向事件处理函数传递参数的两种主要方法：使用箭头函数和使用bind方法。使用箭头函数时，可以显式地传递事件对象作为参数；使用bind方法时，事件对象会自动作为最后一个参数传入。两种方法各有优缺点：箭头函数更灵活但可能导致额外的渲染；bind方法性能稍好但语法不太直观。在函数组件中，通常使用箭头函数来传递参数。"}]}}, {"name": "Synthetic Event Object", "trans": ["合成事件对象"], "usage": {"syntax": "function handleEvent(event) {\n  // event是SyntheticEvent对象\n  event.preventDefault();\n  event.stopPropagation();\n}", "description": "React的事件处理函数接收SyntheticEvent对象，这是一个跨浏览器的包装器，包含了原生DOM事件的所有功能，但确保在不同浏览器中行为一致。", "parameters": [{"name": "event.type", "description": "事件类型，如'click'、'change'、'keydown'等"}, {"name": "event.target", "description": "触发事件的DOM元素"}, {"name": "event.currentTarget", "description": "当前处理事件的DOM元素"}, {"name": "event.preventDefault()", "description": "阻止事件的默认行为，如阻止表单提交或链接跳转"}, {"name": "event.stopPropagation()", "description": "阻止事件冒泡到父元素"}, {"name": "event.nativeEvent", "description": "原生DOM事件对象"}, {"name": "event.persist()", "description": "在React 16及之前版本中，保留事件引用以便在异步回调中访问"}, {"name": "event.isPropagationStopped()", "description": "检查是否调用了stopPropagation()"}, {"name": "event.isDefaultPrevented()", "description": "检查是否调用了preventDefault()"}], "returnValue": "SyntheticEvent对象的方法返回值：preventDefault()和stopPropagation()无返回值；isPropagationStopped()和isDefaultPrevented()返回布尔值，表示是否调用了相应方法。", "examples": [{"code": "// 使用合成事件对象\nimport React from 'react';\n\nclass FormExample extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { value: '' };\n  }\n\n  handleChange = (event) => {\n    // event是SyntheticEvent对象\n    this.setState({ value: event.target.value });\n  };\n\n  handleSubmit = (event) => {\n    event.preventDefault(); // 阻止表单默认提交行为\n    alert('提交的值: ' + this.state.value);\n  };\n\n  render() {\n    return (\n      <form onSubmit={this.handleSubmit}>\n        <label>\n          名称:\n          <input\n            type=\"text\"\n            value={this.state.value}\n            onChange={this.handleChange}\n          />\n        </label>\n        <input type=\"submit\" value=\"提交\" />\n      </form>\n    );\n  }\n}\n\n// 合成事件的常用属性和方法\nfunction EventDemo() {\n  function handleClick(event) {\n    // 常用属性\n    console.log('Event type:', event.type); // 'click'\n    console.log('Target:', event.target); // 被点击的DOM元素\n    console.log('Current target:', event.currentTarget); // 绑定事件的DOM元素\n    console.log('Mouse position:', event.clientX, event.clientY); // 鼠标坐标\n\n    // 常用方法\n    event.preventDefault(); // 阻止默认行为\n    event.stopPropagation(); // 阻止事件冒泡\n    event.isPropagationStopped(); // 检查是否调用了stopPropagation()\n    event.isDefaultPrevented(); // 检查是否调用了preventDefault()\n\n    // 原生事件对象\n    const nativeEvent = event.nativeEvent;\n    console.log('Native event:', nativeEvent);\n  }\n\n  return <button onClick={handleClick}>Test Event</button>;\n}\n\n// 事件池和异步访问\nfunction AsyncEventAccess() {\n  function handleClick(event) {\n    console.log('Immediate access:', event.type); // 正常工作\n\n    // 在React 16及之前版本，异步访问需要调用persist()\n    // 在React 17+中，不再需要调用persist()\n    event.persist(); // 在旧版本React中保留事件引用\n\n    setTimeout(() => {\n      console.log('Async access:', event.type); // 在React 17+中正常工作\n    }, 1000);\n  }\n\n  return <button onClick={handleClick}>Test Async</button>;\n}", "explanation": "这个例子展示了React合成事件对象的使用。SyntheticEvent包装了原生DOM事件，提供了跨浏览器一致性。它包含常用属性（如type、target、currentTarget）和方法（如preventDefault、stopPropagation）。在React 16及之前版本，由于事件池机制，异步访问事件对象需要调用persist()方法；而在React 17及以后版本中，移除了事件池，因此不再需要调用persist()。合成事件系统是React性能优化的重要部分，它减少了内存使用并提高了事件处理效率。"}]}}, {"name": "Event Delegation", "trans": ["事件委托"], "usage": {"syntax": "// React自动实现事件委托，无需特殊语法", "description": "React使用事件委托（Event Delegation）模式来优化性能。所有事件处理器实际上都被附加到了document或root元素上，而不是直接附加到DOM元素本身。这减少了内存使用并提高了性能。", "parameters": [{"name": "事件冒泡", "description": "事件委托利用事件冒泡机制，允许事件从触发元素向上传播到父元素"}, {"name": "单一事件监听器", "description": "React为每种事件类型在document或root元素上只添加一个事件监听器，而不是为每个元素添加"}, {"name": "事件映射", "description": "React内部维护一个映射，记录哪些组件注册了哪些事件处理函数"}, {"name": "事件分发", "description": "当事件触发时，React根据事件目标和内部映射确定应该调用哪些组件的处理函数"}, {"name": "性能优化", "description": "通过减少事件监听器数量，显著降低内存使用和提高性能，特别是在处理大量元素时"}], "returnValue": "事件委托本身是React内部实现的优化机制，不直接返回值。但它带来的好处是显著的性能提升和内存使用减少，特别是在处理大量元素（如长列表）时。", "examples": [{"code": "// React事件委托示例\nimport React from 'react';\n\nclass ItemList extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      items: Array.from({ length: 1000 }, (_, i) => ({ id: i, name: `Item ${i}` }))\n    };\n  }\n\n  handleItemClick = (itemId) => {\n    console.log('Clicked item:', itemId);\n    // 在传统DOM中，为1000个项目各自添加事件监听器会消耗大量内存\n    // React只在document级别添加一个监听器，然后根据事件目标分发事件\n  };\n\n  render() {\n    return (\n      <div>\n        <h2>Items (1000 items with event handlers)</h2>\n        <ul>\n          {this.state.items.map(item => (\n            <li key={item.id} onClick={() => this.handleItemClick(item.id)}>\n              {item.name}\n            </li>\n          ))}\n        </ul>\n      </div>\n    );\n  }\n}\n\n// 事件委托原理示例（React内部实现类似这样）\nfunction vanillaJSEventDelegation() {\n  // 这不是React代码，而是展示事件委托原理的原生JS\n  const container = document.getElementById('container');\n  \n  // 只在容器上添加一个事件监听器\n  container.addEventListener('click', function(event) {\n    // 找到被点击的li元素\n    let target = event.target;\n    while (target && target !== container) {\n      if (target.tagName.toLowerCase() === 'li') {\n        // 提取数据属性\n        const id = target.getAttribute('data-id');\n        console.log('Clicked item:', id);\n        break;\n      }\n      target = target.parentNode;\n    }\n  });\n\n  // 创建1000个li元素\n  for (let i = 0; i < 1000; i++) {\n    const li = document.createElement('li');\n    li.setAttribute('data-id', i);\n    li.textContent = `Item ${i}`;\n    container.appendChild(li);\n  }\n}\n\n// React事件系统的特点\n/*\n1. 所有事件处理器实际上都被附加到了document或root元素\n2. 当事件触发时，React确定正确的组件并调用相应的处理函数\n3. 事件处理器接收的是合成事件对象，而非原生事件\n4. 这种委托机制大大减少了内存使用和事件监听器数量\n5. React会自动处理浏览器兼容性问题\n*/", "explanation": "这个例子展示了React如何使用事件委托来优化性能。在传统DOM操作中，为大量元素各自添加事件监听器会消耗大量内存；而React只在document或root元素级别添加一个监听器，然后根据事件目标分发事件。这种委托机制大大减少了内存使用和事件监听器数量，特别是在处理长列表时效果显著。事件委托是React事件系统的核心优化技术之一，它在幕后自动实现，开发者无需手动处理。"}]}}, {"name": "Common Event Types", "trans": ["常见事件类型处理"], "usage": {"syntax": "<element onEventName={handleEvent} />", "description": "React支持所有常见的DOM事件类型，包括鼠标事件、键盘事件、表单事件、焦点事件等。所有事件名使用驼峰命名法。", "parameters": [{"name": "鼠标事件", "description": "onClick, onDoubleClick, onMouseDown, onMouseUp, onMouseMove, onMouseEnter, onMouseLeave, onMouseOver, onMouseOut"}, {"name": "键盘事件", "description": "onKeyDown, onKeyUp, onKeyPress"}, {"name": "表单事件", "description": "onChange, onInput, onSubmit, onReset"}, {"name": "焦点事件", "description": "onFocus, onBlur"}, {"name": "触摸事件", "description": "onTouchStart, onTouchMove, onTouchEnd, onTouchCancel"}, {"name": "剪贴板事件", "description": "onCopy, onCut, onPaste"}, {"name": "滚动事件", "description": "onScroll"}, {"name": "媒体事件", "description": "onLoad, onError, onPlay, onPause"}, {"name": "动画事件", "description": "onAnimationStart, onAnimationEnd, onAnimationIteration"}, {"name": "过渡事件", "description": "onTransitionEnd"}], "returnValue": "不同事件处理函数可能有不同的返回值：onChange通常不需要返回值；onSubmit中返回false（在早期React版本）或调用preventDefault()可以阻止表单提交；自定义事件处理函数可以返回任何值，但这些返回值在React事件系统中通常不会被使用。", "examples": [{"code": "// 鼠标事件\nimport React, { useState } from 'react';\n\nfunction MouseEvents() {\n  const [position, setPosition] = useState({ x: 0, y: 0 });\n\n  function handleClick() {\n    console.log('Element clicked');\n  }\n\n  function handleDoubleClick() {\n    console.log('Element double clicked');\n  }\n\n  function handleMouseOver() {\n    console.log('Mouse over element');\n  }\n\n  function handleMouseMove(e) {\n    setPosition({ x: e.clientX, y: e.clientY });\n  }\n\n  return (\n    <div>\n      <button onClick={handleClick}>Click Me</button>\n      <button onDoubleClick={handleDoubleClick}>Double Click Me</button>\n      <div\n        onMouseOver={handleMouseOver}\n        onMouseMove={handleMouseMove}\n        style={{ height: '200px', border: '1px solid black' }}\n      >\n        Mouse position: {position.x}, {position.y}\n      </div>\n    </div>\n  );\n}\n\n// 键盘事件\nfunction KeyboardEvents() {\n  const [key, setKey] = useState('');\n\n  function handleKeyDown(e) {\n    setKey(e.key);\n    \n    // 检测特殊键\n    if (e.ctrlKey && e.key === 's') {\n      e.preventDefault(); // 阻止浏览器保存行为\n      console.log('Ctrl+S pressed');\n    }\n  }\n\n  function handleKeyUp() {\n    console.log('Key released');\n  }\n\n  return (\n    <div>\n      <input\n        type=\"text\"\n        onKeyDown={handleKeyDown}\n        onKeyUp={handleKeyUp}\n        placeholder=\"Type something...\"\n      />\n      <p>Last key pressed: {key}</p>\n    </div>\n  );\n}\n\n// 表单事件\nfunction FormEvents() {\n  const [formData, setFormData] = useState({\n    username: '',\n    email: '',\n    message: ''\n  });\n\n  function handleChange(e) {\n    const { name, value } = e.target;\n    setFormData(prevData => ({\n      ...prevData,\n      [name]: value\n    }));\n  }\n\n  function handleSubmit(e) {\n    e.preventDefault();\n    console.log('Form submitted:', formData);\n  }\n\n  function handleFocus() {\n    console.log('Input focused');\n  }\n\n  function handleBlur() {\n    console.log('Input blurred');\n  }\n\n  return (\n    <form onSubmit={handleSubmit}>\n      <div>\n        <label>Username:</label>\n        <input\n          type=\"text\"\n          name=\"username\"\n          value={formData.username}\n          onChange={handleChange}\n          onFocus={handleFocus}\n          onBlur={handleBlur}\n        />\n      </div>\n      <div>\n        <label>Email:</label>\n        <input\n          type=\"email\"\n          name=\"email\"\n          value={formData.email}\n          onChange={handleChange}\n        />\n      </div>\n      <div>\n        <label>Message:</label>\n        <textarea\n          name=\"message\"\n          value={formData.message}\n          onChange={handleChange}\n        />\n      </div>\n      <button type=\"submit\">Submit</button>\n    </form>\n  );\n}\n\n// 其他常见事件\nfunction OtherEvents() {\n  // 剪贴板事件\n  function handleCopy() {\n    console.log('Content copied');\n  }\n\n  function handlePaste(e) {\n    console.log('Pasted content:', e.clipboardData.getData('text'));\n  }\n\n  // 滚动事件\n  function handleScroll() {\n    console.log('Element scrolled');\n  }\n\n  // 加载事件\n  function handleLoad() {\n    console.log('Image loaded successfully');\n  }\n\n  function handleError() {\n    console.log('Image failed to load');\n  }\n\n  return (\n    <div>\n      <p onCopy={handleCopy} onPaste={handlePaste}>\n        Try to copy or paste this text\n      </p>\n      \n      <div \n        style={{ height: '100px', overflow: 'auto' }}\n        onScroll={handleScroll}\n      >\n        <div style={{ height: '500px', background: 'linear-gradient(white, gray)' }}>\n          Scroll me\n        </div>\n      </div>\n      \n      <img\n        src=\"https://example.com/image.jpg\"\n        onLoad={handleLoad}\n        onError={handleError}\n        alt=\"Example\"\n      />\n    </div>\n  );\n}", "explanation": "这个例子展示了React中常见事件类型的处理方法，包括：\n\n1. 鼠标事件：onClick, onDoubleClick, onMouseOver, onMouseMove, onMouseEnter, onMouseLeave等\n2. 键盘事件：onKeyDown, onKeyUp, onKeyPress等\n3. 表单事件：onChange, onSubmit, onFocus, onBlur等\n4. 剪贴板事件：onCopy, onCut, onPaste等\n5. 滚动事件：onScroll\n6. 媒体事件：onLoad, onError等\n\nReact使用驼峰命名法命名所有事件处理器，并提供了跨浏览器一致的事件对象。通过这些事件，可以创建丰富的交互体验，响应用户的各种操作。"}]}}]}