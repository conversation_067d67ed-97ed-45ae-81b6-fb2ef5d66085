{"name": "useCallback", "trans": ["useCallback钩子"], "methods": [{"name": "Function Memoization", "trans": ["函数记忆化"], "usage": {"syntax": "const memoizedCallback = useCallback(callback, dependencies)", "description": "useCallback 是 React 的一个 Hook，用于返回一个记忆化（memoized）的回调函数。它接收一个内联回调函数和一个依赖项数组作为参数，并返回该回调函数的记忆化版本，该版本仅在依赖项发生变化时才会更新。这有助于防止因不必要的函数重新创建而导致的子组件重新渲染。", "parameters": [{"name": "callback", "description": "需要被记忆化的函数"}, {"name": "dependencies", "description": "依赖项数组，只有当这些依赖项发生变化时，才会重新创建回调函数"}], "returnValue": "返回记忆化的回调函数，仅在依赖项变化时更新", "examples": [{"code": "import React, { useState, useCallback } from 'react';\n\nfunction ParentComponent() {\n  const [count, setCount] = useState(0);\n  const [text, setText] = useState('');\n  \n  // 不使用 useCallback - 每次渲染都会创建新的函数引用\n  const handleClickWithoutCallback = () => {\n    console.log('Button clicked, count:', count);\n  };\n  \n  // 使用 useCallback - 只有当 count 变化时才会创建新的函数引用\n  const handleClickWithCallback = useCallback(() => {\n    console.log('Button clicked, count:', count);\n  }, [count]);\n  \n  return (\n    <div>\n      <p>Count: {count}</p>\n      <button onClick={() => setCount(count + 1)}>增加计数</button>\n      <input \n        value={text} \n        onChange={(e) => setText(e.target.value)} \n        placeholder=\"输入文本\"\n      />\n      <ChildComponent onClick={handleClickWithCallback} />\n    </div>\n  );\n}\n\n// 使用 React.memo 包装的子组件，避免不必要的重新渲染\nconst ChildComponent = React.memo(({ onClick }) => {\n  console.log('ChildComponent 渲染');\n  return <button onClick={onClick}>子组件按钮</button>;\n});", "explanation": "这个例子展示了 useCallback 的基本用法。当父组件中的 text 状态变化时，handleClickWithCallback 不会重新创建，因为它只依赖于 count。结合 React.memo，这可以防止子组件不必要的重新渲染。"}]}}, {"name": "Dependency Array", "trans": ["依赖数组"], "usage": {"syntax": "useCallback(fn, [dep1, dep2, ...])", "description": "useCallback 的第二个参数是依赖数组，决定了回调函数何时需要重新创建。依赖数组中的值如果发生变化，React 将重新创建回调函数；如果依赖数组中的值保持不变，React 将返回先前记忆化的回调函数。依赖数组类似于 useEffect 的工作方式。", "parameters": [{"name": "dependencies", "description": "一个数组，包含回调函数中使用的外部变量"}], "returnValue": "无返回值", "examples": [{"code": "import React, { useState, useCallback } from 'react';\n\nfunction SearchComponent() {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filter, setFilter] = useState('all');\n  \n  // 空依赖数组 - 回调函数永远不会重新创建\n  const handleReset = useCallback(() => {\n    setSearchTerm('');\n    setFilter('all');\n  }, []);\n  \n  // 单一依赖 - 只有 searchTerm 变化时才重新创建\n  const handleSearch = useCallback(() => {\n    console.log(`搜索: ${searchTerm}, 过滤器: ${filter}`);\n    // 执行搜索逻辑...\n  }, [searchTerm]);\n  \n  // 多个依赖 - 当 searchTerm 或 filter 任一变化时重新创建\n  const handleFilteredSearch = useCallback(() => {\n    console.log(`使用 ${filter} 过滤器搜索 ${searchTerm}`);\n    // 执行过滤搜索逻辑...\n  }, [searchTerm, filter]);\n  \n  return (\n    <div>\n      <input\n        value={searchTerm}\n        onChange={(e) => setSearchTerm(e.target.value)}\n        placeholder=\"搜索...\"\n      />\n      <select value={filter} onChange={(e) => setFilter(e.target.value)}>\n        <option value=\"all\">全部</option>\n        <option value=\"active\">活跃</option>\n        <option value=\"completed\">已完成</option>\n      </select>\n      <button onClick={handleSearch}>搜索</button>\n      <button onClick={handleFilteredSearch}>过滤搜索</button>\n      <button onClick={handleReset}>重置</button>\n    </div>\n  );\n}", "explanation": "这个例子展示了不同依赖数组配置的 useCallback 用法。handleReset 有空依赖数组，所以永远不会重新创建；handleSearch 只依赖于 searchTerm；handleFilteredSearch 依赖于 searchTerm 和 filter。每个回调函数只会在其依赖项变化时重新创建。"}]}}, {"name": "Performance Optimization", "trans": ["性能优化场景"], "usage": {"syntax": "useCallback(callback, dependencies)", "description": "useCallback 主要用于性能优化，特别是在以下场景：1) 向子组件传递回调函数时，尤其是使用 React.memo 的子组件；2) 作为其他 Hook（如 useEffect）的依赖项时；3) 创建代价昂贵的回调函数时。正确使用 useCallback 可以减少不必要的渲染和计算。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import React, { useState, useCallback } from 'react';\n\nfunction TodoList() {\n  const [todos, setTodos] = useState([]);\n  const [newTodo, setNewTodo] = useState('');\n  \n  // 使用 useCallback 记忆化添加任务的函数\n  const handleAddTodo = useCallback(() => {\n    if (newTodo.trim()) {\n      setTodos([...todos, { id: Date.now(), text: newTodo, completed: false }]);\n      setNewTodo('');\n    }\n  }, [newTodo, todos]);\n  \n  // 使用 useCallback 记忆化切换任务状态的函数\n  const handleToggle = useCallback((id) => {\n    setTodos(todos.map(todo => \n      todo.id === id ? { ...todo, completed: !todo.completed } : todo\n    ));\n  }, [todos]);\n  \n  // 使用 useCallback 记忆化删除任务的函数\n  const handleDelete = useCallback((id) => {\n    setTodos(todos.filter(todo => todo.id !== id));\n  }, [todos]);\n  \n  return (\n    <div>\n      <h2>待办事项列表</h2>\n      <div>\n        <input\n          value={newTodo}\n          onChange={(e) => setNewTodo(e.target.value)}\n          placeholder=\"添加新任务...\"\n        />\n        <button onClick={handleAddTodo}>添加</button>\n      </div>\n      <ul>\n        {todos.map(todo => (\n          <TodoItem\n            key={todo.id}\n            todo={todo}\n            onToggle={handleToggle}\n            onDelete={handleDelete}\n          />\n        ))}\n      </ul>\n    </div>\n  );\n}\n\n// 使用 React.memo 优化 TodoItem 组件\nconst TodoItem = React.memo(({ todo, onToggle, onDelete }) => {\n  console.log(`渲染任务: ${todo.text}`);\n  return (\n    <li style={{ textDecoration: todo.completed ? 'line-through' : 'none' }}>\n      <input\n        type=\"checkbox\"\n        checked={todo.completed}\n        onChange={() => onToggle(todo.id)}\n      />\n      {todo.text}\n      <button onClick={() => onDelete(todo.id)}>删除</button>\n    </li>\n  );\n});", "explanation": "这个例子展示了在待办事项应用中使用 useCallback 进行性能优化。通过记忆化添加、切换和删除任务的回调函数，结合使用 React.memo 包装的 TodoItem 组件，可以避免列表中的所有项在每次状态更新时都重新渲染。"}]}}, {"name": "useCallback with useEffect", "trans": ["与useEffect配合"], "usage": {"syntax": "useCallback(fn, deps) as a dependency of useEffect", "description": "useCallback 经常与 useEffect 一起使用，特别是当 Effect 依赖于某个回调函数时。通过记忆化回调函数，可以防止 Effect 因函数引用变化而不必要地重新执行。这种组合在处理事件监听器、订阅和API调用等场景特别有用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import React, { useState, useCallback, useEffect } from 'react';\n\nfunction WindowResizeTracker() {\n  const [windowSize, setWindowSize] = useState({\n    width: window.innerWidth,\n    height: window.innerHeight\n  });\n  \n  // 使用 useCallback 记忆化处理窗口大小变化的函数\n  const handleResize = useCallback(() => {\n    setWindowSize({\n      width: window.innerWidth,\n      height: window.innerHeight\n    });\n  }, []);\n  \n  useEffect(() => {\n    // 添加事件监听器\n    window.addEventListener('resize', handleResize);\n    \n    // 清理函数移除事件监听器\n    return () => {\n      window.removeEventListener('resize', handleResize);\n    };\n  }, [handleResize]); // handleResize 作为依赖项\n  \n  return (\n    <div>\n      <h2>窗口大小</h2>\n      <p>宽度: {windowSize.width}px</p>\n      <p>高度: {windowSize.height}px</p>\n    </div>\n  );\n}\n\nfunction DataFetcher({ query }) {\n  const [data, setData] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  \n  // 使用 useCallback 记忆化获取数据的函数\n  const fetchData = useCallback(async () => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      // 模拟 API 请求\n      const response = await fetch(`https://api.example.com/data?query=${query}`);\n      const result = await response.json();\n      setData(result);\n    } catch (err) {\n      setError('获取数据失败');\n    } finally {\n      setIsLoading(false);\n    }\n  }, [query]); // 只有 query 变化时才重新创建函数\n  \n  useEffect(() => {\n    // 当 query 或 fetchData 变化时执行数据获取\n    fetchData();\n  }, [fetchData]); // fetchData 作为依赖项\n  \n  return (\n    <div>\n      <h2>数据获取</h2>\n      {isLoading && <p>加载中...</p>}\n      {error && <p>错误: {error}</p>}\n      {data && <pre>{JSON.stringify(data, null, 2)}</pre>}\n    </div>\n  );\n}", "explanation": "这个例子展示了两种将 useCallback 与 useEffect 结合使用的常见场景。在 WindowResizeTracker 组件中，handleResize 回调被记忆化并用作 useEffect 的依赖项，确保事件监听器只添加和移除一次。在 DataFetcher 组件中，fetchData 函数只在 query 变化时重新创建，防止 useEffect 不必要地重新执行数据获取。"}]}}, {"name": "Common Mistakes", "trans": ["常见错误"], "usage": {"syntax": "useCallback(fn, dependencies)", "description": "使用 useCallback 时的常见错误包括：1) 忽略或错误设置依赖数组；2) 过度使用 useCallback，导致代码复杂性增加而性能提升有限；3) 没有结合 React.memo 或其他优化技术使用；4) 在回调函数内部访问的变量没有包含在依赖数组中。理解这些常见错误有助于正确使用 useCallback。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import React, { useState, useCallback } from 'react';\n\nfunction CommonMistakesExample() {\n  const [count, setCount] = useState(0);\n  const [name, setName] = useState('');\n  \n  // 错误 1: 空依赖数组，但回调内部使用了外部变量 count\n  // 这将导致回调总是使用初始的 count 值 (闭包陷阱)\n  const handleClickBad1 = useCallback(() => {\n    console.log(`当前计数: ${count}`);\n    setCount(count + 1); // 总是使用旧的 count 值!\n  }, []); // 应该包含 [count]\n  \n  // 错误 2: 依赖数组中缺少依赖项 name\n  const handleClickBad2 = useCallback(() => {\n    console.log(`名称: ${name}, 计数: ${count}`);\n    setCount(count + 1);\n  }, [count]); // 应该包含 [count, name]\n  \n  // 错误 3: 过度使用 useCallback，简单函数不需要记忆化\n  const handleSimpleChange = useCallback((e) => {\n    setName(e.target.value);\n  }, []); // 不必要的 useCallback 使用\n  \n  // 正确: 包含所有依赖项\n  const handleClickGood = useCallback(() => {\n    console.log(`名称: ${name}, 计数: ${count}`);\n    setCount(count + 1);\n  }, [count, name]);\n  \n  // 正确: 使用函数式更新，减少依赖\n  const handleClickBetter = useCallback(() => {\n    setCount(prevCount => prevCount + 1); // 不需要依赖 count\n    console.log(`名称: ${name}`);\n  }, [name]); // 只需要依赖 name\n  \n  return (\n    <div>\n      <div>\n        <input\n          value={name}\n          onChange={(e) => setName(e.target.value)}\n          placeholder=\"输入名称\"\n        />\n      </div>\n      <p>计数: {count}</p>\n      <p>名称: {name}</p>\n      <button onClick={handleClickBad1}>错误按钮 1</button>\n      <button onClick={handleClickBad2}>错误按钮 2</button>\n      <button onClick={handleClickGood}>正确按钮</button>\n      <button onClick={handleClickBetter}>更好的按钮</button>\n    </div>\n  );\n}", "explanation": "这个例子展示了使用 useCallback 时的常见错误以及正确的解决方法。主要错误包括缺少依赖项（导致闭包陷阱）和过度使用 useCallback。示例还展示了如何使用函数式更新来减少依赖项。"}]}}, {"name": "Best Practices", "trans": ["最佳实践"], "usage": {"syntax": "useCallback(fn, dependencies)", "description": "使用 useCallback 的最佳实践包括：1) 只在必要时使用 useCallback，如向使用 React.memo 的子组件传递回调；2) 确保依赖数组包含回调中使用的所有外部变量；3) 考虑使用函数式更新（如 setPrevState => newState）减少依赖项；4) 结合 React.memo 使用以获得最大性能收益；5) 使用 ESLint 的 exhaustive-deps 规则检查依赖项。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import React, { useState, useCallback } from 'react';\n\nfunction BestPracticesExample() {\n  const [items, setItems] = useState([]);\n  const [text, setText] = useState('');\n  \n  // 最佳实践 1: 使用函数式更新减少依赖\n  const handleAddItem = useCallback(() => {\n    if (text.trim()) {\n      setItems(prevItems => [...prevItems, { id: Date.now(), text }]);\n      setText('');\n    }\n  }, [text]); // 只依赖 text，不需要依赖 items\n  \n  // 最佳实践 2: 使用内联函数处理简单更新，不需要 useCallback\n  const handleTextChange = (e) => {\n    setText(e.target.value);\n  };\n  \n  // 最佳实践 3: 对传递给记忆化组件的回调使用 useCallback\n  const handleRemoveItem = useCallback((id) => {\n    setItems(prevItems => prevItems.filter(item => item.id !== id));\n  }, []); // 使用函数式更新，不需要依赖 items\n  \n  return (\n    <div>\n      <h2>待办事项</h2>\n      <div>\n        <input\n          value={text}\n          onChange={handleTextChange}\n          placeholder=\"添加新项目...\"\n        />\n        <button onClick={handleAddItem}>添加</button>\n      </div>\n      <ItemList items={items} onRemoveItem={handleRemoveItem} />\n    </div>\n  );\n}\n\n// 最佳实践 4: 使用 React.memo 优化子组件\nconst ItemList = React.memo(({ items, onRemoveItem }) => {\n  console.log('ItemList 渲染');\n  return (\n    <ul>\n      {items.map(item => (\n        <li key={item.id}>\n          {item.text}\n          <button onClick={() => onRemoveItem(item.id)}>删除</button>\n        </li>\n      ))}\n    </ul>\n  );\n});\n\n// 最佳实践 5: 将回调逻辑提取到组件外部\n// 这样函数定义不会在每次渲染时重新创建\nfunction createSortFunction(sortOrder) {\n  return (a, b) => {\n    if (sortOrder === 'asc') {\n      return a.localeCompare(b);\n    } else {\n      return b.localeCompare(a);\n    }\n  };\n}\n\nfunction SortableList() {\n  const [sortOrder, setSortOrder] = useState('asc');\n  const [items, setItems] = useState(['香蕉', '苹果', '橙子', '葡萄']);\n  \n  // 使用 useCallback 记忆化排序函数\n  const sortItems = useCallback(() => {\n    const sortFn = createSortFunction(sortOrder);\n    setItems(prevItems => [...prevItems].sort(sortFn));\n  }, [sortOrder]);\n  \n  return (\n    <div>\n      <h2>可排序列表</h2>\n      <button onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}>\n        切换排序顺序: {sortOrder === 'asc' ? '升序' : '降序'}\n      </button>\n      <button onClick={sortItems}>排序</button>\n      <ul>\n        {items.map((item, index) => (\n          <li key={index}>{item}</li>\n        ))}\n      </ul>\n    </div>\n  );\n}", "explanation": "这个例子展示了使用 useCallback 的多种最佳实践，包括使用函数式更新减少依赖、只在必要时使用 useCallback、结合 React.memo 使用、将复杂逻辑提取到组件外部等。这些实践可以帮助你最大化 useCallback 的性能收益，同时保持代码的可读性和可维护性。"}]}}, {"name": "Assignment: Create a Debounced Search", "trans": ["作业：创建防抖搜索"], "usage": {"syntax": "// 使用 useCallback 创建防抖搜索功能", "description": "创建一个带有防抖功能的搜索组件，使用 useCallback 优化性能。搜索组件应该包含一个输入框，在用户停止输入一段时间后才执行实际的搜索操作，避免频繁的API调用。使用 useCallback 确保防抖函数的稳定性，并结合 useEffect 实现搜索功能。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 任务要求：\n// 1. 创建一个搜索组件，包含输入框和搜索结果显示\n// 2. 实现防抖功能，当用户停止输入500毫秒后才执行搜索\n// 3. 使用useCallback优化搜索函数，避免不必要的重新创建\n// 4. 使用useEffect执行搜索操作\n// 5. 模拟API调用，显示搜索结果\n\n// 参考实现框架：\n\nimport React, { useState, useEffect, useCallback } from 'react';\n\nfunction DebouncedSearch() {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [debouncedTerm, setDebouncedTerm] = useState('');\n  const [results, setResults] = useState([]);\n  const [isSearching, setIsSearching] = useState(false);\n  \n  // 1. 实现搜索词的防抖处理\n  useEffect(() => {\n    const timerId = setTimeout(() => {\n      setDebouncedTerm(searchTerm);\n    }, 500);\n    \n    return () => {\n      clearTimeout(timerId);\n    };\n  }, [searchTerm]);\n  \n  // 2. 使用useCallback创建搜索函数\n  const searchAPI = useCallback(async (term) => {\n    // 实现搜索API调用逻辑\n    setIsSearching(true);\n    try {\n      // 模拟API调用\n      // ...\n    } catch (error) {\n      // 处理错误\n    } finally {\n      setIsSearching(false);\n    }\n  }, [/* 添加必要的依赖项 */]);\n  \n  // 3. 当防抖后的搜索词变化时执行搜索\n  useEffect(() => {\n    if (debouncedTerm) {\n      searchAPI(debouncedTerm);\n    } else {\n      setResults([]);\n    }\n  }, [debouncedTerm, searchAPI]);\n  \n  // 4. 实现输入处理函数\n  const handleInputChange = (e) => {\n    setSearchTerm(e.target.value);\n  };\n  \n  return (\n    <div className=\"debounced-search\">\n      <h2>防抖搜索</h2>\n      <input\n        type=\"text\"\n        value={searchTerm}\n        onChange={handleInputChange}\n        placeholder=\"搜索...\"\n      />\n      \n      {isSearching && <p>搜索中...</p>}\n      \n      {!isSearching && results.length > 0 && (\n        <ul className=\"results-list\">\n          {/* 渲染搜索结果 */}\n        </ul>\n      )}\n      \n      {!isSearching && debouncedTerm && results.length === 0 && (\n        <p>未找到结果</p>\n      )}\n    </div>\n  );\n}\n\n// 提示：完成上述代码框架，实现防抖搜索功能\n// 1. 完善searchAPI函数，使用模拟数据或实际API\n// 2. 完善搜索结果渲染逻辑\n// 3. 添加适当的样式使界面更友好\n// 4. 考虑添加加载状态和错误处理", "explanation": "这个作业要求学生创建一个带有防抖功能的搜索组件，使用 useCallback 优化性能。学生需要完成搜索函数实现、处理防抖逻辑、管理搜索状态和结果显示。这个练习结合了 useCallback、useEffect 和状态管理的知识，是实际开发中常见的性能优化场景。"}]}}]}