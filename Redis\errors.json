{"name": "Common Errors", "trans": ["常见错误"], "methods": [{"name": "Connection Timeout", "trans": ["连接超时"], "usage": {"syntax": "连接Redis服务超时，常因网络不通、防火墙或配置错误导致。", "description": "连接超时通常是由于客户端与Redis服务之间网络不通、防火墙阻断、端口未开放或bind配置错误引起。", "parameters": [{"name": "timeout", "description": "连接超时时间设置。"}, {"name": "网络配置", "description": "检查网络连通性和端口开放。"}], "returnValue": "无返回值", "examples": [{"code": "# redis.conf配置超时时间\ntimeout 300\n# 检查端口和防火墙\nnetstat -an | grep 6379\n", "explanation": "通过配置timeout和检查网络解决连接超时问题。"}]}}, {"name": "Out of Memory", "trans": ["内存溢出"], "usage": {"syntax": "Redis内存超出maxmemory限制，触发淘汰或报错。", "description": "当Redis使用内存超过maxmemory限制时，会根据淘汰策略删除数据或报OOM错误。需合理配置内存和淘汰策略。", "parameters": [{"name": "maxmemory", "description": "最大内存限制。"}, {"name": "maxmemory-policy", "description": "内存淘汰策略。"}], "returnValue": "无返回值", "examples": [{"code": "# redis.conf配置内存限制\nmaxmemory 512mb\nmaxmemory-policy allkeys-lru\n", "explanation": "通过配置maxmemory和淘汰策略防止内存溢出。"}]}}, {"name": "Persistence Failure", "trans": ["持久化失败"], "usage": {"syntax": "RDB/AOF持久化过程出错，常因磁盘空间不足或权限问题。", "description": "持久化失败多因磁盘空间不足、文件权限错误、磁盘IO异常等导致。需定期监控磁盘和检查日志。", "parameters": [{"name": "磁盘空间", "description": "检查磁盘剩余空间。"}, {"name": "文件权限", "description": "确保Redis有写入权限。"}], "returnValue": "无返回值", "examples": [{"code": "# 检查磁盘空间\ndf -h\n# 检查文件权限\nls -l dump.rdb appendonly.aof\n", "explanation": "通过检查磁盘和权限解决持久化失败问题。"}]}}, {"name": "Replication Sync Error", "trans": ["主从同步异常"], "usage": {"syntax": "主从同步过程中断或数据不一致，常因网络抖动或配置不当。", "description": "主从同步异常可能由网络中断、主从版本不一致、复制积压缓冲区不足等引起。需监控复制状态和日志。", "parameters": [{"name": "replication backlog", "description": "复制积压缓冲区大小。"}, {"name": "INFO replication", "description": "监控主从同步状态。"}], "returnValue": "无返回值", "examples": [{"code": "# redis.conf配置复制缓冲区\nreplication-backlog-size 128mb\n# 监控主从同步\nINFO replication\n", "explanation": "通过配置缓冲区和监控命令排查主从同步异常。"}]}}, {"name": "常见错误作业", "trans": ["作业：常见错误"], "usage": {"syntax": "请完成以下任务：\n1. 模拟连接超时并排查原因。\n2. 配置maxmemory并观察内存溢出。\n3. 检查持久化失败的日志。\n4. 监控主从同步异常。", "description": "通过实际操作理解常见错误的成因和排查方法。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 作业示例\n# 1. 关闭端口模拟超时\n# 2. 设置maxmemory为10mb并写入大数据\n# 3. 删除RDB文件权限\n# 4. 断开主从网络连接\n", "explanation": "通过动手实验掌握常见错误的排查和处理。"}]}}, {"name": "常见错误正确实现示例", "trans": ["正确实现卡片"], "usage": {"syntax": "常见错误的标准排查与处理流程。", "description": "展示如何标准排查和处理连接超时、内存溢出、持久化失败、主从同步异常等问题。", "parameters": [{"name": "timeout", "description": "连接超时排查。"}, {"name": "maxmemory", "description": "内存溢出排查。"}, {"name": "磁盘空间", "description": "持久化失败排查。"}, {"name": "replication backlog", "description": "主从同步异常排查。"}], "returnValue": "无返回值", "examples": [{"code": "# 标准排查流程\n1. 检查网络和端口\n2. 检查maxmemory和淘汰策略\n3. 检查磁盘空间和权限\n4. 检查主从同步状态和缓冲区\n", "explanation": "标准排查流程，快速定位和解决常见错误。"}]}}]}