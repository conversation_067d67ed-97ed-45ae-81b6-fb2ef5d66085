{"name": "Language and Framework Introduction", "trans": ["语言与框架简介"], "methods": [{"name": "Flutter Overview", "trans": ["Flutter框架概述"], "usage": {"syntax": "Flutter是一个用于构建跨平台高性能应用的UI框架。", "description": "Flutter由Google开发，支持同时为iOS、Android、Web和桌面端开发原生体验的应用。其核心是基于Dart语言，拥有自绘UI引擎和丰富的组件库。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// Flutter应用的入口 main.dart\nimport 'package:flutter/material.dart';\n\nvoid main() {\n  // 运行Flutter应用\n  runApp(MyApp());\n}\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    // MaterialApp是Flutter的顶层组件，提供应用的基本结构\n    return MaterialApp(\n      title: 'Flutter Demo',\n      home: Scaffold(\n        appBar: AppBar(title: Text('Flutter框架概述')),\n        body: Center(child: Text('Hello, Flutter!')),\n      ),\n    );\n  }\n}", "explanation": "本示例展示了一个最简单的Flutter应用结构，包含入口函数、顶层MaterialApp和一个页面。"}]}}, {"name": "Dart Language Introduction", "trans": ["Dart语言简介"], "usage": {"syntax": "void main() { /* Dart代码 */ }", "description": "Dart是Flutter的开发语言，语法简洁，支持面向对象、异步编程等特性，适合构建高性能客户端应用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// Dart语言基础示例\nvoid main() {\n  // 定义变量\n  int a = 10;\n  String name = 'Flutter';\n  // 打印输出\n  print('Hello, ' + name + '!');\n  // 定义函数\n  int add(int x, int y) {\n    return x + y;\n  }\n  print('10 + 5 = \\${add(10, 5)}');\n}", "explanation": "本示例演示了Dart的变量声明、字符串、函数定义和调用等基础语法。"}]}}, {"name": "Flutter vs Native Development", "trans": ["Flutter与原生开发对比"], "usage": {"syntax": "Flutter vs Native: 一套代码多端运行 vs 各端独立开发", "description": "Flutter通过一套Dart代码实现多平台应用，原生开发则需分别用Java/Kotlin（Android）、Swift/ObjC（iOS）等。Flutter开发效率高，UI一致性好，原生开发则在平台特性和性能上有优势。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// Flutter实现按钮点击计数\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(CounterApp());\n\nclass CounterApp extends StatefulWidget {\n  @override\n  _CounterAppState createState() => _CounterAppState();\n}\n\nclass _CounterAppState extends State<CounterApp> {\n  int _count = 0;\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      home: Scaffold(\n        appBar: AppBar(title: Text('Flutter计数器')),\n        body: Center(child: Text('计数: \\$_count', style: TextStyle(fontSize: 24))),\n        floatingActionButton: FloatingActionButton(\n          onPressed: () {\n            setState(() {\n              _count++;\n            });\n          },\n          child: Icon(Icons.add),\n        ),\n      ),\n    );\n  }\n}", "explanation": "本示例展示了Flutter中如何用一套代码实现跨平台的计数器功能，原生开发则需分别实现。"}]}}, {"name": "Flutter Application Scenarios", "trans": ["Flutter的应用场景"], "usage": {"syntax": "Flutter适用于移动端、Web、桌面等多端应用开发。", "description": "Flutter适合开发高性能、UI一致性要求高的多端应用，如企业App、电商、工具类、教育、社交等。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// Flutter开发多端应用示例\n// 只需一套代码即可同时运行在Android、iOS、Web等平台\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      title: '多端应用',\n      home: Scaffold(\n        appBar: AppBar(title: Text('Flutter多端应用场景')),\n        body: Center(child: Text('一套代码，多端运行')),\n      ),\n    );\n  }\n}", "explanation": "本示例说明Flutter可用于开发多端应用，极大提升开发效率和UI一致性。"}]}}, {"name": "Assignment", "trans": ["作业：完成一个简单的Flutter应用，要求显示一段自定义文本，并能点击按钮改变文本内容。"], "usage": {"syntax": "无", "description": "请用Flutter实现一个页面，包含一段文本和一个按钮，点击按钮后文本内容发生变化。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 作业参考实现\nimport 'package:flutter/material.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatefulWidget {\n  @override\n  _MyAppState createState() => _MyAppState();\n}\n\nclass _MyAppState extends State<MyApp> {\n  String _text = '初始内容';\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      home: Scaffold(\n        appBar: AppBar(title: Text('作业实现')),\n        body: Center(child: Text(_text, style: TextStyle(fontSize: 24))),\n        floatingActionButton: FloatingActionButton(\n          onPressed: () {\n            setState(() {\n              _text = '内容已改变';\n            });\n          },\n          child: Icon(Icons.refresh),\n        ),\n      ),\n    );\n  }\n}", "explanation": "点击按钮后，页面上的文本内容会发生变化，符合作业要求。"}]}}]}