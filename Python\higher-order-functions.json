{"name": "Higher-Order Functions", "trans": ["高阶函数"], "methods": [{"name": "map, filter, reduce", "trans": ["map、filter、reduce"], "usage": {"syntax": "map(func, iterable)\nfilter(func, iterable)\nfrom functools import reduce\nreduce(func, iterable[, initial])", "description": "map对序列每个元素执行函数，filter筛选符合条件的元素，reduce累计计算序列。", "parameters": [{"name": "func", "description": "用于处理元素的函数"}, {"name": "iterable", "description": "可迭代对象"}, {"name": "initial", "description": "reduce的初始值，可选"}], "returnValue": "map和filter返回迭代器，reduce返回累计结果", "examples": [{"code": "nums = [1, 2, 3, 4]\nsquares = list(map(lambda x: x*x, nums))  # [1, 4, 9, 16]\nevens = list(filter(lambda x: x%2==0, nums))  # [2, 4]\nfrom functools import reduce\nsum_all = reduce(lambda x, y: x+y, nums)  # 10", "explanation": "演示了map、filter、reduce的基本用法。"}]}}, {"name": "sorted and Custom Key", "trans": ["sorted与自定义key"], "usage": {"syntax": "sorted(iterable, key=None, reverse=False)", "description": "sorted用于排序，可通过key参数自定义排序规则。reverse=True实现逆序。", "parameters": [{"name": "iterable", "description": "可排序的序列"}, {"name": "key", "description": "排序规则函数，默认为None"}, {"name": "reverse", "description": "是否逆序，默认为False"}], "returnValue": "排序后的新列表", "examples": [{"code": "words = ['apple', 'banana', 'pear']\nres = sorted(words, key=len)  # ['pear', 'apple', 'banana']\nnums = [3, 1, 2]\nres2 = sorted(nums, reverse=True)  # [3, 2, 1]", "explanation": "演示了sorted的基本用法和自定义key排序。"}]}}, {"name": "Closure", "trans": ["闭包"], "usage": {"syntax": "def outer(x):\n    def inner(y):\n        return x + y\n    return inner", "description": "闭包是指内部函数引用外部函数变量，返回该内部函数时，外部变量仍可用。", "parameters": [{"name": "x", "description": "外部函数参数"}, {"name": "y", "description": "内部函数参数"}], "returnValue": "内部函数对象（闭包）", "examples": [{"code": "def make_adder(x):\n    def adder(y):\n        return x + y\n    return adder\nadd5 = make_adder(5)\nprint(add5(3))  # 8", "explanation": "演示了闭包的定义和使用。"}]}}, {"name": "Decorator", "trans": ["装饰器"], "usage": {"syntax": "@装饰器名\ndef 被装饰函数():\n    pass", "description": "装饰器用于在不修改原函数代码的情况下，动态增加功能。常用语法为@装饰器。", "parameters": [{"name": "func", "description": "被装饰的函数"}], "returnValue": "增强后的新函数", "examples": [{"code": "def log(func):\n    def wrapper(*args, **kwargs):\n        print('调用', func.__name__)\n        return func(*args, **kwargs)\n    return wrapper\n@log\ndef hello():\n    print('Hello')\nhello()  # 调用hello\n# Hello", "explanation": "演示了装饰器的定义和使用。"}]}}, {"name": "Partial Function", "trans": ["偏函数"], "usage": {"syntax": "from functools import partial\npartial(func, *args, **kwargs)", "description": "偏函数用于固定部分参数，返回新函数。常用于简化多参数函数的调用。", "parameters": [{"name": "func", "description": "原始函数"}, {"name": "*args, **kwargs", "description": "要固定的参数"}], "returnValue": "新的函数对象", "examples": [{"code": "from functools import partial\nint2 = partial(int, base=2)\nprint(int2('1010'))  # 10", "explanation": "演示了偏函数的定义和使用。"}]}}, {"name": "Recursive Function", "trans": ["递归函数"], "usage": {"syntax": "def func():\n    if 条件:\n        return func()", "description": "递归函数是指函数内部调用自身，常用于分治、树结构等问题。需有终止条件。", "parameters": [{"name": "参数", "description": "递归函数的输入参数"}], "returnValue": "递归计算结果", "examples": [{"code": "def factorial(n):\n    if n == 1:\n        return 1\n    return n * factorial(n-1)\nprint(factorial(5))  # 120", "explanation": "演示了递归函数的定义和调用。"}]}}, {"name": "Higher-Order Functions Assignment", "trans": ["高阶函数练习"], "usage": {"syntax": "# 高阶函数练习", "description": "完成以下练习，巩固高阶函数相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用map和lambda将一个数字列表平方。\n2. 用filter筛选出列表中的偶数。\n3. 用reduce计算1~10的累加和。\n4. 编写一个装饰器，统计函数调用次数。\n5. 用递归实现斐波那契数列。", "explanation": "练习要求动手实践map、filter、reduce、装饰器、递归等高阶函数。"}]}}]}