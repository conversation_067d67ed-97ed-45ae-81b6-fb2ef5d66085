# C语言学习路线与核心内容

## 1. C语言基础

### 语言简介
- C语言发展历史
- C语言的特点
- C语言的应用领域
- C语言与其他语言对比

### 开发环境搭建
- 常用编译器（GCC、Clang、VC）
- Windows/Linux/Mac环境配置
- 编辑器与IDE选择
- 编译与运行流程
- 第一个C程序

### 基本语法
- main函数结构
- 语句与分号
- 注释写法
- 标识符与关键字
- 代码风格规范

## 2. 数据类型与运算符

### 基本数据类型
- int、float、double、char
- short、long、unsigned
- 类型转换
- sizeof运算符

### 常量与变量
- 变量声明与初始化
- 常量定义（const、#define）
- 作用域与生命周期

### 运算符
- 算术运算符
- 关系运算符
- 逻辑运算符
- 位运算符
- 赋值运算符
- 其他运算符（条件、逗号、下标等）

## 3. 流程控制

### 条件语句
- if/else语句
- switch语句
- 嵌套条件判断

### 循环语句
- for循环
- while循环
- do...while循环
- break与continue
- 循环嵌套

### 跳转语句
- goto语句
- return语句

## 4. 函数

### 函数定义与声明
- 函数的基本结构
- 参数与返回值
- void类型
- 递归函数
- 函数重载与命名空间（C语言无重载）

### 作用域与存储类型
- 局部变量与全局变量
- static、extern、register、auto

### 参数传递方式
- 值传递
- 指针传递

## 5. 数组与指针

### 数组
- 一维数组
- 二维数组
- 字符数组与字符串
- 数组初始化
- 数组作为函数参数

### 指针
- 指针基础
- 指针与数组
- 指针与函数
- 指针的指针
- const指针
- 空指针与野指针

### 字符串处理
- 字符串常用函数
- 字符串与指针
- 字符串输入输出

## 6. 结构体与共用体

### 结构体
- 结构体定义与初始化
- 结构体数组
- 结构体指针
- 结构体嵌套
- 结构体与函数

### 共用体
- 共用体定义与用途
- 共用体与结构体区别

### 枚举类型
- 枚举定义与使用
- 枚举与switch配合

## 7. 文件操作

### 文件读写
- 文件打开与关闭
- 文本文件读写
- 二进制文件读写
- 文件指针与fopen/fclose
- 文件定位与ftell/rewind

### 标准输入输出
- printf/scanf
- gets/puts
- getchar/putchar

## 8. 内存管理

### 动态内存分配
- malloc/calloc/realloc
- free释放内存
- 内存泄漏与检测
- 指针与动态数组

## 9. 预处理指令

### 常用预处理指令
- #include
- #define
- #ifdef/#ifndef
- #undef
- #error/#pragma

### 宏定义与宏函数
- 宏参数
- 多行宏
- 宏与调试

## 10. 常见错误与调试

### 编译错误与警告
- 语法错误
- 类型不匹配
- 未初始化变量
- 段错误（Segmentation Fault）

### 调试技巧
- gdb调试
- 打印调试
- 断点与单步执行
- 常用调试工具

## 11. 项目实战与练习

### 实战案例
- 简单计算器实现
- 学生成绩管理系统
- 文件批量处理工具
- 字符串查找与替换
- 数据结构（链表、栈、队列）实现

### 练习与项目
- 编写九九乘法表
- 实现冒泡排序与二分查找
- 编写自定义字符串函数
- 文件复制工具
- 动态分配二维数组
- 结构体链表操作 