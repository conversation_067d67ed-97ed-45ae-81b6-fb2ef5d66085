{"name": "Strings", "trans": ["字符串操作"], "methods": [{"name": "String Declaration & Template Literals", "trans": ["字符串声明与模板字符串"], "usage": {"syntax": "const str1 = 'hello';\nconst str2 = \"world\";\nconst name = '<PERSON>';\nconst str3 = `Hi, ${name}!`;", "description": "字符串可用单引号、双引号、反引号声明。模板字符串支持变量插值和多行文本。", "parameters": [{"name": "'...'", "description": "单引号字符串。"}, {"name": "\"...\"", "description": "双引号字符串。"}, {"name": "`...`", "description": "模板字符串。"}], "returnValue": "返回字符串。", "examples": [{"code": "const a = 'abc';\nconst b = \"def\";\nconst n = '小明';\nconst c = `Hello, ${n}!`;\nconsole.log(a, b, c);", "explanation": "三种字符串声明方式和模板字符串插值。"}]}}, {"name": "Common String Methods", "trans": ["常用方法"], "usage": {"syntax": "str.length;\nstr.charAt(i);\nstr.slice(start, end);\nstr.substring(start, end);\nstr.substr(start, len);\nstr.indexOf(substr);\nstr.replace(reg, val);\nstr.split(sep);\nstr.trim();\nstr.padStart(n, pad);\nstr.padEnd(n, pad);", "description": "字符串常用方法包括获取长度、查找、截取、替换、分割、去空格、填充等。", "parameters": [{"name": "length", "description": "字符串长度。"}, {"name": "char<PERSON>t", "description": "按索引取字符。"}, {"name": "slice/substring/substr", "description": "截取字符串。"}, {"name": "indexOf", "description": "查找子串位置。"}, {"name": "replace", "description": "替换内容。"}, {"name": "split", "description": "分割为数组。"}, {"name": "trim", "description": "去除首尾空格。"}, {"name": "padStart/padEnd", "description": "填充字符串。"}], "returnValue": "返回字符串、数字或数组。", "examples": [{"code": "const s = '  hello world  ';\nconsole.log(s.length);\nconsole.log(s.charAt(1));\nconsole.log(s.slice(2,7));\nconsole.log(s.substring(2,7));\nconsole.log(s.substr(2,5));\nconsole.log(s.indexOf('o'));\nconsole.log(s.replace('world','JS'));\nconsole.log(s.split(' '));\nconsole.log(s.trim());\nconsole.log('1'.padStart(3,'0'));\nconsole.log('1'.padEnd(3,'0'));", "explanation": "演示字符串常用方法的用法。"}]}}, {"name": "String Iteration & Processing", "trans": ["字符串遍历与处理"], "usage": {"syntax": "for (let i = 0; i < str.length; i++) { }\nfor (const ch of str) { }\nstr.split('').forEach(fn);", "description": "可用for循环、for...of、split+forEach等方式遍历字符串并处理每个字符。", "parameters": [{"name": "for", "description": "按索引遍历。"}, {"name": "for...of", "description": "按字符遍历。"}, {"name": "for<PERSON>ach", "description": "对每个字符执行回调。"}], "returnValue": "无返回值或新数组。", "examples": [{"code": "const str = 'abc';\nfor(let i=0;i<str.length;i++){console.log(str[i]);}\nfor(const ch of str){console.log(ch);}\nstr.split('').forEach(c=>console.log(c));", "explanation": "三种字符串遍历方式。"}]}}, {"name": "作业：字符串操作实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 声明和模板字符串\n// 2. 使用常用方法处理字符串\n// 3. 遍历和处理每个字符", "description": "通过实践字符串声明、方法、遍历等，掌握JS字符串操作。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 字符串声明/模板\n// 2. 常用方法/遍历", "explanation": "作业提示，需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nconst s = `hi,${'Tom'}`.padEnd(8,'!');\nfor(const c of s){console.log(c);}\nconsole.log(s.replace('<PERSON>','<PERSON>').trim());", "explanation": "涵盖声明、模板、方法、遍历的正确实现。"}]}}]}