{"name": "Other ES6+ Features", "trans": ["其他ES6+特性"], "methods": [{"name": "Symbol", "trans": ["Symbol"], "usage": {"syntax": "const s = Symbol('desc');\nconst obj = { [s]: 123 };", "description": "Symbol是唯一值，常用于对象私有属性或常量。Symbol属性不会被常规遍历发现。", "parameters": [{"name": "Symbol()", "description": "创建唯一值。"}], "returnValue": "返回Symbol类型的值。", "examples": [{"code": "const s = Symbol('id');\nconst obj = { [s]: 1 };\nconsole.log(obj[s]);", "explanation": "Symbol声明和作为对象属性。"}]}}, {"name": "Set/Map (简要)", "trans": ["Set/Map"], "usage": {"syntax": "const set = new Set([1,2]);\nconst map = new Map([[1,'a']]);", "description": "Set存储唯一值，Map存储键值对。常用于去重、映射等。详见ES6集合类型章节。", "parameters": [{"name": "Set", "description": "唯一值集合。"}, {"name": "Map", "description": "键值对集合。"}], "returnValue": "返回Set或Map对象。", "examples": [{"code": "const set = new Set([1,2,2]);\nconst map = new Map([[1,'a']]);\nconsole.log(set,map);", "explanation": "Set去重和Map映射。"}]}}, {"name": "Proxy & Reflect", "trans": ["Proxy/Reflect"], "usage": {"syntax": "const p = new Proxy(obj, { get(target, key) { return target[key]; } });\nReflect.get(obj, 'key');", "description": "Proxy用于拦截对象操作，Reflect提供对应的静态方法。常用于数据劫持、响应式等。", "parameters": [{"name": "Proxy", "description": "代理对象。"}, {"name": "Reflect", "description": "静态方法集合。"}], "returnValue": "返回代理对象或操作结果。", "examples": [{"code": "const obj = {a:1};\nconst p = new Proxy(obj, { get(t,k){ return t[k]*2; } });\nconsole.log(p.a);\nconsole.log(Reflect.get(obj,'a'));", "explanation": "Proxy拦截和Reflect操作对象。"}]}}, {"name": "Optional Chaining & Nullish Coalescing", "trans": ["可选链与空值合并"], "usage": {"syntax": "obj?.prop\nobj?.fn?.()\nval ?? defaultVal", "description": "可选链?.安全访问嵌套属性，空值合并??用于null/undefined时提供默认值。", "parameters": [{"name": "?.", "description": "可选链操作符。"}, {"name": "??", "description": "空值合并操作符。"}], "returnValue": "返回属性值或默认值。", "examples": [{"code": "const obj = {a:{b:1}};\nconsole.log(obj?.a?.b);\nconsole.log(obj?.x?.b);\nconst v = null;\nconsole.log(v ?? '默认');", "explanation": "可选链和空值合并的用法。"}]}}, {"name": "New Array/Number/String Methods", "trans": ["Array/Number/String新方法"], "usage": {"syntax": "[1,2,3].includes(2);\n[1,2,3].find(x=>x>1);\nNumber.isNaN(NaN);\n'abc'.startsWith('a');\n'abc'.repeat(2);", "description": "ES6+为数组、数字、字符串新增了许多实用方法。", "parameters": [{"name": "includes", "description": "判断是否包含。"}, {"name": "find", "description": "查找元素。"}, {"name": "isNaN", "description": "判断NaN。"}, {"name": "startsWith", "description": "字符串前缀判断。"}, {"name": "repeat", "description": "字符串重复。"}], "returnValue": "返回布尔值、元素或新字符串。", "examples": [{"code": "console.log([1,2,3].includes(2));\nconsole.log([1,2,3].find(x=>x>1));\nconsole.log(Number.isNaN(NaN));\nconsole.log('abc'.startsWith('a'));\nconsole.log('abc'.repeat(2));", "explanation": "常用新方法的用法。"}]}}, {"name": "作业：ES6+新特性实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 用Symbol/Set/Map/Proxy等新特性\n// 2. 用可选链/空值合并/新方法处理数据", "description": "通过实践Symbol、Set/Map、Proxy、可选链、空值合并等，掌握ES6+新特性。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. Symbol/Set/Map/Proxy\n// 2. 可选链/空值合并/新方法", "explanation": "作业提示，需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nconst s = Symbol();\nconst set = new Set([1,2,2]);\nconst obj = {a:1};\nconst p = new Proxy(obj, { get(t,k){ return t[k]*2; } });\nconsole.log(p.a, obj?.b ?? '无');", "explanation": "涵盖Symbol、Set、Proxy、可选链、空值合并等新特性的正确实现。"}]}}]}