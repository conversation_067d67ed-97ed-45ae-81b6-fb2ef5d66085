{"name": "RESTful API Development", "trans": ["RESTful API开发"], "methods": [{"name": "@RestController and @RequestMapping", "trans": ["@RestController与@RequestMapping"], "usage": {"syntax": "@RestController\n@RequestMapping(\"/api\")", "description": "@RestController用于声明REST风格的控制器，@RequestMapping用于映射请求路径和方法。", "parameters": [{"name": "@RestController", "description": "声明控制器为REST风格，返回JSON数据"}, {"name": "@RequestMapping", "description": "映射请求路径和HTTP方法"}], "returnValue": "处理HTTP请求并返回JSON响应的控制器类", "examples": [{"code": "@RestController\n@RequestMapping(\"/api\")\npublic class UserController {\n    @GetMapping(\"/user\")\n    public User getUser() {\n        return new User(1, \"张三\");\n    }\n}", "explanation": "@RestController结合@RequestMapping实现RESTful接口。"}]}}, {"name": "GET/POST/PUT/DELETE Requests", "trans": ["GET/POST/PUT/DELETE请求"], "usage": {"syntax": "@GetMapping/@PostMapping/@PutMapping/@DeleteMapping", "description": "Spring Boot通过不同注解处理RESTful的GET、POST、PUT、DELETE等HTTP请求。", "parameters": [{"name": "@GetMapping", "description": "处理GET请求"}, {"name": "@PostMapping", "description": "处理POST请求"}, {"name": "@PutMapping", "description": "处理PUT请求"}, {"name": "@DeleteMapping", "description": "处理DELETE请求"}], "returnValue": "对应HTTP方法的响应数据", "examples": [{"code": "@RestController\n@RequestMapping(\"/api\")\npublic class UserController {\n    @GetMapping(\"/user/{id}\")\n    public User getUser(@PathVariable Integer id) {\n        return new User(id, \"张三\");\n    }\n    @PostMapping(\"/user\")\n    public String addUser(@RequestBody User user) {\n        return \"添加成功\";\n    }\n    @PutMapping(\"/user/{id}\")\n    public String updateUser(@PathVariable Integer id, @RequestBody User user) {\n        return \"更新成功\";\n    }\n    @DeleteMapping(\"/user/{id}\")\n    public String deleteUser(@PathVariable Integer id) {\n        return \"删除成功\";\n    }\n}", "explanation": "不同注解对应不同HTTP方法，参数和返回值灵活。"}]}}, {"name": "Path Variables and Request Parameters", "trans": ["路径参数与请求参数"], "usage": {"syntax": "@PathVariable/@RequestParam", "description": "@PathVariable用于获取URL路径中的参数，@RequestParam用于获取查询参数或表单参数。", "parameters": [{"name": "@PathVariable", "description": "绑定URL路径中的变量"}, {"name": "@RequestParam", "description": "绑定请求中的查询参数或表单参数"}], "returnValue": "方法参数自动注入请求参数值", "examples": [{"code": "@GetMapping(\"/user/{id}\")\npublic User getUser(@PathVariable Integer id) {\n    return new User(id, \"张三\");\n}\n\n@GetMapping(\"/search\")\npublic List<User> search(@RequestParam String name) {\n    // 根据name查询用户\n    return userService.findByName(name);\n}", "explanation": "@PathVariable和@RequestParam分别用于路径和查询参数。"}]}}, {"name": "Request Body and Response Body", "trans": ["请求体与响应体"], "usage": {"syntax": "@RequestBody/@ResponseBody", "description": "@RequestBody用于接收JSON请求体，@ResponseBody用于返回JSON响应体（@RestController已隐式包含）。", "parameters": [{"name": "@RequestBody", "description": "将请求体JSON自动转换为Java对象"}, {"name": "@ResponseBody", "description": "将Java对象自动转换为JSON响应"}], "returnValue": "自动序列化和反序列化的Java对象", "examples": [{"code": "@PostMapping(\"/user\")\npublic String addUser(@RequestBody User user) {\n    // user对象由请求体JSON自动转换\n    return \"添加成功\";\n}", "explanation": "@RequestBody实现请求体到Java对象的自动映射。"}]}}, {"name": "JSON Serialization and Deserialization", "trans": ["JSON序列化与反序列化"], "usage": {"syntax": "Spring Boot自动集成Jackson实现JSON序列化/反序列化", "description": "Spring Boot默认使用Jackson将Java对象与JSON互转，支持自定义格式和注解。", "parameters": [{"name": "<PERSON>", "description": "默认JSON处理库"}, {"name": "@JsonProperty等注解", "description": "自定义序列化/反序列化行为"}], "returnValue": "自动转换的JSON字符串或Java对象", "examples": [{"code": "public class User {\n    private Integer id;\n    @JsonProperty(\"user_name\")\n    private String name;\n    // getter/setter\n}\n// 返回结果：{\"id\":1,\"user_name\":\"张三\"}", "explanation": "@JsonProperty可自定义JSON字段名，Jackson自动处理序列化。"}]}}, {"name": "Response Status and Exception Handling", "trans": ["响应状态码与异常处理"], "usage": {"syntax": "@ResponseStatus/@ExceptionHandler", "description": "@ResponseStatus可自定义响应状态码，@ExceptionHandler用于处理控制器异常。", "parameters": [{"name": "@ResponseStatus", "description": "指定方法或异常的HTTP状态码"}, {"name": "@ExceptionHandler", "description": "处理指定异常类型的方法"}], "returnValue": "自定义状态码或异常响应", "examples": [{"code": "@GetMapping(\"/user/{id}\")\npublic User getUser(@PathVariable Integer id) {\n    if (id == null) throw new UserNotFoundException();\n    return new User(id, \"张三\");\n}\n\n@ResponseStatus(HttpStatus.NOT_FOUND)\npublic class UserNotFoundException extends RuntimeException {}", "explanation": "@ResponseStatus可自定义异常对应的HTTP状态码。"}]}}, {"name": "Global Exception Handling (@ControllerAdvice)", "trans": ["全局异常处理（@ControllerAdvice）"], "usage": {"syntax": "@ControllerAdvice + @ExceptionHandler", "description": "@ControllerAdvice可实现全局异常捕获，统一处理所有控制器抛出的异常，提升健壮性和用户体验。", "parameters": [{"name": "@ControllerAdvice", "description": "全局异常处理注解"}, {"name": "@ExceptionHandler", "description": "指定处理的异常类型"}], "returnValue": "统一格式的异常响应体", "examples": [{"code": "@ControllerAdvice\npublic class GlobalExceptionHandler {\n    @ExceptionHandler(Exception.class)\n    public ResponseEntity<String> handleException(Exception ex) {\n        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(\"服务器异常\");\n    }\n}", "explanation": "@ControllerAdvice统一处理所有控制器异常，返回友好提示。"}]}}, {"name": "RESTful API Assignment", "trans": ["RESTful API开发练习"], "usage": {"syntax": "# RESTful API开发练习", "description": "完成以下练习，巩固Spring Boot RESTful API开发相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 编写一个@RestController实现用户的增删改查接口。\n2. 实现路径参数和请求参数的获取。\n3. 实现请求体与响应体的JSON映射。\n4. 自定义异常和全局异常处理。", "explanation": "通过这些练习掌握Spring Boot RESTful API开发的核心能力。"}]}}]}