{"topics": [{"id": "database-and-tables", "name": "数据库与表", "file": "MySQL/database-and-tables.json", "description": "详细介绍MySQL数据库的基本概念、数据库和数据表的创建、删除、结构查看与修改等基础操作，帮助入门MySQL。"}, {"id": "data-types", "name": "数据类型", "file": "MySQL/data-types.json", "description": "系统讲解MySQL的数值类型、字符串类型、日期与时间类型、枚举与集合类型、NULL与默认值等，帮助理解和应用数据类型。"}, {"id": "crud", "name": "基本增删改查", "file": "MySQL/crud.json", "description": "系统讲解MySQL的插入、查询、更新、删除、条件查询、排序与限制等基本数据操作，帮助掌握数据库的核心用法。"}, {"id": "constraints-and-keys", "name": "约束与主键", "file": "MySQL/constraints-and-keys.json", "description": "系统讲解MySQL的主键、唯一约束、非空约束、默认值、外键约束等，帮助设计高质量的数据库结构。"}, {"id": "simple-exercise", "name": "简单练习", "file": "MySQL/simple-exercise.json", "description": "提供MySQL基础的综合练习，包括创建学生信息表、插入数据、查询等操作，帮助巩固MySQL基础知识。"}, {"id": "multi-table-queries", "name": "多表查询", "file": "MySQL/multi-table-queries.json", "description": "系统讲解MySQL的内连接、左连接、右连接、自连接等多表查询技术，帮助掌握复杂数据查询与表关联。"}, {"id": "aggregation-and-grouping", "name": "聚合与分组", "file": "MySQL/aggregation-and-grouping.json", "description": "系统讲解MySQL的聚合函数、GROUP BY分组、HAVING条件等数据分析技术，帮助掌握数据统计与分析。"}, {"id": "subqueries", "name": "子查询", "file": "MySQL/subqueries.json", "description": "系统讲解MySQL的标量子查询、列子查询、行子查询、IN/EXISTS子查询和相关子查询等，帮助掌握复杂查询技术。"}, {"id": "views", "name": "视图", "file": "MySQL/views.json", "description": "系统讲解MySQL的视图创建、查询、更新、删除和特性等，帮助掌握视图的使用和管理。"}, {"id": "index-basics", "name": "索引基础", "file": "MySQL/index-basics.json", "description": "系统讲解MySQL的索引创建、唯一索引、复合索引、删除索引和查看索引等，帮助掌握索引的基本使用和优化。"}, {"id": "query-optimization", "name": "查询优化", "file": "MySQL/query-optimization.json", "description": "系统讲解MySQL的EXPLAIN分析、查询重写、覆盖索引、慢查询日志和索引优化等技术，帮助提升数据库查询性能。"}, {"id": "table-design-optimization", "name": "表设计优化", "file": "MySQL/table-design-optimization.json", "description": "系统讲解MySQL的规范化与反规范化、合理选择数据类型、分区表和垂直分区等技术，帮助优化数据库表设计。"}, {"id": "performance-optimization-exercise", "name": "性能优化练习", "file": "MySQL/performance-optimization-exercise.json", "description": "提供MySQL性能优化的实践练习，包括为学生表建立索引、使用EXPLAIN分析查询效果、查询优化分析等，帮助巩固性能优化技术。"}, {"id": "transactions", "name": "事务基础", "file": "MySQL/transactions.json", "description": "系统讲解MySQL的事务ACID特性、开启与提交事务、回滚事务、保存点和事务隔离级别等，帮助掌握数据库事务管理。"}, {"id": "locks", "name": "锁机制", "file": "MySQL/locks.json", "description": "系统讲解MySQL的行级锁与表级锁、共享锁与排他锁、死锁与解决方案等，帮助理解数据库并发控制机制。"}, {"id": "isolation-levels", "name": "隔离级别", "file": "MySQL/isolation-levels.json", "description": "系统讲解MySQL的四种事务隔离级别：READ UNCOMMITTED、READ COMMITTED、REPEATABLE READ和SERIALIZABLE，帮助理解并发事务中的数据一致性问题和解决方案。"}, {"id": "transaction-lock-practice", "name": "事务与锁练习", "file": "MySQL/transaction-lock-practice.json", "description": "通过两个会话同时修改同一条数据，观察MySQL锁的行为，深入理解InnoDB行级锁、并发控制和不同存储引擎的差异。"}, {"id": "user-and-privileges", "name": "用户与权限", "file": "MySQL/user-and-privileges.json", "description": "系统讲解MySQL用户的创建、授权、权限查看、回收和删除等管理操作，帮助掌握数据库安全管理。"}, {"id": "security-settings", "name": "安全设置", "file": "MySQL/security-settings.json", "description": "系统讲解MySQL密码策略、防SQL注入、数据备份与恢复等安全管理措施，帮助提升数据库安全性。"}, {"id": "user-management-exercise", "name": "用户管理练习", "file": "MySQL/user-management-exercise.json", "description": "通过创建只读用户并只允许查询学生表，掌握MySQL用户权限的细粒度控制和安全实践。"}, {"id": "data-recovery", "name": "恢复数据", "file": "MySQL/data-recovery.json", "description": "系统讲解MySQL通过SQL文件和物理备份进行数据恢复的方法，帮助掌握数据库灾备与恢复技能。"}, {"id": "backup-and-recovery-exercise", "name": "备份与恢复练习", "file": "MySQL/backup-and-recovery-exercise.json", "description": "通过mysqldump导出学生表并恢复到新表，掌握MySQL表级数据备份与恢复的实用技能。"}, {"id": "stored-procedures-and-functions", "name": "存储过程与函数", "file": "MySQL/stored-procedures-and-functions.json", "description": "系统讲解MySQL存储过程与自定义函数的创建、调用和实际应用，帮助提升数据库编程能力。"}, {"id": "triggers", "name": "触发器", "file": "MySQL/triggers.json", "description": "系统讲解MySQL触发器的创建、删除和常见应用场景，帮助实现自动化数据处理和审计。"}, {"id": "events", "name": "事件调度器", "file": "MySQL/events.json", "description": "系统讲解MySQL事件调度器的创建、定时任务和自动化运维能力，帮助实现定时数据处理。"}, {"id": "json-and-fulltext", "name": "JSON与全文索引", "file": "MySQL/json-and-fulltext.json", "description": "系统讲解MySQL JSON数据类型、JSON查询和全文索引的用法，帮助实现结构化与非结构化数据的高效管理与搜索。"}, {"id": "advanced-exercise", "name": "高级应用练习", "file": "MySQL/advanced-exercise.json", "description": "通过存储过程实现自动分配学号等高级应用，提升MySQL实际开发能力。"}, {"id": "log-management", "name": "日志管理", "file": "MySQL/log-management.json", "description": "系统讲解MySQL错误日志、查询日志和慢查询日志的管理与分析，帮助排查故障和优化性能。"}, {"id": "monitoring-tools", "name": "监控工具", "file": "MySQL/monitoring-tools.json", "description": "系统讲解MySQL常用监控工具（SHOW STATUS、SHOW PROCESSLIST、information_schema），帮助诊断和优化数据库运行状态。"}, {"id": "troubleshooting", "name": "常见故障排查", "file": "MySQL/troubleshooting.json", "description": "系统讲解MySQL连接数、死锁、性能瓶颈等常见故障的排查与优化方法，提升数据库运维能力。"}, {"id": "project-practice", "name": "项目实战", "file": "MySQL/project-practice.json", "description": "通过设计并实现学生成绩管理系统，系统掌握MySQL数据库建模、增删改查、统计分析与综合应用能力。"}]}