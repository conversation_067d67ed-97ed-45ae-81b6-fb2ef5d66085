{"name": "Volume Management", "trans": ["数据卷（Volume）"], "methods": [{"name": "Volume Concept & Usage", "trans": ["Volume概念与作用"], "usage": {"syntax": "docker run -v <卷名或路径>:<容器路径> <镜像名>", "description": "数据卷用于实现容器与主机或容器间的数据持久化和共享，生命周期独立于容器。", "parameters": [{"name": "-v", "description": "挂载卷的参数。"}, {"name": "<卷名或路径>", "description": "本地卷名或主机路径。"}, {"name": "<容器路径>", "description": "容器内挂载点。"}, {"name": "<镜像名>", "description": "要运行的镜像名称。"}], "returnValue": "无返回值，容器可持久化和共享数据。", "examples": [{"code": "# 挂载主机目录到容器\ndocker run -v /data:/app/data nginx", "explanation": "将主机/data目录挂载到nginx容器的/app/data，实现数据持久化。"}]}}, {"name": "Create & Mount Volume", "trans": ["创建与挂载数据卷"], "usage": {"syntax": "docker volume create <卷名>\ndocker run -v <卷名>:<容器路径> <镜像名>", "description": "通过docker volume create创建具名卷，再通过-v参数挂载到容器。", "parameters": [{"name": "docker volume create", "description": "创建具名卷。"}, {"name": "-v", "description": "挂载卷的参数。"}, {"name": "<卷名>", "description": "卷的名称。"}, {"name": "<容器路径>", "description": "容器内挂载点。"}, {"name": "<镜像名>", "description": "要运行的镜像名称。"}], "returnValue": "无返回值，卷被挂载到容器。", "examples": [{"code": "# 创建具名卷\ndocker volume create mydata\n# 挂载到容器\ndocker run -v mydata:/data nginx", "explanation": "先创建mydata卷，再挂载到nginx容器的/data目录。"}]}}, {"name": "Anonymous & Named Volumes", "trans": ["匿名卷与具名卷"], "usage": {"syntax": "docker run -v <容器路径> <镜像名>\ndocker run -v <卷名>:<容器路径> <镜像名>", "description": "不指定卷名时为匿名卷，指定卷名为具名卷。具名卷便于管理和复用。", "parameters": [{"name": "-v", "description": "挂载卷的参数。"}, {"name": "<卷名>", "description": "具名卷名称，可选。"}, {"name": "<容器路径>", "description": "容器内挂载点。"}, {"name": "<镜像名>", "description": "要运行的镜像名称。"}], "returnValue": "无返回值，卷类型不同影响管理方式。", "examples": [{"code": "# 匿名卷\ndocker run -v /data nginx\n# 具名卷\ndocker run -v mydata:/data nginx", "explanation": "不指定卷名为匿名卷，指定mydata为具名卷。"}]}}, {"name": "Volume Lifecycle", "trans": ["卷的生命周期"], "usage": {"syntax": "docker volume ls\ndocker volume inspect <卷名>\ndocker volume rm <卷名>", "description": "卷独立于容器存在，可列出、查看详情和删除。删除卷前需确保未被容器使用。", "parameters": [{"name": "docker volume ls", "description": "列出所有卷。"}, {"name": "docker volume inspect", "description": "查看卷详细信息。"}, {"name": "docker volume rm", "description": "删除卷。"}, {"name": "<卷名>", "description": "目标卷名称。"}], "returnValue": "无返回值，卷可独立管理。", "examples": [{"code": "# 查看所有卷\ndocker volume ls\n# 查看卷详情\ndocker volume inspect mydata\n# 删除卷\ndocker volume rm mydata", "explanation": "依次列出、查看和删除具名卷mydata。"}]}}, {"name": "Volume Backup & Restore", "trans": ["卷的备份与恢复"], "usage": {"syntax": "docker run --rm -v <卷名>:/data -v $(pwd):/backup busybox tar czf /backup/backup.tar.gz -C /data .\ndocker run --rm -v <卷名>:/data -v $(pwd):/backup busybox tar xzf /backup/backup.tar.gz -C /data", "description": "通过挂载卷和本地目录，使用tar命令实现卷的数据备份与恢复。", "parameters": [{"name": "-v", "description": "挂载卷和本地目录。"}, {"name": "<卷名>", "description": "目标卷名称。"}], "returnValue": "无返回值，卷数据可备份与恢复。", "examples": [{"code": "# 备份卷数据\ndocker run --rm -v mydata:/data -v $(pwd):/backup busybox tar czf /backup/backup.tar.gz -C /data .\n# 恢复卷数据\ndocker run --rm -v mydata:/data -v $(pwd):/backup busybox tar xzf /backup/backup.tar.gz -C /data", "explanation": "用busybox容器将mydata卷数据备份到本地，再恢复到卷中。"}]}}, {"name": "Assignment: 数据卷实践", "trans": ["作业：数据卷实践"], "usage": {"syntax": "请完成以下任务：\n1. 创建具名卷mydata并挂载到nginx容器/data\n2. 匿名卷挂载/data2\n3. 查看所有卷并删除mydata\n4. 备份mydata卷到本地并恢复", "description": "通过实际操作掌握数据卷的创建、挂载、生命周期管理和备份恢复。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 1. 创建并挂载具名卷\ndocker volume create mydata\ndocker run -d -v mydata:/data nginx\n# 2. 匿名卷挂载\ndocker run -d -v /data2 nginx\n# 3. 查看并删除卷\ndocker volume ls\ndocker volume rm mydata\n# 4. 备份与恢复\ndocker run --rm -v mydata:/data -v $(pwd):/backup busybox tar czf /backup/backup.tar.gz -C /data .\ndocker run --rm -v mydata:/data -v $(pwd):/backup busybox tar xzf /backup/backup.tar.gz -C /data", "explanation": "依次完成数据卷的核心实践。"}]}}]}