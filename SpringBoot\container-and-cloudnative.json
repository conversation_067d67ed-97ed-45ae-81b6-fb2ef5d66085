{"name": "Containerization and Cloud Native", "trans": ["容器化与云原生"], "methods": [{"name": "Dockerfile Writing", "trans": ["Dockerfile编写"], "usage": {"syntax": "FROM openjdk:17-jdk-alpine\nCOPY target/app.jar app.jar\nENTRYPOINT [\"java\", \"-jar\", \"/app.jar\"]", "description": "通过编写Dockerfile可将Spring Boot应用容器化，便于部署和迁移。常用基础镜像有openjdk、adoptopenjdk等。", "parameters": [{"name": "FROM", "description": "指定基础镜像"}, {"name": "COPY", "description": "复制应用包到镜像"}, {"name": "ENTRYPOINT", "description": "设置容器启动命令"}], "returnValue": "可用于构建镜像的Dockerfile文件", "examples": [{"code": "# Dockerfile示例\nFROM openjdk:17-jdk-alpine\nCOPY target/app.jar app.jar\nENTRYPOINT [\"java\", \"-jar\", \"/app.jar\"]", "explanation": "展示了Spring Boot应用的基础Dockerfile写法。"}]}}, {"name": "Image Build and Push", "trans": ["镜像构建与推送"], "usage": {"syntax": "docker build -t app:latest .\ndocker push app:latest", "description": "通过docker build命令构建镜像，docker push命令推送到镜像仓库（如Docker Hub、Harbor等）。", "parameters": [{"name": "docker build", "description": "构建镜像命令"}, {"name": "docker push", "description": "推送镜像到仓库"}], "returnValue": "本地或远程镜像仓库中的应用镜像", "examples": [{"code": "# 构建镜像\ndocker build -t myapp:1.0 .\n# 推送镜像\ndocker tag myapp:1.0 myrepo/myapp:1.0\ndocker push myrepo/myapp:1.0", "explanation": "展示了镜像构建和推送的常用命令。"}]}}, {"name": "K8s Deployment and Config", "trans": ["K8s部署与配置"], "usage": {"syntax": "kubectl apply -f deployment.yaml\nkubectl apply -f service.yaml", "description": "通过Kubernetes的Deployment、Service等资源对象实现Spring Boot应用的自动化部署和服务暴露。", "parameters": [{"name": "deployment.yaml", "description": "定义Pod副本、镜像、环境变量等"}, {"name": "service.yaml", "description": "定义服务暴露方式（ClusterIP、NodePort等）"}], "returnValue": "K8s集群中的运行服务", "examples": [{"code": "# deployment.yaml示例\napiVersion: apps/v1\nkind: Deployment\nmetadata:\n  name: springboot-app\nspec:\n  replicas: 2\n  selector:\n    matchLabels:\n      app: springboot-app\n  template:\n    metadata:\n      labels:\n        app: springboot-app\n    spec:\n      containers:\n        - name: app\n          image: myrepo/myapp:1.0\n          ports:\n            - containerPort: 8080\n---\n# service.yaml示例\napiVersion: v1\nkind: Service\nmetadata:\n  name: springboot-service\nspec:\n  type: NodePort\n  selector:\n    app: springboot-app\n  ports:\n    - port: 80\n      targetPort: 8080", "explanation": "展示了K8s部署和服务暴露的基本配置。"}]}}, {"name": "Config Center and Secret Management", "trans": ["配置中心与密钥管理"], "usage": {"syntax": "ConfigMap、Secret资源，kubectl apply -f configmap.yaml", "description": "K8s通过ConfigMap和Secret实现配置和敏感信息的集中管理，安全注入到容器环境。", "parameters": [{"name": "ConfigMap", "description": "存储普通配置信息"}, {"name": "Secret", "description": "存储敏感信息如密码、密钥"}], "returnValue": "K8s集群中的配置和密钥资源", "examples": [{"code": "# configmap.yaml示例\napiVersion: v1\nkind: ConfigMap\nmetadata:\n  name: app-config\ndata:\n  SPRING_PROFILES_ACTIVE: prod\n---\n# secret.yaml示例\napiVersion: v1\nkind: Secret\nmetadata:\n  name: app-secret\ntype: Opaque\ndata:\n  password: cGFzc3dvcmQ=  # base64编码", "explanation": "展示了K8s配置中心和密钥管理的基本用法。"}]}}, {"name": "Health Check and Auto Restart", "trans": ["健康检查与自动重启"], "usage": {"syntax": "livenessProbe、readinessProbe配置，Deployment自动重启", "description": "K8s通过livenessProbe和readinessProbe实现服务健康检查，自动重启异常容器，保障高可用。", "parameters": [{"name": "livenessProbe", "description": "存活性探针，检测服务是否存活"}, {"name": "readinessProbe", "description": "就绪性探针，检测服务是否可接收流量"}], "returnValue": "高可用的K8s服务实例", "examples": [{"code": "# deployment.yaml片段\nspec:\n  containers:\n    - name: app\n      image: myrepo/myapp:1.0\n      livenessProbe:\n        httpGet:\n          path: /actuator/health\n          port: 8080\n        initialDelaySeconds: 30\n        periodSeconds: 10\n      readinessProbe:\n        httpGet:\n          path: /actuator/health\n          port: 8080\n        initialDelaySeconds: 10\n        periodSeconds: 5", "explanation": "展示了K8s健康检查和自动重启的配置。"}]}}, {"name": "Container and Cloud Native Assignment", "trans": ["容器化与云原生练习"], "usage": {"syntax": "# 容器化与云原生练习", "description": "完成以下练习，巩固Spring Boot容器化与云原生相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 编写Spring Boot应用的Dockerfile并构建镜像。\n2. 将镜像推送到远程仓库。\n3. 编写K8s deployment和service配置文件并部署。\n4. 使用ConfigMap和Secret管理配置和密钥。\n5. 配置livenessProbe和readinessProbe实现健康检查。", "explanation": "通过这些练习掌握Spring Boot容器化与云原生的核心用法。"}]}}]}