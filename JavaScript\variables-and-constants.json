{"name": "Variables and Constants", "trans": ["变量与常量"], "methods": [{"name": "var/let/const Declaration", "trans": ["var/let/const声明"], "usage": {"syntax": "var a = 1;\nlet b = 2;\nconst c = 3;", "description": "var用于函数作用域变量声明，let和const用于块级作用域。let声明可变变量，const声明常量（不可重新赋值）。", "parameters": [{"name": "var", "description": "函数作用域变量声明。"}, {"name": "let", "description": "块级作用域变量声明。"}, {"name": "const", "description": "块级作用域常量声明。"}], "returnValue": "无返回值，声明变量或常量。", "examples": [{"code": "var x = 1; // 函数作用域\nlet y = 2; // 块级作用域\nconst z = 3; // 块级作用域且不可变", "explanation": "分别用var、let、const声明变量。"}]}}, {"name": "Variable Scope", "trans": ["变量作用域"], "usage": {"syntax": "function test() {\n  var a = 1;\n  if (true) {\n    let b = 2;\n    const c = 3;\n  }\n}", "description": "var声明的变量具有函数作用域，let和const声明的变量具有块级作用域。块级作用域只在大括号内有效。", "parameters": [{"name": "函数作用域", "description": "var声明的变量只在函数内有效。"}, {"name": "块级作用域", "description": "let/const声明的变量只在块内有效。"}], "returnValue": "无返回值，变量作用域影响可访问性。", "examples": [{"code": "function foo() {\n  if (true) {\n    var x = 1;\n    let y = 2;\n  }\n  console.log(x); // 1\n  // console.log(y); // 报错，y只在块内有效\n}", "explanation": "var声明的x在函数内有效，let声明的y只在if块内有效。"}]}}, {"name": "Variable Hoisting", "trans": ["变量提升"], "usage": {"syntax": "console.log(a); // undefined\nvar a = 1;\n// console.log(b); // 报错\nlet b = 2;", "description": "var声明的变量会提升到函数/全局作用域顶部，值为undefined。let/const声明不会提升，访问会报错。", "parameters": [{"name": "变量提升", "description": "var声明的变量提升。"}, {"name": "暂时性死区", "description": "let/const声明在声明前不可访问。"}], "returnValue": "无返回值，影响变量访问时机。", "examples": [{"code": "console.log(a); // undefined\nvar a = 1;\n// console.log(b); // ReferenceError\nlet b = 2;", "explanation": "var声明的变量提升，let声明的变量不提升。"}]}}, {"name": "Constant Features", "trans": ["常量特性"], "usage": {"syntax": "const PI = 3.14;\n// PI = 3.15; // 报错", "description": "const声明的常量不可重新赋值，但如果是对象或数组，其属性或元素可变。", "parameters": [{"name": "const", "description": "声明常量。"}], "returnValue": "无返回值，常量不可变。", "examples": [{"code": "const arr = [1,2,3];\narr[0] = 100; // 允许\n// arr = [4,5,6]; // 报错", "explanation": "const声明的数组引用不可变，但内容可变。"}]}}, {"name": "作业：变量与常量实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 用var/let/const声明变量\n// 2. 理解作用域和变量提升\n// 3. 理解const常量特性", "description": "通过实践变量声明、作用域、提升和常量特性，掌握JS变量基础。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 用var/let/const声明变量\n// 2. 验证作用域和提升\n// 3. 验证const常量特性", "explanation": "作业提示，需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nfunction test() {\n  var a = 1;\n  let b = 2;\n  const c = 3;\n  console.log(a, b, c);\n}\ntest();", "explanation": "变量与常量声明和作用域的正确实现。"}]}}]}