{"name": "Basic Syntax", "trans": ["基本语法"], "methods": [{"name": "Main Function Structure", "trans": ["main函数结构"], "usage": {"syntax": "void main() {\n  // 代码块\n}\n\n// 或者带命令行参数\nvoid main(List<String> arguments) {\n  // 代码块\n}", "description": "main函数是Dart程序的入口点，类似于C、Java等语言。程序执行从main函数开始。Dart中的main函数有两种常见形式：1) 无参数形式：`void main() {}`，适用于简单程序；2) 带命令行参数形式：`void main(List<String> arguments) {}`，可以接收命令行传入的参数。main函数的返回类型通常是`void`，表示不返回任何值。在Dart程序中，main函数是必须的，且只能有一个。", "parameters": [{"name": "arguments", "description": "可选参数，类型为List<String>，用于接收命令行传入的参数"}], "returnValue": "通常为void，表示不返回任何值", "examples": [{"code": "// 简单的main函数\nvoid main() {\n  print('Hello, Dar<PERSON>!'); // 输出：Hello, Dart!\n}\n\n// 带命令行参数的main函数\nvoid main(List<String> arguments) {\n  print('接收到${arguments.length}个参数');\n  \n  // 遍历所有参数\n  for (int i = 0; i < arguments.length; i++) {\n    print('参数 ${i+1}: ${arguments[i]}');\n  }\n  \n  // 使用forEach遍历\n  arguments.forEach((arg) => print('参数: $arg'));\n}\n\n// 如何运行带参数的程序：\n// 在命令行中执行：dart my_program.dart arg1 arg2 arg3\n// 输出：\n// 接收到3个参数\n// 参数 1: arg1\n// 参数 2: arg2\n// 参数 3: arg3\n// 参数: arg1\n// 参数: arg2\n// 参数: arg3", "explanation": "这个示例展示了两种main函数的形式。第一种是没有参数的简单形式，适合大多数简单程序。第二种接收命令行参数，可以处理用户输入的参数，例如配置选项或文件路径等。示例同时展示了如何使用for循环和forEach方法遍历参数列表。"}]}}, {"name": "Variables and Constants", "trans": ["变量与常量"], "usage": {"syntax": "// 变量声明\nvar name = value;\nType name = value;\n\n// 常量声明\nfinal name = value;\nconst name = value;\nfinal Type name = value;\nconst Type name = value;", "description": "Dart中的变量和常量用于存储数据。变量声明有多种方式：1) 使用`var`关键字，让Dart自动推断类型；2) 显式指定类型。常量有两种：1) `final`：运行时常量，值在第一次使用时确定；2) `const`：编译时常量，值必须在编译时确定。所有变量默认值为`null`(在启用空安全后，非空变量必须初始化)。Dart支持类型推断，可以根据初始值确定变量类型。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "void main() {\n  // 使用var声明变量，类型由初始值推断\n  var name = 'Bob'; // String类型\n  var age = 30;     // int类型\n  var height = 1.85; // double类型\n  var isActive = true; // bool类型\n  \n  // 显式指定类型\n  String country = 'China';\n  int population = 1400000000;\n  double gdp = 14.7;\n  bool isLarge = true;\n  \n  // 声明变量但暂不初始化(需要启用空安全特性)\n  // String? nullableName; // 可空类型，默认值为null\n  \n  // 在空安全中，非空变量必须初始化\n  String nonNullName = 'Alice';\n  // String willError; // 错误：非空变量必须初始化\n  \n  // final常量：运行时常量，初始化后不可更改\n  final currentYear = DateTime.now().year; // 运行时计算\n  final String president = 'Current President';\n  // president = 'New President'; // 错误：final变量不可更改\n  \n  // const常量：编译时常量，必须在编译时确定值\n  const pi = 3.14159;\n  const double e = 2.71828;\n  // const today = DateTime.now(); // 错误：DateTime.now()不是编译时常量\n  \n  // 常量集合\n  const fruits = ['apple', 'banana', 'orange']; // 编译时常量列表\n  // fruits.add('mango'); // 错误：不能修改const集合\n  \n  // 打印变量值\n  print('姓名: $name, 年龄: $age');\n  print('国家: $country, 人口: $population');\n  print('今年是 $currentYear 年');\n  print('π的值约为 $pi, e的值约为 $e');\n  \n  // var, final, const的区别\n  var canChange = 100;\n  canChange = 200; // 可以更改\n  \n  final cannotChange = 100;\n  // cannotChange = 200; // 错误：不能更改final变量\n  \n  const compileTimeConstant = 100;\n  // compileTimeConstant = 200; // 错误：不能更改const变量\n}", "explanation": "这个示例展示了Dart中变量和常量的声明和使用方式。展示了var关键字的使用、显式类型声明、final运行时常量和const编译时常量的区别。还说明了空安全特性下的变量初始化要求，以及常量集合的使用。"}]}}, {"name": "Data Types", "trans": ["数据类型"], "usage": {"syntax": "// 数字类型\nint integerValue = 42;\ndouble floatingValue = 3.14;\nnum numericValue = 10; // 可以是int或double\n\n// 字符串类型\nString text = 'Hello';\n\n// 布尔类型\nbool flag = true;\n\n// 列表(数组)类型\nList<Type> listName = [item1, item2, ...];\n\n// 集合类型\nSet<Type> setName = {item1, item2, ...};\n\n// 映射类型\nMap<KeyType, ValueType> mapName = {\n  key1: value1,\n  key2: value2,\n  ...\n};\n\n// Runes (用于表示Unicode字符)\nRunes runes = Runes('\\u2665');\n\n// Symbol类型\nSymbol symbolName = #symbolLiteral;", "description": "Dart是一种强类型语言，提供多种内置数据类型：1) 数字：`int`(整数)、`double`(浮点数)和`num`(可以是int或double)；2) 字符串：`String`，用于文本数据；3) 布尔：`bool`，表示true或false；4) 列表：`List`，有序集合，相当于数组；5) 集合：`Set`，无序且不重复的集合；6) 映射：`Map`，键值对集合；7) Runes：用于表示Unicode字符；8) Symbol：用于表示Dart程序中声明的操作符或标识符。在Dart中，所有数据类型都是对象，都继承自`Object`类，甚至包括基本类型如数字、布尔值，以及函数和null。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "void main() {\n  // 数字类型\n  int age = 30;\n  double height = 1.75;\n  num weight = 70; // num可以是int或double\n  num distance = 12.5; // double类型的num\n  \n  // 数字类型的操作\n  int sum = age + 5; // 加法\n  double product = height * 2; // 乘法\n  double division = weight / 2; // 除法，结果为double\n  int intDivision = age ~/ 7; // 整除，结果为int\n  int remainder = age % 7; // 取余\n  \n  // 字符串类型\n  String name = 'Alice';\n  String greeting = \"Hello, $name\"; // 字符串插值\n  String multiLine = '''\n  这是一个\n  多行字符串\n  示例\n  '''; // 三引号支持多行\n  \n  // 字符串操作\n  String upperCase = name.toUpperCase(); // 转换为大写\n  String concat = name + ' <PERSON>'; // 字符串连接\n  bool contains = greeting.contains('Alice'); // 检查包含\n  String replaced = greeting.replaceAll('Hello', 'Hi'); // 替换\n  \n  // 布尔类型\n  bool isAdult = age >= 18;\n  bool hasName = name.isNotEmpty;\n  \n  // 条件判断\n  if (isAdult && hasName) {\n    print('$name是成年人');\n  }\n  \n  // 列表(数组)类型\n  List<int> numbers = [1, 2, 3, 4, 5];\n  List<String> fruits = ['apple', 'banana', 'orange'];\n  var dynamicList = [1, 'two', true]; // List<dynamic>\n  \n  // 列表操作\n  numbers.add(6); // 添加元素\n  int firstNumber = numbers[0]; // 访问元素\n  numbers[1] = 20; // 修改元素\n  int length = numbers.length; // 获取长度\n  bool containsThree = numbers.contains(3); // 检查包含\n  \n  // 集合类型\n  Set<int> uniqueNumbers = {1, 2, 3, 3, 4}; // {1, 2, 3, 4}\n  Set<String> uniqueFruits = Set.from(fruits);\n  \n  // 集合操作\n  uniqueNumbers.add(5); // 添加元素\n  uniqueNumbers.remove(2); // 删除元素\n  bool hasFour = uniqueNumbers.contains(4); // 检查包含\n  \n  // 映射类型(键值对)\n  Map<String, int> ages = {\n    'Alice': 30,\n    'Bob': 25,\n    'Charlie': 35,\n  };\n  Map<String, dynamic> person = {\n    'name': 'David',\n    'age': 28,\n    'isEmployed': true,\n  };\n  \n  // 映射操作\n  ages['Eve'] = 22; // 添加或更新键值对\n  int bobAge = ages['Bob']!; // 访问值(使用!表示确定键存在)\n  ages.remove('Charlie'); // 删除键值对\n  bool hasAlice = ages.containsKey('Alice'); // 检查键\n  \n  // Runes (Unicode字符)\n  Runes heart = Runes('\\u2665');\n  print(String.fromCharCodes(heart)); // 输出: ♥\n  \n  // Symbol类型\n  Symbol s = #mySymbol;\n  \n  // 类型判断\n  print('age类型: ${age.runtimeType}');\n  print('name类型: ${name.runtimeType}');\n  print('numbers类型: ${numbers.runtimeType}');\n  print('uniqueNumbers类型: ${uniqueNumbers.runtimeType}');\n  print('ages类型: ${ages.runtimeType}');\n}", "explanation": "这个示例全面展示了Dart中的各种数据类型及其操作。包括数字类型(int、double、num)的算术运算、字符串的各种操作、布尔类型的条件判断、列表的增删改查、集合的特性和操作、映射的键值对操作，以及Runes和Symbol的基本用法。还展示了如何使用runtimeType属性来检查变量的类型。"}]}}, {"name": "Comments", "trans": ["注释写法"], "usage": {"syntax": "// 单行注释\n\n/* 多行\n   注释 */\n\n/// 文档注释\n\n/**\n * 多行文档注释\n */", "description": "Dart支持多种注释方式：1) 单行注释：以`//`开始，注释内容一直延续到行尾；2) 多行注释：以`/*`开始，以`*/`结束，可以跨越多行；3) 文档注释：以`///`或`/**`开始，用于生成API文档。文档注释支持Markdown语法，可以使用`dartdoc`工具生成HTML文档。文档注释通常放在变量、函数、类或库声明之前，用于说明其用途、参数、返回值等。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 这是单行注释，用于简短说明\n\n/* 这是多行注释\n   可以跨越多行\n   通常用于较长的说明 */\n\n/// 这是文档注释，用于生成API文档\n/// 支持Markdown语法\n/// - 可以使用列表\n/// - 可以**加粗**或*斜体*\n\n/**\n * 这是多行文档注释\n * 也用于生成API文档\n * \n * 通常格式如下：\n * 1. 简短描述\n * 2. 详细描述\n * 3. 参数、返回值等说明\n */\n\n// 使用注释示例\n\n/// 计算两个数的和\n/// \n/// 接收两个整数参数并返回它们的和\n/// \n/// 参数:\n/// - [a]: 第一个整数\n/// - [b]: 第二个整数\n/// \n/// 返回: 两个数的和\n/// \n/// 示例:\n/// ```dart\n/// int result = add(5, 3); // 返回8\n/// ```\nint add(int a, int b) {\n  // 执行加法操作\n  return a + b;\n}\n\n/// 表示一个人的基本信息\nclass Person {\n  /// 人的名字\n  final String name;\n  \n  /// 人的年龄\n  final int age;\n  \n  /// 创建一个人的实例\n  /// \n  /// 需要提供[name]和[age]参数\n  Person(this.name, this.age);\n  \n  /// 判断是否为成年人\n  /// \n  /// 根据年龄判断，返回布尔值\n  bool isAdult() => age >= 18;\n}\n\n// 代码中的行内注释\nvoid main() {\n  var x = 5; // 初始化变量x\n  var y = 10; // 初始化变量y\n  \n  // 计算x和y的和\n  var sum = x + y;\n  \n  /* 下面是输出结果\n     使用print函数 */\n  print('结果: $sum'); // 打印结果\n}", "explanation": "这个示例展示了Dart中的各种注释类型和用法。包括单行注释、多行注释和文档注释（单行和多行形式）。示例还展示了如何为函数和类编写标准的文档注释，包括参数说明、返回值说明和示例代码。文档注释可以被dartdoc工具处理，生成API文档网站。"}]}}, {"name": "Identifiers and Keywords", "trans": ["标识符与关键字"], "usage": {"syntax": "// 标识符\nidentifierName\n_privateIdentifier\n\n// 关键字使用\nclass MyClass { ... }\nif (condition) { ... }\nwhile (condition) { ... }", "description": "标识符是用于命名变量、函数、类等程序元素的名称。Dart中的标识符规则：1) 必须以字母或下划线(_)开头，后跟字母、数字或下划线的组合；2) 不能是关键字；3) 区分大小写，如name和Name是不同的标识符；4) 以下划线开头的标识符在库中被视为私有。关键字是语言预定义的具有特殊含义的单词，不能用作标识符。Dart有一组保留关键字，如class、if、while等，还有一些内置标识符如true、false、null等。标识符的命名约定：1) 类名、枚举和扩展名采用UpperCamelCase；2) 变量、函数、参数等采用lowerCamelCase；3) 常量采用lowerCamelCase或SCREAMING_CAPS。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 标识符示例\n\n// 有效的标识符\nvar name;\nvar firstName;\nvar _privateVar;\nvar $special; // $也是允许的\nvar counter123;\n\n// 无效的标识符\n// var 123invalid; // 不能以数字开头\n// var my-var;     // 不能包含连字符\n// var class;      // 不能是关键字\n\n// 常用命名约定\n\n// 1. 类、枚举和类型别名使用UpperCamelCase\nclass Person { }\nclass HttpRequest { }\nenum Color { red, green, blue }\ntypedef Predicate<T> = bool Function(T value);\n\n// 2. 变量、函数、参数使用lowerCamelCase\nvar userName;\nvar isVisible;\n\nvoid calculateTotal(double price, int quantity) {\n  var orderTotal = price * quantity;\n  print('总计: $orderTotal');\n}\n\n// 3. 常量可以使用lowerCamelCase或SCREAMING_CAPS\nconst pi = 3.14159;\nconst PI = 3.14159;\nconst MAX_ITEMS = 100;\n\n// 4. 库、包名使用小写加下划线\n// import 'package:my_package/my_library.dart';\n\n// 5. 私有成员以下划线开头\nclass Account {\n  String name;       // 公开成员\n  double _balance;   // 私有成员\n  \n  Account(this.name, this._balance);\n  \n  void deposit(double amount) {\n    _updateBalance(amount);\n  }\n  \n  void _updateBalance(double amount) { // 私有方法\n    _balance += amount;\n  }\n}\n\n// Dart关键字列表\nvoid keywordExamples() {\n  // 控制流关键字\n  if (true) { }\n  else { }\n  for (var i = 0; i < 10; i++) { }\n  while (false) { }\n  do { } while (false);\n  switch (1) {\n    case 1: break;\n    default: break;\n  }\n  \n  // 异常处理关键字\n  try {\n    throw Exception('错误');\n  } catch (e) {\n    print(e);\n  } finally {\n    print('清理');\n  }\n  \n  // 类相关关键字\n  class MyClass { }\n  abstract class AbstractClass { }\n  // extends, implements, with用于类继承和混入\n  \n  // 其他常用关键字\n  var variable; // 变量声明\n  final finalVar = 10; // 不可变变量\n  const constVar = 20; // 编译时常量\n  return; // 返回值\n  // import, export用于库管理\n  // async, await, yield用于异步编程\n}\n\n// 内置标识符(预定义名称，非严格关键字)\nvoid builtInIdentifiers() {\n  dynamic value; // 动态类型\n  value = 10;\n  value = 'string';\n  \n  // true, false是布尔字面量\n  bool flag = true;\n  \n  // null是空值字面量\n  String? nullableString = null;\n}", "explanation": "这个示例展示了Dart中标识符的规则和各种命名约定，包括有效和无效的标识符示例，以及针对不同程序元素的命名风格建议。同时也展示了Dart中常用关键字的使用，如控制流、异常处理和类定义等关键字，以及内置标识符如true、false和null等。"}]}}, {"name": "Code Style and Formatting", "trans": ["代码风格与格式化"], "usage": {"syntax": "// 缩进使用两个空格\nvoid main() {\n  if (condition) {\n    statement;\n  }\n}\n\n// 每行长度建议不超过80个字符\n// 花括号使用K&R风格（左花括号不换行）", "description": "Dart有一套官方的代码风格指南，称为Effective Dart，包含了四个主要方面：风格(Style)、文档(Documentation)、使用(Usage)和设计(Design)。主要的代码风格规范包括：1) 缩进使用两个空格，不使用Tab；2) 每行长度建议不超过80个字符；3) 语句末尾使用分号；4) 使用K&R风格的花括号（左花括号不换行）；5) 公有API应有文档注释；6) 命名遵循特定约定（如类使用UpperCamelCase）；7) 导入语句按类型分组并排序。Dart提供了自动格式化工具`dart format`命令（或IDE中的格式化功能），可以自动应用这些规则。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// Dart推荐的代码风格示例\n\n// 1. 导入语句分组排序\n// dart:开头的标准库导入\nimport 'dart:async';\nimport 'dart:math';\n\n// package:开头的外部包导入\nimport 'package:flutter/material.dart';\nimport 'package:http/http.dart' as http;\n\n// 相对路径导入自己的库\nimport 'src/utils.dart';\n\n// 2. 类声明示例，展示正确的缩进和花括号风格\nclass Person {\n  // 成员变量声明\n  final String name;\n  final int age;\n  final List<String> hobbies;\n  \n  // 构造函数使用简洁语法\n  Person(this.name, this.age, this.hobbies);\n  \n  // 方法定义\n  void introduce() {\n    print('我叫$name，今年$age岁。');\n    print('我的爱好是：${hobbies.join(', ')}');\n  }\n  \n  // getter方法使用箭头语法\n  bool get isAdult => age >= 18;\n}\n\n// 3. 函数格式示例\nString formatName(String firstName, String lastName, {\n  bool uppercase = false,\n  bool includeMiddle = false,\n  String? middleName,\n}) {\n  var result = '';\n  \n  // 参数太多时，换行缩进对齐\n  if (includeMiddle && middleName != null) {\n    result = '$firstName $middleName $lastName';\n  } else {\n    result = '$firstName $lastName';\n  }\n  \n  return uppercase ? result.toUpperCase() : result;\n}\n\n// 4. 条件判断和循环结构\nvoid controlFlow() {\n  const items = ['苹果', '香蕉', '橙子'];\n  \n  // if语句风格\n  if (items.isEmpty) {\n    print('列表为空');\n  } else if (items.length == 1) {\n    print('列表只有一个项目');\n  } else {\n    print('列表有${items.length}个项目');\n  }\n  \n  // for循环风格\n  for (var i = 0; i < items.length; i++) {\n    print('项目 ${i + 1}: ${items[i]}');\n  }\n  \n  // forEach风格\n  items.forEach((item) {\n    print('水果: $item');\n  });\n  \n  // 使用for-in循环\n  for (final item in items) {\n    print('遍历: $item');\n  }\n}\n\n// 5. 长行的拆分\nvoid longLines() {\n  // 长字符串拆分\n  final longText = '这是一个非常长的字符串，需要拆分成多行显示，'\n      '这样可以保持每行不超过80个字符，'\n      '同时保持良好的可读性。';\n  \n  // 长参数列表拆分\n  final result = calculateTotal(\n    subtotal: 100.0,\n    taxRate: 0.08,\n    discount: 10.0,\n    shippingCost: 5.0,\n    handlingFee: 2.0,\n  );\n  \n  // 长条件拆分\n  if (condition1 &&\n      condition2 &&\n      condition3 &&\n      condition4) {\n    // 执行代码\n  }\n}\n\n// 6. 注释与文档\n/// 计算订单总额\n/// \n/// 根据[subtotal]和各种费用计算最终总额\n/// \n/// 返回计算后的总金额\ndouble calculateTotal({\n  required double subtotal,\n  double taxRate = 0.0,\n  double discount = 0.0,\n  double shippingCost = 0.0,\n  double handlingFee = 0.0,\n}) {\n  var total = subtotal;\n  \n  // 应用税率\n  total += subtotal * taxRate;\n  \n  // 减去折扣\n  total -= discount;\n  \n  // 添加运费和手续费\n  total += shippingCost + handlingFee;\n  \n  return total;\n}\n\n// 7. 使用dart format命令格式化代码\n// 在命令行中运行:\n// dart format path/to/your/file.dart\n// 或者在项目根目录中运行:\n// dart format .", "explanation": "这个示例展示了Dart推荐的代码风格和格式化规范，包括导入语句的组织、类和函数的声明格式、缩进和花括号样式、控制流结构的格式、长行的拆分方法、注释和文档的规范，以及如何使用dart format命令自动格式化代码。遵循这些规范可以使代码更一致、更易读，也有助于团队协作。"}]}}, {"name": "Assignment", "trans": ["实践作业"], "usage": {"syntax": "// 基本语法实践", "description": "完成以下任务，练习Dart的基本语法：1) 创建一个Dart程序，使用main函数并接收命令行参数；2) 声明并使用不同类型的变量和常量（至少包括int、double、String、bool、List和Map）；3) 使用适当的注释（单行、多行和文档注释）为代码添加说明；4) 确保代码遵循Dart的命名约定和代码风格；5) 实现一个简单的计算器功能，可以执行基本的数学运算（加减乘除）；6) 使用dart format命令格式化你的代码。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 参考解决方案\n\n/// 简易计算器程序\n/// \n/// 这个程序演示了Dart的基本语法，包括变量、常量、数据类型、\n/// 注释和代码风格等。它实现了一个可以执行基本数学运算的计算器。\n/// \n/// 用法：dart calculator.dart 操作 数字1 数字2\n/// 例如：dart calculator.dart add 5 3\nvoid main(List<String> arguments) {\n  // 打印欢迎信息\n  print('===== Dart简易计算器 =====');\n  \n  // 检查参数数量\n  if (arguments.length != 3) {\n    printUsage();\n    return;\n  }\n  \n  // 声明变量存储参数\n  final String operation = arguments[0].toLowerCase();\n  \n  // 尝试将输入参数转换为数字\n  double? num1, num2;\n  try {\n    num1 = double.parse(arguments[1]);\n    num2 = double.parse(arguments[2]);\n  } catch (e) {\n    print('错误：请输入有效的数字');\n    printUsage();\n    return;\n  }\n  \n  /* \n   * 根据操作类型执行相应的计算\n   * 支持的操作：add(加)、subtract(减)、multiply(乘)、divide(除)\n   */\n  double? result;\n  String operationSymbol = '';\n  \n  switch (operation) {\n    case 'add':\n      result = add(num1, num2);\n      operationSymbol = '+';\n      break;\n    case 'subtract':\n      result = subtract(num1, num2);\n      operationSymbol = '-';\n      break;\n    case 'multiply':\n      result = multiply(num1, num2);\n      operationSymbol = '*';\n      break;\n    case 'divide':\n      // 检查除数是否为零\n      if (num2 == 0) {\n        print('错误：除数不能为零');\n        return;\n      }\n      result = divide(num1, num2);\n      operationSymbol = '/';\n      break;\n    default:\n      print('错误：不支持的操作 \"$operation\"');\n      printUsage();\n      return;\n  }\n  \n  // 打印计算结果\n  print('计算结果: $num1 $operationSymbol $num2 = $result');\n  \n  // 存储历史记录\n  final Map<String, dynamic> calculation = {\n    'operation': operation,\n    'num1': num1,\n    'num2': num2,\n    'result': result,\n    'timestamp': DateTime.now(),\n  };\n  \n  // 打印计算历史的内容\n  printCalculationDetails(calculation);\n}\n\n/// 打印使用说明\nvoid printUsage() {\n  print('\\n使用方法: dart calculator.dart 操作 数字1 数字2');\n  print('支持的操作:');\n  \n  // 使用列表存储支持的操作\n  final List<String> supportedOperations = [\n    'add - 加法',\n    'subtract - 减法',\n    'multiply - 乘法',\n    'divide - 除法',\n  ];\n  \n  // 遍历并打印支持的操作\n  for (final operation in supportedOperations) {\n    print('  $operation');\n  }\n  \n  print('\\n示例: dart calculator.dart add 5 3');\n}\n\n/// 打印计算详情\n/// \n/// [calculation] 包含计算的详细信息\nvoid printCalculationDetails(Map<String, dynamic> calculation) {\n  // 定义常量文本\n  const String divider = '------------------------';\n  \n  print(divider);\n  print('计算详情:');\n  print('操作: ${calculation['operation']}');\n  print('第一个数: ${calculation['num1']}');\n  print('第二个数: ${calculation['num2']}');\n  print('结果: ${calculation['result']}');\n  print('计算时间: ${calculation['timestamp']}');\n  print(divider);\n}\n\n/// 执行加法运算\n/// \n/// 接收两个数字[a]和[b]，返回它们的和\ndouble add(double a, double b) => a + b;\n\n/// 执行减法运算\n/// \n/// 接收两个数字[a]和[b]，返回a减b的差\ndouble subtract(double a, double b) => a - b;\n\n/// 执行乘法运算\n/// \n/// 接收两个数字[a]和[b]，返回它们的乘积\ndouble multiply(double a, double b) => a * b;\n\n/// 执行除法运算\n/// \n/// 接收两个数字[a]和[b]，返回a除以b的商\n/// 注意：调用前应确保b不为零\ndouble divide(double a, double b) => a / b;", "explanation": "这个参考解决方案实现了一个命令行计算器程序，演示了Dart的基本语法特性。它使用了main函数接收命令行参数，声明了不同类型的变量和常量，使用了各种注释类型，遵循了Dart的命名约定和代码风格。程序能够执行基本的数学运算（加减乘除），并包含了错误处理和使用说明。这个示例涵盖了Dart基本语法的主要方面，可以作为学习者的参考和实践模板。"}]}}]}