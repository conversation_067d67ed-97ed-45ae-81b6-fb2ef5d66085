# React学习路线与核心内容

## 1. React基础

### JSX语法        //已完成
- JSX基本语法规则
- JSX中的表达式
- JSX中的属性
- JSX中的子元素
- JSX防注入攻击
- JSX的编译转换

### 组件基础    //已完成
- 函数组件
- 类组件
- 组件的渲染过程
- 组件的导入和导出
- 组件的组合
- 组件的拆分

### Props       // 已完成
- Props的传递和接收
- Props的只读性
- 默认Props
- Props类型检查
- 通过Props传递复杂数据
- 通过Props传递函数
- 通过Props传递组件

### State        // 已完成
- 类组件中的State
- State的声明与初始化
- 修改State(setState)
- State的异步更新
- State的合并更新
- 基于之前的State更新
- 状态提升

### 生命周期    //已完成
- 挂载阶段(Mounting)
- 更新阶段(Updating)
- 卸载阶段(Unmounting)
- 错误处理阶段
- 常用生命周期方法
- 过时的生命周期方法
- 使用生命周期的最佳实践

### 事件处理    //已完成
- React事件系统
- 事件处理函数绑定
- 向事件处理程序传递参数
- 合成事件对象
- 事件委托
- 常见事件类型处理

### 条件渲染     //已完成
- if语句条件渲染
- 三元表达式条件渲染
- 逻辑与(&&)条件渲染
- 变量控制条件渲染
- 阻止组件渲染
- 条件渲染最佳实践

### 列表与Keys   //已完成
- 使用map()渲染列表
- key的作用和重要性
- 正确的key使用方式
- 索引作为key的问题
- 列表项组件提取
- 列表性能优化

### 表单处理     //已完成
- 受控组件
- 非受控组件
- 表单元素类型
- 处理多个输入
- 表单提交
- 表单验证
- 复杂表单解决方案

## 2. React Hooks

### useState     //已完成
- 状态声明和初始化
- 状态更新
- 函数式更新
- 对象和数组状态处理
- 惰性初始化
- 状态重置

### useEffect    //已完成
- 效果执行时机
- 依赖数组
- 清除副作用
- 条件执行副作用
- 数据获取
- 订阅管理
- DOM操作
- 常见陷阱

### useContext   //已完成
- Context创建
- 提供者(Provider)
- 消费者(Consumer)
- 跨组件状态共享
- 性能考虑
- 与其他Hooks组合

### useReducer   //已完成
- reducer函数
- 初始化状态
- 分发动作(dispatch)
- 复杂状态管理
- 与useState对比
- 惰性初始化
- 结合Context使用

### useCallback   //已完成
- 函数记忆化
- 依赖数组
- 性能优化场景
- 与useEffect配合
- 常见错误
- 最佳实践

### useMemo  //已完成
- 值记忆化
- 依赖数组
- 计算属性
- 复杂计算优化
- 与useCallback对比
- 最佳实践

### useRef  //已完成
- 引用DOM元素
- 存储可变值
- 保存前一个值
- 与useEffect结合
- 实现命令式操作
- 避免的陷阱

### 自定义Hooks //已完成
- 创建自定义Hook
- 逻辑复用
- 状态隔离
- 常见自定义Hook示例
- 测试自定义Hook
- 发布和共享

## 3. 组件模式

### 组合组件        //已完成
- children属性
- 特殊化组件
- 组合VS继承
- 组件包装技巧
- 布局组件
- 容器组件

### 高阶组件(HOC)   //已完成
- HOC概念和用途
- 创建HOC
- 属性传递
- 显示名称处理
- 组合多个HOC
- 常见HOC示例
- HOC注意事项

### Render Props模式    //已完成
- render props概念
- 实现render props
- 控制渲染逻辑
- 与HOC对比
- 组合render props
- 性能考虑

### 复合组件    //已完成
- 复合组件模式
- 内部状态共享
- 组件API设计
- 上下文结合
- 实现可定制性
- 实际应用示例

### 受控与非受控组件    //已完成
- 受控组件原则
- 非受控组件应用场景
- 表单处理模式
- 实现受控组件
- 实现非受控组件
- 选择合适的模式

### Context API         //已完成
- 受控组件原则
- Context设计模式
- 创建和提供Context
- 消费Context
- 动态Context
- 嵌套Context
- 性能优化

### Suspense与错误边界      //已完成
- Suspense原理
- 数据加载处理
- 错误边界实现
- 错误捕获和展示
- 错误恢复策略
- 优雅降级

## 4. 状态管理

### React内置状态管理       //已完成
- 组件本地状态
- 状态提升
- props向下传递
- 兄弟组件通信
- 跨层级通信
- 状态设计原则

### Context + useReducer             //已完成
- 全局状态管理
- reducer设计
- action类型定义
- 状态分割
- 中间件模式
- 异步操作处理

### Redux基础            //已完成
- Store概念
- Action定义
- Reducer编写
- 中间件使用
- 连接React组件
- 异步处理
- Redux开发工具

### Redux工具包(Redux Toolkit)      //已完成
- 配置Store
- createSlice API
- 创建Reducer和Action
- createAsyncThunk
- RTK Query
- 不可变更新逻辑
- 性能优化

### Zustand         //已完成
- 创建Store
- 状态读取和更新
- 异步操作
- 状态持久化
- 中间件
- 与Redux对比
- 实际应用示例

### Recoil          //已完成
- 原子(Atom)
- 选择器(Selector)
- 状态共享
- 异步数据处理
- 状态持久化
- 状态同步
- 与Context对比

### Jotai/Valtio          //已完成
- 原子化状态
- 代理状态
- 衍生状态
- 异步状态
- 与其他状态库对比
- 适用场景分析

## 5. 路由

### React Router基础        //已完成
- 安装和设置
- 路由类型
- 路由匹配
- 导航链接
- 路由渲染
- 编程式导航
- URL参数

### 嵌套路由        //已完成
- 路由嵌套设计
- 嵌套路由配置
- 嵌套路由渲染
- 路由组合
- 共享布局
- 索引路由

### 动态路由        //已完成
- 路径参数
- 查询参数
- 可选参数
- 参数验证
- 通配符路由
- 路由优先级

### 路由守卫        //已完成
- 路由拦截
- 身份验证
- 权限控制
- 重定向
- 导航确认
- 路由生命周期

### 懒加载与代码分割    //已完成
- React.lazy()
- Suspense集成
- 路由级代码分割
- 预加载策略
- 加载状态处理
- 错误处理

## 6. 性能优化

### React.memo       //已完成
- 组件记忆化
- 自定义比较函数
- 适用场景
- 与类组件shouldComponentUpdate对比
- 优化策略
- 过度优化问题

### 懒加载组件       //已完成
- 动态导入
- 条件加载
- 预加载技术
- 加载边界
- 失败处理
- 用户体验考虑

### 虚拟列表        //已完成
- 虚拟列表原理
- 实现虚拟滚动
- 动态高度处理
- 滚动位置恢复
- 常用虚拟列表库
- 性能测试和调优

### 缓存策略        //已完成
- 数据缓存
- 请求缓存
- 组件缓存
- 计算结果缓存
- 缓存失效策略
- 持久化缓存

### 避免不必要的渲染        //已完成
- 渲染优化原则
- 使用React DevTools分析
- 重渲染原因识别
- 状态设计优化
- 组件分割策略
- 批量更新

### 性能分析工具             //已完成
- React Profiler
- Chrome Performance面板
- Lighthouse
- 网络请求分析
- 渲染性能分析
- 内存泄漏排查

## 7. 测试

### 单元测试             //已完成
- 测试环境设置
- 测试库选择
- 组件单元测试
- Hooks测试
- 函数测试
- 异步代码测试
- 模拟外部依赖

### 组件测试            //已完成
- 组件渲染测试
- 交互测试
- 快照测试
- 事件测试
- 状态变化测试
- 属性验证

### 集成测试        //已完成
- 组件集成
- 路由测试
- 状态管理测试
- API交互测试
- 用户流程测试
- 测试覆盖率

### React Testing Library       //已完成
- 渲染组件
- 查询元素
- 触发事件
- 断言结果
- 异步测试
- 最佳实践

### Jest与RTL的结合使用         //已完成
- 测试结构
- 模拟函数
- 快照测试
- 钩子使用
- 测试覆盖率报告
- 持续集成

## 8. React生态

### Next.js                 //已完成
- 服务端渲染(SSR)
- 静态站点生成(SSG)
- 增量静态再生成(ISR)
- API路由
- 文件系统路由
- 图像优化
- 布局和页面

### TypeScript与React       //已完成
- 类型定义
- 组件类型
- Props类型
- Hooks类型
- 事件类型
- 通用组件
- 类型断言

### React Query/SWR             //已完成
- 数据获取
- 缓存管理
- 请求状态
- 乐观更新
- 无限加载
- 轮询和重试
- 预取数据

### Styled Components/Emotion           //已完成
- 组件样式化
- 主题设计
- 动态样式
- 全局样式
- 样式复用
- CSS-in-JS性能
- 服务端渲染支持

### Framer Motion/React Spring          //已完成
- 动画基础
- 转场动画
- 手势交互
- 页面转场
- 微交互
- 性能优化
- 可访问性考虑

### React DevTools              //已完成
- 组件检查
- Props和State查看
- 性能分析
- 调试Hooks
- 组件过滤
- 时间旅行调试
- 网络环境模拟 