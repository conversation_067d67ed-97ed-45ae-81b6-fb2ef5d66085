{"name": "Package and Deploy", "trans": ["打包与部署"], "methods": [{"name": "Maven/Gradle Packaging", "trans": ["Ma<PERSON>/Gradle打包"], "usage": {"syntax": "mvn clean package 或 gradle build", "description": "Spring Boot支持Maven和Gradle两种主流构建工具，分别通过mvn clean package和gradle build命令进行打包，生成可执行JAR或WAR文件。", "parameters": [{"name": "mvn clean package", "description": "Maven打包命令"}, {"name": "gradle build", "description": "Gradle打包命令"}], "returnValue": "target或build/libs目录下的JAR/WAR包", "examples": [{"code": "# Maven打包\nmvn clean package\n# Gradle打包\ngradle build", "explanation": "分别展示了Maven和Gradle的打包命令。"}]}}, {"name": "Executable JAR/WAR", "trans": ["可执行JAR/WAR"], "usage": {"syntax": "java -jar app.jar 或 部署到Servlet容器", "description": "Spring Boot默认生成可执行JAR包，支持直接运行。也可配置为WAR包部署到Tomcat等Servlet容器。", "parameters": [{"name": "java -jar", "description": "运行可执行JAR包"}, {"name": "WAR包", "description": "适用于传统Servlet容器部署"}], "returnValue": "启动的Spring Boot应用实例", "examples": [{"code": "# 运行JAR包\njava -jar target/app.jar\n# 部署WAR包\n将WAR包放入Tomcat的webapps目录", "explanation": "展示了JAR和WAR两种部署方式。"}]}}, {"name": "External Config and Environment Variables", "trans": ["外部配置与环境变量"], "usage": {"syntax": "--spring.config.location= 或 环境变量SPRING_PROFILES_ACTIVE", "description": "Spring Boot支持通过命令行参数、环境变量、外部配置文件等多种方式灵活配置应用环境。", "parameters": [{"name": "--spring.config.location", "description": "指定外部配置文件路径"}, {"name": "SPRING_PROFILES_ACTIVE", "description": "激活指定Profile环境"}], "returnValue": "应用启动时加载的外部配置", "examples": [{"code": "# 指定外部配置文件\njava -jar app.jar --spring.config.location=/etc/app/application-prod.yml\n# 设置环境变量\nexport SPRING_PROFILES_ACTIVE=prod", "explanation": "展示了外部配置和环境变量的常用用法。"}]}}, {"name": "Log Management (Logback/Log4j2)", "trans": ["日志管理（Logback/Log4j2）"], "usage": {"syntax": "logback-spring.xml或log4j2-spring.xml配置文件", "description": "Spring Boot默认集成Logback，也支持Log4j2。可通过配置文件自定义日志格式、级别、输出位置等。", "parameters": [{"name": "logback-spring.xml", "description": "Logback日志配置文件"}, {"name": "log4j2-spring.xml", "description": "Log4j2日志配置文件"}], "returnValue": "自定义的日志输出效果", "examples": [{"code": "# logback-spring.xml示例\n<configuration>\n  <appender name=\"STDOUT\" class=\"ch.qos.logback.core.ConsoleAppender\"/>\n  <root level=\"INFO\">\n    <appender-ref ref=\"STDOUT\"/>\n  </root>\n</configuration>", "explanation": "展示了Logback日志配置的基本用法。"}]}}, {"name": "Process Management (systemd, Supervisor)", "trans": ["进程管理（systemd、Supervisor）"], "usage": {"syntax": "systemd服务单元或supervisor配置文件", "description": "生产环境可通过systemd或Supervisor等工具管理Spring Boot进程，实现自动重启、日志管理等。", "parameters": [{"name": "systemd", "description": "Linux服务管理工具"}, {"name": "Supervisor", "description": "进程守护与管理工具"}], "returnValue": "受控的Spring Boot应用进程", "examples": [{"code": "# systemd服务单元示例\n[Unit]\nDescription=Spring Boot App\n[Service]\nExecStart=/usr/bin/java -jar /opt/app/app.jar\nRestart=always\n[Install]\nWantedBy=multi-user.target\n\n# supervisor配置示例\n[program:springboot]\ncommand=java -jar /opt/app/app.jar\nautorestart=true", "explanation": "展示了systemd和Supervisor管理Spring Boot进程的配置。"}]}}, {"name": "Package and Deploy Assignment", "trans": ["打包与部署练习"], "usage": {"syntax": "# 打包与部署练习", "description": "完成以下练习，巩固Spring Boot打包与部署相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 使用Maven或Gradle打包Spring Boot应用。\n2. 以JAR和WAR两种方式部署应用。\n3. 配置外部application.yml和环境变量。\n4. 自定义日志输出格式。\n5. 使用systemd或Supervisor管理Spring Boot进程。", "explanation": "通过这些练习掌握Spring Boot打包与部署的核心用法。"}]}}]}