{"name": "Suspense and Error Boundaries", "trans": ["Suspense与错误边界"], "methods": [{"name": "Suspense Principle", "trans": ["Suspense原理"], "usage": {"syntax": "<Suspense fallback={<Loading />}>\n  <LazyComponent />\n</Suspense>", "description": "Suspense用于等待异步操作（如懒加载组件、数据请求）完成后再渲染UI。通过fallback属性指定加载时的占位内容。", "parameters": [{"name": "fallback", "description": "加载等待时显示的占位内容"}, {"name": "children", "description": "需要等待的异步子组件"}], "returnValue": "渲染异步内容或占位内容", "examples": [{"code": "import React, { Suspense, lazy } from 'react';\n// 懒加载组件\nconst LazyComponent = lazy(() => import('./MyComponent'));\n\nfunction App() {\n  return (\n    // Suspense包裹懒加载组件，fallback为加载时显示的内容\n    <Suspense fallback={<div>加载中...</div>}>\n      <LazyComponent />\n    </Suspense>\n  );\n}", "explanation": "Suspense等待LazyComponent加载完成后再渲染，期间显示加载中。"}]}}, {"name": "Data Loading with Suspense", "trans": ["数据加载处理"], "usage": {"syntax": "<Suspense fallback={<Loading />}>\n  <DataComponent />\n</Suspense>", "description": "结合React 18的Suspense和数据获取库（如React Query、Relay），可实现数据请求的等待和占位渲染。", "parameters": [{"name": "fallback", "description": "加载等待时显示的占位内容"}, {"name": "children", "description": "需要等待数据的子组件"}], "returnValue": "渲染数据加载完成后的内容或占位内容", "examples": [{"code": "// 伪代码示例，实际需配合数据库\nimport { Suspense } from 'react';\nfunction DataComponent() {\n  // 假设useData会抛出Promise，Suspense会捕获并显示fallback\n  const data = useData();\n  return <div>数据: {data}</div>;\n}\nfunction App() {\n  return (\n    <Suspense fallback={<div>数据加载中...</div>}>\n      <DataComponent />\n    </Suspense>\n  );\n}", "explanation": "Suspense可用于等待数据加载，配合数据请求库实现。"}]}}, {"name": "Implementing Error <PERSON>", "trans": ["错误边界实现"], "usage": {"syntax": "class ErrorBoundary extends React.Component {\n  static getDerivedStateFromError(error) { ... }\n  componentDidCatch(error, info) { ... }\n  render() { ... }\n}", "description": "错误边界用于捕获子组件渲染、生命周期、构造函数中的JS错误，防止整个应用崩溃。只能用于class组件。", "parameters": [{"name": "children", "description": "需要错误保护的子组件"}], "returnValue": "渲染正常内容或错误提示UI", "examples": [{"code": "import React from 'react';\n// 错误边界组件\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    // 初始化state，hasError为false\n    this.state = { hasError: false };\n  }\n  // 捕获子组件抛出的错误，更新state\n  static getDerivedStateFromError(error) {\n    return { hasError: true };\n  }\n  // 可用于上报错误信息\n  componentDidCatch(error, info) {\n    console.error('捕获到错误:', error, info);\n  }\n  render() {\n    if (this.state.hasError) {\n      // 渲染错误提示UI\n      return <div>出错了，请稍后重试。</div>;\n    }\n    // 正常渲染子组件\n    return this.props.children;\n  }\n}\n// 用法\nfunction App() {\n  return (\n    <ErrorBoundary>\n      <MyComponent />\n    </ErrorBoundary>\n  );\n}", "explanation": "ErrorBoundary可捕获子组件的渲染错误并展示友好提示。"}]}}, {"name": "Error Capturing and Display", "trans": ["错误捕获和展示"], "usage": {"syntax": "componentDidCatch(error, info) { ... }", "description": "通过componentDidCatch方法捕获错误并展示自定义UI，可结合日志上报和用户提示。", "parameters": [{"name": "error", "description": "捕获到的错误对象"}, {"name": "info", "description": "错误发生的组件栈信息"}], "returnValue": "渲染错误提示或日志上报", "examples": [{"code": "class ErrorBoundary extends React.Component {\n  // ...省略构造和getDerivedStateFromError\n  componentDidCatch(error, info) {\n    // 打印错误信息\n    console.error('错误:', error);\n    console.error('组件栈:', info.componentStack);\n    // 可以在此发送错误日志到服务器\n  }\n  render() {\n    if (this.state.hasError) {\n      return <div>页面发生错误</div>;\n    }\n    return this.props.children;\n  }\n}", "explanation": "componentDidCatch可捕获错误并展示自定义UI。"}]}}, {"name": "Error Recovery Strategies", "trans": ["错误恢复策略"], "usage": {"syntax": "// 通过重置state、刷新页面等方式恢复", "description": "可通过重置错误边界state、局部刷新、降级渲染等方式恢复应用。", "parameters": [], "returnValue": "恢复后的正常UI或降级UI", "examples": [{"code": "class ErrorBoundary extends React.Component {\n  // ...省略构造和componentDidCatch\n  // 提供重置错误的方法\n  resetError = () => {\n    this.setState({ hasError: false });\n  };\n  render() {\n    if (this.state.hasError) {\n      return (\n        <div>\n          出错了 <button onClick={this.resetError}>重试</button>\n        </div>\n      );\n    }\n    return this.props.children;\n  }\n}", "explanation": "通过重置state实现错误恢复，支持用户手动重试。"}]}}, {"name": "Graceful Degradation", "trans": ["优雅降级"], "usage": {"syntax": "// 通过备用UI、降级功能等方式保证可用性", "description": "当部分功能不可用时，通过降级UI、提示、备用方案等方式保证核心功能可用。", "parameters": [], "returnValue": "降级后的可用UI", "examples": [{"code": "function ImageWithFallback({ src, alt }) {\n  const [error, setError] = React.useState(false);\n  if (error) {\n    // 加载失败时显示占位图\n    return <img src=\"/fallback.png\" alt=\"占位图\" />;\n  }\n  return (\n    <img src={src} alt={alt} onError={() => setError(true)} />\n  );\n}", "explanation": "图片加载失败时优雅降级为占位图。"}]}}, {"name": "作业：实现一个带错误边界的异步组件加载", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 用Suspense实现异步组件加载。\n// 2. 用ErrorBoundary捕获加载错误并展示提示。\n// 3. 代码结构清晰，注释完整。", "description": "请实现上述功能，提交时请附上完整代码和注释。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 示例代码略，请学生自行实现\n// 提示：用lazy、Suspense和ErrorBoundary组合。", "explanation": "作业要求学生掌握Suspense和错误边界的组合应用。"}, {"code": "import React, { Suspense, lazy } from 'react';\n// 懒加载组件\nconst LazyComponent = lazy(() => import('./MyComponent'));\n// 错误边界组件\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { hasError: false };\n  }\n  static getDerivedStateFromError(error) {\n    return { hasError: true };\n  }\n  componentDidCatch(error, info) {\n    console.error('捕获到错误:', error, info);\n  }\n  render() {\n    if (this.state.hasError) {\n      return <div>加载失败，请重试。</div>;\n    }\n    return this.props.children;\n  }\n}\n// 用法\nfunction App() {\n  return (\n    <ErrorBoundary>\n      <Suspense fallback={<div>加载中...</div>}>\n        <LazyComponent />\n      </Suspense>\n    </ErrorBoundary>\n  );\n}", "explanation": "完整实现带错误边界的异步组件加载。"}]}}]}