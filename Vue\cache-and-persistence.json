{"name": "Cache and Persistence", "trans": ["缓存与持久化"], "methods": [{"name": "keep-alive", "trans": ["keep-alive"], "usage": {"syntax": "<keep-alive>\n  <router-view />\n</keep-alive>", "description": "keep-alive是Vue内置组件，用于缓存动态组件或路由组件，切换时保留状态，提升用户体验。", "parameters": [{"name": "include/exclude", "description": "指定需要缓存或不缓存的组件名"}, {"name": "max", "description": "最大缓存组件数"}], "returnValue": "被缓存的组件实例，切换时状态保留", "examples": [{"code": "<keep-alive :include=\"['A', 'B']\" :max=\"5\">\n  <component :is=\"current\" />\n</keep-alive>", "explanation": "只缓存A和B组件，最多缓存5个实例。"}]}}, {"name": "localStorage/sessionStorage", "trans": ["localStorage/sessionStorage"], "usage": {"syntax": "localStorage.setItem('key', value)\nlocalStorage.getItem('key')\nsessionStorage.setItem('key', value)", "description": "localStorage和sessionStorage用于在浏览器本地持久化数据，localStorage长期保存，sessionStorage会话级保存。", "parameters": [{"name": "setItem/getItem", "description": "存取本地数据的方法"}], "returnValue": "本地持久化的数据值", "examples": [{"code": "localStorage.setItem('token', 'abc')\nconst token = localStorage.getItem('token')", "explanation": "将token保存到localStorage并读取。"}]}}, {"name": "IndexedDB", "trans": ["IndexedDB"], "usage": {"syntax": "const db = indexedDB.open('mydb', 1)", "description": "IndexedDB是浏览器内建的NoSQL数据库，适合存储大量结构化数据，支持事务和索引。", "parameters": [{"name": "open", "description": "打开数据库的方法"}, {"name": "objectStore", "description": "数据表对象"}], "returnValue": "本地数据库实例，可存取结构化数据", "examples": [{"code": "const db = indexedDB.open('mydb', 1)\ndb.onsuccess = e => {\n  const database = e.target.result\n  const tx = database.transaction('users', 'readwrite')\n  const store = tx.objectStore('users')\n  store.add({ id: 1, name: '张三' })\n}", "explanation": "用IndexedDB存储结构化用户数据。"}]}}, {"name": "Data Caching Strategy", "trans": ["数据缓存策略"], "usage": {"syntax": "// 先查缓存再查接口\nconst cache = localStorage.getItem('data')\nif (!cache) {\n  fetch('/api').then(...)\n}", "description": "常见缓存策略有先查缓存再查接口、定时刷新、失效淘汰等，合理设计可提升性能和体验。", "parameters": [{"name": "缓存优先/接口优先", "description": "缓存与接口的优先级策略"}, {"name": "失效机制", "description": "缓存过期与淘汰"}], "returnValue": "高效的数据获取与缓存方案", "examples": [{"code": "const cache = localStorage.getItem('user')\nif (cache) {\n  return JSON.parse(cache)\n} else {\n  fetch('/api/user').then(...)\n}", "explanation": "优先读取缓存，无缓存时再请求接口。"}]}}, {"name": "Cache and Persistence Assignment", "trans": ["缓存与持久化练习"], "usage": {"syntax": "# 缓存与持久化练习", "description": "完成以下练习，巩固缓存与持久化相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用keep-alive缓存组件状态。\n2. 用localStorage保存用户信息。\n3. 用IndexedDB存储结构化数据。\n4. 实现缓存优先的数据获取策略。", "explanation": "通过这些练习掌握前端缓存与持久化方案。"}]}}]}