{"name": "Advanced Application Exercise", "trans": ["高级应用练习"], "methods": [{"name": "Auto Assign Student ID Procedure", "trans": ["自动分配学号存储过程"], "usage": {"syntax": "DELIMITER //\nCREATE PROCEDURE insert_student_auto_id(IN s_name VARCHAR(50), IN s_score INT)\nBEGIN\n  DECLARE new_id INT;\n  SELECT IFNULL(MAX(student_id), 1000) + 1 INTO new_id FROM student;\n  INSERT INTO student(student_id, name, score) VALUES(new_id, s_name, s_score);\nEND //\nDELIMITER ;", "description": "本练习演示如何编写一个存储过程，在插入新学生时自动分配递增学号（如从1001开始），避免手动指定主键。", "parameters": [{"name": "s_name", "description": "学生姓名"}, {"name": "s_score", "description": "学生成绩"}], "returnValue": "无返回值（新学生记录插入student表）", "examples": [{"code": "-- 创建自动分配学号的存储过程\nDELIMITER //\nCREATE PROCEDURE insert_student_auto_id(IN s_name VARCHAR(50), IN s_score INT)\nBEGIN\n  DECLARE new_id INT;\n  SELECT IFNULL(MAX(student_id), 1000) + 1 INTO new_id FROM student;\n  INSERT INTO student(student_id, name, score) VALUES(new_id, s_name, s_score);\nEND //\nDELIMITER ;\n\n-- 调用存储过程插入新学生\nCALL insert_student_auto_id('张三', 88);", "explanation": "本例创建了一个插入学生时自动分配学号的存储过程，并演示了如何调用。"}]}}, {"name": "Advanced Application Assignment", "trans": ["高级应用练习题"], "usage": {"syntax": "# 高级应用练习题", "description": "完成以下练习，巩固MySQL存储过程与自动主键分配的高级应用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 编写存储过程insert_student_auto_id，自动为新插入的学生分配学号。\n2. 调用该过程插入多名学生，验证学号自动递增且唯一。\n3. 总结自动主键分配的优点和注意事项。", "explanation": "通过这些练习，你将掌握MySQL存储过程与自动主键分配的实际开发技巧。"}]}}]}