{"name": "Nested Routes", "trans": ["嵌套路由"], "methods": [{"name": "Route Nesting Design", "trans": ["路由嵌套设计"], "usage": {"syntax": "// React Router v6 嵌套路由设计\n<Routes>\n  <Route path=\"/\" element={<Layout />}>\n    <Route index element={<Home />} />\n    <Route path=\"products\" element={<Products />}>\n      <Route path=\":id\" element={<ProductDetail />} />\n    </Route>\n  </Route>\n</Routes>", "description": "嵌套路由是React Router的核心功能，允许将路由组织成层次结构，反映应用的UI层次结构。父路由可以包含子路由，共享布局和路径前缀。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 嵌套路由设计示例\n// 路由结构：\n// / (布局) - 共享导航和页脚\n// ├── / (首页)\n// ├── /dashboard - 仪表盘布局\n// │   ├── /dashboard (默认视图)\n// │   ├── /dashboard/stats - 统计数据\n// │   └── /dashboard/settings - 设置页面\n// └── /products - 产品布局\n//     ├── /products (产品列表)\n//     └── /products/:id - 产品详情\n\nimport { Routes, Route } from 'react-router-dom';\nimport Layout from './components/Layout';\nimport Home from './pages/Home';\nimport Dashboard from './pages/Dashboard';\nimport DashboardHome from './pages/DashboardHome';\nimport Stats from './pages/Stats';\nimport Settings from './pages/Settings';\nimport Products from './pages/Products';\nimport ProductDetail from './pages/ProductDetail';\n\n// 应用路由配置\nfunction AppRoutes() {\n  return (\n    <Routes>\n      {/* 根布局路由 */}\n      <Route path=\"/\" element={<Layout />}>\n        {/* 首页 - 索引路由 */}\n        <Route index element={<Home />} />\n        \n        {/* 仪表盘嵌套路由 */}\n        <Route path=\"dashboard\" element={<Dashboard />}>\n          <Route index element={<DashboardHome />} />\n          <Route path=\"stats\" element={<Stats />} />\n          <Route path=\"settings\" element={<Settings />} />\n        </Route>\n        \n        {/* 产品嵌套路由 */}\n        <Route path=\"products\" element={<Products />}>\n          <Route path=\":id\" element={<ProductDetail />} />\n        </Route>\n      </Route>\n    </Routes>\n  );\n}", "explanation": "展示了一个完整的嵌套路由设计，包含主布局、仪表盘和产品模块的嵌套路由结构。"}]}}, {"name": "Nested Routes Configuration", "trans": ["嵌套路由配置"], "usage": {"syntax": "// 父路由\n<Route path=\"parent\" element={<ParentComponent />}>\n  {/* 子路由 */}\n  <Route path=\"child\" element={<ChildComponent />} />\n</Route>", "description": "在React Router v6中，嵌套路由通过将Route组件作为父Route的子元素进行配置。子路由的path是相对于父路由的，实际路径是父路由路径加上子路由路径。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import { Routes, Route } from 'react-router-dom';\n\n// 嵌套路由配置示例\nfunction AppRoutes() {\n  return (\n    <Routes>\n      {/* 根路由 */}\n      <Route path=\"/\" element={<Layout />}>\n        \n        {/* 绝对路径 - /about */}\n        <Route path=\"about\" element={<About />} />\n        \n        {/* 嵌套路由 - /shop */}\n        <Route path=\"shop\" element={<Shop />}>\n          {/* 索引路由 - /shop */}\n          <Route index element={<ShopHome />} />\n          \n          {/* /shop/categories */}\n          <Route path=\"categories\" element={<Categories />}>\n            {/* /shop/categories 默认视图 */}\n            <Route index element={<CategoryList />} />\n            \n            {/* /shop/categories/:categoryId */}\n            <Route path=\":categoryId\" element={<CategoryDetail />} />\n          </Route>\n          \n          {/* /shop/cart */}\n          <Route path=\"cart\" element={<Cart />} />\n        </Route>\n        \n        {/* 通配符路由，匹配任何未匹配的路径 */}\n        <Route path=\"*\" element={<NotFound />} />\n      </Route>\n    </Routes>\n  );\n}\n\n// 每个路由组件都需要使用Outlet组件来渲染子路由\nimport { Outlet } from 'react-router-dom';\n\nfunction Layout() {\n  return (\n    <div className=\"app-layout\">\n      <header>全局导航</header>\n      <main>\n        {/* 子路由在这里渲染 */}\n        <Outlet />\n      </main>\n      <footer>页脚</footer>\n    </div>\n  );\n}\n\nfunction Shop() {\n  return (\n    <div className=\"shop-layout\">\n      <aside>商店侧边栏</aside>\n      <div className=\"shop-content\">\n        {/* 子路由在这里渲染 */}\n        <Outlet />\n      </div>\n    </div>\n  );\n}", "explanation": "展示了如何配置嵌套路由，包括多层嵌套、索引路由和动态路由参数，以及如何在布局组件中使用Outlet渲染子路由。"}]}}, {"name": "Nested Routes Rendering", "trans": ["嵌套路由渲染"], "usage": {"syntax": "// 在父组件中使用Outlet渲染子路由\nimport { Outlet } from 'react-router-dom';\n\nfunction Parent() {\n  return (\n    <div>\n      <h1>父组件内容</h1>\n      <Outlet /> {/* 子路由在这里渲染 */}\n    </div>\n  );\n}", "description": "嵌套路由的渲染通过Outlet组件实现，它是一个占位符，表示子路由内容应该渲染的位置。父路由组件可以提供共享UI元素，如导航、侧边栏等。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import { Outlet, useParams, useOutletContext } from 'react-router-dom';\n\n// 应用布局 - 顶层父路由\nfunction AppLayout() {\n  // 可以提供给子组件的共享状态或函数\n  const [user, setUser] = useState(null);\n  \n  return (\n    <div className=\"app\">\n      <header className=\"app-header\">\n        <nav>{/* 导航菜单 */}</nav>\n      </header>\n      \n      <main className=\"app-content\">\n        {/* \n          所有子路由在这里渲染\n          同时可以传递context给子路由使用\n        */}\n        <Outlet context={{ user, setUser }} />\n      </main>\n      \n      <footer className=\"app-footer\">\n        {/* 页脚内容 */}\n      </footer>\n    </div>\n  );\n}\n\n// 用户资料页面 - 二级父路由\nfunction UserProfile() {\n  const { userId } = useParams();\n  // 从父级Outlet获取上下文\n  const { user } = useOutletContext();\n  \n  return (\n    <div className=\"user-profile\">\n      <aside className=\"profile-sidebar\">\n        <nav className=\"profile-nav\">\n          <ul>\n            <li><Link to={`/users/${userId}`}>个人信息</Link></li>\n            <li><Link to={`/users/${userId}/posts`}>用户文章</Link></li>\n            <li><Link to={`/users/${userId}/settings`}>账户设置</Link></li>\n          </ul>\n        </nav>\n      </aside>\n      \n      <section className=\"profile-content\">\n        {/* 子路由在这里渲染 */}\n        <Outlet context={{ userId }} />\n      </section>\n    </div>\n  );\n}\n\n// 用户文章页面 - 三级子路由\nfunction UserPosts() {\n  // 获取路由参数\n  const { userId } = useParams();\n  // 获取从父Outlet传递的上下文\n  const { user } = useOutletContext();\n  \n  return (\n    <div className=\"user-posts\">\n      <h2>{user?.name || userId}的文章</h2>\n      {/* 文章列表 */}\n    </div>\n  );\n}", "explanation": "展示了如何使用Outlet组件渲染嵌套路由，以及如何通过useOutletContext钩子在不同层级的路由组件间传递数据。"}]}}, {"name": "Route Composition", "trans": ["路由组合"], "usage": {"syntax": "// 通过嵌套Route实现路由组合\n<Route path=\"/parent\" element={<Parent />}>\n  <Route path=\"child1\" element={<Child1 />} />\n  <Route path=\"child2\" element={<Child2 />} />\n</Route>", "description": "路由组合是指将多个子路由组织在同一个父路由下，形成清晰的层级结构。这样可以让不同的页面或功能模块共享父级的布局和逻辑。", "parameters": [{"name": "path", "description": "路由路径，子路由的path为相对路径。"}, {"name": "element", "description": "对应路由渲染的React组件。"}, {"name": "Parent", "description": "父级组件，通常包含<Outlet />用于渲染子路由。"}, {"name": "Child1/Child2", "description": "子路由组件。"}], "returnValue": "无返回值", "examples": [{"code": "// 路由组合示例\nimport { Routes, Route, Outlet } from 'react-router-dom';\n\nfunction Parent() {\n  return (\n    <div>\n      <h2>父级内容</h2>\n      <Outlet /> {/* 子路由内容在这里渲染 */}\n    </div>\n  );\n}\n\nfunction AppRoutes() {\n  return (\n    <Routes>\n      <Route path=\"/parent\" element={<Parent />}>\n        <Route path=\"child1\" element={<div>子页面1</div>} />\n        <Route path=\"child2\" element={<div>子页面2</div>} />\n      </Route>\n    </Routes>\n  );\n}", "explanation": "父路由/parent下组合了两个子路由child1和child2，子页面内容通过Outlet渲染。"}]}}, {"name": "Assignment: 路由组合", "trans": ["练习：路由组合"], "usage": {"syntax": "// 请实现一个父路由，包含两个子路由：/profile/info 和 /profile/settings", "description": "要求：父组件名为Profile，子组件分别为ProfileInfo和ProfileSettings，使用嵌套路由实现。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 只需写出路由配置部分即可", "explanation": "练习题，无需标准答案。"}]}}, {"name": "Shared Layout", "trans": ["共享布局"], "usage": {"syntax": "// 在父组件中实现共享布局\nfunction Layout() {\n  return (\n    <div>\n      <header>头部</header>\n      <main>\n        <Outlet /> {/* 子路由内容 */}\n      </main>\n      <footer>底部</footer>\n    </div>\n  );\n}", "description": "共享布局是指父路由组件中包含通用的UI元素（如导航、头部、底部），子路由内容通过<Outlet />渲染在指定位置，实现页面结构复用。", "parameters": [{"name": "Outlet", "description": "React Router提供的组件，用于渲染子路由内容。"}, {"name": "header/footer/main", "description": "页面的通用结构部分。"}], "returnValue": "无返回值", "examples": [{"code": "// 共享布局示例\nimport { Outlet } from 'react-router-dom';\n\nfunction Layout() {\n  return (\n    <div>\n      <header>导航栏</header>\n      <main>\n        <Outlet /> {/* 子页面内容 */}\n      </main>\n      <footer>版权信息</footer>\n    </div>\n  );\n}", "explanation": "Layout组件作为父路由，包含头部、内容区和底部，子路由内容通过Outlet渲染。"}]}}, {"name": "Assignment: 共享布局", "trans": ["练习：共享布局"], "usage": {"syntax": "// 实现一个带有头部和底部的共享布局组件Layout，并通过嵌套路由渲染子页面", "description": "要求：Layout组件包含<header>和<footer>，子页面通过<Outlet />渲染。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 只需写出Layout组件和路由配置部分", "explanation": "练习题，无需标准答案。"}]}}, {"name": "Index Route", "trans": ["索引路由"], "usage": {"syntax": "<Route index element={<Home />} />", "description": "索引路由用于指定父路由下的默认子页面。index属性为true时，表示当父路由路径被访问但没有匹配到其他子路由时，渲染该组件。", "parameters": [{"name": "index", "description": "布尔值，表示该路由为索引路由。"}, {"name": "element", "description": "要渲染的React组件。"}, {"name": "Home", "description": "作为默认页面的组件。"}], "returnValue": "无返回值", "examples": [{"code": "// 索引路由示例\n<Routes>\n  <Route path=\"/dashboard\" element={<DashboardLayout />}>\n    <Route index element={<DashboardHome />} />\n    <Route path=\"stats\" element={<Stats />} />\n  </Route>\n</Routes>", "explanation": "访问/dashboard时，默认渲染DashboardHome组件；访问/dashboard/stats时，渲染Stats组件。"}]}}, {"name": "Assignment: 索引路由", "trans": ["练习：索引路由"], "usage": {"syntax": "// 在/products父路由下，设置ProductList为索引路由，ProductDetail为动态子路由", "description": "要求：访问/products时显示ProductList，访问/products/:id时显示ProductDetail。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 只需写出路由配置部分", "explanation": "练习题，无需标准答案。"}]}}, {"name": "Correct Implementation Example", "trans": ["正确实现示例"], "usage": {"syntax": "// 综合嵌套路由、共享布局和索引路由的完整实现", "description": "本示例展示了如何在实际项目中结合使用嵌套路由、共享布局和索引路由，形成清晰的页面结构。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 完整实现示例\nimport { Routes, Route, Outlet } from 'react-router-dom';\n\nfunction Layout() {\n  return (\n    <div>\n      <header>导航栏</header>\n      <main>\n        <Outlet />\n      </main>\n      <footer>版权信息</footer>\n    </div>\n  );\n}\n\nfunction AppRoutes() {\n  return (\n    <Routes>\n      <Route path=\"/\" element={<Layout />}>\n        <Route index element={<Home />} />\n        <Route path=\"about\" element={<About />} />\n        <Route path=\"products\" element={<ProductsLayout />}>\n          <Route index element={<ProductList />} />\n          <Route path=\":id\" element={<ProductDetail />} />\n        </Route>\n      </Route>\n    </Routes>\n  );\n}", "explanation": "该实现包含了共享布局、嵌套路由和索引路由的最佳实践。"}]}}]}