{"name": "Unit Test", "trans": ["单元测试"], "methods": [{"name": "Using test Package", "trans": ["test包用法"], "usage": {"syntax": "import 'package:test/test.dart';\ntest('描述', () { ... });", "description": "test包是Dart官方的单元测试框架，支持编写和运行测试用例。", "parameters": [{"name": "test", "description": "测试用例函数，包含描述和测试逻辑"}], "returnValue": "无返回值，测试通过或失败。", "examples": [{"code": "// test包用法示例\nimport 'package:test/test.dart';\n\nvoid main() {\n  test('加法测试', () {\n    expect(1 + 1, 2); // 断言1+1等于2\n  });\n}\n", "explanation": "演示如何用test包编写和运行一个简单的单元测试。"}]}}, {"name": "Assertion and Mock", "trans": ["断言与Mock"], "usage": {"syntax": "expect(actual, matcher);\nMock类实现", "description": "expect用于断言测试结果，Mock用于模拟依赖对象，常配合mockito包使用。", "parameters": [{"name": "actual", "description": "实际值"}, {"name": "matcher", "description": "期望值或匹配器"}, {"name": "<PERSON><PERSON>", "description": "模拟类，用于替代真实依赖"}], "returnValue": "无返回值，断言通过或失败，Mock返回模拟数据。", "examples": [{"code": "// 断言与Mock示例\nimport 'package:test/test.dart';\nimport 'package:mockito/mockito.dart';\n\nclass Calculator {\n  int add(int a, int b) => a + b;\n}\n\nclass MockCalculator extends Mock implements Calculator {}\n\nvoid main() {\n  test('Mock加法', () {\n    final mock = MockCalculator();\n    when(mock.add(1, 2)).thenReturn(100);\n    expect(mock.add(1, 2), 100); // 断言mock返回模拟值\n  });\n}\n", "explanation": "演示如何用expect断言和mockito模拟依赖对象。"}]}}, {"name": "Assignment", "trans": ["作业：编写一个简单的单元测试用例。"], "usage": {"syntax": "无", "description": "请为一个加法函数编写单元测试，并用Mock模拟依赖。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 作业参考实现\nimport 'package:test/test.dart';\nimport 'package:mockito/mockito.dart';\n\nint add(int a, int b) => a + b;\n\nclass MockAdder extends Mock {\n  int add(int a, int b) => 42;\n}\n\nvoid main() {\n  test('加法函数测试', () {\n    expect(add(2, 3), 5);\n  });\n  test('Mock加法', () {\n    final mock = MockAdder();\n    when(mock.add(2, 3)).thenReturn(42);\n    expect(mock.add(2, 3), 42);\n  });\n}\n", "explanation": "作业要求为加法函数编写单元测试，并用Mock模拟依赖。"}]}}]}