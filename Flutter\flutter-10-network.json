{"name": "Network Request", "trans": ["网络请求"], "methods": [{"name": "Using http Package", "trans": ["http包使用"], "usage": {"syntax": "import 'package:http/http.dart' as http; await http.get(Uri.parse(url));", "description": "http包是Flutter官方推荐的轻量级网络请求库，适合简单的GET/POST请求。", "parameters": [{"name": "url", "description": "请求地址"}, {"name": "headers", "description": "请求头，可选"}], "returnValue": "Future<http.Response>，包含响应数据。", "examples": [{"code": "// http包GET请求示例\nimport 'package:flutter/material.dart';\nimport 'package:http/http.dart' as http;\nimport 'dart:convert';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(home: HomePage());\n  }\n}\n\nclass HomePage extends StatefulWidget {\n  @override\n  _HomePageState createState() => _HomePageState();\n}\n\nclass _HomePageState extends State<HomePage> {\n  String _data = '暂无数据';\n  Future<void> fetchData() async {\n    final response = await http.get(Uri.parse('https://jsonplaceholder.typicode.com/todos/1'));\n    if (response.statusCode == 200) {\n      setState(() { _data = json.decode(response.body)['title']; });\n    } else {\n      setState(() { _data = '请求失败'; });\n    }\n  }\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('http包示例')),\n      body: Center(\n        child: Column(\n          mainAxisAlignment: MainAxisAlignment.center,\n          children: [\n            Text(_data),\n            ElevatedButton(onPressed: fetchData, child: Text('获取数据')),\n          ],\n        ),\n      ),\n    );\n  }\n}\n", "explanation": "演示了http包的GET请求和简单的JSON解析。"}]}}, {"name": "Using Dio Package", "trans": ["Dio包使用"], "usage": {"syntax": "import 'package:dio/dio.dart'; await Dio().get(url);", "description": "Dio是功能强大的第三方网络库，支持拦截器、全局配置、FormData等，适合复杂项目。", "parameters": [{"name": "url", "description": "请求地址"}, {"name": "options", "description": "请求配置，如headers、timeout等"}], "returnValue": "Future<Response>，包含响应数据。", "examples": [{"code": "// Dio包GET请求示例\nimport 'package:flutter/material.dart';\nimport 'package:dio/dio.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(home: HomePage());\n  }\n}\n\nclass HomePage extends StatefulWidget {\n  @override\n  _HomePageState createState() => _HomePageState();\n}\n\nclass _HomePageState extends State<HomePage> {\n  String _data = '暂无数据';\n  Future<void> fetchData() async {\n    try {\n      final response = await Dio().get('https://jsonplaceholder.typicode.com/todos/1');\n      setState(() { _data = response.data['title']; });\n    } catch (e) {\n      setState(() { _data = '请求异常'; });\n    }\n  }\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('Dio包示例')),\n      body: Center(\n        child: Column(\n          mainAxisAlignment: MainAxisAlignment.center,\n          children: [\n            Text(_data),\n            ElevatedButton(onPressed: fetchData, child: Text('获取数据')),\n          ],\n        ),\n      ),\n    );\n  }\n}\n", "explanation": "演示了Dio包的GET请求和异常处理。"}]}}, {"name": "Async Data Fetching", "trans": ["异步数据获取"], "usage": {"syntax": "FutureBuilder(future: ..., builder: ...)", "description": "FutureBuilder用于异步数据获取和UI渲染，常用于网络请求结果展示。", "parameters": [{"name": "future", "description": "异步请求对象"}, {"name": "builder", "description": "根据异步结果构建UI"}], "returnValue": "Widget，根据异步状态渲染不同内容。", "examples": [{"code": "// FutureBuilder异步数据获取示例\nimport 'package:flutter/material.dart';\nimport 'package:http/http.dart' as http;\nimport 'dart:convert';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(home: HomePage());\n  }\n}\n\nclass HomePage extends StatefulWidget {\n  @override\n  _HomePageState createState() => _HomePageState();\n}\n\nclass _HomePageState extends State<HomePage> {\n  Future<String> fetchData() async {\n    final response = await http.get(Uri.parse('https://jsonplaceholder.typicode.com/todos/1'));\n    if (response.statusCode == 200) {\n      return json.decode(response.body)['title'];\n    } else {\n      throw Exception('请求失败');\n    }\n  }\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('FutureBuilder示例')),\n      body: Center(\n        child: FutureBuilder<String>(\n          future: fetchData(),\n          builder: (context, snapshot) {\n            if (snapshot.connectionState == ConnectionState.waiting) {\n              return CircularProgressIndicator();\n            } else if (snapshot.hasError) {\n              return Text('错误: \\${snapshot.error}');\n            } else {\n              return Text('结果: \\${snapshot.data}');\n            }\n          },\n        ),\n      ),\n    );\n  }\n}\n", "explanation": "演示了FutureBuilder结合http包实现异步数据获取和UI渲染。"}]}}, {"name": "JSON Parsing", "trans": ["JSON解析"], "usage": {"syntax": "import 'dart:convert'; json.decode(jsonString);", "description": "Dart内置dart:convert库支持JSON解析和编码，常用于网络数据处理。", "parameters": [{"name": "jsonString", "description": "待解析的JSON字符串"}], "returnValue": "Map或List，解析后的数据结构。", "examples": [{"code": "// JSON解析示例\nimport 'dart:convert';\n\nvoid main() {\n  String jsonStr = '{\"name\":\"<PERSON>\",\"age\":18}';\n  Map<String, dynamic> data = json.decode(jsonStr);\n  print('姓名: \\${data['name']}, 年龄: \\${data['age']}');\n}", "explanation": "演示了如何用dart:convert解析JSON字符串。"}]}}, {"name": "Network Error Handling", "trans": ["网络异常处理"], "usage": {"syntax": "try/catch、on SocketException等捕获异常。", "description": "网络请求常见异常包括超时、无网络、服务器错误等，需通过try/catch和特定异常类型处理。", "parameters": [{"name": "try/catch", "description": "捕获所有异常"}, {"name": "SocketException", "description": "捕获网络相关异常"}], "returnValue": "无返回值，异常时可提示用户或重试。", "examples": [{"code": "// 网络异常处理示例\nimport 'package:flutter/material.dart';\nimport 'package:http/http.dart' as http;\nimport 'dart:io';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(home: HomePage());\n  }\n}\n\nclass HomePage extends StatefulWidget {\n  @override\n  _HomePageState createState() => _HomePageState();\n}\n\nclass _HomePageState extends State<HomePage> {\n  String _data = '暂无数据';\n  Future<void> fetchData() async {\n    try {\n      final response = await http.get(Uri.parse('https://jsonplaceholder.typicode.com/todos/1'));\n      setState(() { _data = response.body; });\n    } on SocketException {\n      setState(() { _data = '网络异常，请检查网络'; });\n    } catch (e) {\n      setState(() { _data = '其他异常'; });\n    }\n  }\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('网络异常处理示例')),\n      body: Center(\n        child: Column(\n          mainAxisAlignment: MainAxisAlignment.center,\n          children: [\n            Text(_data),\n            ElevatedButton(onPressed: fetchData, child: Text('获取数据')),\n          ],\n        ),\n      ),\n    );\n  }\n}\n", "explanation": "演示了网络请求的异常捕获和用户提示。"}]}}, {"name": "Assignment", "trans": ["作业：实现一个网络请求页面，展示获取到的JSON数据。"], "usage": {"syntax": "无", "description": "请用http或Dio实现一个页面，点击按钮后发起网络请求并展示返回的JSON数据。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 作业参考实现\nimport 'package:flutter/material.dart';\nimport 'package:http/http.dart' as http;\nimport 'dart:convert';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(home: HomePage());\n  }\n}\n\nclass HomePage extends StatefulWidget {\n  @override\n  _HomePageState createState() => _HomePageState();\n}\n\nclass _HomePageState extends State<HomePage> {\n  String _data = '暂无数据';\n  Future<void> fetchData() async {\n    final response = await http.get(Uri.parse('https://jsonplaceholder.typicode.com/todos/1'));\n    if (response.statusCode == 200) {\n      setState(() { _data = response.body; });\n    } else {\n      setState(() { _data = '请求失败'; });\n    }\n  }\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('作业实现')),\n      body: Center(\n        child: Column(\n          mainAxisAlignment: MainAxisAlignment.center,\n          children: [\n            Text(_data),\n            ElevatedButton(onPressed: fetchData, child: Text('获取数据')),\n          ],\n        ),\n      ),\n    );\n  }\n}\n", "explanation": "作业要求实现网络请求并展示JSON数据。"}]}}]}