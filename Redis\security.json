{"name": "Authentication and Authorization", "trans": ["认证与权限"], "methods": [{"name": "Requirepass Configuration", "trans": ["requirepass配置"], "usage": {"syntax": "通过requirepass参数设置访问密码，提升Redis安全性。", "description": "requirepass用于设置Redis实例的访问密码，防止未授权访问。客户端连接时需提供正确密码。", "parameters": [{"name": "requirepass", "description": "设置访问密码。"}], "returnValue": "无返回值", "examples": [{"code": "# redis.conf配置密码\nrequirepass myStrongPassword\n\n# 客户端连接时需AUTH认证\nAUTH myStrongPassword\n", "explanation": "通过requirepass参数设置密码，客户端需认证后才能操作。"}]}}, {"name": "ACL Access Control", "trans": ["ACL访问控制"], "usage": {"syntax": "通过ACL规则实现多用户权限细粒度控制。", "description": "Redis 6.0及以上支持ACL（访问控制列表），可为不同用户分配命令和key的访问权限，实现多用户隔离和安全管理。", "parameters": [{"name": "user", "description": "用户名。"}, {"name": "ACL规则", "description": "允许或禁止的命令、key等。"}], "returnValue": "无返回值", "examples": [{"code": "# 创建新用户并分配权限\nACL SETUSER readonly on >readonly123 ~* +@read\n# 只允许readonly用户执行只读命令\n", "explanation": "通过ACL实现多用户权限控制和隔离。"}]}}, {"name": "Encryption and Security Advice", "trans": ["加密与安全建议"], "usage": {"syntax": "通过配置加密、网络隔离等手段提升安全性。", "description": "建议开启TLS加密、只绑定本地或内网IP、关闭不必要端口，防止未授权访问和数据泄露。", "parameters": [{"name": "TLS", "description": "开启传输加密。"}, {"name": "bind", "description": "只绑定本地或内网IP。"}, {"name": "protected-mode", "description": "开启保护模式，防止外部访问。"}], "returnValue": "无返回值", "examples": [{"code": "# redis.conf安全配置\ntls-cert-file /path/to/cert.pem\nbind 127.0.0.1\nprotected-mode yes\n", "explanation": "通过加密和网络隔离提升Redis安全性。"}]}}, {"name": "认证与权限作业", "trans": ["作业：认证与权限"], "usage": {"syntax": "请完成以下任务：\n1. 配置requirepass密码。\n2. 创建ACL用户并分配权限。\n3. 配置TLS加密和网络隔离。", "description": "通过实际操作理解认证与权限的配置和安全建议。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 作业示例\n# 1. 配置requirepass\n# 2. 创建ACL用户readonly\n# 3. 配置TLS和bind参数\n", "explanation": "通过动手实验掌握认证与权限的安全配置。"}]}}, {"name": "认证与权限正确实现示例", "trans": ["正确实现卡片"], "usage": {"syntax": "认证与权限的标准配置与安全建议。", "description": "展示如何标准配置密码、ACL和加密，确保Redis安全运行。", "parameters": [{"name": "requirepass", "description": "设置访问密码。"}, {"name": "ACL", "description": "配置多用户权限。"}, {"name": "TLS", "description": "开启加密。"}], "returnValue": "无返回值", "examples": [{"code": "# redis.conf标准安全配置\nrequirepass strongPass\nACL SETUSER admin on >admin123 ~* +@all\ntls-cert-file /path/to/cert.pem\nbind 127.0.0.1\nprotected-mode yes\n", "explanation": "标准认证与权限配置，保证Redis安全可靠。"}]}}]}