{"name": "Simple Exercise", "trans": ["简单练习"], "methods": [{"name": "Student Information Table Exercise", "trans": ["学生信息表练习"], "usage": {"syntax": "# MySQL基础综合练习", "description": "综合应用MySQL基础知识，创建学生信息表，插入数据并查询。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "-- 1. 创建数据库\nCREATE DATABASE school;\n\n-- 2. 使用数据库\nUSE school;\n\n-- 3. 创建学生表\nCREATE TABLE students (\n  id INT PRIMARY KEY,\n  name VARCHAR(20) NOT NULL,\n  gender CHAR(1),\n  age INT\n);\n\n-- 4. 插入数据\nINSERT INTO students (id, name, gender, age) VALUES (1, '张三', 'M', 18);\nINSERT INTO students (id, name, gender, age) VALUES (2, '李四', 'M', 19);\nINSERT INTO students (id, name, gender, age) VALUES (3, '王五', 'F', 18);\n\n-- 5. 查询所有学生信息\nSELECT * FROM students;", "explanation": "这个练习综合了创建数据库、创建表、设置主键和非空约束、插入数据和查询数据等基础操作。"}]}}, {"name": "Complete Solution", "trans": ["完整解决方案"], "usage": {"syntax": "# 练习解答", "description": "学生信息表练习的完整解决方案，包括创建表、插入数据和查询。", "parameters": [], "returnValue": "查询结果集", "examples": [{"code": "-- 创建表\nCREATE TABLE students (\n  id INT PRIMARY KEY,\n  name VARCHAR(20) NOT NULL,\n  gender CHAR(1),\n  age INT\n);\n\n-- 插入数据\nINSERT INTO students (id, name, gender, age) VALUES (1, '张三', 'M', 18);\nINSERT INTO students (id, name, gender, age) VALUES (2, '李四', 'M', 19);\nINSERT INTO students (id, name, gender, age) VALUES (3, '王五', 'F', 18);\n\n-- 查询数据\nSELECT * FROM students;\n\n-- 查询结果\n/*\n+----+------+--------+------+\n| id | name | gender | age  |\n+----+------+--------+------+\n|  1 | 张三 | M      |   18 |\n|  2 | 李四 | M      |   19 |\n|  3 | 王五 | F      |   18 |\n+----+------+--------+------+\n*/", "explanation": "完整解决方案展示了如何创建学生表，设置id为主键、name为非空，插入3条学生记录，并查询所有数据。"}]}}, {"name": "Extended Exercise", "trans": ["扩展练习"], "usage": {"syntax": "# 扩展练习", "description": "在基础练习上进行扩展，添加更多操作。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 向students表再添加一个email字段，设为唯一约束。\n2. 为所有学生添加默认邮箱格式为'姓名@school.com'。\n3. 查询所有年龄大于18岁的学生。\n4. 将id为1的学生年龄改为20。\n5. 按年龄降序查询所有学生。", "explanation": "这个扩展练习涵盖了修改表结构、添加约束、条件查询、更新数据和排序等操作。"}]}}]}