# Flutter学习路线与核心内容

## 1. Flutter基础

### 语言与框架简介
- Flutter框架概述
- Dart语言简介
- Flutter与原生开发对比
- Flutter的应用场景

### 开发环境搭建
- Flutter SDK安装
- Android Studio/VS Code配置
- 模拟器与真机调试
- 第一个Flutter应用
- 项目结构与运行

### 基本语法与Dart入门
- main函数结构
- 变量与常量
- 数据类型
- 函数与方法
- 类与对象
- 异步编程（async/await、Future、Stream）

## 2. 组件与布局

### 常用基础组件
- Text、Image、Icon
- Button（ElevatedButton、TextButton等）
- 输入框（TextField）
- 列表（ListView、GridView）
- 容器（Container、Card、SizedBox）

### 布局组件
- Row、Column
- Stack、Align、Positioned
- Expanded、Flexible、Spacer
- Padding、Margin、Center
- 自适应布局

### 自定义组件
- StatelessWidget
- StatefulWidget
- 组件组合与复用
- 组件生命周期

## 3. 路由与导航

### 路由管理
- Navigator基本用法
- 命名路由
- 路由参数传递
- 路由返回值
- 路由守卫与中间件

### 页面跳转与动画
- 页面切换动画
- 自定义路由动画

## 4. 状态管理

### 本地状态管理
- setState用法
- InheritedWidget
- Provider
- Riverpod
- Bloc/Cubit
- GetX
- 状态管理对比与选择

## 5. 网络与数据

### 网络请求
- http包使用
- Dio包使用
- 异步数据获取
- JSON解析
- 网络异常处理

### 本地存储
- SharedPreferences
- 文件读写
- 数据库（sqflite）
- 数据加密与安全

## 6. 动画与交互

### 动画基础
- AnimationController
- Tween与Curve
- 隐式动画组件
- Hero动画
- 交互反馈（手势、点击、滑动）

### 自定义动画
- 自定义动画实现
- 动画与状态结合

## 7. 平台集成与原生能力

### 原生集成
- 插件开发与使用
- 与原生代码通信（Platform Channel）
- 调用相机、定位、传感器等
- 推送与通知

### 多端适配
- 响应式布局
- 国际化与本地化
- 主题与样式自定义

## 8. 测试

### 单元测试
- test包用法
- 断言与Mock

### Widget测试
- flutter_test包
- 组件渲染与交互测试

### 集成测试
- integration_test包
- 自动化UI测试

## 9. 性能优化

### 性能分析
- Flutter DevTools
- 帧率与内存分析
- 性能瓶颈定位

### 优化技巧
- 组件重用与懒加载
- 图片与资源优化
- 动画性能优化
- 启动速度优化

## 10. 调试与常见错误

### 常见错误排查
- 编译与运行错误
- Widget渲染异常
- 网络与数据错误
- 状态同步问题

### 调试技巧
- 日志与断点调试
- 热重载与热重启
- DevTools调试

## 11. 项目实战与练习

### 实战案例
- 计数器应用
- 待办事项App
- 天气查询App
- 聊天室App
- 电商商品列表与详情

### 练习与项目
- 自定义组件开发
- 多页面路由实现
- 集成网络请求与本地存储
- 动画与交互综合练习
- 性能优化实战
- 单元与集成测试实践 