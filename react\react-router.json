{"name": "React Router", "trans": ["React 路由"], "methods": [{"name": "Installation and Setup", "trans": ["安装和设置"], "usage": {"syntax": "npm install react-router-dom\n\n// 基础设置\nimport { BrowserRouter } from 'react-router-dom';\n\n<BrowserRouter>\n  <App />\n</BrowserRouter>", "description": "React Router 是 React 应用中最流行的路由库，用于创建单页应用中的页面导航。安装后需要用路由器组件包裹应用根组件。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 安装命令\n// npm install react-router-dom\n\n// index.js - 应用入口\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { BrowserRouter } from 'react-router-dom'; // 导入BrowserRouter\nimport App from './App';\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n  <React.StrictMode>\n    <BrowserRouter> {/* 包裹整个应用 */}\n      <App />\n    </BrowserRouter>\n  </React.StrictMode>\n);", "explanation": "展示了如何安装 React Router 并在应用入口设置 BrowserRouter 包裹整个应用。"}]}}, {"name": "Router Types", "trans": ["路由器类型"], "usage": {"syntax": "// 浏览器路由器\nimport { BrowserRouter } from 'react-router-dom';\n\n// Hash路由器\nimport { HashRouter } from 'react-router-dom';\n\n// 内存路由器\nimport { MemoryRouter } from 'react-router-dom';", "description": "React Router 提供多种路由器类型，适应不同部署环境和需求。最常用的是 BrowserRouter（使用 HTML5 History API）和 HashRouter（使用 URL 哈希）。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 1. BrowserRouter - 使用HTML5 History API\n// 干净URL: example.com/about\nimport { BrowserRouter } from 'react-router-dom';\n\n// 2. HashRouter - 使用URL哈希部分\n// 哈希URL: example.com/#/about\nimport { HashRouter } from 'react-router-dom';\n\n// 3. MemoryRouter - 内存中保存历史记录\n// 用于测试或非浏览器环境\nimport { MemoryRouter } from 'react-router-dom';\n\n// 4. 静态路由器 - 用于服务器端渲染\nimport { StaticRouter } from 'react-router-dom/server';\n\n// 示例：基于环境使用不同路由器\nfunction Root() {\n  // 生产环境使用BrowserRouter，否则使用HashRouter\n  const Router = process.env.NODE_ENV === 'production' ? BrowserRouter : HashRouter;\n    \n  return (\n    <Router>\n      <App />\n    </Router>\n  );\n}", "explanation": "展示了不同类型的路由器及其适用场景，包括BrowserRouter、<PERSON>h<PERSON>outer、MemoryRouter和St<PERSON>Router。"}]}}, {"name": "Route Matching", "trans": ["路由匹配"], "usage": {"syntax": "import { Routes, Route } from 'react-router-dom';\n\n<Routes>\n  <Route path=\"/path\" element={<Component />} />\n  <Route path=\"/path/:param\" element={<Component />} />\n  <Route path=\"*\" element={<NotFound />} />\n</Routes>", "description": "使用 Route 组件定义 URL 路径与 React 组件的对应关系。Route 可以包含静态路径、动态参数和通配符。", "parameters": [{"name": "path", "description": "URL路径模式，可包含参数(:param)和通配符(*)"}, {"name": "element", "description": "匹配路径时渲染的React组件"}], "returnValue": "无返回值", "examples": [{"code": "import { Routes, Route } from 'react-router-dom';\nimport Home from './pages/Home';\nimport About from './pages/About';\nimport ProductList from './pages/ProductList';\nimport ProductDetail from './pages/ProductDetail';\nimport NotFound from './pages/NotFound';\n\nfunction App() {\n  return (\n    <div className=\"app\">\n      <header>{/* 导航栏 */}</header>\n      \n      <main>\n        <Routes>\n          {/* 精确匹配根路径 */}\n          <Route path=\"/\" element={<Home />} />\n          \n          {/* 静态路由 */}\n          <Route path=\"/about\" element={<About />} />\n          <Route path=\"/products\" element={<ProductList />} />\n          \n          {/* 带参数的动态路由 */}\n          <Route path=\"/products/:productId\" element={<ProductDetail />} />\n          \n          {/* 嵌套路由 */}\n          <Route path=\"/account\" element={<AccountLayout />}>\n            <Route path=\"profile\" element={<Profile />} />\n            <Route path=\"settings\" element={<Settings />} />\n          </Route>\n          \n          {/* 通配符路由（404页面） */}\n          <Route path=\"*\" element={<NotFound />} />\n        </Routes>\n      </main>\n    </div>\n  );\n}\n\n// 带子路由的布局组件\nimport { Outlet } from 'react-router-dom';\n\nfunction AccountLayout() {\n  return (\n    <div className=\"account-layout\">\n      <nav className=\"account-nav\">\n        {/* 账户导航菜单 */}\n      </nav>\n      \n      {/* 子路由在这里渲染 */}\n      <Outlet />\n    </div>\n  );\n}", "explanation": "展示了如何使用Routes和Route组件定义不同类型的路由，包括静态路由、动态路由、嵌套路由和通配符路由。"}]}}, {"name": "Navigation Links", "trans": ["导航链接"], "usage": {"syntax": "import { Link, NavLink } from 'react-router-dom';\n\n<Link to=\"/path\">链接文本</Link>\n<NavLink to=\"/path\" className={({ isActive }) => isActive ? 'active' : ''}>导航链接</NavLink>", "description": "Link组件用于创建导航链接，类似于HTML的<a>标签但不会触发页面刷新。NavLink是特殊的Link，可根据当前路由匹配状态应用样式。", "parameters": [{"name": "to", "description": "目标路径，可以是字符串或对象"}, {"name": "className/style", "description": "NavLink可以接收函数，根据isActive状态动态设置样式"}], "returnValue": "无返回值", "examples": [{"code": "import { Link, NavLink } from 'react-router-dom';\n\nfunction Navigation() {\n  return (\n    <nav className=\"main-nav\">\n      {/* 基本Link组件 */}\n      <Link to=\"/\">首页</Link>\n      \n      {/* NavLink自动添加active类 */}\n      <NavLink to=\"/products\" className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}>\n        产品列表\n      </NavLink>\n      \n      {/* 带参数的Link */}\n      <Link to=\"/products/featured?sort=price\">特色产品</Link>\n      \n      {/* 对象形式的to属性 */}\n      <Link to={{\n        pathname: '/products',\n        search: '?category=electronics',\n        hash: '#top'\n      }}>\n        电子产品\n      </Link>\n      \n      {/* 替换当前历史记录(不能后退) */}\n      <Link to=\"/login\" replace>登录</Link>\n      \n      {/* 带状态的Link (传递数据) */}\n      <Link to=\"/products/promo\" state={{ from: 'navigation', discount: '20%' }}>\n        促销产品\n      </Link>\n    </nav>\n  );\n}", "explanation": "展示了Link和NavLink组件的各种用法，包括基本链接、active状态、URL参数、对象形式、replace模式和state传递。"}]}}, {"name": "Route Rendering", "trans": ["路由渲染"], "usage": {"syntax": "// 路由定义\n<Route path=\"/path\" element={<Component />} />\n\n// 路由出口\nimport { Outlet } from 'react-router-dom';\n<Outlet />", "description": "React Router v6使用声明式路由和组件式渲染，通过element属性指定渲染组件。嵌套路由需要在父路由组件中使用Outlet组件作为子路由的渲染位置。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import { Routes, Route, Outlet } from 'react-router-dom';\n\n// 主应用布局\nfunction Layout() {\n  return (\n    <div className=\"app-layout\">\n      <header>\n        <h1>我的应用</h1>\n        <nav>{/* 导航菜单 */}</nav>\n      </header>\n      \n      <main>\n        {/* 子路由在这里渲染 */}\n        <Outlet />\n      </main>\n      \n      <footer>© 2023 我的应用</footer>\n    </div>\n  );\n}\n\n// 产品页布局\nfunction ProductLayout() {\n  return (\n    <div className=\"product-section\">\n      <aside>\n        <h2>产品分类</h2>\n        {/* 产品分类导航 */}\n      </aside>\n      \n      <section>\n        {/* 产品子路由在这里渲染 */}\n        <Outlet />\n      </section>\n    </div>\n  );\n}\n\n// 应用路由配置\nfunction App() {\n  return (\n    <Routes>\n      {/* 使用布局包裹路由 */}\n      <Route path=\"/\" element={<Layout />}>\n        <Route index element={<Home />} /> {/* 索引路由 */}\n        \n        {/* 产品嵌套路由 */}\n        <Route path=\"products\" element={<ProductLayout />}>\n          <Route index element={<ProductList />} />\n          <Route path=\":id\" element={<ProductDetail />} />\n          <Route path=\"new\" element={<NewProduct />} />\n        </Route>\n        \n        <Route path=\"about\" element={<About />} />\n        <Route path=\"contact\" element={<Contact />} />\n        <Route path=\"*\" element={<NotFound />} />\n      </Route>\n    </Routes>\n  );\n}", "explanation": "展示了如何使用嵌套路由和Outlet组件创建带布局的路由结构，支持多层嵌套的页面布局。"}]}}, {"name": "Programmatic Navigation", "trans": ["编程式导航"], "usage": {"syntax": "import { useNavigate } from 'react-router-dom';\n\nconst navigate = useNavigate();\nnavigate('/path');\nnavigate(-1); // 后退", "description": "使用useNavigate钩子在事件处理函数或副作用中实现编程式导航，可以在用户交互后跳转页面、传递状态或进行重定向。", "parameters": [], "returnValue": "navigate函数，用于编程式导航", "examples": [{"code": "import { useNavigate } from 'react-router-dom';\n\nfunction LoginForm() {\n  const navigate = useNavigate(); // 获取导航函数\n  const [formData, setFormData] = useState({\n    username: '',\n    password: ''\n  });\n  \n  // 处理表单输入\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  \n  // 处理表单提交\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    try {\n      // 假设这是登录API调用\n      const response = await loginUser(formData);\n      \n      // 登录成功，导航到首页\n      navigate('/', { \n        replace: true, // 替换历史记录，防止返回登录页\n        state: { user: response.user } // 传递状态数据\n      });\n    } catch (error) {\n      // 处理错误...\n    }\n  };\n  \n  // 取消登录\n  const handleCancel = () => {\n    navigate(-1); // 返回上一页\n  };\n  \n  return (\n    <form onSubmit={handleSubmit}>\n      <div>\n        <label>用户名:</label>\n        <input type=\"text\" name=\"username\" value={formData.username} onChange={handleChange} />\n      </div>\n      \n      <div>\n        <label>密码:</label>\n        <input type=\"password\" name=\"password\" value={formData.password} onChange={handleChange} />\n      </div>\n      \n      <div>\n        <button type=\"submit\">登录</button>\n        <button type=\"button\" onClick={handleCancel}>取消</button>\n      </div>\n    </form>\n  );\n}\n\n// 使用重定向进行路由守卫\nimport { useEffect } from 'react';\n\nfunction ProtectedRoute({ children }) {\n  const navigate = useNavigate();\n  const isAuthenticated = useSelector(state => state.auth.isAuthenticated);\n  \n  useEffect(() => {\n    // 如果用户未认证，重定向到登录页\n    if (!isAuthenticated) {\n      navigate('/login', { \n        replace: true,\n        state: { from: location.pathname } // 保存尝试访问的路径\n      });\n    }\n  }, [isAuthenticated, navigate]);\n  \n  // 已认证则渲染子组件\n  return isAuthenticated ? children : null;\n}", "explanation": "展示了如何使用useNavigate钩子实现表单提交后的页面跳转、传递状态数据、返回上一页，以及实现路由守卫和重定向功能。"}]}}, {"name": "URL Parameters", "trans": ["URL参数"], "usage": {"syntax": "// 定义带参数的路由\n<Route path=\"/users/:userId\" element={<UserProfile />} />\n\n// 在组件中获取参数\nimport { useParams } from 'react-router-dom';\nconst { userId } = useParams();", "description": "使用URL参数实现动态路由，通过:paramName语法在路径中定义参数，并在组件中通过useParams钩子获取参数值。", "parameters": [], "returnValue": "一个包含URL参数的对象", "examples": [{"code": "import { Routes, Route, useParams, useSearchParams, useLocation } from 'react-router-dom';\n\n// 应用路由配置\nfunction App() {\n  return (\n    <Routes>\n      <Route path=\"/\" element={<Home />} />\n      <Route path=\"/products\" element={<ProductList />} />\n      <Route path=\"/products/:productId\" element={<ProductDetail />} />\n      <Route path=\"/blog/:category/:postId\" element={<BlogPost />} />\n      <Route path=\"/search\" element={<SearchResults />} />\n    </Routes>\n  );\n}\n\n// 使用路径参数(Path Parameters)\nfunction ProductDetail() {\n  // 获取URL参数\n  const { productId } = useParams();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  \n  useEffect(() => {\n    // 根据productId获取产品数据\n    async function fetchProduct() {\n      try {\n        setLoading(true);\n        const data = await fetchProductById(productId);\n        setProduct(data);\n      } catch (error) {\n        console.error('获取产品失败:', error);\n      }\n    }\n    \n    fetchProduct();\n  }, [productId]); // 当参数变化时重新获取\n  \n  if (loading) return <p>加载中...</p>;\n  if (!product) return <p>产品未找到</p>;\n  \n  return (\n    <div className=\"product-detail\">\n      <h1>{product.name}</h1>\n      <p>ID: {productId}</p>\n      <p>价格: ¥{product.price}</p>\n      <p>{product.description}</p>\n    </div>\n  );\n}\n\n// 使用查询参数(Query Parameters)\nfunction SearchResults() {\n  // 获取和设置查询参数\n  const [searchParams, setSearchParams] = useSearchParams();\n  const query = searchParams.get('q') || '';\n  const category = searchParams.get('category') || 'all';\n  const page = parseInt(searchParams.get('page') || '1');\n  \n  // 更新筛选条件\n  const updateCategory = (newCategory) => {\n    setSearchParams({\n      q: query,\n      category: newCategory,\n      page: 1 // 重置页码\n    });\n  };\n  \n  // 翻页\n  const goToPage = (newPage) => {\n    setSearchParams({\n      q: query, \n      category,\n      page: newPage\n    });\n  };\n  \n  return (\n    <div>\n      <h1>搜索结果: {query}</h1>\n      <div className=\"filters\">\n        <button onClick={() => updateCategory('all')}>全部</button>\n        <button onClick={() => updateCategory('electronics')}>电子产品</button>\n        <button onClick={() => updateCategory('clothing')}>服装</button>\n      </div>\n      {/* 搜索结果列表 */}\n      <div className=\"pagination\">\n        <button onClick={() => goToPage(page - 1)} disabled={page === 1}>上一页</button>\n        <span>第 {page} 页</span>\n        <button onClick={() => goToPage(page + 1)}>下一页</button>\n      </div>\n    </div>\n  );\n}\n\n// 使用location.state (隐藏状态)\nfunction BlogPost() {\n  const { category, postId } = useParams();\n  const location = useLocation();\n  const { author, publishDate } = location.state || {};\n  \n  return (\n    <article>\n      <h1>博客文章 {postId}</h1>\n      <p>分类: {category}</p>\n      {author && <p>作者: {author}</p>}\n      {publishDate && <p>发布日期: {publishDate}</p>}\n    </article>\n  );\n}", "explanation": "展示了如何使用路径参数(useParams)、查询参数(useSearchParams)和location状态，实现动态路由和数据传递。"}]}}, {"name": "作业：实现多页面React应用", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 使用React Router实现一个包含4个页面的React应用\n// 2. 包含首页、产品列表、产品详情和关于我们页面\n// 3. 添加导航菜单，支持页面间跳转\n// 4. 实现产品详情页的动态路由", "description": "通过实践React Router核心功能，实现一个多页面React应用，熟悉路由配置、导航和参数传递。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下仅为提示\n// 1. 创建页面组件：Home、ProductList、ProductDetail、About\n// 2. 配置路由，使用BrowserRouter、Routes和Route\n// 3. 实现导航菜单，使用Link或NavLink\n// 4. 实现产品列表到详情页的跳转，使用动态路由", "explanation": "作业提示，学生需自行完成实现。"}, {"code": "// 完整实现示例\nimport React from 'react';\nimport { BrowserRouter, Routes, Route, Link, useParams } from 'react-router-dom';\n\n// 产品数据\nconst products = [\n  { id: 1, name: '智能手机', price: 1999, description: '最新款高性能智能手机' },\n  { id: 2, name: '笔记本电脑', price: 4999, description: '轻薄便携的笔记本电脑' },\n  { id: 3, name: '智能手表', price: 999, description: '多功能健康监测智能手表' }\n];\n\n// 首页组件\nfunction Home() {\n  return (\n    <div className=\"page home-page\">\n      <h1>欢迎访问我们的网站</h1>\n      <p>这是一个使用React Router实现的多页面应用示例</p>\n      <Link to=\"/products\" className=\"cta-button\">浏览产品</Link>\n    </div>\n  );\n}\n\n// 产品列表页\nfunction ProductList() {\n  return (\n    <div className=\"page product-list\">\n      <h1>产品列表</h1>\n      <div className=\"product-grid\">\n        {products.map(product => (\n          <div key={product.id} className=\"product-card\">\n            <h2>{product.name}</h2>\n            <p className=\"price\">¥{product.price}</p>\n            <Link to={`/products/${product.id}`} className=\"view-button\">\n              查看详情\n            </Link>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n}\n\n// 产品详情页\nfunction ProductDetail() {\n  // 获取URL参数\n  const { productId } = useParams();\n  // 根据ID查找产品\n  const product = products.find(p => p.id === parseInt(productId));\n  \n  // 产品不存在\n  if (!product) {\n    return (\n      <div className=\"page error-page\">\n        <h1>产品未找到</h1>\n        <p>抱歉，您查找的产品不存在</p>\n        <Link to=\"/products\">返回产品列表</Link>\n      </div>\n    );\n  }\n  \n  return (\n    <div className=\"page product-detail\">\n      <h1>{product.name}</h1>\n      <div className=\"product-info\">\n        <p className=\"price\">价格: ¥{product.price}</p>\n        <p className=\"description\">{product.description}</p>\n      </div>\n      <div className=\"actions\">\n        <button className=\"add-to-cart\">加入购物车</button>\n        <Link to=\"/products\" className=\"back-link\">返回列表</Link>\n      </div>\n    </div>\n  );\n}\n\n// 关于我们页面\nfunction About() {\n  return (\n    <div className=\"page about-page\">\n      <h1>关于我们</h1>\n      <p>我们是一家专注于提供高品质电子产品的公司。</p>\n      <p>成立于2010年，致力于为消费者提供最优质的产品和服务。</p>\n    </div>\n  );\n}\n\n// 导航菜单组件\nfunction Navigation() {\n  return (\n    <nav className=\"main-nav\">\n      <Link to=\"/\" className=\"logo\">MyStore</Link>\n      <div className=\"nav-links\">\n        <Link to=\"/\">首页</Link>\n        <Link to=\"/products\">产品</Link>\n        <Link to=\"/about\">关于我们</Link>\n      </div>\n    </nav>\n  );\n}\n\n// 应用布局组件\nfunction Layout({ children }) {\n  return (\n    <div className=\"app-container\">\n      <Navigation />\n      <main className=\"content\">\n        {children}\n      </main>\n      <footer className=\"app-footer\">\n        <p>© 2023 MyStore. 保留所有权利。</p>\n      </footer>\n    </div>\n  );\n}\n\n// 主应用组件\nfunction App() {\n  return (\n    <BrowserRouter>\n      <Layout>\n        <Routes>\n          <Route path=\"/\" element={<Home />} />\n          <Route path=\"/products\" element={<ProductList />} />\n          <Route path=\"/products/:productId\" element={<ProductDetail />} />\n          <Route path=\"/about\" element={<About />} />\n          <Route path=\"*\" element={<h1>页面未找到</h1>} />\n        </Routes>\n      </Layout>\n    </BrowserRouter>\n  );\n}\n\nexport default App;", "explanation": "完整实现了一个包含首页、产品列表、产品详情和关于我们页面的React应用，使用React Router处理路由和导航。"}]}}]}