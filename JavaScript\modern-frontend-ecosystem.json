{"name": "Modern Frontend Ecosystem", "trans": ["现代前端生态"], "methods": [{"name": "TypeScript Integration", "trans": ["TypeScript集成"], "usage": {"syntax": "tsc / tsconfig.json / @types包", "description": "TypeScript是JavaScript的超集，支持类型系统和编译检查。集成方式：\n- 安装typescript和@types类型声明包\n- 配置tsconfig.json\n- 使用tsc编译TS代码", "parameters": [{"name": "option", "description": "TypeScript配置项或命令"}], "returnValue": "无返回值", "examples": [{"code": "// 安装TypeScript和类型声明\nnpm install typescript @types/node --save-dev\n\n// tsconfig.json配置\n{\n  \"compilerOptions\": {\n    \"target\": \"ES6\",\n    \"module\": \"commonjs\",\n    \"strict\": true\n  }\n}\n\n// 编译TS代码\nnpx tsc", "explanation": "示例展示了TypeScript的安装、配置和编译流程。"}]}}, {"name": "Frontend Frameworks (React/Vue/Angular)", "trans": ["前端框架（React/Vue/Angular）"], "usage": {"syntax": "npx create-react-app / vue create / ng new", "description": "现代前端开发常用框架有React、Vue、Angular。\n- React：组件化、声明式UI、生态丰富\n- Vue：易上手、响应式、渐进式框架\n- Angular：全家桶、强类型、企业级", "parameters": [{"name": "command", "description": "创建或管理项目的命令"}], "returnValue": "无返回值", "examples": [{"code": "// 创建React项目\nnpx create-react-app my-app\n\n// 创建Vue项目\nnpx @vue/cli create my-app\n\n// 创建Angular项目\nnpx @angular/cli new my-app", "explanation": "示例展示了三大主流前端框架的项目初始化命令。"}]}}, {"name": "State Management Libraries", "trans": ["状态管理库"], "usage": {"syntax": "Redux / Vuex / Pinia / Zustand / MobX", "description": "状态管理库用于集中管理应用状态，适合中大型项目。\n- Redux：React生态主流，单向数据流\n- Vuex/Pinia：Vue官方推荐\n- Zustand/MobX：轻量、响应式", "parameters": [{"name": "library", "description": "状态管理库名称"}], "returnValue": "无返回值", "examples": [{"code": "// Redux基本用法\nimport { createStore } from 'redux';\nfunction reducer(state = { count: 0 }, action) {\n  switch (action.type) {\n    case 'inc': return { count: state.count + 1 };\n    default: return state;\n  }\n}\nconst store = createStore(reducer);\nstore.dispatch({ type: 'inc' });\nconsole.log(store.getState()); // { count: 1 }\n\n// Vuex基本用法\nimport { createStore } from 'vuex';\nconst store = createStore({\n  state: { count: 0 },\n  mutations: { inc(state) { state.count++; } }\n});\nstore.commit('inc');\nconsole.log(store.state.count); // 1", "explanation": "示例展示了Redux和Vuex的基本用法。"}]}}, {"name": "Routing and Data Flow", "trans": ["路由与数据流"], "usage": {"syntax": "react-router / vue-router / angular-router", "description": "路由用于管理单页应用的页面切换，数据流用于组件间通信。\n- React Router：React生态主流路由\n- Vue Router：Vue官方路由\n- Angular Router：Angular官方路由", "parameters": [{"name": "router", "description": "路由库名称"}], "returnValue": "无返回值", "examples": [{"code": "// React Router基本用法\nimport { BrowserRouter, Route, Routes } from 'react-router-dom';\n<BrowserRouter>\n  <Routes>\n    <Route path=\"/\" element={<Home />} />\n    <Route path=\"/about\" element={<About />} />\n  </Routes>\n</BrowserRouter>\n\n// Vue Router基本用法\nimport { createRouter, createWebHistory } from 'vue-router';\nconst router = createRouter({\n  history: createWebHistory(),\n  routes: [\n    { path: '/', component: Home },\n    { path: '/about', component: About }\n  ]\n});", "explanation": "示例展示了React Router和Vue Router的基本配置。"}]}}, {"name": "UI Component Libraries", "trans": ["UI组件库"], "usage": {"syntax": "Ant Design / Element Plus / Material UI / Vuetify", "description": "UI组件库提供丰富的现成组件，提升开发效率和界面一致性。\n- Ant Design：React生态主流UI库\n- Element Plus：Vue生态主流UI库\n- Material UI：谷歌风格，跨框架\n- Vuetify：Vue生态Material风格", "parameters": [{"name": "library", "description": "UI组件库名称"}], "returnValue": "无返回值", "examples": [{"code": "// 安装Ant Design\nnpm install antd\n// React中使用\nimport { Button } from 'antd';\n<Button type=\"primary\">按钮</Button>\n\n// 安装Element Plus\nnpm install element-plus\n// Vue中使用\nimport { ElButton } from 'element-plus';\n<el-button type=\"primary\">按钮</el-button>", "explanation": "示例展示了Ant Design和Element Plus的安装与基本用法。"}]}}, {"name": "Mobile and PWA", "trans": ["移动端与PWA"], "usage": {"syntax": "React Native / Vue3 + Vant / PWA配置", "description": "移动端开发和PWA（渐进式Web应用）提升跨平台体验。\n- React Native：原生App开发\n- Vue3+Vant：移动端H5开发\n- PWA：支持离线、安装、推送等特性", "parameters": [{"name": "tool", "description": "移动端或PWA相关工具/库"}], "returnValue": "无返回值", "examples": [{"code": "// React Native初始化\nnpx react-native init MyApp\n\n// Vue3 + Vant移动端项目\nnpx @vue/cli create my-mobile-app\nnpm install vant\n\n// PWA配置（manifest.json）\n{\n  \"name\": \"MyPWA\",\n  \"start_url\": \"./index.html\",\n  \"display\": \"standalone\",\n  \"background_color\": \"#fff\"\n}", "explanation": "示例展示了React Native、Vue3+Vant和PWA的基本用法和配置。"}]}}, {"name": "Modern Frontend Assignment", "trans": ["现代前端生态练习"], "usage": {"syntax": "// 现代前端生态练习", "description": "完成以下练习，巩固现代前端生态相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "/*\n练习：\n1. 使用TypeScript重构一个JS函数\n2. 初始化一个React或Vue项目并集成UI组件库\n3. 配置路由和状态管理\n4. 尝试添加PWA支持\n*/", "explanation": "练习要求动手实践TypeScript、前端框架、UI库、路由、状态管理和PWA配置。"}]}}]}