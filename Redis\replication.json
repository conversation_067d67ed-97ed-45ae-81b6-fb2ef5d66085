{"name": "Replication", "trans": ["主从复制"], "methods": [{"name": "Replication Principle", "trans": ["复制原理"], "usage": {"syntax": "主从复制通过主节点（master）将数据同步到一个或多个从节点（slave/replica），实现数据冗余和高可用。", "description": "Redis主从复制机制允许一个主节点的数据自动同步到一个或多个从节点。主节点处理写操作，从节点只读，提升读性能并实现数据备份。", "parameters": [{"name": "master", "description": "主节点，负责处理写操作并同步数据。"}, {"name": "replicaof", "description": "从节点配置，指定主节点地址。"}], "returnValue": "无返回值", "examples": [{"code": "# 从节点配置主从关系\nreplicaof 127.0.0.1 6379  # 指定主节点IP和端口\n\n# 主节点写入数据后，从节点自动同步\n", "explanation": "通过replicaof命令配置主从关系，实现数据同步。"}]}}, {"name": "Configuration and Management", "trans": ["配置与管理"], "usage": {"syntax": "通过配置文件或命令行设置主从关系，并可动态切换主从角色。", "description": "主从复制可通过redis.conf或命令行动态配置。支持主从切换、断线重连、只读设置等管理操作，便于高可用部署。", "parameters": [{"name": "replica-read-only", "description": "设置从节点只读，防止写入。"}, {"name": "slaveof/replicaof", "description": "动态切换主从角色。"}], "returnValue": "无返回值", "examples": [{"code": "# redis.conf配置从节点只读\nreplica-read-only yes\n\n# 动态切换主从\nSLAVEOF <host> <port>\n", "explanation": "通过配置文件和命令行管理主从关系和只读属性。"}]}}, {"name": "Replication Delay and Consistency", "trans": ["延迟与一致性"], "usage": {"syntax": "主从复制存在一定延迟，需关注数据一致性和同步状态。", "description": "主从复制过程中，从节点数据同步存在网络和处理延迟。可通过监控复制偏移量、延迟指标等手段保障数据一致性。", "parameters": [{"name": "replication lag", "description": "主从数据同步的延迟时间。"}, {"name": "offset", "description": "主从复制偏移量，用于判断同步进度。"}], "returnValue": "无返回值", "examples": [{"code": "# 监控主从延迟\nINFO replication  # 查看主从状态和延迟\n\n# 检查offset一致性\n", "explanation": "通过INFO replication命令监控主从延迟和同步状态。"}]}}, {"name": "主从复制作业", "trans": ["作业：主从复制"], "usage": {"syntax": "请完成以下任务：\n1. 配置一主一从的Redis环境。\n2. 在主节点写入数据，观察从节点同步。\n3. 监控主从延迟。", "description": "通过实际操作理解主从复制的配置、同步和延迟监控。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 作业示例\n# 1. 配置主从节点，主节点端口6379，从节点6380\n# 2. 主节点写入key，检查从节点同步\n# 3. 使用INFO replication监控延迟\n", "explanation": "通过动手实验掌握主从复制的配置和监控。"}]}}, {"name": "主从复制正确实现示例", "trans": ["正确实现卡片"], "usage": {"syntax": "主从复制的标准配置与监控流程。", "description": "展示如何标准配置主从复制，并通过命令监控同步状态，确保数据一致性。", "parameters": [{"name": "replicaof", "description": "指定主节点地址。"}, {"name": "INFO replication", "description": "监控主从同步状态。"}], "returnValue": "无返回值", "examples": [{"code": "# redis.conf标准配置\nreplicaof 127.0.0.1 6379\n\n# 监控流程\n# 1. 启动主从节点\n# 2. 使用INFO replication命令监控同步\n", "explanation": "标准主从复制配置和监控流程，保证数据同步和一致性。"}]}}]}