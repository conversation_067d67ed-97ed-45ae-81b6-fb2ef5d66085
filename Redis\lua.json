{"name": "<PERSON><PERSON>", "trans": ["<PERSON>a脚本"], "methods": [{"name": "EVAL Command", "trans": ["EVAL命令"], "usage": {"syntax": "EVAL script numkeys key [key ...] arg [arg ...]", "description": "EVAL用于执行Lua脚本，numkeys指定键数量，后续为键和参数。可实现复杂原子操作。", "parameters": [{"name": "script", "description": "Lua脚本内容"}, {"name": "numkeys", "description": "键数量"}, {"name": "key", "description": "参与操作的键"}, {"name": "arg", "description": "传递给脚本的参数"}], "returnValue": "脚本执行结果", "examples": [{"code": "$ redis-cli eval 'return ARGV[1]' 0 hello", "explanation": "执行简单的Lua脚本返回参数。"}, {"code": "$ redis-cli eval 'return redis.call(\"set\", KEYS[1], ARGV[1])' 1 mykey myval", "explanation": "用Lua脚本设置键值。"}]}}, {"name": "Script Atomicity", "trans": ["脚本原子性"], "usage": {"syntax": "EVAL脚本在单线程中执行，期间不会被其他命令打断。", "description": "Redis保证Lua脚本的原子性，脚本执行期间不会有其他命令插入，适合实现分布式锁、复杂事务等。", "parameters": [], "returnValue": "原子执行的脚本结果", "examples": [{"code": "$ redis-cli eval 'local v=redis.call(\"get\", KEYS[1]); if v then return v else return nil end' 1 mykey", "explanation": "演示了脚本原子性和条件判断。"}]}}, {"name": "Common Script Applications", "trans": ["常见脚本应用"], "usage": {"syntax": "EVAL实现分布式锁、计数器、批量操作等", "description": "Lua脚本常用于实现原子计数、分布式锁、批量操作、复杂业务逻辑等。", "parameters": [], "returnValue": "业务操作结果", "examples": [{"code": "$ redis-cli eval 'if redis.call(\"get\", KEYS[1]) == false then return redis.call(\"set\", KEYS[1], ARGV[1]) else return nil end' 1 lock 1", "explanation": "用Lua脚本实现分布式锁。"}, {"code": "$ redis-cli eval 'for i=1,ARGV[1] do redis.call(\"incr\", KEYS[1]) end return redis.call(\"get\", KEYS[1])' 1 counter 5", "explanation": "用Lua脚本实现批量自增。"}]}}, {"name": "Practice: Lua脚本练习", "trans": ["Lua脚本练习"], "usage": {"syntax": "# Redis Lua脚本练习", "description": "完成以下练习，掌握EVAL命令和Lua脚本的原子操作。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用EVAL实现设置并获取一个键。\n2. 用Lua脚本实现分布式锁。\n3. 用Lua脚本实现批量自增。", "explanation": "通过这些练习掌握Lua脚本的常用命令和原子操作。"}, {"code": "# 正确实现示例\n$ redis-cli eval 'return redis.call(\"set\", KEYS[1], ARGV[1])' 1 mykey myval\n$ redis-cli eval 'return redis.call(\"get\", KEYS[1])' 1 mykey\n$ redis-cli eval 'if redis.call(\"get\", KEYS[1]) == false then return redis.call(\"set\", KEYS[1], ARGV[1]) else return nil end' 1 lock 1\n$ redis-cli eval 'for i=1,ARGV[1] do redis.call(\"incr\", KEYS[1]) end return redis.call(\"get\", KEYS[1])' 1 counter 3", "explanation": "展示了练习的标准实现过程。"}]}}]}