{"name": "useReducer", "trans": ["useReducer钩子"], "methods": [{"name": "Reducer Function", "trans": ["reducer函数"], "usage": {"syntax": "function reducer(state, action) { return newState; }", "description": "reducer是一个纯函数，接收当前状态和一个动作(action)对象作为参数，返回新的状态。reducer函数必须是纯函数，不应该有副作用，不应该修改现有的state对象，而是返回一个全新的状态对象。", "parameters": [{"name": "state", "description": "当前的应用状态"}, {"name": "action", "description": "一个描述如何更新状态的对象，通常包含type属性和可选的payload"}], "returnValue": "返回新的状态对象", "examples": [{"code": "// 定义一个简单的reducer函数\nfunction counterReducer(state, action) {\n  switch (action.type) {\n    case 'increment':\n      return { count: state.count + 1 };\n    case 'decrement':\n      return { count: state.count - 1 };\n    case 'reset':\n      return { count: 0 };\n    default:\n      throw new Error('未知的action类型');\n  }\n}", "explanation": "这个reducer函数处理计数器的状态更新，根据不同的action类型执行不同的状态更新逻辑。"}]}}, {"name": "useReducer Hook", "trans": ["使用useReducer钩子"], "usage": {"syntax": "const [state, dispatch] = useReducer(reducer, initialState, init?)", "description": "useReducer是React的一个Hook，用于管理复杂的组件状态逻辑。它是useState的替代方案，更适合处理包含多个子值的复杂状态对象或下一个状态依赖于之前状态的情况。", "parameters": [{"name": "reducer", "description": "接收state和action的reducer函数"}, {"name": "initialState", "description": "初始状态值"}, {"name": "init", "description": "可选的初始化函数，用于惰性初始化"}], "returnValue": "返回当前状态值和dispatch函数的数组", "examples": [{"code": "import React, { useReducer } from 'react';\n\n// 定义reducer函数\nfunction counterReducer(state, action) {\n  switch (action.type) {\n    case 'increment':\n      return { count: state.count + 1 };\n    case 'decrement':\n      return { count: state.count - 1 };\n    default:\n      return state;\n  }\n}\n\nfunction Counter() {\n  // 使用useReducer初始化状态和dispatch函数\n  const [state, dispatch] = useReducer(counterReducer, { count: 0 });\n\n  return (\n    <div>\n      <p>计数: {state.count}</p>\n      <button onClick={() => dispatch({ type: 'increment' })}>增加</button>\n      <button onClick={() => dispatch({ type: 'decrement' })}>减少</button>\n    </div>\n  );\n}", "explanation": "这个例子展示了如何使用useReducer来管理计数器状态。通过dispatch函数发送不同类型的action来更新状态。"}]}}, {"name": "Dispatching Actions", "trans": ["分发动作(dispatch)"], "usage": {"syntax": "dispatch(action)", "description": "dispatch是useReducer返回的函数，用于发送action到reducer以更新状态。action通常是一个包含type属性的对象，可以包含额外的数据（通常称为payload）。", "parameters": [{"name": "action", "description": "一个描述要执行的状态更新的对象，通常包含type和可选的payload"}], "returnValue": "无返回值", "examples": [{"code": "// 基本的dispatch调用\ndispatch({ type: 'increment' });\n\n// 带有payload的dispatch调用\ndispatch({ type: 'add', payload: 5 });\n\n// 在事件处理程序中使用\n<button onClick={() => dispatch({ type: 'reset' })}>重置</button>", "explanation": "这些示例展示了如何使用dispatch函数发送不同类型的action，包括带有额外数据的action。"}]}}, {"name": "Complex State Management", "trans": ["复杂状态管理"], "usage": {"syntax": "const [state, dispatch] = useReducer(reducer, initialComplexState)", "description": "useReducer特别适合管理包含多个子值的复杂状态对象，或者当下一个状态依赖于前一个状态时。对于具有复杂状态逻辑的组件，使用useReducer通常比useState更清晰。", "parameters": [{"name": "reducer", "description": "处理复杂状态逻辑的reducer函数"}, {"name": "initialComplexState", "description": "初始的复杂状态对象"}], "returnValue": "返回当前状态和dispatch函数的数组", "examples": [{"code": "import React, { useReducer } from 'react';\n\n// 定义一个处理复杂表单状态的reducer\nfunction formReducer(state, action) {\n  switch (action.type) {\n    case 'update_field':\n      return {\n        ...state,\n        [action.field]: action.value,\n        errors: {\n          ...state.errors,\n          [action.field]: action.value ? '' : `${action.field}不能为空`\n        }\n      };\n    case 'submit':\n      return {\n        ...state,\n        isSubmitting: true\n      };\n    case 'submit_success':\n      return {\n        ...state,\n        isSubmitting: false,\n        isSubmitted: true\n      };\n    case 'reset':\n      return initialState;\n    default:\n      return state;\n  }\n}\n\nconst initialState = {\n  username: '',\n  email: '',\n  password: '',\n  errors: {},\n  isSubmitting: false,\n  isSubmitted: false\n};\n\nfunction ComplexForm() {\n  const [state, dispatch] = useReducer(formReducer, initialState);\n\n  const handleChange = (e) => {\n    dispatch({\n      type: 'update_field',\n      field: e.target.name,\n      value: e.target.value\n    });\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    dispatch({ type: 'submit' });\n    \n    // 模拟API调用\n    setTimeout(() => {\n      dispatch({ type: 'submit_success' });\n    }, 1000);\n  };\n\n  return (\n    <form onSubmit={handleSubmit}>\n      <div>\n        <input\n          name=\"username\"\n          value={state.username}\n          onChange={handleChange}\n          placeholder=\"用户名\"\n        />\n        {state.errors.username && <p>{state.errors.username}</p>}\n      </div>\n      <div>\n        <input\n          name=\"email\"\n          value={state.email}\n          onChange={handleChange}\n          placeholder=\"邮箱\"\n        />\n        {state.errors.email && <p>{state.errors.email}</p>}\n      </div>\n      <div>\n        <input\n          type=\"password\"\n          name=\"password\"\n          value={state.password}\n          onChange={handleChange}\n          placeholder=\"密码\"\n        />\n        {state.errors.password && <p>{state.errors.password}</p>}\n      </div>\n      <button type=\"submit\" disabled={state.isSubmitting}>\n        {state.isSubmitting ? '提交中...' : '提交'}\n      </button>\n      {state.isSubmitted && <p>表单提交成功！</p>}\n    </form>\n  );\n}", "explanation": "这个例子展示了如何使用useReducer管理包含多个字段、错误状态和提交状态的复杂表单。reducer函数处理不同类型的action来更新表单状态的不同部分。"}]}}, {"name": "useReducer vs useState", "trans": ["与useState对比"], "usage": {"syntax": "// useState: const [state, setState] = useState(initialState);\n// useReducer: const [state, dispatch] = useReducer(reducer, initialState);", "description": "useReducer和useState都是React的状态管理Hook，但它们适用于不同的场景。useReducer更适合处理复杂的状态逻辑，特别是当状态包含多个子值或下一个状态依赖于前一个状态时。useState更适合简单的状态管理。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 使用useState处理简单状态\nimport React, { useState } from 'react';\n\nfunction CounterWithState() {\n  const [count, setCount] = useState(0);\n\n  return (\n    <div>\n      <p>计数: {count}</p>\n      <button onClick={() => setCount(count + 1)}>增加</button>\n      <button onClick={() => setCount(count - 1)}>减少</button>\n    </div>\n  );\n}\n\n// 使用useReducer处理相同的状态\nimport React, { useReducer } from 'react';\n\nfunction counterReducer(state, action) {\n  switch (action.type) {\n    case 'increment':\n      return state + 1;\n    case 'decrement':\n      return state - 1;\n    default:\n      return state;\n  }\n}\n\nfunction CounterWithReducer() {\n  const [count, dispatch] = useReducer(counterReducer, 0);\n\n  return (\n    <div>\n      <p>计数: {count}</p>\n      <button onClick={() => dispatch({ type: 'increment' })}>增加</button>\n      <button onClick={() => dispatch({ type: 'decrement' })}>减少</button>\n    </div>\n  );\n}", "explanation": "这个例子对比了使用useState和useReducer实现相同功能的两种方式。对于简单的计数器，useState可能更简洁，但随着状态逻辑变得复杂，useReducer可能提供更好的可维护性和可测试性。"}]}}, {"name": "Lazy Initialization", "trans": ["惰性初始化"], "usage": {"syntax": "const [state, dispatch] = useReducer(reducer, initialArg, init)", "description": "useReducer支持惰性初始化，通过提供第三个参数init函数来实现。这对于计算初始状态的昂贵操作特别有用，因为初始化函数只在组件的初始渲染时执行一次。", "parameters": [{"name": "reducer", "description": "状态更新的reducer函数"}, {"name": "initialArg", "description": "传递给初始化函数的参数"}, {"name": "init", "description": "初始化函数，接收initialArg并返回初始状态"}], "returnValue": "返回初始化的状态和dispatch函数的数组", "examples": [{"code": "import React, { useReducer } from 'react';\n\nfunction init(initialCount) {\n  // 这里可以进行复杂的初始状态计算\n  return { count: initialCount, lastAction: null };\n}\n\nfunction reducer(state, action) {\n  switch (action.type) {\n    case 'increment':\n      return { ...state, count: state.count + 1, lastAction: 'increment' };\n    case 'decrement':\n      return { ...state, count: state.count - 1, lastAction: 'decrement' };\n    case 'reset':\n      return init(action.payload);\n    default:\n      throw new Error('未知的action类型');\n  }\n}\n\nfunction Counter({ initialCount = 0 }) {\n  // 使用惰性初始化\n  const [state, dispatch] = useReducer(reducer, initialCount, init);\n\n  return (\n    <div>\n      <p>计数: {state.count}</p>\n      <p>上次操作: {state.lastAction || '无'}</p>\n      <button onClick={() => dispatch({ type: 'increment' })}>增加</button>\n      <button onClick={() => dispatch({ type: 'decrement' })}>减少</button>\n      <button onClick={() => dispatch({ type: 'reset', payload: initialCount })}>重置</button>\n    </div>\n  );\n}", "explanation": "这个例子展示了如何使用惰性初始化来设置初始状态。init函数接收initialCount作为参数并返回完整的初始状态对象。reset action也可以重用init函数来重置状态。"}]}}, {"name": "useReducer with Context", "trans": ["结合Context使用"], "usage": {"syntax": "// 创建Context\nconst StateContext = createContext();\nconst DispatchContext = createContext();\n\n// 在父组件中提供Context\n<StateContext.Provider value={state}>\n  <DispatchContext.Provider value={dispatch}>\n    {children}\n  </DispatchContext.Provider>\n</StateContext.Provider>", "description": "useReducer可以与React Context结合使用，实现在组件树中深层次传递状态和dispatch函数而不必通过props层层传递。这种模式对于大型应用中的状态管理特别有用，类似于Redux的功能但不需要额外的库。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import React, { createContext, useContext, useReducer } from 'react';\n\n// 创建Context\nconst StateContext = createContext();\nconst DispatchContext = createContext();\n\n// Reducer函数\nfunction taskReducer(state, action) {\n  switch (action.type) {\n    case 'ADD_TASK':\n      return [...state, { id: Date.now(), text: action.payload, completed: false }];\n    case 'TOGGLE_TASK':\n      return state.map(task => \n        task.id === action.payload ? { ...task, completed: !task.completed } : task\n      );\n    case 'DELETE_TASK':\n      return state.filter(task => task.id !== action.payload);\n    default:\n      return state;\n  }\n}\n\n// Context Provider组件\nfunction TaskProvider({ children }) {\n  const [tasks, dispatch] = useReducer(taskReducer, []);\n\n  return (\n    <StateContext.Provider value={tasks}>\n      <DispatchContext.Provider value={dispatch}>\n        {children}\n      </DispatchContext.Provider>\n    </StateContext.Provider>\n  );\n}\n\n// 自定义Hook，用于访问Context\nfunction useTasks() {\n  const context = useContext(StateContext);\n  if (context === undefined) {\n    throw new Error('useTasks必须在TaskProvider内部使用');\n  }\n  return context;\n}\n\nfunction useTaskDispatch() {\n  const context = useContext(DispatchContext);\n  if (context === undefined) {\n    throw new Error('useTaskDispatch必须在TaskProvider内部使用');\n  }\n  return context;\n}\n\n// 任务列表组件\nfunction TaskList() {\n  const tasks = useTasks();\n  \n  return (\n    <ul>\n      {tasks.map(task => (\n        <TaskItem key={task.id} task={task} />\n      ))}\n    </ul>\n  );\n}\n\n// 任务项组件\nfunction TaskItem({ task }) {\n  const dispatch = useTaskDispatch();\n  \n  return (\n    <li style={{ textDecoration: task.completed ? 'line-through' : 'none' }}>\n      <input\n        type=\"checkbox\"\n        checked={task.completed}\n        onChange={() => dispatch({ type: 'TOGGLE_TASK', payload: task.id })}\n      />\n      {task.text}\n      <button onClick={() => dispatch({ type: 'DELETE_TASK', payload: task.id })}>\n        删除\n      </button>\n    </li>\n  );\n}\n\n// 添加任务表单\nfunction AddTask() {\n  const dispatch = useTaskDispatch();\n  const [text, setText] = React.useState('');\n  \n  const handleSubmit = (e) => {\n    e.preventDefault();\n    if (text.trim()) {\n      dispatch({ type: 'ADD_TASK', payload: text });\n      setText('');\n    }\n  };\n  \n  return (\n    <form onSubmit={handleSubmit}>\n      <input\n        value={text}\n        onChange={(e) => setText(e.target.value)}\n        placeholder=\"添加新任务...\"\n      />\n      <button type=\"submit\">添加</button>\n    </form>\n  );\n}\n\n// 应用组件\nfunction TaskApp() {\n  return (\n    <TaskProvider>\n      <h1>任务列表</h1>\n      <AddTask />\n      <TaskList />\n    </TaskProvider>\n  );\n}", "explanation": "这个例子展示了如何将useReducer与Context结合使用，创建一个简单的任务管理应用。通过Context提供状态和dispatch函数，任何层级的子组件都可以直接访问它们而不需要通过props传递。这种模式类似于Redux，但使用React内置的API实现。"}]}}, {"name": "Assignment: Create a Shopping Cart", "trans": ["作业：创建购物车"], "usage": {"syntax": "// 使用useReducer实现购物车功能", "description": "创建一个使用useReducer管理状态的购物车应用。购物车应该支持添加商品、删除商品、更新商品数量和清空购物车等功能。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 任务要求：\n// 1. 创建一个购物车reducer，处理添加、删除、更新数量和清空购物车的action\n// 2. 使用useReducer管理购物车状态\n// 3. 创建商品列表和购物车界面\n// 4. 实现商品的添加、删除和数量调整功能\n// 5. 计算购物车总金额\n// 6. 使用Context在组件间共享购物车状态和dispatch函数\n\n// 示例代码结构：\n// 1. 创建reducer函数\nfunction cartReducer(state, action) {\n  switch (action.type) {\n    case 'ADD_ITEM':\n      // 实现添加商品逻辑\n    case 'REMOVE_ITEM':\n      // 实现删除商品逻辑\n    case 'UPDATE_QUANTITY':\n      // 实现更新数量逻辑\n    case 'CLEAR_CART':\n      // 实现清空购物车逻辑\n    default:\n      return state;\n  }\n}\n\n// 2. 创建Context Provider\nfunction CartProvider({ children }) {\n  const [cart, dispatch] = useReducer(cartReducer, []);\n  \n  // 计算总金额\n  const total = cart.reduce((sum, item) => sum + item.price * item.quantity, 0);\n  \n  return (\n    <CartContext.Provider value={{ cart, dispatch, total }}>\n      {children}\n    </CartContext.Provider>\n  );\n}\n\n// 3. 创建商品列表和购物车组件\nfunction ProductList() {\n  // 商品数据\n  const products = [\n    { id: 1, name: '商品1', price: 10 },\n    { id: 2, name: '商品2', price: 20 },\n    { id: 3, name: '商品3', price: 30 }\n  ];\n  \n  // 实现添加到购物车功能\n}\n\nfunction Cart() {\n  // 实现购物车展示、数量调整和删除功能\n}\n\n// 4. 创建应用主组件\nfunction ShoppingApp() {\n  return (\n    <CartProvider>\n      <h1>购物应用</h1>\n      <div className=\"container\">\n        <ProductList />\n        <Cart />\n      </div>\n    </CartProvider>\n  );\n}", "explanation": "这个作业要求学生使用useReducer和Context创建一个购物车应用，实践复杂状态管理的概念。学生需要实现购物车的核心功能，包括添加商品、删除商品、更新数量和计算总金额。"}]}}]}