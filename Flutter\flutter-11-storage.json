{"name": "Local Storage", "trans": ["本地存储"], "methods": [{"name": "SharedPreferences", "trans": ["SharedPreferences"], "usage": {"syntax": "import 'package:shared_preferences/shared_preferences.dart'; await SharedPreferences.getInstance();", "description": "SharedPreferences用于简单的本地键值对存储，适合保存用户设置、登录状态等轻量数据。", "parameters": [{"name": "key", "description": "存储的键名"}, {"name": "value", "description": "存储的值，支持基本类型"}], "returnValue": "Future<SharedPreferences>，用于读写本地数据。", "examples": [{"code": "// SharedPreferences示例\nimport 'package:flutter/material.dart';\nimport 'package:shared_preferences/shared_preferences.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(home: HomePage());\n  }\n}\n\nclass HomePage extends StatefulWidget {\n  @override\n  _HomePageState createState() => _HomePageState();\n}\n\nclass _HomePageState extends State<HomePage> {\n  String _value = '无';\n  Future<void> saveData() async {\n    final prefs = await SharedPreferences.getInstance();\n    await prefs.setString('key', 'Hello Flutter');\n  }\n  Future<void> loadData() async {\n    final prefs = await SharedPreferences.getInstance();\n    setState(() { _value = prefs.getString('key') ?? '无'; });\n  }\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('SharedPreferences示例')),\n      body: Center(\n        child: Column(\n          mainAxisAlignment: MainAxisAlignment.center,\n          children: [\n            Text('存储值: \\${_value}'),\n            ElevatedButton(onPressed: saveData, child: Text('保存')),\n            ElevatedButton(onPressed: loadData, child: Text('读取')),\n          ],\n        ),\n      ),\n    );\n  }\n}\n", "explanation": "演示了SharedPreferences的基本用法，实现本地键值对存储和读取。"}]}}, {"name": "File Read and Write", "trans": ["文件读写"], "usage": {"syntax": "import 'dart:io'; File('path').writeAsString('内容'); File('path').readAsString();", "description": "Dart的dart:io库支持本地文件的读写，适合存储文本、配置等数据。", "parameters": [{"name": "path", "description": "文件路径"}, {"name": "content", "description": "写入的内容"}], "returnValue": "Future<String>，读取时返回文件内容。", "examples": [{"code": "// 文件读写示例\nimport 'package:flutter/material.dart';\nimport 'dart:io';\nimport 'package:path_provider/path_provider.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(home: HomePage());\n  }\n}\n\nclass HomePage extends StatefulWidget {\n  @override\n  _HomePageState createState() => _HomePageState();\n}\n\nclass _HomePageState extends State<HomePage> {\n  String _content = '无';\n  Future<String> get _localPath async {\n    final dir = await getApplicationDocumentsDirectory();\n    return dir.path;\n  }\n  Future<File> get _localFile async {\n    final path = await _localPath;\n    return File('$path/demo.txt');\n  }\n  Future<void> writeFile() async {\n    final file = await _localFile;\n    await file.writeAsString('Hello File');\n  }\n  Future<void> readFile() async {\n    try {\n      final file = await _localFile;\n      String contents = await file.readAsString();\n      setState(() { _content = contents; });\n    } catch (e) {\n      setState(() { _content = '读取失败'; });\n    }\n  }\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('文件读写示例')),\n      body: Center(\n        child: Column(\n          mainAxisAlignment: MainAxisAlignment.center,\n          children: [\n            Text('文件内容: \\${_content}'),\n            ElevatedButton(onPressed: writeFile, child: Text('写入')),\n            ElevatedButton(onPressed: readFile, child: Text('读取')),\n          ],\n        ),\n      ),\n    );\n  }\n}\n", "explanation": "演示了文件的写入和读取操作。"}]}}, {"name": "Database (sqflite)", "trans": ["数据库（sqflite）"], "usage": {"syntax": "import 'package:sqflite/sqflite.dart'; openDatabase(...);", "description": "sqflite是Flutter常用的本地数据库插件，支持SQL语句操作，适合结构化数据存储。", "parameters": [{"name": "path", "description": "数据库文件路径"}, {"name": "SQL", "description": "执行的SQL语句"}], "returnValue": "Future<Database>，用于增删改查操作。", "examples": [{"code": "// sqflite数据库示例\nimport 'package:flutter/material.dart';\nimport 'package:sqflite/sqflite.dart';\nimport 'package:path/path.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(home: HomePage());\n  }\n}\n\nclass HomePage extends StatefulWidget {\n  @override\n  _HomePageState createState() => _HomePageState();\n}\n\nclass _HomePageState extends State<HomePage> {\n  String _result = '无';\n  Database? _db;\n  Future<void> openDb() async {\n    String dbPath = await getDatabasesPath();\n    _db = await openDatabase(join(dbPath, 'demo.db'), version: 1, onCreate: (db, version) {\n      db.execute('CREATE TABLE test (id INTEGER PRIMARY KEY, value TEXT)');\n    });\n  }\n  Future<void> insertData() async {\n    await openDb();\n    await _db!.insert('test', {'value': 'Hello DB'});\n  }\n  Future<void> queryData() async {\n    await openDb();\n    List<Map> list = await _db!.query('test');\n    setState(() { _result = list.isNotEmpty ? list.last['value'] : '无数据'; });\n  }\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('sqflite数据库示例')),\n      body: Center(\n        child: Column(\n          mainAxisAlignment: MainAxisAlignment.center,\n          children: [\n            Text('数据库内容: \\${_result}'),\n            ElevatedButton(onPressed: insertData, child: Text('插入')),\n            ElevatedButton(onPressed: queryData, child: Text('查询')),\n          ],\n        ),\n      ),\n    );\n  }\n}\n", "explanation": "演示了sqflite数据库的插入和查询操作。"}]}}, {"name": "Data Encryption and Security", "trans": ["数据加密与安全"], "usage": {"syntax": "import 'package:encrypt/encrypt.dart';加密解密字符串。", "description": "可通过encrypt等第三方库实现本地数据加密，保护敏感信息安全。", "parameters": [{"name": "key", "description": "加密密钥"}, {"name": "iv", "description": "初始化向量"}], "returnValue": "加密/解密后的字符串。", "examples": [{"code": "// 数据加密与安全示例\nimport 'package:encrypt/encrypt.dart';\n\nvoid main() {\n  final key = Key.fromUtf8('my32lengthsupersecretnooneknows1');\n  final iv = IV.fromLength(16);\n  final encrypter = Encrypter(AES(key));\n  final encrypted = encrypter.encrypt('Hello Flutter', iv: iv);\n  final decrypted = encrypter.decrypt(encrypted, iv: iv);\n  print('加密后: \\${encrypted.base64}');\n  print('解密后: \\${decrypted}');\n}\n", "explanation": "演示了encrypt库的AES加密与解密用法。"}]}}, {"name": "Assignment", "trans": ["作业：实现本地存储功能，保存和读取一段文本。"], "usage": {"syntax": "无", "description": "请用SharedPreferences或文件读写实现一个页面，输入文本后保存到本地，并能读取显示。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 作业参考实现\nimport 'package:flutter/material.dart';\nimport 'package:shared_preferences/shared_preferences.dart';\n\nvoid main() => runApp(MyApp());\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(home: HomePage());\n  }\n}\n\nclass HomePage extends StatefulWidget {\n  @override\n  _HomePageState createState() => _HomePageState();\n}\n\nclass _HomePageState extends State<HomePage> {\n  final TextEditingController _controller = TextEditingController();\n  String _value = '无';\n  Future<void> saveData() async {\n    final prefs = await SharedPreferences.getInstance();\n    await prefs.setString('key', _controller.text);\n  }\n  Future<void> loadData() async {\n    final prefs = await SharedPreferences.getInstance();\n    setState(() { _value = prefs.getString('key') ?? '无'; });\n  }\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(title: Text('作业实现')),\n      body: Padding(\n        padding: EdgeInsets.all(16),\n        child: Column(\n          children: [\n            TextField(controller: _controller, decoration: InputDecoration(labelText: '请输入内容')),\n            SizedBox(height: 16),\n            ElevatedButton(onPressed: saveData, child: Text('保存')),\n            ElevatedButton(onPressed: loadData, child: Text('读取')),\n            SizedBox(height: 16),\n            Text('本地内容: \\${_value}'),\n          ],\n        ),\n      ),\n    );\n  }\n}\n", "explanation": "作业要求实现本地存储和读取功能。"}]}}]}