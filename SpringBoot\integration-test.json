{"name": "Integration Testing", "trans": ["集成测试"], "methods": [{"name": "@SpringBootTest Usage", "trans": ["@SpringBootTest用法"], "usage": {"syntax": "@SpringBootTest\n@Test\nvoid testIntegration() { ... }", "description": "@SpringBootTest可加载完整Spring Boot应用环境，适合集成测试，支持Web环境、Mock环境等多种模式。", "parameters": [{"name": "@SpringBootTest", "description": "加载Spring Boot应用上下文"}, {"name": "webEnvironment", "description": "指定Web环境类型（MOCK、RANDOM_PORT等）"}], "returnValue": "无返回值", "examples": [{"code": "@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)\npublic class IntegrationTest {\n    @Test\n    void testApp() {\n        // 集成测试逻辑\n    }\n}", "explanation": "演示了@SpringBootTest在集成测试中的用法。"}]}}, {"name": "Test Environment Isolation", "trans": ["测试环境隔离"], "usage": {"syntax": "@DirtiesContext、@TestPropertySource、@ActiveProfiles注解", "description": "通过@DirtiesContext、@TestPropertySource、@ActiveProfiles等注解可实现测试环境隔离，避免数据污染和配置冲突。", "parameters": [{"name": "@DirtiesContext", "description": "每次测试后重置Spring上下文"}, {"name": "@TestPropertySource", "description": "为测试类指定专用配置文件"}, {"name": "@ActiveProfiles", "description": "指定激活的Profile环境"}], "returnValue": "无返回值", "examples": [{"code": "@SpringBootTest\n@ActiveProfiles(\"test\")\n@TestPropertySource(locations = \"classpath:application-test.properties\")\n@DirtiesContext\npublic class IsolatedTest {\n    @Test\n    void testIsolation() {\n        // 隔离环境下的测试逻辑\n    }\n}", "explanation": "演示了测试环境隔离的常用注解和用法。"}]}}, {"name": "Test Data Preparation", "trans": ["测试数据准备"], "usage": {"syntax": "@Sql、@BeforeEach、@BeforeAll注解", "description": "通过@Sql注解可在测试前后执行SQL脚本，@BeforeEach/@BeforeAll可初始化测试数据，保证测试的独立性和可重复性。", "parameters": [{"name": "@Sql", "description": "执行SQL脚本初始化或清理数据"}, {"name": "@BeforeEach", "description": "每个测试方法前执行初始化"}, {"name": "@BeforeAll", "description": "所有测试前执行一次初始化"}], "returnValue": "无返回值", "examples": [{"code": "@SpringBootTest\npublic class DataTest {\n    @BeforeEach\n    void setUp() {\n        // 初始化测试数据\n    }\n    @Sql(\"classpath:insert_test_data.sql\")\n    @Test\n    void testWithData() {\n        // 使用初始化数据的测试\n    }\n}", "explanation": "演示了@Sql和@BeforeEach进行测试数据准备的方法。"}]}}, {"name": "Containerized Test (Testcontainers)", "trans": ["容器化测试（Testcontainers）"], "usage": {"syntax": "@Testcontainers、@Container注解，定义容器资源", "description": "Testcontainers可在测试时自动拉起数据库、Redis等依赖服务的Docker容器，实现真实环境下的集成测试。", "parameters": [{"name": "@Testcontainers", "description": "标记测试类使用Testcontainers"}, {"name": "@Container", "description": "定义具体的容器资源"}], "returnValue": "无返回值", "examples": [{"code": "@Testcontainers\npublic class ContainerTest {\n    @Container\n    public static MySQLContainer<?> mysql = new MySQLContainer<>(\"mysql:8.0\");\n    @Test\n    void testWithContainer() {\n        // 使用容器化数据库的测试\n    }\n}", "explanation": "演示了Testcontainers实现容器化集成测试的方法。"}]}}, {"name": "Integration Test Assignment", "trans": ["集成测试练习"], "usage": {"syntax": "# 集成测试练习", "description": "完成以下练习，巩固Spring Boot集成测试相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 使用@SpringBootTest编写一个集成测试类。\n2. 使用@ActiveProfiles和@TestPropertySource实现环境隔离。\n3. 使用@Sql和@BeforeEach准备测试数据。\n4. 使用Testcontainers实现数据库集成测试。", "explanation": "通过这些练习掌握Spring Boot集成测试的核心用法。"}]}}]}