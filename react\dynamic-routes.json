{"name": "Dynamic Routes", "trans": ["动态路由"], "methods": [{"name": "Path Parameters", "trans": ["路径参数"], "usage": {"syntax": "<Route path=\"/users/:userId\" element={<UserProfile />} />", "description": "路径参数是指URL中可变的部分，通过冒号(:)语法定义。它们允许一个路由组件处理多个特定路径模式，参数值可以通过useParams钩子在组件内访问。", "parameters": [{"name": "path", "description": "路由路径，包含由冒号(:)标识的参数。"}, {"name": "userId", "description": "路径参数名，可以在组件中通过useParams()获取。"}, {"name": "element", "description": "当路径匹配时渲染的React组件。"}, {"name": "UserProfile", "description": "用于渲染的组件，可以访问路径参数。"}], "returnValue": "无返回值", "examples": [{"code": "// 定义带路径参数的路由\nimport { Routes, Route, useParams, Link } from 'react-router-dom';\n\n// 路由配置\nfunction AppRoutes() {\n  return (\n    <Routes>\n      {/* 静态路由 */}\n      <Route path=\"/users\" element={<UserList />} />\n      \n      {/* 动态路由，使用:userId作为路径参数 */}\n      <Route path=\"/users/:userId\" element={<UserProfile />} />\n      \n      {/* 多参数路由 */}\n      <Route path=\"/users/:userId/posts/:postId\" element={<UserPost />} />\n    </Routes>\n  );\n}\n\n// 用户列表页面\nfunction UserList() {\n  return (\n    <div>\n      <h1>用户列表</h1>\n      <ul>\n        <li><Link to=\"/users/1\">用户1</Link></li>\n        <li><Link to=\"/users/2\">用户2</Link></li>\n        <li><Link to=\"/users/3\">用户3</Link></li>\n      </ul>\n    </div>\n  );\n}\n\n// 用户资料页面 - 使用useParams获取参数\nfunction UserProfile() {\n  // 解构获取URL参数\n  const { userId } = useParams();\n  \n  return (\n    <div>\n      <h1>用户资料</h1>\n      <p>用户ID: {userId}</p>\n      <Link to={`/users/${userId}/posts/1`}>查看第一篇文章</Link>\n    </div>\n  );\n}\n\n// 用户文章页面 - 使用多个参数\nfunction UserPost() {\n  // 获取所有参数\n  const params = useParams();\n  const { userId, postId } = params;\n  \n  return (\n    <div>\n      <h1>用户文章</h1>\n      <p>用户ID: {userId}</p>\n      <p>文章ID: {postId}</p>\n    </div>\n  );\n}", "explanation": "展示了如何定义带有路径参数的路由，以及如何在组件内部使用useParams钩子获取这些参数。"}]}}, {"name": "Assignment: 路径参数", "trans": ["练习：路径参数"], "usage": {"syntax": "// 实现一个商品详情路由，使用商品ID作为路径参数", "description": "要求：创建/products/:productId路由，在ProductDetail组件中获取并显示商品ID。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 完成路由配置和ProductDetail组件", "explanation": "练习题，无需标准答案。"}]}}, {"name": "Query Parameters", "trans": ["查询参数"], "usage": {"syntax": "// 使用useSearchParams钩子处理查询参数\nconst [searchParams, setSearchParams] = useSearchParams();", "description": "查询参数是URL中?后面的键值对，用于传递非层次化的可选数据。React Router通过useSearchParams钩子提供查询参数的读取和修改能力。", "parameters": [{"name": "useSearchParams", "description": "React Router提供的钩子，用于读取和修改URL查询参数。"}, {"name": "searchParams", "description": "类似URLSearchParams的对象，用于获取查询参数值。"}, {"name": "setSearchParams", "description": "用于更新查询参数的函数。"}], "returnValue": "返回一个包含当前搜索参数和更新函数的数组[searchParams, setSearchParams]。", "examples": [{"code": "// 查询参数示例 - 实现筛选和分页功能\nimport { useSearchParams, Link } from 'react-router-dom';\n\nfunction ProductList() {\n  // 获取查询参数和更新函数\n  const [searchParams, setSearchParams] = useSearchParams();\n  \n  // 从查询参数中读取值，提供默认值\n  const category = searchParams.get('category') || 'all';\n  const page = parseInt(searchParams.get('page') || '1');\n  \n  // 模拟产品数据\n  const products = [/*...产品数据*/];\n  \n  // 根据分类筛选产品\n  const filteredProducts = category === 'all' \n    ? products \n    : products.filter(product => product.category === category);\n  \n  // 处理分类变更\n  const handleCategoryChange = (newCategory) => {\n    // 更新查询参数，保持页码为1\n    setSearchParams({ category: newCategory, page: 1 });\n  };\n  \n  // 处理翻页\n  const goToPage = (newPage) => {\n    // 更新页码，保持分类不变\n    setSearchParams({ category, page: newPage });\n  };\n  \n  return (\n    <div>\n      <h1>产品列表</h1>\n      \n      {/* 分类筛选 */}\n      <div className=\"filters\">\n        <button \n          onClick={() => handleCategoryChange('all')} \n          className={category === 'all' ? 'active' : ''}\n        >\n          全部\n        </button>\n        <button \n          onClick={() => handleCategoryChange('electronics')} \n          className={category === 'electronics' ? 'active' : ''}\n        >\n          电子产品\n        </button>\n        <button \n          onClick={() => handleCategoryChange('books')} \n          className={category === 'books' ? 'active' : ''}\n        >\n          图书\n        </button>\n      </div>\n      \n      {/* 产品列表 */}\n      <ul className=\"product-list\">\n        {filteredProducts.map(product => (\n          <li key={product.id}>\n            <Link to={`/products/${product.id}?category=${category}`}>\n              {product.name}\n            </Link>\n          </li>\n        ))}\n      </ul>\n      \n      {/* 分页 */}\n      <div className=\"pagination\">\n        <button \n          onClick={() => goToPage(page - 1)} \n          disabled={page === 1}\n        >\n          上一页\n        </button>\n        <span>第 {page} 页</span>\n        <button \n          onClick={() => goToPage(page + 1)} \n          disabled={page * 10 >= filteredProducts.length}\n        >\n          下一页\n        </button>\n      </div>\n      \n      {/* 显示当前URL状态 */}\n      <div className=\"url-info\">\n        <p>当前URL: /products?category={category}&page={page}</p>\n      </div>\n    </div>\n  );\n}", "explanation": "展示了如何使用useSearchParams钩子读取和更新URL查询参数，实现产品筛选和分页功能。"}]}}, {"name": "Assignment: 查询参数", "trans": ["练习：查询参数"], "usage": {"syntax": "// 实现一个带搜索功能的用户列表，使用查询参数存储搜索词", "description": "要求：创建一个搜索框，输入搜索词后更新URL查询参数search，并根据search参数筛选显示用户列表。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 完成UserSearchList组件，实现查询参数的使用", "explanation": "练习题，无需标准答案。"}]}}, {"name": "Optional Parameters", "trans": ["可选参数"], "usage": {"syntax": "<Route path=\"/products/:category?\" element={<ProductList />} />", "description": "可选参数是指URL路径中不强制要求出现的部分。在React Router中，可以通过添加问号(?)标记路径参数为可选，或使用多个路由定义来实现可选参数效果。", "parameters": [{"name": "path", "description": "路由路径，包含可选参数。"}, {"name": "?", "description": "在参数名后添加问号，表示该参数是可选的。"}, {"name": "element", "description": "当路径匹配时渲染的React组件。"}], "returnValue": "无返回值", "examples": [{"code": "// 可选参数实现方式\nimport { Routes, Route, useParams } from 'react-router-dom';\n\n// 方式一：使用两个独立的路由（推荐，更明确）\nfunction AppRoutes() {\n  return (\n    <Routes>\n      {/* 不带参数的路由 */}\n      <Route path=\"/products\" element={<ProductList />} />\n      \n      {/* 带参数的路由 */}\n      <Route path=\"/products/:category\" element={<ProductList />} />\n    </Routes>\n  );\n}\n\n// 方式二：使用React Router v6.4+的新语法标记可选参数\nfunction AppRoutesV64() {\n  return (\n    <Routes>\n      {/* 使用?标记category为可选参数 */}\n      <Route path=\"/products/:category?\" element={<ProductList />} />\n    </Routes>\n  );\n}\n\n// 处理可选参数的组件\nfunction ProductList() {\n  const { category } = useParams();\n  \n  return (\n    <div>\n      <h1>产品列表</h1>\n      {category ? (\n        <p>当前分类: {category}</p>\n      ) : (\n        <p>显示所有分类</p>\n      )}\n      \n      {/* 产品列表内容 */}\n    </div>\n  );\n}", "explanation": "展示了在React Router中实现可选参数的两种方式，以及如何在组件中处理可能为undefined的参数。"}]}}, {"name": "Assignment: 可选参数", "trans": ["练习：可选参数"], "usage": {"syntax": "// 实现一个博客文章路由，包含可选的年份和月份参数", "description": "要求：创建路由/blog、/blog/:year和/blog/:year/:month，在BlogList组件中根据有无参数显示不同内容。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 完成路由配置和BlogList组件", "explanation": "练习题，无需标准答案。"}]}}, {"name": "Parameter Validation", "trans": ["参数验证"], "usage": {"syntax": "// 在组件内验证路由参数\nconst { paramName } = useParams();\nif (!isValidParam(paramName)) {\n  return <Navigate to=\"/error\" />;\n}", "description": "参数验证是确保URL中的参数符合预期格式和范围的过程。React Router没有内置的参数验证机制，但可以在组件内进行验证，并通过Navigate组件实现重定向。", "parameters": [{"name": "useParams", "description": "React Router的钩子，用于获取当前URL的路径参数。"}, {"name": "Navigate", "description": "React Router的组件，用于编程式导航/重定向。"}, {"name": "isValidParam", "description": "自定义验证函数，用于检查参数是否合法。"}], "returnValue": "无返回值", "examples": [{"code": "// 路由参数验证示例\nimport { useParams, Navigate } from 'react-router-dom';\n\nfunction UserProfile() {\n  // 获取用户ID参数\n  const { userId } = useParams();\n  \n  // 参数验证：确保userId是数字\n  const isValidUserId = /^\\d+$/.test(userId);\n  \n  // 如果参数无效，重定向到错误页面\n  if (!isValidUserId) {\n    return <Navigate to=\"/invalid-user-id\" replace />;\n  }\n  \n  // 参数有效，继续渲染组件\n  return (\n    <div>\n      <h1>用户资料</h1>\n      <p>用户ID: {userId}</p>\n    </div>\n  );\n}\n\n// 日期参数验证示例\nfunction BlogPost() {\n  const { year, month, slug } = useParams();\n  \n  // 日期参数验证\n  const isValidYear = /^\\d{4}$/.test(year) && parseInt(year) >= 2000 && parseInt(year) <= new Date().getFullYear();\n  const isValidMonth = /^\\d{1,2}$/.test(month) && parseInt(month) >= 1 && parseInt(month) <= 12;\n  const isValidSlug = /^[a-z0-9-]+$/.test(slug);\n  \n  // 任一参数无效，重定向到404页面\n  if (!isValidYear || !isValidMonth || !isValidSlug) {\n    return <Navigate to=\"/not-found\" replace />;\n  }\n  \n  // 参数都有效，继续渲染\n  return (\n    <div>\n      <h1>博客文章</h1>\n      <p>发布日期: {year}年{month}月</p>\n      <p>文章标识: {slug}</p>\n    </div>\n  );\n}", "explanation": "示例演示了如何在React组件中验证路由参数，并在参数无效时通过Navigate组件重定向到错误页面。"}]}}, {"name": "Assignment: 参数验证", "trans": ["练习：参数验证"], "usage": {"syntax": "// 实现一个产品详情页面，验证productId参数必须为数字", "description": "要求：在ProductDetail组件中验证productId是否为有效的数字，若无效则重定向到错误页面。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 完成ProductDetail组件，包含参数验证逻辑", "explanation": "练习题，无需标准答案。"}]}}, {"name": "Wildcard Routes", "trans": ["通配符路由"], "usage": {"syntax": "<Route path=\"*\" element={<NotFound />} />", "description": "通配符路由使用星号(*)匹配任意路径，常用于创建404页面或捕获所有未明确定义的路由。在React Router中，通配符路由通常放在路由配置的最后，作为兜底方案。", "parameters": [{"name": "path", "description": "路由路径，星号(*)表示匹配任意路径。"}, {"name": "element", "description": "当路径匹配时渲染的组件，通常是404页面。"}, {"name": "NotFound", "description": "用于显示404错误的组件。"}], "returnValue": "无返回值", "examples": [{"code": "// 通配符路由示例\nimport { Routes, Route, Link } from 'react-router-dom';\n\nfunction AppRoutes() {\n  return (\n    <Routes>\n      {/* 普通路由 */}\n      <Route path=\"/\" element={<Home />} />\n      <Route path=\"/about\" element={<About />} />\n      <Route path=\"/contact\" element={<Contact />} />\n      \n      {/* 带参数的路由 */}\n      <Route path=\"/users/:userId\" element={<UserProfile />} />\n      \n      {/* 嵌套路由中的通配符 */}\n      <Route path=\"/dashboard\" element={<Dashboard />}>\n        <Route index element={<DashboardHome />} />\n        <Route path=\"settings\" element={<Settings />} />\n        {/* 匹配/dashboard下的任何未定义路径 */}\n        <Route path=\"*\" element={<DashboardNotFound />} />\n      </Route>\n      \n      {/* 全局通配符路由 - 放在最后作为兜底 */}\n      <Route path=\"*\" element={<NotFound />} />\n    </Routes>\n  );\n}\n\n// 404组件\nfunction NotFound() {\n  return (\n    <div>\n      <h1>404 - 页面未找到</h1>\n      <p>您访问的页面不存在。</p>\n      <Link to=\"/\">返回首页</Link>\n    </div>\n  );\n}\n\n// 仪表盘专用404组件\nfunction DashboardNotFound() {\n  return (\n    <div>\n      <h2>仪表盘功能未找到</h2>\n      <p>您访问的仪表盘功能不存在。</p>\n      <Link to=\"/dashboard\">返回仪表盘主页</Link>\n    </div>\n  );\n}", "explanation": "示例展示了如何在React Router中使用通配符路由处理未匹配的路径，包括全局404页面和特定区域的未匹配页面。"}]}}, {"name": "Assignment: 通配符路由", "trans": ["练习：通配符路由"], "usage": {"syntax": "// 实现一个完整的路由系统，包含主路由和404页面", "description": "要求：创建包含首页、关于页、产品列表页的路由系统，并添加通配符路由显示自定义404页面。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 完成路由配置和NotFound组件", "explanation": "练习题，无需标准答案。"}]}}, {"name": "Route Priority", "trans": ["路由优先级"], "usage": {"syntax": "// 路由优先级顺序\n<Routes>\n  {/* 1. 静态路径 */}\n  <Route path=\"/products/new\" element={<NewProduct />} />\n  {/* 2. 动态路径 */}\n  <Route path=\"/products/:id\" element={<Product />} />\n  {/* 3. 通配符 */}\n  <Route path=\"*\" element={<NotFound />} />\n</Routes>", "description": "路由优先级决定了当多个路由模式都能匹配一个URL时，使用哪个路由。在React Router中，路由优先级从高到低依次是：静态路径 > 动态路径 > 通配符路径。路由定义的顺序也会影响匹配结果。", "parameters": [{"name": "Routes", "description": "React Router的容器组件，用于定义路由规则。"}, {"name": "Route", "description": "定义单个路由的组件，包含path和element属性。"}, {"name": "path", "description": "路由路径，可以是静态的、动态的或通配符。"}, {"name": "element", "description": "当路径匹配时渲染的组件。"}], "returnValue": "无返回值", "examples": [{"code": "// 路由优先级示例\nimport { Routes, Route } from 'react-router-dom';\n\nfunction AppRoutes() {\n  return (\n    <Routes>\n      {/* 高优先级：精确的静态路径 */}\n      <Route path=\"/products/featured\" element={<FeaturedProducts />} />\n      <Route path=\"/products/new\" element={<NewProduct />} />\n      \n      {/* 中优先级：带参数的动态路径 */}\n      <Route path=\"/products/:productId\" element={<ProductDetail />} />\n      \n      {/* 低优先级：通配符路径 */}\n      <Route path=\"/products/*\" element={<ProductNotFound />} />\n      \n      {/* 最低优先级：全局通配符 */}\n      <Route path=\"*\" element={<NotFound />} />\n    </Routes>\n  );\n}\n\n// 分析不同URL的匹配情况：\n// URL: /products/featured\n// 匹配: <Route path=\"/products/featured\" element={<FeaturedProducts />} />\n// 原因: 精确的静态路径优先级最高\n\n// URL: /products/new\n// 匹配: <Route path=\"/products/new\" element={<NewProduct />} />\n// 原因: 精确的静态路径优先级最高\n\n// URL: /products/123\n// 匹配: <Route path=\"/products/:productId\" element={<ProductDetail />} />\n// 原因: 动态路径可以匹配，且优先级高于通配符\n\n// URL: /products/category/electronics\n// 匹配: <Route path=\"/products/*\" element={<ProductNotFound />} />\n// 原因: 不匹配前面的路径，但匹配通配符路径\n\n// URL: /about\n// 匹配: <Route path=\"*\" element={<NotFound />} />\n// 原因: 只匹配全局通配符路由", "explanation": "示例演示了路由优先级的工作原理，包括静态路径、动态路径和通配符路径的优先级顺序，以及如何应用这些原则来构建路由配置。"}]}}, {"name": "Assignment: 路由优先级", "trans": ["练习：路由优先级"], "usage": {"syntax": "// 实现一个商品路由系统，处理不同优先级的路由", "description": "要求：创建商品路由系统，包括新商品页(/products/new)、特价商品页(/products/sale)、商品详情页(/products/:id)和兜底的商品类别页(/products/*)。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 完成路由配置，确保按正确优先级排序", "explanation": "练习题，无需标准答案。"}]}}, {"name": "Correct Implementation Example", "trans": ["正确实现示例"], "usage": {"syntax": "// 综合动态路由、参数验证、路由优先级的完整实现", "description": "本示例展示了如何在实际项目中结合使用路径参数、查询参数、参数验证、通配符路由和路由优先级，形成完整的动态路由系统。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 完整动态路由实现示例\nimport { \n  Routes, \n  Route, \n  useParams, \n  useSearchParams, \n  Navigate, \n  Link, \n  Outlet \n} from 'react-router-dom';\n\n// 主应用路由配置\nfunction AppRoutes() {\n  return (\n    <Routes>\n      {/* 主页 */}\n      <Route path=\"/\" element={<Home />} />\n      \n      {/* 商品模块路由 - 注意优先级排序 */}\n      <Route path=\"/products\" element={<ProductLayout />}>\n        {/* 索引路由：商品列表 */}\n        <Route index element={<ProductList />} />\n        \n        {/* 特殊静态路由：优先级高 */}\n        <Route path=\"new\" element={<NewProduct />} />\n        <Route path=\"featured\" element={<FeaturedProducts />} />\n        <Route path=\"sale\" element={<SaleProducts />} />\n        \n        {/* 商品分类路由：带参数 */}\n        <Route path=\"category/:categoryId\" element={<CategoryProducts />} />\n        \n        {/* 商品详情路由：带参数和验证 */}\n        <Route path=\":productId\" element={<ProductDetail />} />\n        \n        {/* 商品模块下的通配符路由 */}\n        <Route path=\"*\" element={<ProductNotFound />} />\n      </Route>\n      \n      {/* 用户模块路由 */}\n      <Route path=\"/users\" element={<UserLayout />}>\n        <Route index element={<UserList />} />\n        <Route path=\":userId\" element={<UserProfile />} />\n        <Route path=\":userId/posts/:postId\" element={<UserPost />} />\n      </Route>\n      \n      {/* 关于页面 - 可选参数演示 */}\n      <Route path=\"/about\" element={<About />} />\n      <Route path=\"/about/:section\" element={<About />} />\n      \n      {/* 全局通配符路由 - 404页面 */}\n      <Route path=\"*\" element={<NotFound />} />\n    </Routes>\n  );\n}\n\n// 商品模块布局组件\nfunction ProductLayout() {\n  return (\n    <div>\n      <h1>商品模块</h1>\n      <nav>\n        <Link to=\"/products\">所有商品</Link> |\n        <Link to=\"/products/new\">新品上市</Link> |\n        <Link to=\"/products/featured\">精选商品</Link> |\n        <Link to=\"/products/sale\">特价商品</Link> |\n        <Link to=\"/products/category/electronics\">电子产品</Link>\n      </nav>\n      <Outlet />\n    </div>\n  );\n}\n\n// 商品列表：演示查询参数\nfunction ProductList() {\n  // 使用查询参数处理筛选和分页\n  const [searchParams, setSearchParams] = useSearchParams();\n  const sort = searchParams.get('sort') || 'newest';\n  const page = parseInt(searchParams.get('page') || '1');\n  \n  // 排序处理函数\n  const handleSortChange = (newSort) => {\n    setSearchParams({ sort: newSort, page });\n  };\n  \n  // 翻页处理函数\n  const handlePageChange = (newPage) => {\n    setSearchParams({ sort, page: newPage });\n  };\n  \n  return (\n    <div>\n      <h2>商品列表</h2>\n      <div className=\"filters\">\n        <label>排序: </label>\n        <select \n          value={sort} \n          onChange={(e) => handleSortChange(e.target.value)}\n        >\n          <option value=\"newest\">最新上架</option>\n          <option value=\"price_low\">价格从低到高</option>\n          <option value=\"price_high\">价格从高到低</option>\n        </select>\n      </div>\n      \n      <div className=\"product-grid\">\n        {/* 商品列表内容 */}\n        <div className=\"product-card\">\n          <h3>商品1</h3>\n          <Link to=\"/products/1\">查看详情</Link>\n        </div>\n        {/* 更多商品... */}\n      </div>\n      \n      <div className=\"pagination\">\n        <button onClick={() => handlePageChange(page - 1)} disabled={page <= 1}>上一页</button>\n        <span>第 {page} 页</span>\n        <button onClick={() => handlePageChange(page + 1)}>下一页</button>\n      </div>\n    </div>\n  );\n}\n\n// 商品详情：演示路径参数和参数验证\nfunction ProductDetail() {\n  const { productId } = useParams();\n  \n  // 参数验证\n  const isValidProductId = /^\\d+$/.test(productId);\n  if (!isValidProductId) {\n    return <Navigate to=\"/products\" replace />\n  }\n  \n  // 获取查询参数\n  const [searchParams] = useSearchParams();\n  const referrer = searchParams.get('ref');\n  \n  return (\n    <div>\n      <h2>商品详情</h2>\n      <p>商品ID: {productId}</p>\n      {referrer && <p>来源: {referrer}</p>}\n      \n      <Link to=\"/products\">返回商品列表</Link>\n    </div>\n  );\n}\n\n// 分类商品：演示嵌套参数\nfunction CategoryProducts() {\n  const { categoryId } = useParams();\n  \n  return (\n    <div>\n      <h2>{categoryId}分类商品</h2>\n      {/* 分类商品列表 */}\n    </div>\n  );\n}\n\n// 关于页面：演示可选参数\nfunction About() {\n  const { section } = useParams();\n  \n  return (\n    <div>\n      <h1>关于我们</h1>\n      \n      {/* 根据可选参数显示不同内容 */}\n      {!section ? (\n        <div>\n          <h2>企业简介</h2>\n          <nav>\n            <Link to=\"/about/history\">公司历史</Link> |\n            <Link to=\"/about/team\">团队介绍</Link> |\n            <Link to=\"/about/contact\">联系方式</Link>\n          </nav>\n        </div>\n      ) : section === 'history' ? (\n        <div>\n          <h2>公司历史</h2>\n          <Link to=\"/about\">返回</Link>\n        </div>\n      ) : section === 'team' ? (\n        <div>\n          <h2>团队介绍</h2>\n          <Link to=\"/about\">返回</Link>\n        </div>\n      ) : section === 'contact' ? (\n        <div>\n          <h2>联系方式</h2>\n          <Link to=\"/about\">返回</Link>\n        </div>\n      ) : (\n        <Navigate to=\"/about\" replace />\n      )}\n    </div>\n  );\n}\n\n// 404页面\nfunction NotFound() {\n  return (\n    <div>\n      <h1>404 - 页面未找到</h1>\n      <p>您访问的页面不存在。</p>\n      <Link to=\"/\">返回首页</Link>\n    </div>\n  );\n}", "explanation": "该示例展示了在实际项目中如何综合使用动态路由的各种技术，包括路径参数、查询参数、路由优先级、参数验证和通配符路由，形成完整的路由系统。"}]}}]}