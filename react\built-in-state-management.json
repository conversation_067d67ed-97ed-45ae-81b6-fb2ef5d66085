{"name": "Built-in State Management", "trans": ["React内置状态管理"], "methods": [{"name": "Component Local State", "trans": ["组件本地状态"], "usage": {"syntax": "const [state, setState] = useState(initialValue);", "description": "组件本地状态是指通过useState等Hook在组件内部管理的数据，仅当前组件可读写。适合UI交互、表单输入等局部状态。", "parameters": [{"name": "initialValue", "description": "状态的初始值"}], "returnValue": "当前状态值和更新函数", "examples": [{"code": "import React, { useState } from 'react';\n// 计数器组件，使用本地状态\nfunction Counter() {\n  // 声明一个名为count的本地状态，初始值为0\n  const [count, setCount] = useState(0);\n  // 点击按钮时，count加1\n  function handleClick() {\n    setCount(count + 1);\n  }\n  return (\n    <div>\n      <span>当前计数: {count}</span>\n      <button onClick={handleClick}>加一</button>\n    </div>\n  );\n}", "explanation": "Counter组件通过useState管理本地计数状态。"}]}}, {"name": "State Lifting", "trans": ["状态提升"], "usage": {"syntax": "// 父组件提升状态并通过props传递给子组件", "description": "当多个子组件需要共享状态时，将状态提升到它们的最近共同父组件，由父组件统一管理并通过props传递。", "parameters": [], "returnValue": "父组件统一管理的共享状态", "examples": [{"code": "import React, { useState } from 'react';\n// 子组件，接收value和onChange作为props\nfunction Input({ value, onChange }) {\n  return <input value={value} onChange={onChange} />;\n}\n// 父组件提升状态\nfunction Parent() {\n  // 父组件统一管理输入值\n  const [text, setText] = useState('');\n  return (\n    <div>\n      <Input value={text} onChange={e => setText(e.target.value)} />\n      <Input value={text} onChange={e => setText(e.target.value)} />\n      <div>当前输入: {text}</div>\n    </div>\n  );\n}", "explanation": "父组件提升状态，实现多个子组件共享同一份数据。"}]}}, {"name": "Props Downward Passing", "trans": ["props向下传递"], "usage": {"syntax": "<Child propA={valueA} propB={valueB} />", "description": "父组件通过props向下传递数据和回调，子组件通过props接收和使用。是React数据流的核心机制。", "parameters": [{"name": "propA", "description": "传递给子组件的数据A"}, {"name": "propB", "description": "传递给子组件的数据B"}], "returnValue": "子组件可访问的props数据", "examples": [{"code": "function Child({ name, age }) {\n  // 通过props接收父组件传递的数据\n  return <div>姓名: {name}, 年龄: {age}</div>;\n}\nfunction Parent() {\n  return <Child name=\"小明\" age={10} />;\n}", "explanation": "父组件通过props向下传递数据，子组件通过props接收。"}]}}, {"name": "Sibling Communication", "trans": ["兄弟组件通信"], "usage": {"syntax": "// 通过父组件中转实现兄弟通信", "description": "兄弟组件间通信需通过父组件中转：父组件管理共享状态或回调，子组件通过props与父组件交互。", "parameters": [], "returnValue": "兄弟组件间的数据同步或事件响应", "examples": [{"code": "import React, { useState } from 'react';\n// 第一个子组件，触发事件\nfunction SiblingA({ onSend }) {\n  return <button onClick={() => onSend('来自A的信息')}>发送给B</button>;\n}\n// 第二个子组件，显示信息\nfunction SiblingB({ message }) {\n  return <div>收到: {message}</div>;\n}\n// 父组件中转通信\nfunction Parent() {\n  const [msg, setMsg] = useState('');\n  return (\n    <div>\n      <SiblingA onSend={setMsg} />\n      <SiblingB message={msg} />\n    </div>\n  );\n}", "explanation": "父组件中转实现兄弟组件间通信。"}]}}, {"name": "Cross-level Communication", "trans": ["跨层级通信"], "usage": {"syntax": "// 通过props逐层传递或Context实现跨层通信", "description": "跨层级通信可通过props逐层传递（繁琐）或Context（推荐）实现。Context适合全局状态、主题等场景。", "parameters": [], "returnValue": "深层子组件可访问的共享数据", "examples": [{"code": "import React, { createContext, useContext } from 'react';\n// 创建Context对象\nconst UserContext = createContext('匿名');\n// 顶层组件提供Context\nfunction App() {\n  return (\n    <UserContext.Provider value=\"小红\">\n      <Parent />\n    </UserContext.Provider>\n  );\n}\n// 中间组件\nfunction Parent() {\n  return <Child />;\n}\n// 深层子组件消费Context\nfunction Child() {\n  const user = useContext(UserContext);\n  return <div>用户名: {user}</div>;\n}", "explanation": "通过Context实现跨层级通信，深层组件可直接访问数据。"}]}}, {"name": "State Design Principles", "trans": ["状态设计原则"], "usage": {"syntax": "// 只在必要时提升状态，保持单向数据流", "description": "状态设计应遵循单向数据流、最小必要原则、可维护性和可预测性。避免不必要的状态提升和重复。", "parameters": [], "returnValue": "高可维护性和可预测性的状态结构", "examples": [{"code": "// 状态只提升到最近需要共享的父组件\n// 保持props自上而下单向流动\n// 避免重复和无用状态", "explanation": "良好的状态设计有助于组件解耦和维护。"}]}}, {"name": "作业：实现一个兄弟组件通信的计数器", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 实现两个兄弟组件A和B。\n// 2. A点击按钮，B显示计数。\n// 3. 状态提升到父组件。\n// 4. 代码结构清晰，注释完整。", "description": "请实现上述兄弟通信计数器，提交时请附上完整代码和注释。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 示例代码略，请学生自行实现\n// 提示：用useState和props实现。", "explanation": "作业要求学生掌握状态提升和兄弟通信。"}, {"code": "import React, { useState } from 'react';\n// 兄弟A，点击按钮增加计数\nfunction SiblingA({ onAdd }) {\n  return <button onClick={onAdd}>A加一</button>;\n}\n// 兄弟B，显示计数\nfunction SiblingB({ count }) {\n  return <div>B收到计数: {count}</div>;\n}\n// 父组件提升状态并中转\nfunction Parent() {\n  const [count, setCount] = useState(0);\n  return (\n    <div>\n      <SiblingA onAdd={() => setCount(count + 1)} />\n      <SiblingB count={count} />\n    </div>\n  );\n}", "explanation": "完整实现兄弟通信计数器，状态提升到父组件。"}]}}]}