{"name": "Practical Projects and Exercises", "trans": ["项目实战与练习"], "methods": [{"name": "Console Calculator Project", "trans": ["控制台计算器项目"], "usage": {"syntax": "// 计算器项目结构\nclass Calculator {\n  double calculate(double a, double b, String operator) {\n    // 计算逻辑\n  }\n}\n\nvoid main() {\n  // 主程序循环\n  while (true) {\n    // 用户交互\n    // 计算处理\n    // 结果显示\n  }\n}", "description": "控制台计算器是一个经典的编程练习项目，它综合运用了Dart的多个核心概念。这个项目包含：1) 用户输入处理和验证；2) 字符串解析和数学运算；3) 异常处理和错误提示；4) 循环控制和程序流程；5) 函数设计和代码组织。通过实现计算器，学习者可以掌握控制台应用开发的基本模式，理解如何设计用户友好的交互界面，以及如何处理各种边界情况和错误。项目可以从简单的四则运算开始，逐步扩展到支持括号、函数、变量等高级功能。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import 'dart:io';\nimport 'dart:math';\n\nclass Calculator {\n  // 计算历史记录\n  List<String> history = [];\n  \n  // 执行计算\n  double calculate(double a, double b, String operator) {\n    switch (operator) {\n      case '+':\n        return a + b;\n      case '-':\n        return a - b;\n      case '*':\n        return a * b;\n      case '/':\n        if (b == 0) throw Exception('除数不能为零');\n        return a / b;\n      case '^':\n        return pow(a, b).toDouble();\n      case '%':\n        return a % b;\n      default:\n        throw Exception('不支持的运算符: $operator');\n    }\n  }\n  \n  // 解析表达式\n  double evaluateExpression(String expression) {\n    expression = expression.replaceAll(' ', '');\n    \n    // 查找运算符\n    for (int i = 1; i < expression.length; i++) {\n      String char = expression[i];\n      if (['+', '-', '*', '/', '^', '%'].contains(char)) {\n        String leftStr = expression.substring(0, i);\n        String rightStr = expression.substring(i + 1);\n        \n        double left = double.parse(leftStr);\n        double right = double.parse(rightStr);\n        \n        double result = calculate(left, right, char);\n        \n        // 记录到历史\n        String record = '$leftStr $char $rightStr = $result';\n        history.add(record);\n        \n        return result;\n      }\n    }\n    \n    throw Exception('无效的表达式');\n  }\n  \n  // 显示历史记录\n  void showHistory() {\n    if (history.isEmpty) {\n      print('暂无计算历史');\n      return;\n    }\n    \n    print('\\n=== 计算历史 ===');\n    for (int i = 0; i < history.length; i++) {\n      print('${i + 1}. ${history[i]}');\n    }\n  }\n  \n  // 清除历史记录\n  void clearHistory() {\n    history.clear();\n    print('历史记录已清除');\n  }\n}\n\nvoid main() {\n  Calculator calculator = Calculator();\n  \n  print('=== Dart控制台计算器 ===');\n  print('支持运算: +, -, *, /, ^(幂), %(取余)');\n  print('特殊命令: history(历史), clear(清除), help(帮助), exit(退出)');\n  \n  while (true) {\n    stdout.write('\\n计算器> ');\n    String? input = stdin.readLineSync();\n    \n    if (input == null || input.trim().isEmpty) {\n      continue;\n    }\n    \n    input = input.trim().toLowerCase();\n    \n    // 处理特殊命令\n    if (input == 'exit' || input == 'quit') {\n      print('感谢使用计算器！');\n      break;\n    }\n    \n    if (input == 'help') {\n      showHelp();\n      continue;\n    }\n    \n    if (input == 'history') {\n      calculator.showHistory();\n      continue;\n    }\n    \n    if (input == 'clear') {\n      calculator.clearHistory();\n      continue;\n    }\n    \n    // 执行计算\n    try {\n      double result = calculator.evaluateExpression(input);\n      print('结果: $result');\n    } catch (e) {\n      print('错误: $e');\n      print('输入 \"help\" 查看使用说明');\n    }\n  }\n}\n\nvoid showHelp() {\n  print('\\n=== 使用说明 ===');\n  print('基本运算:');\n  print('  加法: 5 + 3');\n  print('  减法: 10 - 4');\n  print('  乘法: 6 * 7');\n  print('  除法: 15 / 3');\n  print('  幂运算: 2 ^ 3');\n  print('  取余: 10 % 3');\n  print('\\n特殊命令:');\n  print('  history - 查看计算历史');\n  print('  clear   - 清除历史记录');\n  print('  help    - 显示此帮助');\n  print('  exit    - 退出程序');\n  print('\\n示例: 2.5 * 4');\n}", "explanation": "这个计算器项目展示了一个完整的控制台应用程序的实现。包含了Calculator类来处理计算逻辑，支持基本的四则运算、幂运算和取余运算。程序具有历史记录功能、帮助系统、错误处理和用户友好的交互界面。这是一个很好的综合练习项目。"}]}}, {"name": "Student Management System", "trans": ["学生管理系统"], "usage": {"syntax": "// 学生管理系统结构\nclass Student {\n  String name;\n  int age;\n  Map<String, double> scores;\n  \n  Student(this.name, this.age) : scores = {};\n}\n\nclass StudentManager {\n  List<Student> students = [];\n  \n  void addStudent(Student student) { }\n  void removeStudent(String name) { }\n  Student? findStudent(String name) { }\n  void showStatistics() { }\n}", "description": "学生管理系统是一个更复杂的项目，涉及数据管理、文件操作和统计分析。这个项目包含：1) 学生信息的增删改查；2) 成绩录入和管理；3) 统计分析功能（平均分、排名等）；4) 数据持久化（保存到文件）；5) 数据验证和异常处理；6) 菜单驱动的用户界面。通过这个项目，学习者可以掌握面向对象编程、集合操作、文件I/O、数据分析等重要概念，同时学会如何设计和实现一个完整的管理系统。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import 'dart:io';\nimport 'dart:convert';\n\nclass Student {\n  String name;\n  int age;\n  String studentId;\n  Map<String, double> scores;\n  \n  Student(this.name, this.age, this.studentId) : scores = {};\n  \n  void addScore(String subject, double score) {\n    if (score < 0 || score > 100) {\n      throw Exception('成绩必须在0-100之间');\n    }\n    scores[subject] = score;\n  }\n  \n  double getAverage() {\n    if (scores.isEmpty) return 0.0;\n    double total = scores.values.reduce((a, b) => a + b);\n    return total / scores.length;\n  }\n  \n  Map<String, dynamic> toJson() {\n    return {\n      'name': name,\n      'age': age,\n      'studentId': studentId,\n      'scores': scores,\n    };\n  }\n  \n  factory Student.fromJson(Map<String, dynamic> json) {\n    Student student = Student(\n      json['name'],\n      json['age'],\n      json['studentId'],\n    );\n    \n    Map<String, dynamic> scoresMap = json['scores'] ?? {};\n    scoresMap.forEach((subject, score) {\n      student.scores[subject] = score.toDouble();\n    });\n    \n    return student;\n  }\n  \n  @override\n  String toString() {\n    return '学号: $studentId, 姓名: $name, 年龄: $age, 平均分: ${getAverage().toStringAsFixed(2)}';\n  }\n}\n\nclass StudentManager {\n  List<Student> students = [];\n  final String dataFile = 'students.json';\n  \n  // 添加学生\n  void addStudent() {\n    print('\\n=== 添加学生 ===');\n    \n    stdout.write('请输入学生姓名: ');\n    String? name = stdin.readLineSync();\n    if (name == null || name.trim().isEmpty) {\n      print('姓名不能为空！');\n      return;\n    }\n    \n    stdout.write('请输入学生年龄: ');\n    String? ageStr = stdin.readLineSync();\n    int age;\n    try {\n      age = int.parse(ageStr ?? '0');\n      if (age <= 0 || age > 100) {\n        print('请输入有效的年龄（1-100）！');\n        return;\n      }\n    } catch (e) {\n      print('年龄格式不正确！');\n      return;\n    }\n    \n    stdout.write('请输入学号: ');\n    String? studentId = stdin.readLineSync();\n    if (studentId == null || studentId.trim().isEmpty) {\n      print('学号不能为空！');\n      return;\n    }\n    \n    // 检查学号是否已存在\n    if (findStudentById(studentId.trim()) != null) {\n      print('学号已存在！');\n      return;\n    }\n    \n    Student student = Student(name.trim(), age, studentId.trim());\n    students.add(student);\n    print('学生 ${student.name} 添加成功！');\n    \n    saveData();\n  }\n  \n  // 查找学生\n  Student? findStudentById(String studentId) {\n    for (Student student in students) {\n      if (student.studentId == studentId) {\n        return student;\n      }\n    }\n    return null;\n  }\n  \n  // 录入成绩\n  void addScore() {\n    if (students.isEmpty) {\n      print('\\n还没有学生记录，请先添加学生！');\n      return;\n    }\n    \n    print('\\n=== 录入成绩 ===');\n    listStudents();\n    \n    stdout.write('请输入学号: ');\n    String? studentId = stdin.readLineSync();\n    if (studentId == null || studentId.trim().isEmpty) {\n      print('学号不能为空！');\n      return;\n    }\n    \n    Student? student = findStudentById(studentId.trim());\n    if (student == null) {\n      print('未找到该学号的学生！');\n      return;\n    }\n    \n    stdout.write('请输入科目名称: ');\n    String? subject = stdin.readLineSync();\n    if (subject == null || subject.trim().isEmpty) {\n      print('科目名称不能为空！');\n      return;\n    }\n    \n    stdout.write('请输入成绩（0-100）: ');\n    String? scoreStr = stdin.readLineSync();\n    \n    try {\n      double score = double.parse(scoreStr ?? '0');\n      student.addScore(subject.trim(), score);\n      print('成绩录入成功！');\n      saveData();\n    } catch (e) {\n      print('成绩录入失败: $e');\n    }\n  }\n  \n  // 显示学生列表\n  void listStudents() {\n    if (students.isEmpty) {\n      print('\\n暂无学生记录。');\n      return;\n    }\n    \n    print('\\n=== 学生列表 ===');\n    for (int i = 0; i < students.length; i++) {\n      print('${i + 1}. ${students[i]}');\n      \n      if (students[i].scores.isNotEmpty) {\n        print('   科目成绩:');\n        students[i].scores.forEach((subject, score) {\n          print('     $subject: ${score.toStringAsFixed(1)}');\n        });\n      }\n    }\n  }\n  \n  // 统计信息\n  void showStatistics() {\n    if (students.isEmpty) {\n      print('\\n暂无学生记录。');\n      return;\n    }\n    \n    print('\\n=== 统计信息 ===');\n    print('学生总数: ${students.length}');\n    \n    // 计算全班平均分\n    List<double> allAverages = students\n        .map((s) => s.getAverage())\n        .where((avg) => avg > 0)\n        .toList();\n    \n    if (allAverages.isNotEmpty) {\n      double classAverage = allAverages.reduce((a, b) => a + b) / allAverages.length;\n      print('全班平均分: ${classAverage.toStringAsFixed(2)}');\n      \n      double highest = allAverages.reduce((a, b) => a > b ? a : b);\n      double lowest = allAverages.reduce((a, b) => a < b ? a : b);\n      \n      print('最高平均分: ${highest.toStringAsFixed(2)}');\n      print('最低平均分: ${lowest.toStringAsFixed(2)}');\n    }\n    \n    // 按平均分排序显示\n    List<Student> sortedStudents = List.from(students);\n    sortedStudents.sort((a, b) => b.getAverage().compareTo(a.getAverage()));\n    \n    print('\\n排名（按平均分）:');\n    for (int i = 0; i < sortedStudents.length; i++) {\n      Student s = sortedStudents[i];\n      if (s.getAverage() > 0) {\n        print('${i + 1}. ${s.name} (${s.studentId}) - ${s.getAverage().toStringAsFixed(2)}分');\n      }\n    }\n  }\n  \n  // 保存数据到文件\n  void saveData() {\n    try {\n      List<Map<String, dynamic>> jsonData = students.map((s) => s.toJson()).toList();\n      String jsonString = JsonEncoder.withIndent('  ').convert(jsonData);\n      File(dataFile).writeAsStringSync(jsonString);\n    } catch (e) {\n      print('保存数据失败: $e');\n    }\n  }\n  \n  // 从文件加载数据\n  void loadData() {\n    try {\n      File file = File(dataFile);\n      if (file.existsSync()) {\n        String jsonString = file.readAsStringSync();\n        List<dynamic> jsonData = jsonDecode(jsonString);\n        \n        students = jsonData.map((json) => Student.fromJson(json)).toList();\n        print('成功加载 ${students.length} 条学生记录');\n      }\n    } catch (e) {\n      print('加载数据失败: $e');\n    }\n  }\n}\n\nvoid main() {\n  StudentManager manager = StudentManager();\n  \n  print('=== 学生管理系统 ===');\n  \n  // 加载数据\n  manager.loadData();\n  \n  while (true) {\n    print('\\n请选择操作:');\n    print('1. 添加学生');\n    print('2. 录入成绩');\n    print('3. 查看学生列表');\n    print('4. 统计信息');\n    print('0. 退出系统');\n    \n    stdout.write('\\n请输入选项: ');\n    String? choice = stdin.readLineSync();\n    \n    switch (choice) {\n      case '1':\n        manager.addStudent();\n        break;\n      case '2':\n        manager.addScore();\n        break;\n      case '3':\n        manager.listStudents();\n        break;\n      case '4':\n        manager.showStatistics();\n        break;\n      case '0':\n        print('\\n感谢使用学生管理系统！');\n        return;\n      default:\n        print('\\n无效的选项，请重新选择！');\n    }\n  }\n}", "explanation": "这个学生管理系统是一个完整的数据管理应用，展示了面向对象编程、JSON序列化、文件操作、数据验证、统计分析等多个重要概念。系统支持学生信息管理、成绩录入、数据持久化和统计分析，是一个很好的综合实战项目。"}]}}, {"name": "File Processing Tool", "trans": ["文件处理工具"], "usage": {"syntax": "// 文件处理工具结构\nclass FileProcessor {\n  void processTextFile(String filePath) { }\n  void countWords(String filePath) { }\n  void findAndReplace(String filePath, String find, String replace) { }\n  void mergeFiles(List<String> filePaths, String outputPath) { }\n}\n\nvoid main() {\n  FileProcessor processor = FileProcessor();\n  // 处理文件操作\n}", "description": "文件处理工具是一个实用的项目，专注于文件操作和文本处理。这个项目包含：1) 文件读写操作；2) 文本分析（字数统计、行数统计等）；3) 查找和替换功能；4) 文件合并和分割；5) 批量文件处理；6) 文件格式转换。通过这个项目，学习者可以掌握文件I/O操作、字符串处理、正则表达式、异常处理等重要技能，同时学会如何处理大文件和批量操作。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import 'dart:io';\nimport 'dart:convert';\n\nclass FileProcessor {\n  // 统计文件信息\n  Map<String, dynamic> analyzeFile(String filePath) {\n    try {\n      File file = File(filePath);\n      if (!file.existsSync()) {\n        throw Exception('文件不存在: $filePath');\n      }\n      \n      String content = file.readAsStringSync();\n      List<String> lines = content.split('\\n');\n      \n      // 统计信息\n      int lineCount = lines.length;\n      int wordCount = 0;\n      int charCount = content.length;\n      int charCountNoSpaces = content.replaceAll(RegExp(r'\\s'), '').length;\n      \n      // 计算单词数\n      for (String line in lines) {\n        List<String> words = line.trim().split(RegExp(r'\\s+'));\n        if (line.trim().isNotEmpty) {\n          wordCount += words.length;\n        }\n      }\n      \n      return {\n        'filePath': filePath,\n        'fileSize': file.lengthSync(),\n        'lineCount': lineCount,\n        'wordCount': wordCount,\n        'charCount': charCount,\n        'charCountNoSpaces': charCountNoSpaces,\n        'lastModified': file.lastModifiedSync(),\n      };\n    } catch (e) {\n      throw Exception('分析文件失败: $e');\n    }\n  }\n  \n  // 查找和替换\n  void findAndReplace(String filePath, String find, String replace, {bool caseSensitive = true}) {\n    try {\n      File file = File(filePath);\n      if (!file.existsSync()) {\n        throw Exception('文件不存在: $filePath');\n      }\n      \n      String content = file.readAsStringSync();\n      String originalContent = content;\n      \n      if (caseSensitive) {\n        content = content.replaceAll(find, replace);\n      } else {\n        content = content.replaceAll(RegExp(find, caseSensitive: false), replace);\n      }\n      \n      if (content != originalContent) {\n        // 备份原文件\n        String backupPath = '$filePath.backup';\n        file.copySync(backupPath);\n        print('原文件已备份到: $backupPath');\n        \n        // 写入新内容\n        file.writeAsStringSync(content);\n        \n        // 统计替换次数\n        int replaceCount = originalContent.split(find).length - 1;\n        print('替换完成，共替换 $replaceCount 处');\n      } else {\n        print('未找到要替换的内容');\n      }\n    } catch (e) {\n      throw Exception('查找替换失败: $e');\n    }\n  }\n  \n  // 合并文件\n  void mergeFiles(List<String> filePaths, String outputPath) {\n    try {\n      File outputFile = File(outputPath);\n      IOSink sink = outputFile.openWrite();\n      \n      for (int i = 0; i < filePaths.length; i++) {\n        String filePath = filePaths[i];\n        File file = File(filePath);\n        \n        if (!file.existsSync()) {\n          print('警告: 文件不存在，跳过 $filePath');\n          continue;\n        }\n        \n        print('正在合并: $filePath');\n        \n        // 添加文件分隔符\n        sink.writeln('\\n=== 文件: $filePath ===');\n        \n        // 读取并写入文件内容\n        String content = file.readAsStringSync();\n        sink.write(content);\n        \n        if (i < filePaths.length - 1) {\n          sink.writeln('\\n');\n        }\n      }\n      \n      sink.close();\n      print('文件合并完成: $outputPath');\n    } catch (e) {\n      throw Exception('合并文件失败: $e');\n    }\n  }\n  \n  // 分割文件\n  void splitFile(String filePath, int linesPerFile) {\n    try {\n      File file = File(filePath);\n      if (!file.existsSync()) {\n        throw Exception('文件不存在: $filePath');\n      }\n      \n      List<String> lines = file.readAsLinesSync();\n      String baseName = filePath.substring(0, filePath.lastIndexOf('.'));\n      String extension = filePath.substring(filePath.lastIndexOf('.'));\n      \n      int fileIndex = 1;\n      int lineIndex = 0;\n      \n      while (lineIndex < lines.length) {\n        String outputPath = '${baseName}_part$fileIndex$extension';\n        File outputFile = File(outputPath);\n        IOSink sink = outputFile.openWrite();\n        \n        int endIndex = (lineIndex + linesPerFile < lines.length) \n            ? lineIndex + linesPerFile \n            : lines.length;\n        \n        for (int i = lineIndex; i < endIndex; i++) {\n          sink.writeln(lines[i]);\n        }\n        \n        sink.close();\n        print('创建分割文件: $outputPath (${endIndex - lineIndex} 行)');\n        \n        lineIndex = endIndex;\n        fileIndex++;\n      }\n      \n      print('文件分割完成，共创建 ${fileIndex - 1} 个文件');\n    } catch (e) {\n      throw Exception('分割文件失败: $e');\n    }\n  }\n  \n  // 批量处理目录中的文件\n  void processDirectory(String dirPath, String extension, Function(File) processor) {\n    try {\n      Directory dir = Directory(dirPath);\n      if (!dir.existsSync()) {\n        throw Exception('目录不存在: $dirPath');\n      }\n      \n      List<FileSystemEntity> entities = dir.listSync();\n      int processedCount = 0;\n      \n      for (FileSystemEntity entity in entities) {\n        if (entity is File && entity.path.endsWith(extension)) {\n          try {\n            print('处理文件: ${entity.path}');\n            processor(entity);\n            processedCount++;\n          } catch (e) {\n            print('处理文件失败 ${entity.path}: $e');\n          }\n        }\n      }\n      \n      print('批量处理完成，共处理 $processedCount 个文件');\n    } catch (e) {\n      throw Exception('批量处理失败: $e');\n    }\n  }\n  \n  // 文件格式转换（CSV到JSON）\n  void csvToJson(String csvPath, String jsonPath) {\n    try {\n      File csvFile = File(csvPath);\n      if (!csvFile.existsSync()) {\n        throw Exception('CSV文件不存在: $csvPath');\n      }\n      \n      List<String> lines = csvFile.readAsLinesSync();\n      if (lines.isEmpty) {\n        throw Exception('CSV文件为空');\n      }\n      \n      // 解析表头\n      List<String> headers = lines[0].split(',').map((h) => h.trim()).toList();\n      \n      // 解析数据行\n      List<Map<String, String>> data = [];\n      for (int i = 1; i < lines.length; i++) {\n        List<String> values = lines[i].split(',').map((v) => v.trim()).toList();\n        \n        if (values.length == headers.length) {\n          Map<String, String> row = {};\n          for (int j = 0; j < headers.length; j++) {\n            row[headers[j]] = values[j];\n          }\n          data.add(row);\n        }\n      }\n      \n      // 写入JSON文件\n      File jsonFile = File(jsonPath);\n      String jsonString = JsonEncoder.withIndent('  ').convert(data);\n      jsonFile.writeAsStringSync(jsonString);\n      \n      print('CSV转JSON完成: $jsonPath');\n      print('转换了 ${data.length} 行数据');\n    } catch (e) {\n      throw Exception('CSV转JSON失败: $e');\n    }\n  }\n}\n\nvoid main() {\n  FileProcessor processor = FileProcessor();\n  \n  print('=== 文件处理工具 ===');\n  \n  while (true) {\n    print('\\n请选择操作:');\n    print('1. 分析文件');\n    print('2. 查找替换');\n    print('3. 合并文件');\n    print('4. 分割文件');\n    print('5. CSV转JSON');\n    print('6. 批量处理');\n    print('0. 退出');\n    \n    stdout.write('\\n请输入选项: ');\n    String? choice = stdin.readLineSync();\n    \n    try {\n      switch (choice) {\n        case '1':\n          stdout.write('请输入文件路径: ');\n          String? filePath = stdin.readLineSync();\n          if (filePath != null && filePath.isNotEmpty) {\n            Map<String, dynamic> info = processor.analyzeFile(filePath);\n            print('\\n文件分析结果:');\n            print('文件路径: ${info['filePath']}');\n            print('文件大小: ${info['fileSize']} 字节');\n            print('行数: ${info['lineCount']}');\n            print('单词数: ${info['wordCount']}');\n            print('字符数: ${info['charCount']}');\n            print('字符数(不含空格): ${info['charCountNoSpaces']}');\n            print('最后修改: ${info['lastModified']}');\n          }\n          break;\n          \n        case '2':\n          stdout.write('请输入文件路径: ');\n          String? filePath = stdin.readLineSync();\n          stdout.write('请输入要查找的文本: ');\n          String? find = stdin.readLineSync();\n          stdout.write('请输入替换文本: ');\n          String? replace = stdin.readLineSync();\n          \n          if (filePath != null && find != null && replace != null) {\n            processor.findAndReplace(filePath, find, replace);\n          }\n          break;\n          \n        case '3':\n          print('请输入要合并的文件路径（每行一个，空行结束）:');\n          List<String> filePaths = [];\n          while (true) {\n            String? path = stdin.readLineSync();\n            if (path == null || path.trim().isEmpty) break;\n            filePaths.add(path.trim());\n          }\n          \n          stdout.write('请输入输出文件路径: ');\n          String? outputPath = stdin.readLineSync();\n          \n          if (filePaths.isNotEmpty && outputPath != null) {\n            processor.mergeFiles(filePaths, outputPath);\n          }\n          break;\n          \n        case '4':\n          stdout.write('请输入文件路径: ');\n          String? filePath = stdin.readLineSync();\n          stdout.write('请输入每个文件的行数: ');\n          String? linesStr = stdin.readLineSync();\n          \n          if (filePath != null && linesStr != null) {\n            int lines = int.parse(linesStr);\n            processor.splitFile(filePath, lines);\n          }\n          break;\n          \n        case '5':\n          stdout.write('请输入CSV文件路径: ');\n          String? csvPath = stdin.readLineSync();\n          stdout.write('请输入JSON输出路径: ');\n          String? jsonPath = stdin.readLineSync();\n          \n          if (csvPath != null && jsonPath != null) {\n            processor.csvToJson(csvPath, jsonPath);\n          }\n          break;\n          \n        case '6':\n          stdout.write('请输入目录路径: ');\n          String? dirPath = stdin.readLineSync();\n          stdout.write('请输入文件扩展名（如.txt）: ');\n          String? extension = stdin.readLineSync();\n          \n          if (dirPath != null && extension != null) {\n            processor.processDirectory(dirPath, extension, (File file) {\n              Map<String, dynamic> info = processor.analyzeFile(file.path);\n              print('  ${file.path}: ${info['lineCount']} 行, ${info['wordCount']} 单词');\n            });\n          }\n          break;\n          \n        case '0':\n          print('\\n感谢使用文件处理工具！');\n          return;\n          \n        default:\n          print('\\n无效的选项，请重新选择！');\n      }\n    } catch (e) {\n      print('操作失败: $e');\n    }\n  }\n}", "explanation": "这个文件处理工具展示了完整的文件操作功能，包括文件分析、查找替换、文件合并分割、格式转换和批量处理。项目涵盖了文件I/O、字符串处理、数据转换、异常处理等多个重要概念，是一个很实用的工具项目。"}]}}, {"name": "Assignment", "trans": ["综合实践作业"], "usage": {"syntax": "// 综合项目实践", "description": "完成以下综合项目，将所学的Dart知识融会贯通：1) 个人财务管理器：实现收支记录、分类统计、预算管理、数据可视化等功能；2) 简易图书管理系统：包含图书信息管理、借阅记录、搜索功能、数据持久化；3) 命令行游戏：实现猜数字、井字棋、简单RPG等游戏，包含游戏逻辑、用户交互、存档功能；4) 数据分析工具：读取CSV/JSON数据，进行统计分析，生成报告；5) 挑战项目：选择一个实际问题，设计并实现完整的解决方案，要求使用面向对象设计、异常处理、文件操作、数据结构等多种技术。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 参考实现：个人财务管理器\n\nimport 'dart:io';\nimport 'dart:convert';\n\nenum TransactionType { income, expense }\n\nclass Transaction {\n  String id;\n  String description;\n  double amount;\n  String category;\n  TransactionType type;\n  DateTime date;\n  \n  Transaction({\n    required this.id,\n    required this.description,\n    required this.amount,\n    required this.category,\n    required this.type,\n    required this.date,\n  });\n  \n  Map<String, dynamic> toJson() {\n    return {\n      'id': id,\n      'description': description,\n      'amount': amount,\n      'category': category,\n      'type': type.toString(),\n      'date': date.toIso8601String(),\n    };\n  }\n  \n  factory Transaction.fromJson(Map<String, dynamic> json) {\n    return Transaction(\n      id: json['id'],\n      description: json['description'],\n      amount: json['amount'].toDouble(),\n      category: json['category'],\n      type: TransactionType.values.firstWhere(\n        (e) => e.toString() == json['type']\n      ),\n      date: DateTime.parse(json['date']),\n    );\n  }\n  \n  @override\n  String toString() {\n    String typeStr = type == TransactionType.income ? '收入' : '支出';\n    String amountStr = type == TransactionType.income ? '+${amount.toStringAsFixed(2)}' : '-${amount.toStringAsFixed(2)}';\n    return '${date.toString().substring(0, 10)} | $typeStr | $category | $description | ¥$amountStr';\n  }\n}\n\nclass FinanceManager {\n  List<Transaction> transactions = [];\n  final String dataFile = 'finance_data.json';\n  \n  // 添加交易记录\n  void addTransaction() {\n    print('\\n=== 添加交易记录 ===');\n    \n    // 选择类型\n    print('请选择交易类型:');\n    print('1. 收入');\n    print('2. 支出');\n    stdout.write('请输入选项: ');\n    String? typeChoice = stdin.readLineSync();\n    \n    TransactionType type;\n    if (typeChoice == '1') {\n      type = TransactionType.income;\n    } else if (typeChoice == '2') {\n      type = TransactionType.expense;\n    } else {\n      print('无效的选项！');\n      return;\n    }\n    \n    // 输入描述\n    stdout.write('请输入描述: ');\n    String? description = stdin.readLineSync();\n    if (description == null || description.trim().isEmpty) {\n      print('描述不能为空！');\n      return;\n    }\n    \n    // 输入金额\n    stdout.write('请输入金额: ');\n    String? amountStr = stdin.readLineSync();\n    double amount;\n    try {\n      amount = double.parse(amountStr ?? '0');\n      if (amount <= 0) {\n        print('金额必须大于0！');\n        return;\n      }\n    } catch (e) {\n      print('金额格式不正确！');\n      return;\n    }\n    \n    // 选择分类\n    List<String> categories = type == TransactionType.income \n        ? ['工资', '奖金', '投资', '其他收入']\n        : ['餐饮', '交通', '购物', '娱乐', '医疗', '教育', '其他支出'];\n    \n    print('\\n请选择分类:');\n    for (int i = 0; i < categories.length; i++) {\n      print('${i + 1}. ${categories[i]}');\n    }\n    stdout.write('请输入选项: ');\n    String? categoryChoice = stdin.readLineSync();\n    \n    int categoryIndex;\n    try {\n      categoryIndex = int.parse(categoryChoice ?? '0') - 1;\n      if (categoryIndex < 0 || categoryIndex >= categories.length) {\n        print('无效的分类选项！');\n        return;\n      }\n    } catch (e) {\n      print('分类选项格式不正确！');\n      return;\n    }\n    \n    // 创建交易记录\n    Transaction transaction = Transaction(\n      id: DateTime.now().millisecondsSinceEpoch.toString(),\n      description: description.trim(),\n      amount: amount,\n      category: categories[categoryIndex],\n      type: type,\n      date: DateTime.now(),\n    );\n    \n    transactions.add(transaction);\n    print('\\n交易记录添加成功！');\n    print(transaction);\n    \n    saveData();\n  }\n  \n  // 查看交易记录\n  void viewTransactions() {\n    if (transactions.isEmpty) {\n      print('\\n暂无交易记录。');\n      return;\n    }\n    \n    print('\\n=== 交易记录 ===');\n    \n    // 按日期排序\n    List<Transaction> sortedTransactions = List.from(transactions);\n    sortedTransactions.sort((a, b) => b.date.compareTo(a.date));\n    \n    for (int i = 0; i < sortedTransactions.length; i++) {\n      print('${i + 1}. ${sortedTransactions[i]}');\n    }\n  }\n  \n  // 统计分析\n  void showStatistics() {\n    if (transactions.isEmpty) {\n      print('\\n暂无交易记录。');\n      return;\n    }\n    \n    print('\\n=== 财务统计 ===');\n    \n    // 计算总收入和总支出\n    double totalIncome = 0;\n    double totalExpense = 0;\n    \n    for (Transaction t in transactions) {\n      if (t.type == TransactionType.income) {\n        totalIncome += t.amount;\n      } else {\n        totalExpense += t.amount;\n      }\n    }\n    \n    double balance = totalIncome - totalExpense;\n    \n    print('总收入: ¥${totalIncome.toStringAsFixed(2)}');\n    print('总支出: ¥${totalExpense.toStringAsFixed(2)}');\n    print('余额: ¥${balance.toStringAsFixed(2)}');\n    \n    // 按分类统计支出\n    Map<String, double> expenseByCategory = {};\n    for (Transaction t in transactions) {\n      if (t.type == TransactionType.expense) {\n        expenseByCategory[t.category] = (expenseByCategory[t.category] ?? 0) + t.amount;\n      }\n    }\n    \n    if (expenseByCategory.isNotEmpty) {\n      print('\\n支出分类统计:');\n      List<MapEntry<String, double>> sortedExpenses = expenseByCategory.entries.toList();\n      sortedExpenses.sort((a, b) => b.value.compareTo(a.value));\n      \n      for (MapEntry<String, double> entry in sortedExpenses) {\n        double percentage = (entry.value / totalExpense) * 100;\n        print('${entry.key}: ¥${entry.value.toStringAsFixed(2)} (${percentage.toStringAsFixed(1)}%)');\n      }\n    }\n    \n    // 按分类统计收入\n    Map<String, double> incomeByCategory = {};\n    for (Transaction t in transactions) {\n      if (t.type == TransactionType.income) {\n        incomeByCategory[t.category] = (incomeByCategory[t.category] ?? 0) + t.amount;\n      }\n    }\n    \n    if (incomeByCategory.isNotEmpty) {\n      print('\\n收入分类统计:');\n      List<MapEntry<String, double>> sortedIncomes = incomeByCategory.entries.toList();\n      sortedIncomes.sort((a, b) => b.value.compareTo(a.value));\n      \n      for (MapEntry<String, double> entry in sortedIncomes) {\n        double percentage = (entry.value / totalIncome) * 100;\n        print('${entry.key}: ¥${entry.value.toStringAsFixed(2)} (${percentage.toStringAsFixed(1)}%)');\n      }\n    }\n  }\n  \n  // 保存数据\n  void saveData() {\n    try {\n      List<Map<String, dynamic>> jsonData = transactions.map((t) => t.toJson()).toList();\n      String jsonString = JsonEncoder.withIndent('  ').convert(jsonData);\n      File(dataFile).writeAsStringSync(jsonString);\n    } catch (e) {\n      print('保存数据失败: $e');\n    }\n  }\n  \n  // 加载数据\n  void loadData() {\n    try {\n      File file = File(dataFile);\n      if (file.existsSync()) {\n        String jsonString = file.readAsStringSync();\n        List<dynamic> jsonData = jsonDecode(jsonString);\n        \n        transactions = jsonData.map((json) => Transaction.fromJson(json)).toList();\n        print('成功加载 ${transactions.length} 条交易记录');\n      }\n    } catch (e) {\n      print('加载数据失败: $e');\n    }\n  }\n}\n\nvoid main() {\n  FinanceManager manager = FinanceManager();\n  \n  print('=== 个人财务管理器 ===');\n  \n  // 加载数据\n  manager.loadData();\n  \n  while (true) {\n    print('\\n请选择操作:');\n    print('1. 添加交易记录');\n    print('2. 查看交易记录');\n    print('3. 财务统计');\n    print('0. 退出');\n    \n    stdout.write('\\n请输入选项: ');\n    String? choice = stdin.readLineSync();\n    \n    switch (choice) {\n      case '1':\n        manager.addTransaction();\n        break;\n      case '2':\n        manager.viewTransactions();\n        break;\n      case '3':\n        manager.showStatistics();\n        break;\n      case '0':\n        print('\\n感谢使用个人财务管理器！');\n        return;\n      default:\n        print('\\n无效的选项，请重新选择！');\n    }\n  }\n}", "explanation": "这个个人财务管理器是一个完整的实战项目，展示了枚举、类设计、JSON序列化、文件操作、数据分析、用户交互等多个重要概念。项目具有实用价值，可以帮助用户管理个人财务，同时是一个很好的综合练习。"}]}}]}