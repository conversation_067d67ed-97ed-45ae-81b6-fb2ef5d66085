{"name": "Message Queue Integration", "trans": ["消息队列集成"], "methods": [{"name": "RabbitMQ Integration", "trans": ["RabbitMQ集成"], "usage": {"syntax": "引入spring-boot-starter-amqp依赖，@RabbitListener监听队列，RabbitTemplate发送消息", "description": "RabbitMQ是常用的消息中间件，Spring Boot通过spring-boot-starter-amqp快速集成，支持队列、交换机、路由等多种模式。", "parameters": [{"name": "spring-boot-starter-amqp", "description": "RabbitMQ集成依赖"}, {"name": "@RabbitListener", "description": "注解方式监听队列消息"}, {"name": "RabbitTemplate", "description": "发送消息的模板类"}], "returnValue": "消息发送与接收的结果", "examples": [{"code": "// 1. 发送消息\n@Autowired\nprivate RabbitTemplate rabbitTemplate;\n\nrabbitTemplate.convertAndSend(\"queueName\", \"Hello MQ!\");\n\n// 2. 监听消息\n@RabbitListener(queues = \"queueName\")\npublic void receive(String msg) {\n    System.out.println(\"收到消息: \" + msg);\n}", "explanation": "演示了RabbitMQ消息的发送与监听。"}]}}, {"name": "Kafka Integration", "trans": ["Kafka集成"], "usage": {"syntax": "引入spring-boot-starter-kafka依赖，@KafkaListener监听主题，KafkaTemplate发送消息", "description": "Kafka是高吞吐量的分布式消息系统，适合大数据和日志场景。Spring Boot通过spring-boot-starter-kafka集成，支持主题、分区、消费组等。", "parameters": [{"name": "spring-boot-starter-kafka", "description": "Kafka集成依赖"}, {"name": "@KafkaListener", "description": "注解方式监听主题消息"}, {"name": "KafkaTemplate", "description": "发送消息的模板类"}], "returnValue": "消息发送与接收的结果", "examples": [{"code": "// 1. 发送消息\n@Autowired\nprivate KafkaTemplate<String, String> kafkaTemplate;\n\nkafkaTemplate.send(\"topicName\", \"Hello Kafka!\");\n\n// 2. 监听消息\n@KafkaListener(topics = \"topicName\")\npublic void listen(String msg) {\n    System.out.println(\"收到Kafka消息: \" + msg);\n}", "explanation": "演示了Kafka消息的发送与监听。"}]}}, {"name": "ActiveMQ Integration", "trans": ["ActiveMQ集成"], "usage": {"syntax": "引入spring-boot-starter-activemq依赖，JmsTemplate发送消息，@JmsListener监听队列", "description": "ActiveMQ是经典的JMS实现，适合企业级应用。Spring Boot通过spring-boot-starter-activemq集成，支持点对点和发布订阅模式。", "parameters": [{"name": "spring-boot-starter-activemq", "description": "ActiveMQ集成依赖"}, {"name": "JmsTemplate", "description": "发送消息的模板类"}, {"name": "@JmsListener", "description": "注解方式监听队列消息"}], "returnValue": "消息发送与接收的结果", "examples": [{"code": "// 1. 发送消息\n@Autowired\nprivate JmsTemplate jmsTemplate;\n\njmsTemplate.convertAndSend(\"queueName\", \"Hello ActiveMQ!\");\n\n// 2. 监听消息\n@JmsListener(destination = \"queueName\")\npublic void receive(String msg) {\n    System.out.println(\"收到ActiveMQ消息: \" + msg);\n}", "explanation": "演示了ActiveMQ消息的发送与监听。"}]}}, {"name": "Message Reliability and Transaction", "trans": ["消息可靠性与事务"], "usage": {"syntax": "配置消息确认、持久化、事务机制，保证消息可靠传递", "description": "为保证消息不丢失，需配置消息持久化、消费确认、事务机制等。RabbitMQ支持ACK确认，Kafka支持offset提交，ActiveMQ支持JMS事务。", "parameters": [{"name": "消息持久化", "description": "消息存储到磁盘，防止丢失"}, {"name": "消费确认", "description": "消费者收到消息后确认，避免重复消费或丢失"}, {"name": "事务机制", "description": "发送和消费消息的原子性保障"}], "returnValue": "高可靠的消息传递保障", "examples": [{"code": "// RabbitMQ消息确认\n@RabbitListener(queues = \"queueName\", ackMode = \"MANUAL\")\npublic void receive(Message message, Channel channel) throws IOException {\n    // 业务处理\n    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false); // 手动确认\n}\n\n// Kafka消费手动提交offset\n@KafkaListener(topics = \"topicName\")\npublic void listen(ConsumerRecord<?, ?> record, Acknowledgment ack) {\n    // 业务处理\n    ack.acknowledge(); // 手动提交offset\n}", "explanation": "演示了RabbitMQ和Kafka的消息确认机制，提升消息可靠性。"}]}}, {"name": "Message Queue Assignment", "trans": ["消息队列练习"], "usage": {"syntax": "# 消息队列练习", "description": "完成以下练习，巩固Spring Boot消息队列集成相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 集成RabbitMQ，实现消息的发送与监听。\n2. 集成Kafka，实现消息的发送与监听。\n3. 配置消息持久化和消费确认机制，保证消息可靠性。\n4. 实现一个简单的消息事务，确保消息处理的原子性。", "explanation": "通过这些练习掌握Spring Boot主流消息队列的集成与可靠性保障。"}]}}]}