{"name": "Hash", "trans": ["哈希类型"], "methods": [{"name": "Hash Commands", "trans": ["哈希表操作命令"], "usage": {"syntax": "HSET key field value [field value ...]\nHGET key field\nHDEL key field [field ...]\nHGETALL key\nHMSET key field value [field value ...]", "description": "HSET设置哈希字段，HGET获取，HDEL删除，HGETALL获取所有字段，HMSET批量设置。适合存储对象属性。", "parameters": [{"name": "key", "description": "哈希表键名"}, {"name": "field", "description": "字段名"}, {"name": "value", "description": "字段值"}], "returnValue": "OK、实际值或字段数量", "examples": [{"code": "$ redis-cli hset user:1 name Tom age 20\n$ redis-cli hget user:1 name\n$ redis-cli hdel user:1 age\n$ redis-cli hgetall user:1", "explanation": "演示了哈希表的增删查操作。"}]}}, {"name": "Object Storage and Caching", "trans": ["对象存储与缓存"], "usage": {"syntax": "HSET/HMSET + EXPIRE\nHGETALL/HMGET", "description": "哈希类型适合存储对象属性，如用户信息、商品详情等。可结合EXPIRE实现对象缓存。", "parameters": [{"name": "key", "description": "对象键名"}, {"name": "field", "description": "属性名"}, {"name": "value", "description": "属性值"}], "returnValue": "对象属性值或缓存状态", "examples": [{"code": "$ redis-cli hmset product:1001 name 'iPhone' price 5999 stock 10\n$ redis-cli expire product:1001 300\n$ redis-cli hgetall product:1001", "explanation": "演示了对象属性的存储和缓存。"}]}}, {"name": "Practice: Hash类型练习", "trans": ["哈希类型练习"], "usage": {"syntax": "# Redis哈希类型练习", "description": "完成以下练习，掌握哈希类型的常用操作和对象缓存。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用HSET设置一个用户对象的多个属性。\n2. 用HGETALL获取该对象所有属性。\n3. 用EXPIRE设置对象缓存过期。\n4. 用HDEL删除某个属性。", "explanation": "通过这些练习掌握哈希类型的常用命令和对象缓存。"}, {"code": "# 正确实现示例\n$ redis-cli hset user:2 name Alice age 18\n$ redis-cli hgetall user:2\n$ redis-cli expire user:2 120\n$ redis-cli hdel user:2 age", "explanation": "展示了练习的标准实现过程。"}]}}]}