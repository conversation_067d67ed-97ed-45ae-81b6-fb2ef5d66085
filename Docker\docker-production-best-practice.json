{"name": "Production Best Practices", "trans": ["生产环境最佳实践"], "methods": [{"name": "Image Version Management Standard", "trans": ["镜像版本管理规范"], "usage": {"syntax": "docker tag <镜像名>:<标签> <镜像名>:<新标签>\ndocker push <镜像名>:<新标签>\n# 版本规范如: v1.0.0, release-20240601", "description": "采用语义化版本或日期型标签，规范镜像命名与推送，便于回滚和追溯。", "parameters": [{"name": "docker tag", "description": "为镜像打规范标签。"}, {"name": "docker push", "description": "推送带标签的镜像。"}, {"name": "版本规范", "description": "如v1.0.0、release-20240601等。"}], "returnValue": "无返回值，提升镜像管理规范性。", "examples": [{"code": "docker tag myapp:latest myapp:v1.0.0\ndocker push myapp:v1.0.0", "explanation": "采用语义化标签推送镜像，便于管理和回滚。"}]}}, {"name": "Config & Secret Management", "trans": ["配置与密钥管理"], "usage": {"syntax": "# Docker Compose配置注入\nservices:\n  app:\n    env_file:\n      - .env\n    secrets:\n      - db_password\n# Kubernetes Secret\nkubectl create secret generic db-pass --from-literal=password=123456", "description": "通过env_file、secrets等方式安全注入配置和密钥，避免明文暴露。K8s推荐使用Secret资源。", "parameters": [{"name": "env_file", "description": "通过文件批量注入环境变量。"}, {"name": "secrets", "description": "Docker/K8s安全注入敏感信息。"}, {"name": "Kubernetes Secret", "description": "K8s原生密钥管理。"}], "returnValue": "无返回值，提升配置与密钥安全。", "examples": [{"code": "services:\n  app:\n    env_file:\n      - .env\n    secrets:\n      - db_password\n# K8s Secret\nkubectl create secret generic db-pass --from-literal=password=123456", "explanation": "Compose和K8s安全注入配置与密钥。"}]}}, {"name": "Gray Release & Rollback", "trans": ["灰度发布与回滚"], "usage": {"syntax": "# K8s灰度发布\nkubectl set image deployment/myapp myapp=myapp:v2 --record\n# 回滚\nkubectl rollout undo deployment/myapp", "description": "通过K8s的set image和rollout undo命令实现灰度发布和快速回滚，降低发布风险。", "parameters": [{"name": "kubectl set image", "description": "灰度发布新版本镜像。"}, {"name": "kubectl rollout undo", "description": "一键回滚到上一个版本。"}], "returnValue": "无返回值，保障生产环境稳定。", "examples": [{"code": "kubectl set image deployment/myapp myapp=myapp:v2 --record\nkubectl rollout undo deployment/myapp", "explanation": "K8s灰度发布与回滚操作。"}]}}, {"name": "Log Collection & Analysis", "trans": ["日志采集与分析"], "usage": {"syntax": "# Docker日志采集\ndocker logs <容器名>\n# ELK/EFK日志方案\n# K8s日志采集\nkubectl logs <Pod名>\n# Fluentd/Logstash/ElasticSearch/Kibana", "description": "通过docker logs、K8s logs及ELK/EFK等方案集中采集和分析日志，便于故障定位和审计。", "parameters": [{"name": "docker logs", "description": "查看单容器日志。"}, {"name": "kubectl logs", "description": "K8s环境下采集Pod日志。"}, {"name": "ELK/EFK", "description": "集中式日志采集与分析。"}], "returnValue": "无返回值，提升日志管理和分析能力。", "examples": [{"code": "docker logs myapp\nkubectl logs myapp-pod\n# ELK/EFK方案需参考官方文档部署", "explanation": "多种方式采集和分析容器日志。"}]}}, {"name": "Container Orchestration & K8s Integration", "trans": ["容器编排与K8s集成"], "usage": {"syntax": "# Compose编排\ndocker-compose up -d\n# K8s编排\nkubectl apply -f deployment.yaml", "description": "通过Compose或Kubernetes实现多容器编排与自动化管理，提升生产环境的弹性和可维护性。", "parameters": [{"name": "docker-compose", "description": "适合中小型多容器编排。"}, {"name": "kubectl apply", "description": "K8s大规模编排与自动化。"}], "returnValue": "无返回值，提升生产环境自动化和弹性。", "examples": [{"code": "docker-compose up -d\nkubectl apply -f deployment.yaml", "explanation": "分别用Compose和K8s实现容器编排。"}]}}, {"name": "Assignment: 生产环境最佳实践实践", "trans": ["作业：生产环境最佳实践实践"], "usage": {"syntax": "请完成以下任务：\n1. 规范镜像版本管理并推送\n2. 配置安全的环境变量和密钥注入\n3. 实现一次灰度发布与回滚\n4. 部署日志采集分析方案\n5. 用Compose或K8s实现多容器编排", "description": "通过实际操作掌握生产环境下镜像、配置、发布、日志和编排的最佳实践。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 1. 镜像版本管理\ndocker tag ...\ndocker push ...\n# 2. 配置与密钥\n.env、secrets、K8s Secret\n# 3. 灰度发布与回滚\nkubectl set image ...\nkubectl rollout undo ...\n# 4. 日志采集\nELK/EFK\n# 5. 编排\ndocker-compose up -d\nkubectl apply -f ...", "explanation": "依次完成生产环境下的最佳实践操作。"}]}}]}