{"name": "Iterators and Generators", "trans": ["迭代器与生成器"], "methods": [{"name": "Iterator Protocol", "trans": ["迭代器协议"], "usage": {"syntax": "iter(obj), next(iterator)", "description": "迭代器协议要求对象实现__iter__()和__next__()方法，可用于for循环等迭代场景。", "parameters": [{"name": "obj", "description": "可迭代对象"}, {"name": "iterator", "description": "迭代器对象"}], "returnValue": "下一个元素或StopIteration异常", "examples": [{"code": "lst = [1, 2, 3]\nit = iter(lst)\nprint(next(it))  # 1\nprint(next(it))  # 2", "explanation": "演示了迭代器协议的基本用法。"}]}}, {"name": "Generator Functions and yield", "trans": ["生成器函数与yield"], "usage": {"syntax": "def gen():\n    yield value", "description": "生成器函数通过yield语句返回值，每次调用next生成下一个值，节省内存。", "parameters": [{"name": "yield", "description": "生成下一个值并暂停函数执行"}], "returnValue": "生成器对象", "examples": [{"code": "def count():\n    yield 1\n    yield 2\nfor x in count():\n    print(x)", "explanation": "演示了生成器函数和yield的用法。"}]}}, {"name": "Generator Expressions", "trans": ["生成器表达式"], "usage": {"syntax": "(expr for var in iterable)", "description": "生成器表达式可快速创建生成器，节省内存，适合大数据流式处理。", "parameters": [{"name": "expr", "description": "每次生成的表达式"}, {"name": "iterable", "description": "可迭代对象"}], "returnValue": "生成器对象", "examples": [{"code": "g = (x * x for x in range(3))\nfor v in g:\n    print(v)", "explanation": "演示了生成器表达式的用法。"}]}}, {"name": "Advanced itertools Usage", "trans": ["itertools高级用法"], "usage": {"syntax": "import itertools", "description": "itertools模块提供高效的迭代器工具，如count、cycle、chain、groupby等。", "parameters": [{"name": "itertools.count", "description": "生成无限递增序列"}, {"name": "itertools.groupby", "description": "分组迭代器"}], "returnValue": "迭代器对象或分组结果", "examples": [{"code": "import itertools\nfor i in itertools.islice(itertools.count(5), 3):\n    print(i)  # 5, 6, 7\nfor k, g in itertools.groupby('aabbcc'):\n    print(k, list(g))", "explanation": "演示了itertools的count和groupby用法。"}]}}, {"name": "Iterators and Generators Assignment", "trans": ["迭代器与生成器练习"], "usage": {"syntax": "# 迭代器与生成器练习", "description": "完成以下练习，巩固迭代器与生成器相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用迭代器遍历一个列表。\n2. 编写一个生成器函数返回斐波那契数列。\n3. 用生成器表达式生成前10个平方数。\n4. 用itertools.groupby对字符串分组。", "explanation": "练习要求动手实践迭代器、生成器函数、生成器表达式、itertools等。"}]}}]}