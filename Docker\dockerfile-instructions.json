{"name": "Dockerfile Instructions", "trans": ["Dockerfile指令详解"], "methods": [{"name": "FROM", "trans": ["指定基础镜像"], "usage": {"syntax": "FROM <镜像名>[:<标签>]", "description": "指定构建镜像所基于的基础镜像，是每个Dockerfile的第一条指令。", "parameters": [{"name": "<镜像名>", "description": "基础镜像名称。"}, {"name": "<标签>", "description": "可选，指定镜像版本标签。"}], "returnValue": "无返回值，定义镜像基础。", "examples": [{"code": "FROM ubuntu:20.04", "explanation": "以ubuntu 20.04为基础镜像。"}]}}, {"name": "RUN", "trans": ["执行命令"], "usage": {"syntax": "RUN <shell命令>", "description": "在镜像构建过程中执行命令，生成新镜像层。", "parameters": [{"name": "<shell命令>", "description": "要执行的命令。"}], "returnValue": "无返回值，命令执行结果写入镜像。", "examples": [{"code": "RUN apt-get update && apt-get install -y nginx", "explanation": "安装nginx到镜像中。"}]}}, {"name": "CMD", "trans": ["容器启动命令"], "usage": {"syntax": "CMD [\"可执行文件\", \"参数1\", ...]", "description": "指定容器启动时默认执行的命令，只能有一条，后续CMD会覆盖前面的。", "parameters": [{"name": "[\"可执行文件\", \"参数\"]", "description": "启动时执行的命令及参数。"}], "returnValue": "无返回值，定义容器启动行为。", "examples": [{"code": "CMD [\"nginx\", \"-g\", \"daemon off;\"]", "explanation": "容器启动时以前台模式运行nginx。"}]}}, {"name": "ENTRYPOINT", "trans": ["入口点命令"], "usage": {"syntax": "ENTRYPOINT [\"可执行文件\", \"参数1\", ...]", "description": "设置容器启动时的主命令，常与CMD配合实现参数灵活传递。", "parameters": [{"name": "[\"可执行文件\", \"参数\"]", "description": "主命令及参数。"}], "returnValue": "无返回值，定义容器主进程。", "examples": [{"code": "ENTRYPOINT [\"/app/start.sh\"]", "explanation": "容器启动时执行/app/start.sh脚本。"}]}}, {"name": "COPY", "trans": ["复制文件"], "usage": {"syntax": "COPY <源路径> <目标路径>", "description": "将本地文件或目录复制到镜像内。", "parameters": [{"name": "<源路径>", "description": "主机上的源文件或目录。"}, {"name": "<目标路径>", "description": "镜像内的目标路径。"}], "returnValue": "无返回值，文件被复制到镜像。", "examples": [{"code": "COPY ./app /app", "explanation": "将主机app目录复制到镜像/app。"}]}}, {"name": "ADD", "trans": ["添加文件/自动解压"], "usage": {"syntax": "ADD <源路径> <目标路径>", "description": "类似COPY，但支持自动解压tar包和URL下载。", "parameters": [{"name": "<源路径>", "description": "主机文件、目录或URL。"}, {"name": "<目标路径>", "description": "镜像内的目标路径。"}], "returnValue": "无返回值，文件被添加到镜像。", "examples": [{"code": "ADD app.tar.gz /app", "explanation": "自动解压app.tar.gz到镜像/app。"}]}}, {"name": "WORKDIR", "trans": ["设置工作目录"], "usage": {"syntax": "WORKDIR <路径>", "description": "设置后续指令的工作目录，若不存在会自动创建。", "parameters": [{"name": "<路径>", "description": "镜像内的工作目录路径。"}], "returnValue": "无返回值，影响后续指令的执行目录。", "examples": [{"code": "WORKDIR /app", "explanation": "后续命令都在/app目录下执行。"}]}}, {"name": "ENV", "trans": ["设置环境变量"], "usage": {"syntax": "ENV <变量名> <值>", "description": "设置环境变量，后续指令和容器运行时可用。", "parameters": [{"name": "<变量名>", "description": "环境变量名。"}, {"name": "<值>", "description": "环境变量值。"}], "returnValue": "无返回值，环境变量生效。", "examples": [{"code": "ENV LANG C.UTF-8", "explanation": "设置LANG环境变量为C.UTF-8。"}]}}, {"name": "EXPOSE", "trans": ["声明端口"], "usage": {"syntax": "EXPOSE <端口>", "description": "声明容器运行时会监听的端口，便于文档和自动化工具识别。", "parameters": [{"name": "<端口>", "description": "要暴露的端口号。"}], "returnValue": "无返回值，仅做声明，无实际端口映射。", "examples": [{"code": "EXPOSE 80", "explanation": "声明容器会监听80端口。"}]}}, {"name": "VOLUME", "trans": ["声明数据卷"], "usage": {"syntax": "VOLUME [<路径>, ...]", "description": "声明镜像中需要挂载为数据卷的目录，便于数据持久化。", "parameters": [{"name": "<路径>", "description": "要挂载为卷的目录路径。"}], "returnValue": "无返回值，容器启动时自动创建卷。", "examples": [{"code": "VOLUME [\"/data\"]", "explanation": "声明/data为数据卷，容器启动时自动挂载。"}]}}, {"name": "USER", "trans": ["指定运行用户"], "usage": {"syntax": "USER <用户名或UID>", "description": "指定后续指令和容器运行时的用户身份，提升安全性。", "parameters": [{"name": "<用户名或UID>", "description": "用户名或用户ID。"}], "returnValue": "无返回值，影响容器进程权限。", "examples": [{"code": "USER nginx", "explanation": "后续命令和容器以nginx用户身份运行。"}]}}, {"name": "HEALTHCHECK", "trans": ["健康检查"], "usage": {"syntax": "HEALTHCHECK --interval=<间隔> --timeout=<超时> --retries=<次数> CMD <命令>", "description": "为容器设置健康检查命令及参数，定期检测服务状态。", "parameters": [{"name": "--interval", "description": "检查间隔时间。"}, {"name": "--timeout", "description": "单次检查超时时间。"}, {"name": "--retries", "description": "失败重试次数。"}, {"name": "CMD <命令>", "description": "健康检查执行的命令。"}], "returnValue": "无返回值，容器运行时自动健康检查。", "examples": [{"code": "HEALTHCHECK --interval=30s --timeout=5s --retries=3 CMD curl -f http://localhost/ || exit 1", "explanation": "每30秒检测一次服务健康，失败3次视为不健康。"}]}}, {"name": "Multi-stage Build", "trans": ["多阶段构建"], "usage": {"syntax": "FROM <镜像> AS <阶段名>\n...\nFROM <镜像>\nCOPY --from=<阶段名> <路径> <目标>", "description": "通过多阶段构建，先用一个镜像编译应用，再用另一个更小的镜像作为最终产物，减少镜像体积。", "parameters": [{"name": "AS", "description": "为阶段命名，便于后续引用。"}, {"name": "COPY --from", "description": "从前一阶段复制文件到新阶段。"}], "returnValue": "无返回值，生成优化后的小体积镜像。", "examples": [{"code": "FROM node:16 AS build\nWORKDIR /app\nCOPY . .\nRUN npm install && npm run build\nFROM nginx:alpine\nCOPY --from=build /app/dist /usr/share/nginx/html", "explanation": "先用node镜像编译，再用nginx镜像只包含静态文件，极大减小体积。"}]}}, {"name": "Assignment: Dockerfile指令实践", "trans": ["作业：Dockerfile指令实践"], "usage": {"syntax": "请完成以下任务：\n1. 编写Dockerfile，包含FROM、COPY、RUN、CMD等常用指令\n2. 使用ENV和EXPOSE声明环境变量和端口\n3. 添加HEALTHCHECK健康检查\n4. 实现多阶段构建优化镜像体积", "description": "通过实际操作掌握Dockerfile各类指令的用法和最佳实践。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 1. 编写Dockerfile\nFROM node:16\nWORKDIR /app\nCOPY . .\nRUN npm install\nCMD [\"npm\", \"start\"]\n# 2. 设置环境变量和端口\nENV NODE_ENV=production\nEXPOSE 3000\n# 3. 健康检查\nHEALTHCHECK --interval=30s CMD curl -f http://localhost:3000/ || exit 1\n# 4. 多阶段构建\nFROM node:16 AS build\n...", "explanation": "依次完成Dockerfile常用指令和多阶段构建的核心实践。"}]}}]}