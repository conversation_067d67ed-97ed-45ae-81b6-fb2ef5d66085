{"name": "User Management Exercise", "trans": ["用户管理练习"], "methods": [{"name": "Create Readonly User for Student Table", "trans": ["创建只读用户（学生表）"], "usage": {"syntax": "-- 创建只读用户\nCREATE USER 'readonly'@'localhost' IDENTIFIED BY 'readonly123';\n-- 授权只读用户只能查询student表\nGRANT SELECT ON testdb.student TO 'readonly'@'localhost';\n-- 查看权限\nSHOW GRANTS FOR 'readonly'@'localhost';", "description": "本练习演示如何创建一个只读用户，并只允许其查询testdb数据库中的student表。适用于需要开放部分数据但保证安全的场景。", "parameters": [{"name": "用户名", "description": "只读用户名称"}, {"name": "主机", "description": "允许登录的主机"}, {"name": "表名", "description": "被授权的表名"}], "returnValue": "无返回值", "examples": [{"code": "-- 假设已存在testdb.student表\n-- 创建只读用户\nCREATE USER 'readonly'@'localhost' IDENTIFIED BY 'readonly123';\n-- 授权只读用户只能查询student表\nGRANT SELECT ON testdb.student TO 'readonly'@'localhost';\n-- 查看权限\nSHOW GRANTS FOR 'readonly'@'localhost';\n\n-- 测试只读用户登录后只能查询student表，不能插入、更新或删除数据\n-- INSERT INTO testdb.student VALUES (...); -- 会报错\n-- UPDATE testdb.student SET ...; -- 会报错", "explanation": "本例展示了如何创建一个只读用户readonly，并只授予其查询student表的权限。只读用户无法对student表进行写操作，保障了数据安全。"}]}}, {"name": "User Management Assignment", "trans": ["用户管理练习题"], "usage": {"syntax": "# 用户管理练习题", "description": "完成以下练习，巩固MySQL用户权限细粒度控制的能力。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 创建一个只能本地登录的只读用户readonly，密码为readonly123。\n2. 只授予readonly用户查询testdb.student表的权限。\n3. 验证readonly用户无法对student表进行插入、更新和删除操作。\n4. 尝试让readonly用户查询testdb的其他表，观察结果。", "explanation": "通过这些练习，你将掌握MySQL只读用户的创建、权限分配和权限验证等实用技能。"}]}}]}