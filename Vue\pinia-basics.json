{"name": "Pinia Basics", "trans": ["Pinia"], "methods": [{"name": "Store Definition", "trans": ["Store定义"], "usage": {"syntax": "import { defineStore } from 'pinia'\nconst useCounterStore = defineStore('counter', { ... })", "description": "Pinia通过defineStore定义store，每个store独立管理一组状态，推荐以useXxxStore命名。", "parameters": [{"name": "defineStore", "description": "定义store的方法"}, {"name": "id", "description": "store唯一标识"}], "returnValue": "store实例，包含state、getter、action等", "examples": [{"code": "import { defineStore } from 'pinia'\nconst useCounterStore = defineStore('counter', {\n  state: () => ({ count: 0 })\n})", "explanation": "定义一个计数器store。"}]}}, {"name": "State/Getter/Action", "trans": ["State/Getter/Action"], "usage": {"syntax": "const useStore = defineStore('main', {\n  state: () => ({ count: 0 }),\n  getters: { double: state => state.count * 2 },\n  actions: { inc() { this.count++ } }\n})", "description": "state存储数据，getter派生数据，action处理同步和异步逻辑。Pinia不区分mutation和action，推荐直接用action。", "parameters": [{"name": "state", "description": "响应式状态对象"}, {"name": "getters", "description": "派生状态的计算属性"}, {"name": "actions", "description": "同步或异步操作"}], "returnValue": "store实例，包含所有状态和方法", "examples": [{"code": "const useStore = defineStore('main', {\n  state: () => ({ count: 0 }),\n  getters: { double: state => state.count * 2 },\n  actions: { inc() { this.count++ } }\n})", "explanation": "定义包含state、getter、action的store。"}]}}, {"name": "Composition API Integration", "trans": ["组合式API集成"], "usage": {"syntax": "const store = useCounterStore()\nstore.count++\nstore.inc()", "description": "Pinia天然支持组合式API，store可在setup、组合函数等任意位置调用，响应式无缝集成。", "parameters": [{"name": "useXxxStore", "description": "store实例调用函数"}], "returnValue": "响应式store实例，可直接在模板和逻辑中使用", "examples": [{"code": "<script setup>\nconst counter = useCounterStore()\ncounter.inc()\n</script>\n<template>\n  <button @click=\"counter.inc\">{{ counter.count }}</button>\n</template>", "explanation": "在setup中直接使用store，实现响应式数据和方法调用。"}]}}, {"name": "Module Splitting", "trans": ["模块拆分"], "usage": {"syntax": "// 每个store单独定义\nconst useUserStore = defineStore('user', { ... })\nconst useCartStore = defineStore('cart', { ... })", "description": "Pinia推荐将每个功能模块单独定义为一个store，便于维护和按需加载。", "parameters": [{"name": "defineStore", "description": "为每个模块定义独立store"}], "returnValue": "多个独立的store实例", "examples": [{"code": "const useUserStore = defineStore('user', { state: () => ({ name: '' }) })\nconst useCartStore = defineStore('cart', { state: () => ({ items: [] }) })", "explanation": "将用户和购物车分别定义为独立store。"}]}}, {"name": "Plugins and Middleware", "trans": ["插件与中间件"], "usage": {"syntax": "pinia.use(plugin)", "description": "Pinia支持插件机制，可扩展store功能，如持久化、日志、权限等。插件通过pinia.use注册。", "parameters": [{"name": "plugin", "description": "接收store上下文的函数"}], "returnValue": "扩展功能的store实例", "examples": [{"code": "import { createPinia } from 'pinia'\nconst pinia = createPinia()\npinia.use(plugin)", "explanation": "通过pinia.use注册插件扩展功能。"}]}}, {"name": "State Persistence", "trans": ["状态持久化"], "usage": {"syntax": "import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'\npinia.use(piniaPluginPersistedstate)", "description": "通过pinia-plugin-persistedstate等插件可将store状态持久化到localStorage，实现刷新不丢失。", "parameters": [{"name": "piniaPluginPersistedstate", "description": "持久化插件函数"}], "returnValue": "持久化的store实例", "examples": [{"code": "import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'\npinia.use(piniaPluginPersistedstate)", "explanation": "注册持久化插件，实现store状态持久化。"}]}}, {"name": "Pinia vs Vuex", "trans": ["与Vuex对比"], "usage": {"syntax": "// Pinia无需mutation，API更简洁，天然支持组合式API", "description": "Pinia相比Vuex更轻量、API更简洁、类型更友好，天然支持组合式API，推荐在Vue3项目中优先使用。", "parameters": [{"name": "Pinia", "description": "新一代状态管理库"}, {"name": "Vuex", "description": "传统状态管理库"}], "returnValue": "对比两者的优缺点和适用场景", "examples": [{"code": "// Pinia用法更简洁，无需mutation，直接定义action\nconst useStore = defineStore('main', {\n  state: () => ({ count: 0 }),\n  actions: { inc() { this.count++ } }\n})", "explanation": "Pinia代码更简洁，类型推断更好，推荐Vue3使用。"}]}}, {"name": "Pinia Assignment", "trans": ["Pinia练习"], "usage": {"syntax": "# Pinia练习", "description": "完成以下练习，巩固Pinia相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 定义一个计数器store，包含state和action。\n2. 拆分用户和购物车store。\n3. 在setup中集成store。\n4. 注册插件实现状态持久化。\n5. 对比Pinia和Vuex的用法和API。", "explanation": "通过这些练习掌握Pinia的核心用法和优势。"}]}}]}