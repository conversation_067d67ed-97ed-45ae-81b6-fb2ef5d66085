{"name": "Configuration Management", "trans": ["配置管理"], "methods": [{"name": "application.properties and application.yml", "trans": ["application.properties与application.yml"], "usage": {"syntax": "application.properties 或 application.yml 文件用于集中管理Spring Boot应用的配置项", "description": "Spring Boot支持两种主流配置文件格式：application.properties（键值对）和application.yml（YAML层级结构），用于统一管理数据库、端口、日志等应用配置。", "parameters": [{"name": "application.properties", "description": "传统的key=value格式配置文件"}, {"name": "application.yml", "description": "YAML层级结构配置文件"}], "returnValue": "应用启动时自动加载的全局配置对象", "examples": [{"code": "# application.properties 示例\nspring.datasource.url=**********************************spring.datasource.username=root\nspring.datasource.password=123456\nserver.port=8080\n\n# application.yml 示例\nspring:\n  datasource:\n    url: **********************************    username: root\n    password: 123456\nserver:\n  port: 8080", "explanation": "两种格式都可用于配置Spring Boot应用，推荐yml格式结构更清晰。"}]}}, {"name": "Configuration Priority and Loading Order", "trans": ["配置优先级与加载顺序"], "usage": {"syntax": "Spring Boot按优先级顺序加载配置文件，优先级高的覆盖低的", "description": "Spring Boot支持多种配置来源，加载顺序为：命令行参数 > application.properties/yml > 外部配置 > 默认配置。优先级高的配置会覆盖低优先级。", "parameters": [{"name": "命令行参数", "description": "启动时传递的--key=value"}, {"name": "application.properties/yml", "description": "项目根目录或resources下的配置文件"}, {"name": "外部配置", "description": "环境变量、外部文件等"}, {"name": "默认配置", "description": "Spring Boot内置默认值"}], "returnValue": "最终生效的配置项值", "examples": [{"code": "# 启动命令行参数优先级最高\njava -jar demo.jar --server.port=9090\n# application.properties中配置\nserver.port=8080\n# 实际端口为9090，因为命令行参数覆盖了配置文件", "explanation": "命令行参数可临时覆盖配置文件，适合多环境部署。"}]}}, {"name": "Externalized Configuration", "trans": ["外部化配置"], "usage": {"syntax": "通过环境变量、外部文件、命令行等方式实现配置外部化", "description": "Spring Boot支持将配置放在项目外部，如环境变量、外部配置文件、命令行参数等，便于不同环境灵活切换和安全管理。", "parameters": [{"name": "环境变量", "description": "操作系统级别的变量"}, {"name": "外部配置文件", "description": "项目外部的properties/yml文件"}, {"name": "命令行参数", "description": "启动时传递的参数"}], "returnValue": "可灵活切换和管理的配置项", "examples": [{"code": "# 通过环境变量配置端口\nexport SERVER_PORT=9090\n# 通过外部文件覆盖配置\njava -jar demo.jar --spring.config.location=/opt/config/\n# 通过命令行参数覆盖\njava -jar demo.jar --server.port=8081", "explanation": "外部化配置适合生产环境安全和灵活运维。"}]}}, {"name": "Profile-based Multi-environment Configuration", "trans": ["Profile多环境配置"], "usage": {"syntax": "application-{profile}.properties/yml 支持多环境配置，激活方式多样", "description": "通过Profile机制可为开发、测试、生产等环境分别配置不同的参数，Spring Boot自动根据激活的Profile加载对应配置。", "parameters": [{"name": "Profile名称", "description": "如dev、test、prod"}, {"name": "激活方式", "description": "application.properties中spring.profiles.active=dev，或命令行--spring.profiles.active=dev"}], "returnValue": "当前激活环境下的配置项", "examples": [{"code": "# application-dev.yml\nserver:\n  port: 8081\n# application-prod.yml\nserver:\n  port: 80\n# 激活dev环境\nspring.profiles.active=dev", "explanation": "通过Profile机制实现多环境灵活切换。"}]}}, {"name": "Configuration Hot Reload", "trans": ["配置热更新"], "usage": {"syntax": "集成Spring Boot DevTools实现配置文件热更新", "description": "Spring Boot DevTools可实现配置文件修改后自动重启应用，提升开发效率。", "parameters": [{"name": "spring-boot-devtools", "description": "开发时依赖，自动监控配置变更并重启"}], "returnValue": "配置变更后自动重启的开发环境", "examples": [{"code": "# pom.xml中添加依赖\n<dependency>\n  <groupId>org.springframework.boot</groupId>\n  <artifactId>spring-boot-devtools</artifactId>\n  <optional>true</optional>\n</dependency>\n# 修改配置文件后，应用自动重启，无需手动重启服务。", "explanation": "DevTools极大提升开发效率，适合本地开发环境。"}]}}, {"name": "Type-safe Configuration (@ConfigurationProperties)", "trans": ["类型安全配置（@ConfigurationProperties）"], "usage": {"syntax": "@ConfigurationProperties(prefix = \"xxx\") 注解实现类型安全配置绑定", "description": "通过@ConfigurationProperties可将配置项自动绑定到Java Bean，支持类型校验、IDE自动提示，提升配置安全性和可维护性。", "parameters": [{"name": "@ConfigurationProperties", "description": "注解，指定配置前缀"}, {"name": "Java Bean", "description": "用于接收配置的POJO类"}], "returnValue": "自动绑定配置项的类型安全Java对象", "examples": [{"code": "# application.yml\nmyapp:\n  name: 测试系统\n  timeout: 30\n\n// Java Bean\n@Component\n@ConfigurationProperties(prefix = \"myapp\")\npublic class MyAppProperties {\n    private String name;\n    private int timeout;\n    // getter/setter\n}", "explanation": "@ConfigurationProperties可自动将配置项注入到Java对象，类型安全。"}]}}, {"name": "Configuration Encryption and Security", "trans": ["配置加密与安全"], "usage": {"syntax": "集成Jasypt等加密库，对敏感配置加密存储", "description": "通过Jasypt等加密库可对数据库密码等敏感信息加密，提升配置安全性，Spring Boot支持自动解密。", "parameters": [{"name": "Jasypt", "description": "常用的配置加密库"}, {"name": "ENC()格式", "description": "加密后的配置值格式"}], "returnValue": "自动解密的安全配置项", "examples": [{"code": "# application.properties\nspring.datasource.password=ENC(加密后的密文)\n# 启动时指定解密密钥\n-Djasypt.encryptor.password=密钥", "explanation": "敏感信息加密存储，运行时自动解密，保障安全。"}]}}, {"name": "Configuration Management Assignment", "trans": ["配置管理练习"], "usage": {"syntax": "# 配置管理练习", "description": "完成以下练习，巩固Spring Boot配置管理相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 分别用properties和yml格式配置数据库和端口。\n2. 实验命令行参数、环境变量、外部文件的优先级覆盖。\n3. 配置多环境Profile并切换。\n4. 集成DevTools实现配置热更新。\n5. 用@ConfigurationProperties实现类型安全配置。\n6. 用Jasypt加密数据库密码。", "explanation": "通过这些练习掌握Spring Boot配置管理的核心能力。"}]}}]}