{"name": "Performance Tuning", "trans": ["性能调优"], "methods": [{"name": "Startup Speed Optimization", "trans": ["启动速度优化"], "usage": {"syntax": "spring.main.lazy-initialization=true，精简依赖，Profile裁剪", "description": "通过懒加载、精简依赖、Profile裁剪等方式可显著提升Spring Boot应用的启动速度。", "parameters": [{"name": "spring.main.lazy-initialization", "description": "启用懒加载"}, {"name": "依赖精简", "description": "移除不必要的Starter和依赖"}, {"name": "Profile裁剪", "description": "按需加载配置和Bean"}], "returnValue": "更快的应用启动速度", "examples": [{"code": "# application.properties\nspring.main.lazy-initialization=true\n# 只引入必要依赖，使用prod/test等Profile区分环境", "explanation": "通过懒加载和依赖精简提升启动速度。"}]}}, {"name": "Memory and GC Tuning", "trans": ["内存与GC调优"], "usage": {"syntax": "-Xms -Xmx -XX:参数，G1/Parallel GC配置", "description": "通过合理设置JVM堆大小、选择合适的GC算法（如G1、Parallel GC）和参数，提升内存利用率和垃圾回收效率。", "parameters": [{"name": "-Xms/-Xmx", "description": "设置JVM初始和最大堆内存"}, {"name": "-XX:+UseG1GC", "description": "启用G1垃圾回收器"}, {"name": "-XX:参数", "description": "其他GC相关参数"}], "returnValue": "更优的内存占用和GC表现", "examples": [{"code": "# 启动参数示例\njava -Xms512m -Xmx2g -XX:+UseG1GC -jar app.jar", "explanation": "设置JVM堆大小和G1 GC提升内存与GC性能。"}]}}, {"name": "Database Performance Tuning", "trans": ["数据库性能优化"], "usage": {"syntax": "索引优化、SQL调优、分页查询、只查必要字段", "description": "通过合理设计索引、优化SQL语句、避免全表扫描、分页查询和只查必要字段等方式提升数据库性能。", "parameters": [{"name": "索引优化", "description": "为高频查询字段建索引"}, {"name": "SQL调优", "description": "避免N+1、全表扫描等低效SQL"}, {"name": "分页查询", "description": "大数据量时使用分页"}, {"name": "字段裁剪", "description": "只查询必要字段"}], "returnValue": "更快的数据库响应和更低的资源消耗", "examples": [{"code": "-- SQL示例\nSELECT id, name FROM user WHERE status = 1 LIMIT 0, 20;\n-- 为status字段建索引\nCREATE INDEX idx_user_status ON user(status);", "explanation": "通过索引和字段裁剪提升查询效率。"}]}}, {"name": "Connection Pool Tuning", "trans": ["连接池优化"], "usage": {"syntax": "spring.datasource.hikari.*参数配置", "description": "通过调整HikariCP等连接池的最大连接数、空闲连接数、超时时间等参数，提升数据库连接的利用率和稳定性。", "parameters": [{"name": "maximumPoolSize", "description": "最大连接数"}, {"name": "minimumIdle", "description": "最小空闲连接数"}, {"name": "connectionTimeout", "description": "连接超时时间"}], "returnValue": "高效稳定的数据库连接池", "examples": [{"code": "# application.properties\nspring.datasource.hikari.maximumPoolSize=20\nspring.datasource.hikari.minimumIdle=5\nspring.datasource.hikari.connectionTimeout=30000", "explanation": "配置HikariCP参数优化连接池性能。"}]}}, {"name": "Cache Optimization", "trans": ["缓存优化"], "usage": {"syntax": "@Cacheable、Redis缓存、热点数据预热", "description": "通过@Cacheable注解、合理使用Redis等缓存中间件、热点数据预热等方式，提升系统响应速度和并发能力。", "parameters": [{"name": "@Cacheable", "description": "方法级缓存注解"}, {"name": "Redis", "description": "高性能分布式缓存"}, {"name": "预热", "description": "启动时加载热点数据"}], "returnValue": "更快的接口响应和更高的并发能力", "examples": [{"code": "@Cacheable(\"hotData\")\npublic Data getHotData(Long id) {\n    // 查询数据库\n}\n// 启动时预热缓存", "explanation": "通过缓存和预热提升热点数据访问性能。"}]}}, {"name": "Hotspot Data and API Optimization", "trans": ["热点数据与热点接口优化"], "usage": {"syntax": "限流、降级、热点参数隔离、异步处理", "description": "通过限流、降级、热点参数隔离、异步处理等手段，防止热点数据和接口成为系统瓶颈。", "parameters": [{"name": "限流", "description": "限制高频访问，保护系统"}, {"name": "降级", "description": "高峰期返回默认值或友好提示"}, {"name": "参数隔离", "description": "热点参数单独处理"}, {"name": "异步处理", "description": "将耗时操作异步化"}], "returnValue": "高可用高性能的系统接口", "examples": [{"code": "// Sentinel限流示例\n@SentinelResource(value = \"hotApi\", blockHandler = \"blockHandler\")\npublic String hotApi(@RequestParam String key) {\n    // 业务逻辑\n}\npublic String blockHandler(String key, BlockException ex) {\n    return \"请求过于频繁，请稍后再试\";\n}", "explanation": "通过限流和降级保护热点接口。"}]}}, {"name": "Performance Tuning Assignment", "trans": ["性能调优练习"], "usage": {"syntax": "# 性能调优练习", "description": "完成以下练习，巩固Spring Boot性能调优相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 配置懒加载和Profile裁剪优化启动速度。\n2. 设置JVM参数和GC算法优化内存。\n3. 优化数据库索引和SQL语句。\n4. 配置HikariCP参数优化连接池。\n5. 使用@Cacheable和Redis优化热点数据。\n6. 对热点接口实现限流和降级。", "explanation": "通过这些练习掌握Spring Boot性能调优的核心用法。"}]}}]}