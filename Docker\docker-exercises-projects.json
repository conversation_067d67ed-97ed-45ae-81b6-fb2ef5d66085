{"name": "Exercises & Projects", "trans": ["练习与项目"], "methods": [{"name": "Write Your Own Dockerfile", "trans": ["编写自己的Dockerfile"], "usage": {"syntax": "# Dockerfile示例\nFROM python:3.10\nWORKDIR /app\nCOPY . .\nRUN pip install -r requirements.txt\nCMD [\"python\", \"main.py\"]", "description": "根据实际项目需求，独立编写Dockerfile，实现应用容器化。", "parameters": [{"name": "FROM", "description": "选择基础镜像。"}, {"name": "WORKDIR", "description": "设置工作目录。"}, {"name": "COPY", "description": "复制项目文件。"}, {"name": "RUN", "description": "安装依赖。"}, {"name": "CMD", "description": "设置启动命令。"}], "returnValue": "无返回值，生成可用的Dockerfile。", "examples": [{"code": "FROM python:3.10\nWORKDIR /app\nCOPY . .\nRUN pip install -r requirements.txt\nCMD [\"python\", \"main.py\"]", "explanation": "为Python项目编写Dockerfile，实现容器化。"}]}}, {"name": "Build Private Image Registry", "trans": ["搭建私有镜像仓库"], "usage": {"syntax": "docker run -d -p 5000:5000 --name registry registry:2\n# 推送镜像\ndocker tag myapp:latest localhost:5000/myapp:latest\ndocker push localhost:5000/myapp:latest", "description": "通过官方registry镜像快速搭建本地私有仓库，实现镜像的本地存储与管理。", "parameters": [{"name": "registry:2", "description": "官方私有仓库镜像。"}, {"name": "docker tag", "description": "打标签推送到私有仓库。"}, {"name": "docker push", "description": "推送镜像到私有仓库。"}], "returnValue": "无返回值，成功搭建并推送镜像到私有仓库。", "examples": [{"code": "docker run -d -p 5000:5000 --name registry registry:2\ndocker tag myapp:latest localhost:5000/myapp:latest\ndocker push localhost:5000/myapp:latest", "explanation": "本地搭建registry并推送自定义镜像。"}]}}, {"name": "Multi-environment Compose Orchestration", "trans": ["实现多环境Compose编排"], "usage": {"syntax": "# docker-compose.yml\nservices:\n  app:\n    build: .\n    env_file:\n      - .env.dev\n# docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d", "description": "通过env_file和多compose文件实现开发、测试、生产等多环境编排。", "parameters": [{"name": "env_file", "description": "按环境注入变量。"}, {"name": "多compose文件", "description": "覆盖和扩展配置。"}], "returnValue": "无返回值，支持多环境灵活编排。", "examples": [{"code": "services:\n  app:\n    build: .\n    env_file:\n      - .env.dev\ndocker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d", "explanation": "通过多compose文件实现多环境切换。"}]}}, {"name": "Optimize Image Size & Build Speed", "trans": ["优化镜像体积和构建速度"], "usage": {"syntax": "# 多阶段构建\nFROM golang:1.20 AS build\nWORKDIR /src\nCOPY . .\nRUN go build -o app\nFROM alpine\nCOPY --from=build /src/app /app\nCMD [\"/app\"]", "description": "通过多阶段构建、精简基础镜像等方式优化镜像体积和构建速度。", "parameters": [{"name": "多阶段构建", "description": "分离构建与运行，减小体积。"}, {"name": "精简基础镜像", "description": "如alpine等。"}], "returnValue": "无返回值，生成更小更快的镜像。", "examples": [{"code": "FROM golang:1.20 AS build\nWORKDIR /src\nCOPY . .\nRUN go build -o app\nFROM alpine\nCOPY --from=build /src/app /app\nCMD [\"/app\"]", "explanation": "Go项目多阶段构建，极大优化镜像体积。"}]}}, {"name": "Write Deploy & Rollback Scripts", "trans": ["编写部署与回滚脚本"], "usage": {"syntax": "#!/bin/bash\ndocker pull myapp:latest\ndocker stop myapp || true\ndocker rm myapp || true\ndocker run -d --name myapp myapp:latest\n# 回滚\ndocker run -d --name myapp myapp:old", "description": "通过Shell脚本实现自动化部署和回滚，提升运维效率和可靠性。", "parameters": [{"name": "docker pull", "description": "拉取最新镜像。"}, {"name": "docker stop/rm", "description": "停止并删除旧容器。"}, {"name": "docker run", "description": "启动新容器或回滚旧容器。"}], "returnValue": "无返回值，自动化部署与回滚。", "examples": [{"code": "#!/bin/bash\ndocker pull myapp:latest\ndocker stop myapp || true\ndocker rm myapp || true\ndocker run -d --name myapp myapp:latest\n# 回滚\ndocker run -d --name myapp myapp:old", "explanation": "一键部署和回滚脚本示例。"}]}}, {"name": "Production Security Hardening", "trans": ["生产环境安全加固"], "usage": {"syntax": "docker scan <镜像名>\ndocker run --read-only --user 1000 --memory=512m ...", "description": "通过安全扫描、只读文件系统、最小权限和资源限制等手段加固生产环境容器安全。", "parameters": [{"name": "docker scan", "description": "镜像安全扫描。"}, {"name": "--read-only", "description": "只读文件系统。"}, {"name": "--user", "description": "非root用户运行。"}, {"name": "--memory", "description": "限制内存。"}], "returnValue": "无返回值，提升生产环境安全性。", "examples": [{"code": "docker scan myapp\ndocker run --read-only --user 1000 --memory=512m myapp", "explanation": "安全扫描+只读+最小权限+资源限制的安全加固实践。"}]}}, {"name": "Assignment: 综合项目实践", "trans": ["作业：综合项目实践"], "usage": {"syntax": "请完成以下任务：\n1. 独立编写Dockerfile并运行项目\n2. 搭建本地私有仓库并推送镜像\n3. 实现多环境Compose编排\n4. 优化镜像体积和构建速度\n5. 编写自动化部署与回滚脚本\n6. 实施生产环境安全加固", "description": "通过综合练习掌握Docker核心技能，涵盖镜像、仓库、编排、优化、自动化和安全。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 1. Dockerfile编写\nFROM ...\n# 2. 私有仓库\nregistry:2 ...\n# 3. 多环境Compose\ndocker-compose.yml ...\n# 4. 多阶段构建 ...\n# 5. 部署脚本 ...\n# 6. 安全加固 ...", "explanation": "依次完成综合项目的各项实践任务。"}]}}]}