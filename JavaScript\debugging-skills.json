{"name": "Debugging Skills", "trans": ["调试技巧"], "methods": [{"name": "Console.log and Breakpoint Debugging", "trans": ["console.log与断点调试"], "usage": {"syntax": "console.log(value, ...args) / 浏览器断点调试", "description": "console.log用于输出变量、对象、表达式等信息，帮助快速定位问题。断点调试可在浏览器开发者工具中暂停代码执行，逐步查看变量状态和执行流程。", "parameters": [{"name": "value", "description": "需要输出的内容"}, {"name": "...args", "description": "可选，更多输出内容"}], "returnValue": "无返回值", "examples": [{"code": "// 使用console.log输出变量和对象\nlet user = { name: '张三', age: 20 };\nconsole.log('用户信息:', user);\n\n// 输出数组和表达式结果\nconst arr = [1, 2, 3];\nconsole.log('数组长度:', arr.length);\nconsole.log('总和:', arr.reduce((a, b) => a + b));\n\n// 断点调试：\n// 1. 在浏览器F12打开开发者工具\n// 2. 在Sources面板找到对应JS文件\n// 3. 点击行号设置断点，刷新页面即可断点暂停\n// 4. 使用Step Over/Into/Out等按钮逐步调试\n// 5. 在Console面板查看变量值和调用栈", "explanation": "示例展示了console.log的常见用法，以及如何在浏览器中设置断点进行逐步调试。"}]}}, {"name": "Browser DevTools", "trans": ["浏览器调试工具"], "usage": {"syntax": "F12或右键检查，使用Elements、Console、Sources等面板", "description": "浏览器开发者工具（DevTools）集成了元素检查、控制台、断点调试、网络请求、性能分析等功能，是前端调试的核心工具。", "parameters": [{"name": "panel", "description": "需要使用的面板，如Elements、Console、Sources、Network等"}], "returnValue": "无返回值", "examples": [{"code": "// 常用面板说明：\n// Elements：查看和编辑DOM结构、样式\n// Console：输出日志、执行JS代码\n// Sources：查看源码、设置断点、调试JS\n// Network：分析网络请求和响应\n// Performance：性能分析和帧率监控\n// Application：本地存储、Cookie、缓存管理\n\n// 示例：在Console面板执行代码\nconsole.log('Hello, DevTools!');\n\n// 示例：在Elements面板修改元素样式\n// 1. 选中元素\n// 2. 在右侧Styles区域直接编辑CSS属性\n\n// 示例：在Network面板查看接口请求\n// 1. 刷新页面\n// 2. 观察接口请求的URL、状态码、响应内容", "explanation": "示例介绍了各个常用面板的功能和典型操作，帮助快速定位和解决前端问题。"}]}}, {"name": "SourceMap", "trans": ["SourceMap"], "usage": {"syntax": "//# sourceMappingURL=xxx.map.js", "description": "SourceMap用于将压缩、混淆或编译后的代码映射回源代码，方便调试。常见于Webpack、Babel等工具生成的项目。", "parameters": [{"name": "sourceMappingURL", "description": "SourceMap文件的路径或URL"}], "returnValue": "无返回值", "examples": [{"code": "// 在打包后的JS文件末尾自动添加：\n//# sourceMappingURL=app.js.map\n\n// 使用SourceMap调试：\n// 1. 确保开发环境开启SourceMap（如Webpack配置devtool: 'source-map'）\n// 2. 浏览器会自动加载.map文件\n// 3. 在DevTools中可直接调试源代码而非压缩代码\n// 4. 错误堆栈信息会指向真实源码位置", "explanation": "示例说明了SourceMap的作用、生成方式和在调试中的实际应用。"}]}}, {"name": "Error Capturing and Handling", "trans": ["错误捕获与处理"], "usage": {"syntax": "try { ... } catch (e) { ... } finally { ... } / window.onerror / Promise.catch", "description": "错误捕获与处理可以防止程序崩溃，提升用户体验。常用方式有：try...catch、全局onerror、Promise.catch等。", "parameters": [{"name": "e", "description": "捕获到的错误对象"}], "returnValue": "无返回值", "examples": [{"code": "// try...catch捕获同步错误\ntry {\n  let result = riskyOperation();\n  console.log('结果:', result);\n} catch (e) {\n  console.error('发生错误:', e.message);\n} finally {\n  console.log('无论是否出错都会执行');\n}\n\n// 捕获异步错误（Promise）\nfetch('/api/data')\n  .then(res => res.json())\n  .catch(err => {\n    console.error('请求失败:', err);\n  });\n\n// 全局错误捕获\nwindow.onerror = function(message, source, lineno, colno, error) {\n  console.error('全局错误:', message, 'at', source + ':' + lineno + ':' + colno);\n  // 可上报错误信息到服务器\n};", "explanation": "示例展示了同步、异步和全局错误的捕获与处理方式，帮助提升程序健壮性。"}]}}, {"name": "Debugging Assignment", "trans": ["调试练习"], "usage": {"syntax": "// 调试练习", "description": "完成以下练习，巩固调试技巧。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "/*\n练习：调试下列代码，找出并修复错误，并用console.log输出关键变量\n\nfunction calcAverage(arr) {\n  let sum = 0;\n  for (let i = 0; i < arr.length; i++) {\n    sum += arr[i];\n  }\n  return sum / arr.length;\n}\n\nconst data = [10, 20, 30, 40, 50];\nconst avg = calcAverage(data);\nconsole.log('平均值:', avg);\n*/", "explanation": "练习要求调试并修复代码中的错误，并用console.log输出关键变量，巩固调试技巧。"}]}}]}