{"name": "Troubleshooting", "trans": ["常见故障排查"], "methods": [{"name": "Too Many Connections", "trans": ["连接数过多"], "usage": {"syntax": "SHOW STATUS LIKE 'Threads_connected';\nSHOW VARIABLES LIKE 'max_connections';", "description": "通过查看当前连接数和最大连接数，判断是否因连接数过多导致无法连接数据库。可通过调整max_connections参数或优化连接池解决。", "parameters": [{"name": "Threads_connected", "description": "当前已连接线程数"}, {"name": "max_connections", "description": "最大允许连接数"}], "returnValue": "返回当前连接数和最大连接数", "examples": [{"code": "-- 查看当前连接数\nSHOW STATUS LIKE 'Threads_connected';\n-- 查看最大连接数\nSHOW VARIABLES LIKE 'max_connections';\n-- 临时调整最大连接数\nSET GLOBAL max_connections = 300;", "explanation": "本例展示了如何排查和调整MySQL连接数过多的问题。"}]}}, {"name": "Deadlock Detection", "trans": ["死锁检测"], "usage": {"syntax": "SHOW ENGINE INNODB STATUS;", "description": "通过SHOW ENGINE INNODB STATUS命令查看最近一次死锁信息，分析死锁原因并优化SQL或加锁顺序。", "parameters": [], "returnValue": "返回InnoDB引擎状态和死锁信息", "examples": [{"code": "-- 查看死锁信息\nSHOW ENGINE INNODB STATUS;", "explanation": "本例展示了如何获取和分析MySQL的死锁信息。"}]}}, {"name": "Performance Bottleneck Analysis", "trans": ["性能瓶颈分析"], "usage": {"syntax": "SHOW PROCESSLIST;\nSHOW STATUS LIKE 'Slow_queries';\nSHOW VARIABLES LIKE 'long_query_time';", "description": "通过SHOW PROCESSLIST、慢查询日志等工具，分析SQL执行慢的原因，定位性能瓶颈。", "parameters": [{"name": "Slow_queries", "description": "慢查询总数"}, {"name": "long_query_time", "description": "慢查询阈值"}], "returnValue": "返回慢查询相关统计和当前活跃线程信息", "examples": [{"code": "-- 查看当前慢查询数\nSHOW STATUS LIKE 'Slow_queries';\n-- 查看慢查询阈值\nSHOW VARIABLES LIKE 'long_query_time';\n-- 查看当前活跃线程\nSHOW PROCESSLIST;", "explanation": "本例展示了如何结合慢查询统计和线程信息分析性能瓶颈。"}]}}, {"name": "Troubleshooting Assignment", "trans": ["故障排查练习"], "usage": {"syntax": "# 故障排查练习", "description": "完成以下练习，巩固MySQL常见故障排查与性能分析能力。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 模拟连接数过多，观察报错并调整max_connections参数。\n2. 制造死锁场景，使用SHOW ENGINE INNODB STATUS分析死锁原因。\n3. 通过慢查询日志和SHOW PROCESSLIST定位性能瓶颈。", "explanation": "通过这些练习，你将掌握MySQL常见故障的排查和性能优化方法。"}]}}]}