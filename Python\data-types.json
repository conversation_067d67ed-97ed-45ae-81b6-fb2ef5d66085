{"name": "Data Types", "trans": ["数据类型"], "methods": [{"name": "Number Types (int, float, complex)", "trans": ["数字类型（int、float、complex）"], "usage": {"syntax": "a = 10  # int\nb = 3.14  # float\nc = 1 + 2j  # complex", "description": "Python支持三种主要的数字类型：int（整数）、float（浮点数）、complex（复数）。可直接赋值，支持常见算术运算。", "parameters": [{"name": "int", "description": "整数类型，如1、-5、0"}, {"name": "float", "description": "浮点数类型，如3.14、-0.5"}, {"name": "complex", "description": "复数类型，如1+2j"}], "returnValue": "数字类型变量", "examples": [{"code": "a = 10  # 整数\nb = 3.14  # 浮点数\nc = 1 + 2j  # 复数\nprint(type(a))  # <class 'int'>\nprint(type(b))  # <class 'float'>\nprint(type(c))  # <class 'complex'>", "explanation": "演示了三种数字类型的声明和类型判断。"}]}}, {"name": "String (str)", "trans": ["字符串（str）"], "usage": {"syntax": "s = 'hello'\ns2 = \"world\"\ns3 = '''多行字符串'''", "description": "字符串用于存储文本数据。可用单引号、双引号或三引号定义，支持切片、拼接、格式化等操作。", "parameters": [{"name": "str", "description": "字符串类型"}], "returnValue": "字符串变量", "examples": [{"code": "s = 'hello'\ns2 = \"world\"\ns3 = '''多行\n字符串'''\nprint(s + s2)  # 拼接\nprint(s3)  # 多行输出", "explanation": "演示了字符串的定义、拼接和多行字符串。"}]}}, {"name": "Boolean (bool)", "trans": ["布尔值（bool）"], "usage": {"syntax": "flag = True\nflag2 = False", "description": "布尔类型只有True和False，常用于条件判断。0、空对象等视为False，非零或非空为True。", "parameters": [{"name": "bool", "description": "布尔类型，只能为True或False"}], "returnValue": "布尔值变量", "examples": [{"code": "flag = True\nflag2 = False\nprint(bool(0))    # False\nprint(bool('abc'))  # True", "explanation": "演示了布尔类型的定义和常见转换。"}]}}, {"name": "List (list)", "trans": ["列表（list）"], "usage": {"syntax": "lst = [1, 2, 3]\nlst2 = list('abc')", "description": "列表是有序可变的元素集合，支持增删改查、切片、遍历等操作。", "parameters": [{"name": "list", "description": "列表类型，可包含任意类型元素"}], "returnValue": "列表变量", "examples": [{"code": "lst = [1, 2, 3]\nlst.append(4)\nlst[0] = 10\nprint(lst)  # [10, 2, 3, 4]", "explanation": "演示了列表的创建、添加和修改。"}]}}, {"name": "<PERSON><PERSON> (tuple)", "trans": ["元组（tuple）"], "usage": {"syntax": "t = (1, 2, 3)\nt2 = tuple('abc')", "description": "元组是有序不可变的元素集合，常用于不可变数据。支持索引、切片、遍历。", "parameters": [{"name": "tuple", "description": "元组类型，元素不可修改"}], "returnValue": "元组变量", "examples": [{"code": "t = (1, 2, 3)\nprint(t[0])  # 1\n# t[0] = 5  # 错误，元组不可变", "explanation": "演示了元组的定义和不可变特性。"}]}}, {"name": "Set and Frozenset", "trans": ["集合（set、frozenset）"], "usage": {"syntax": "s = {1, 2, 3}\ns2 = set([1, 2, 2, 3])\nfs = frozenset([1, 2, 3])", "description": "集合是无序不重复元素的可变集合，frozenset为不可变集合。常用于去重、集合运算。", "parameters": [{"name": "set", "description": "可变集合"}, {"name": "frozenset", "description": "不可变集合"}], "returnValue": "集合变量", "examples": [{"code": "s = {1, 2, 3}\ns.add(4)\nprint(s)  # {1, 2, 3, 4}\nfs = frozenset([1, 2, 3])\n# fs.add(4)  # 错误，frozenset不可变", "explanation": "演示了set和frozenset的用法和区别。"}]}}, {"name": "Dictionary (dict)", "trans": ["字典（dict）"], "usage": {"syntax": "d = {'a': 1, 'b': 2}\nd2 = dict(c=3, d=4)", "description": "字典是键值对的无序可变集合，键必须唯一且不可变。支持增删改查、遍历等操作。", "parameters": [{"name": "dict", "description": "字典类型，键值对集合"}], "returnValue": "字典变量", "examples": [{"code": "d = {'a': 1, 'b': 2}\nd['c'] = 3\nprint(d['a'])  # 1\nprint(d)  # {'a': 1, 'b': 2, 'c': 3}", "explanation": "演示了字典的创建、添加和访问。"}]}}, {"name": "Type Conversion", "trans": ["类型转换"], "usage": {"syntax": "int(x)\nfloat(x)\nstr(x)\nlist(x)\ntuple(x)\nset(x)\ndict(x)", "description": "Python支持多种内置类型间的转换，如数字、字符串、列表、元组、集合、字典等。", "parameters": [{"name": "x", "description": "要转换的对象"}], "returnValue": "转换后的新对象", "examples": [{"code": "a = '123'\nb = int(a)  # 123\nc = list('abc')  # ['a', 'b', 'c']", "explanation": "演示了字符串转整数、字符串转列表等常见类型转换。"}]}}, {"name": "Mutable and Immutable Types", "trans": ["不可变与可变类型"], "usage": {"syntax": "不可变：int, float, str, tuple, frozenset\n可变：list, dict, set", "description": "不可变类型的值一旦创建不可更改（如int、str、tuple），可变类型的值可修改（如list、dict、set）。", "parameters": [{"name": "类型", "description": "任意Python内置类型"}], "returnValue": "类型特性说明", "examples": [{"code": "a = (1, 2, 3)\n# a[0] = 5  # 错误，元组不可变\nlst = [1, 2, 3]\nlst[0] = 5  # 正确，列表可变", "explanation": "演示了可变与不可变类型的区别。"}]}}, {"name": "Data Types Assignment", "trans": ["数据类型练习"], "usage": {"syntax": "# 数据类型练习", "description": "完成以下练习，巩固数据类型相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 创建一个包含不同类型元素的列表，并输出其类型。\n2. 将字符串'123'转换为整数并相加。\n3. 定义一个字典，添加和访问元素。\n4. 体会元组和列表的可变性区别。", "explanation": "练习要求动手实践数字、字符串、列表、元组、字典等数据类型的基本操作。"}]}}]}