{"name": "Provide and Inject", "trans": ["provide/inject"], "methods": [{"name": "Cross-level Dependency Injection", "trans": ["跨层级依赖注入"], "usage": {"syntax": "import { provide, inject } from 'vue'\n// 父组件\nprovide('key', value)\n// 子孙组件\nconst val = inject('key')", "description": "provide/inject用于实现祖先组件向任意后代组件传递数据，解决多层嵌套props传递繁琐的问题。", "parameters": [{"name": "provide(key, value)", "description": "在祖先组件提供数据"}, {"name": "inject(key)", "description": "在后代组件注入数据"}], "returnValue": "后代组件可直接访问祖先组件提供的数据", "examples": [{"code": "// 父组件\nprovide('color', 'red')\n// 孙组件\nconst color = inject('color')", "explanation": "父组件通过provide提供color，孙组件通过inject获取。"}]}}, {"name": "Provide/Inject Usage", "trans": ["provide/inject用法"], "usage": {"syntax": "setup() {\n  provide('key', value)\n  const val = inject('key')\n}", "description": "provide/inject只能在setup或生命周期钩子中调用，key建议用Symbol防止冲突。", "parameters": [{"name": "key", "description": "注入的唯一标识，建议用Symbol"}, {"name": "value", "description": "要注入的数据"}], "returnValue": "注入的数据，未找到时可设默认值", "examples": [{"code": "const key = Symbol('myKey')\nprovide(key, 123)\nconst val = inject(key, 0)", "explanation": "用Symbol作为key，inject可设置默认值。"}]}}, {"name": "Reactive Injection", "trans": ["响应式注入"], "usage": {"syntax": "const state = reactive({ a: 1 })\nprovide('state', state)\nconst s = inject('state')", "description": "通过provide注入响应式对象，后代组件获取到的也是响应式的，数据变更会自动同步。", "parameters": [{"name": "reactive对象", "description": "注入的响应式数据"}], "returnValue": "后代组件获得响应式对象，数据联动", "examples": [{"code": "const state = reactive({ count: 0 })\nprovide('state', state)\nconst s = inject('state')\ns.count++ // 父子组件数据同步", "explanation": "注入响应式对象实现多组件数据共享。"}]}}, {"name": "Scenarios and Notes", "trans": ["场景与注意事项"], "usage": {"syntax": "// 适用场景：主题、全局配置、表单上下文等", "description": "provide/inject适合全局配置、主题、表单上下文等场景。注意：注入的数据不是响应式时不会自动更新，建议注入响应式对象。", "parameters": [{"name": "适用场景", "description": "全局配置、主题、表单等"}, {"name": "注意事项", "description": "非响应式数据不会自动更新"}], "returnValue": "合理使用可提升组件解耦和复用性", "examples": [{"code": "// 主题色注入\nprovide('theme', ref('dark'))\nconst theme = inject('theme')", "explanation": "适合全局主题、表单上下文等场景。"}]}}, {"name": "Provide/Inject Assignment", "trans": ["provide/inject练习"], "usage": {"syntax": "# provide/inject练习", "description": "完成以下练习，巩固provide/inject相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用provide/inject实现父子组件数据共享。\n2. 用Symbol作为key防止冲突。\n3. 注入响应式对象并在多个组件中联动。\n4. 总结适用场景和注意事项。", "explanation": "通过这些练习掌握provide/inject的用法和最佳实践。"}]}}]}