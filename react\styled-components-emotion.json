{"name": "Styled Components/Emotion", "trans": ["Styled Components/Emotion"], "methods": [{"name": "Component Styling", "trans": ["组件样式化"], "usage": {"syntax": "import styled from 'styled-components';\nconst Button = styled.button`color: red;`;\n<Button>按钮</Button>", "description": "通过styled-components或@emotion/styled为组件编写CSS，支持标签模板语法和props动态样式。", "parameters": [{"name": "styled", "description": "样式化组件的工厂函数。"}, {"name": "tag", "description": "HTML标签或自定义组件。"}], "returnValue": "返回带样式的React组件。", "examples": [{"code": "import styled from 'styled-components';\nconst Button = styled.button`color: red;`;\nfunction App() { return <Button>按钮</Button>; }", "explanation": "用styled-components实现按钮样式。"}]}}, {"name": "Theme Design", "trans": ["主题设计"], "usage": {"syntax": "import { ThemeProvider } from 'styled-components';\nconst theme = { primary: 'blue' };\n<ThemeProvider theme={theme}>...</ThemeProvider>", "description": "通过ThemeProvider提供全局主题，组件可通过props.theme访问主题变量，实现统一风格。", "parameters": [{"name": "ThemeProvider", "description": "主题上下文提供组件。"}, {"name": "theme", "description": "主题对象。"}], "returnValue": "无返回值，主题通过context传递。", "examples": [{"code": "import styled, { ThemeProvider } from 'styled-components';\nconst theme = { primary: 'blue' };\nconst Button = styled.button`color: ${props => props.theme.primary};`;\nfunction App() {\n  return <ThemeProvider theme={theme}><Button>按钮</Button></ThemeProvider>;\n}", "explanation": "通过ThemeProvider实现主题切换。"}]}}, {"name": "Dynamic Styles", "trans": ["动态样式"], "usage": {"syntax": "const Button = styled.button`color: ${props => props.active ? 'red' : 'gray'};`;", "description": "通过props动态控制样式，实现交互反馈和状态变化。", "parameters": [{"name": "props", "description": "传递给组件的属性。"}], "returnValue": "返回带动态样式的组件。", "examples": [{"code": "const Button = styled.button`color: ${props => props.active ? 'red' : 'gray'};`;\n<Button active={true}>激活</Button>", "explanation": "根据active属性动态切换颜色。"}]}}, {"name": "Global Styles", "trans": ["全局样式"], "usage": {"syntax": "import { createGlobalStyle } from 'styled-components';\nconst GlobalStyle = createGlobalStyle`body { margin: 0; }`;\n<GlobalStyle />", "description": "通过createGlobalStyle定义全局CSS，影响整个应用。", "parameters": [{"name": "createGlobalStyle", "description": "创建全局样式的函数。"}], "returnValue": "返回全局样式组件。", "examples": [{"code": "import { createGlobalStyle } from 'styled-components';\nconst GlobalStyle = createGlobalStyle`body { margin: 0; }`;\nfunction App() { return <><GlobalStyle /><div>内容</div></>; }", "explanation": "定义并应用全局样式。"}]}}, {"name": "Style Reuse", "trans": ["样式复用"], "usage": {"syntax": "const common = css`padding: 8px;`;\nconst Button = styled.button`${common}`;", "description": "通过css辅助函数和继承机制实现样式片段复用，提升开发效率。", "parameters": [{"name": "css", "description": "样式片段辅助函数。"}], "returnValue": "返回可复用的样式片段。", "examples": [{"code": "import styled, { css } from 'styled-components';\nconst common = css`padding: 8px;`;\nconst Button = styled.button`${common}`;\nfunction App() { return <Button>按钮</Button>; }", "explanation": "通过css函数实现样式复用。"}]}}, {"name": "CSS-in-JS Performance", "trans": ["CSS-in-JS性能"], "usage": {"syntax": "// 性能优化建议：\n// 1. 避免在render中动态生成样式对象\n// 2. 合理拆分组件和样式\n// 3. 使用shouldForwardProp等过滤无关props", "description": "关注运行时样式生成、样式隔离和渲染性能，合理优化可提升体验。", "parameters": [], "returnValue": "无返回值，优化后提升性能。", "examples": [{"code": "// 避免在render中频繁创建新样式\n// 拆分大组件为小组件，减少样式重计算", "explanation": "性能优化建议示例。"}]}}, {"name": "SSR Support", "trans": ["服务端渲染支持"], "usage": {"syntax": "// Next.js中使用styled-components\n// 需配置babel-plugin-styled-components和SSR相关设置", "description": "styled-components和emotion均支持SSR，需结合框架配置，保证样式正确注入。", "parameters": [], "returnValue": "无返回值，样式可在服务端渲染。", "examples": [{"code": "// Next.js配置styled-components SSR\n// 1. 安装babel-plugin-styled-components\n// 2. 配置_babelrc和_document.js", "explanation": "在Next.js中实现样式SSR支持。"}]}}, {"name": "作业：CSS-in-JS实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 实现组件样式化、主题、动态样式、全局样式\n// 2. 复用样式片段和优化性能\n// 3. 支持SSR", "description": "通过实践组件样式化、主题、动态样式、全局样式、样式复用、性能优化和SSR，掌握CSS-in-JS开发。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 用styled-components实现样式化和主题\n// 2. 动态样式、全局样式、样式复用和SSR", "explanation": "作业提示，学生需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nimport styled from 'styled-components';\nconst Button = styled.button`color: red;`;\nfunction App() { return <Button>按钮</Button>; }", "explanation": "CSS-in-JS样式化组件的正确实现示例。"}]}}]}