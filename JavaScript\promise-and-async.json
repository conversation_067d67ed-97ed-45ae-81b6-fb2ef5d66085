{"name": "Promise and Async", "trans": ["Promise与异步"], "methods": [{"name": "Promise Basics", "trans": ["Promise基础"], "usage": {"syntax": "const p = new Promise((resolve, reject) => {\n  // 异步操作\n  resolve('成功');\n  // 或 reject('失败');\n});\np.then(res => {}).catch(err => {});", "description": "Promise用于处理异步操作，三种状态：pending、fulfilled、rejected。通过then/catch处理结果。", "parameters": [{"name": "resolve", "description": "成功时调用。"}, {"name": "reject", "description": "失败时调用。"}], "returnValue": "返回Promise对象。", "examples": [{"code": "const p = new Promise((res, rej) => { setTimeout(() => res(1), 100); });\np.then(data => console.log(data));", "explanation": "Promise异步执行并通过then获取结果。"}]}}, {"name": "async/await", "trans": ["async/await"], "usage": {"syntax": "async function foo() {\n  const res = await Promise.resolve(1);\n  return res;\n}\nfoo().then(console.log);", "description": "async声明异步函数，await等待Promise结果，简化异步流程。", "parameters": [{"name": "async", "description": "声明异步函数。"}, {"name": "await", "description": "等待Promise结果。"}], "returnValue": "返回Promise对象。", "examples": [{"code": "async function getData() {\n  const data = await Promise.resolve('ok');\n  return data;\n}\ngetData().then(console.log);", "explanation": "async/await简化Promise异步写法。"}]}}, {"name": "fetch & AJAX", "trans": ["fetch与AJAX"], "usage": {"syntax": "fetch(url).then(res => res.json()).then(data => {}).catch(err => {});", "description": "fetch是原生异步请求API，返回Promise。AJAX常用XMLHttpRequest或fetch实现。", "parameters": [{"name": "url", "description": "请求地址。"}], "returnValue": "返回Promise对象，解析后为响应数据。", "examples": [{"code": "fetch('https://api.example.com').then(res => res.json()).then(data => console.log(data));", "explanation": "fetch请求并处理JSON响应。"}]}}, {"name": "Generator Functions", "trans": ["Generator函数"], "usage": {"syntax": "function* gen() {\n  yield 1;\n  yield 2;\n}\nconst g = gen();\ng.next();", "description": "Generator函数通过function*声明，yield暂停执行，next()恢复。可用于异步流程控制。", "parameters": [{"name": "function*", "description": "声明生成器函数。"}, {"name": "yield", "description": "暂停并返回值。"}, {"name": "next", "description": "恢复执行。"}], "returnValue": "返回迭代器对象。", "examples": [{"code": "function* g() { yield 1; yield 2; }\nconst it = g();\nconsole.log(it.next()); // {value:1,done:false}\nconsole.log(it.next()); // {value:2,done:false}\nconsole.log(it.next()); // {value:undefined,done:true}", "explanation": "Generator函数的声明与迭代。"}]}}, {"name": "Async Flow Control", "trans": ["异步流程控制"], "usage": {"syntax": "Promise.all([p1, p2]);\nPromise.race([p1, p2]);", "description": "Promise.all并发执行多个Promise，全部成功才resolve。Promise.race第一个完成就resolve。", "parameters": [{"name": "Promise.all", "description": "并发等待所有Promise。"}, {"name": "Promise.race", "description": "并发等待最快的Promise。"}], "returnValue": "返回新的Promise对象。", "examples": [{"code": "const p1 = Promise.resolve(1);\nconst p2 = Promise.resolve(2);\nPromise.all([p1,p2]).then(res=>console.log(res));\nPromise.race([p1,p2]).then(res=>console.log(res));", "explanation": "Promise.all和Promise.race的用法。"}]}}, {"name": "作业：Promise与异步实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 用Promise处理异步\n// 2. 用async/await简化流程\n// 3. 用fetch请求数据\n// 4. 用Generator/Promise.all控制异步", "description": "通过实践Promise、async/await、fetch、Generator、流程控制等，掌握ES6异步编程。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. Promise/async/await/fetch\n// 2. Generator/Promise.all", "explanation": "作业提示，需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nconst p = Promise.resolve(1);\nasync function f(){ return await p; }\nf().then(console.log);\nPromise.all([p,Promise.resolve(2)]).then(console.log);", "explanation": "涵盖Promise、async/await、Promise.all的正确实现。"}]}}]}