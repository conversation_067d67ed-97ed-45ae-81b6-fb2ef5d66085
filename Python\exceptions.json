{"name": "Exceptions", "trans": ["异常基础"], "methods": [{"name": "try...except Structure", "trans": ["try...except结构"], "usage": {"syntax": "try:\n    代码块\nexcept 异常类型 as 变量:\n    处理代码", "description": "try...except用于捕获并处理异常，防止程序崩溃。可指定异常类型。", "parameters": [{"name": "异常类型", "description": "要捕获的异常类，如ValueError等"}, {"name": "变量", "description": "异常对象变量名，可选"}], "returnValue": "无返回值", "examples": [{"code": "try:\n    x = 1 / 0\nexcept ZeroDivisionError as e:\n    print('除零错误:', e)", "explanation": "演示了try...except结构的用法。"}]}}, {"name": "else and finally", "trans": ["else与finally"], "usage": {"syntax": "try:\n    代码块\nexcept 异常类型:\n    处理\nelse:\n    无异常时执行\nfinally:\n    总会执行", "description": "else块在无异常时执行，finally块无论是否异常都会执行，常用于资源释放。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "try:\n    print('ok')\nexcept:\n    print('error')\nelse:\n    print('无异常')\nfinally:\n    print('总会执行')", "explanation": "演示了else和finally的用法。"}]}}, {"name": "Multiple Exception Handling", "trans": ["多异常捕获"], "usage": {"syntax": "try:\n    ...\nexcept (异常1, 异常2) as e:\n    处理", "description": "可同时捕获多种异常，异常类型用元组表示。", "parameters": [{"name": "异常1, 异常2", "description": "要捕获的多个异常类型"}], "returnValue": "无返回值", "examples": [{"code": "try:\n    int('abc')\nexcept (ValueError, TypeError) as e:\n    print('异常:', e)", "explanation": "演示了多异常捕获。"}]}}, {"name": "Raise Exception", "trans": ["主动抛出异常raise"], "usage": {"syntax": "raise 异常类型('错误信息')", "description": "raise语句用于主动抛出异常，常用于参数校验等场景。", "parameters": [{"name": "异常类型", "description": "要抛出的异常类"}, {"name": "错误信息", "description": "异常描述信息"}], "returnValue": "无返回值", "examples": [{"code": "def set_age(age):\n    if age < 0:\n        raise ValueError('年龄不能为负数')\nset_age(-1)", "explanation": "演示了raise主动抛出异常。"}]}}, {"name": "Custom Exception Class", "trans": ["自定义异常类"], "usage": {"syntax": "class MyError(Exception):\n    pass", "description": "可通过继承Exception自定义异常类，便于业务异常处理。", "parameters": [{"name": "Exception", "description": "内置异常基类"}], "returnValue": "自定义异常对象", "examples": [{"code": "class MyError(Exception):\n    pass\ntry:\n    raise MyError('自定义异常')\nexcept MyError as e:\n    print(e)", "explanation": "演示了自定义异常类的定义和使用。"}]}}, {"name": "Exception Chaining and traceback", "trans": ["异常链与traceback"], "usage": {"syntax": "raise 新异常 from 原异常\nimport traceback\ntraceback.print_exc()", "description": "raise ... from ...可形成异常链，traceback模块可打印详细异常信息。", "parameters": [{"name": "新异常", "description": "要抛出的异常对象"}, {"name": "原异常", "description": "原始异常对象"}], "returnValue": "无返回值", "examples": [{"code": "import traceback\ntry:\n    1 / 0\nexcept ZeroDivisionError as e:\n    raise ValueError('新异常') from e\ntry:\n    1 / 0\nexcept:\n    traceback.print_exc()", "explanation": "演示了异常链和traceback的用法。"}]}}, {"name": "Exceptions Assignment", "trans": ["异常处理练习"], "usage": {"syntax": "# 异常处理练习", "description": "完成以下练习，巩固异常处理相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 捕获除零异常并输出提示。\n2. 编写一个自定义异常类。\n3. 用try...except...finally安全读写文件。\n4. 用traceback打印完整异常信息。", "explanation": "练习要求动手实践try...except、raise、自定义异常、traceback等。"}]}}]}