{"name": "AOF Persistence", "trans": ["AOF持久化"], "methods": [{"name": "Append-Only File Principle", "trans": ["日志追加原理"], "usage": {"syntax": "AOF通过将写命令以追加方式记录到日志文件（appendonly.aof）中实现数据持久化。", "description": "AOF（Append Only File）持久化机制会将Redis执行的每一个写操作命令（如SET、DEL等）以文本形式追加到AOF文件中，保证即使Redis异常退出也能通过AOF文件恢复数据。", "parameters": [{"name": "appendonly.aof", "description": "AOF日志文件，记录所有写操作命令。"}, {"name": "appendfsync", "description": "AOF刷盘策略，决定写入磁盘的频率。"}], "returnValue": "无返回值", "examples": [{"code": "# redis.conf配置AOF\nappendonly yes  # 启用AOF持久化\nappendfsync everysec  # 每秒同步一次AOF文件\n\n# Redis执行写命令时，命令会被追加到AOF文件\nSET key1 value1  # AOF文件记录：SET key1 value1\nDEL key1         # AOF文件记录：DEL key1\n", "explanation": "通过配置文件开启AOF，写操作会被追加到AOF文件，实现持久化。"}]}}, {"name": "AOF Rewrite and Recovery", "trans": ["AOF重写与恢复"], "usage": {"syntax": "AOF重写通过生成最小化的命令集来重写AOF文件，恢复时通过重放AOF文件命令恢复数据。", "description": "AOF文件随着写操作不断增大，Redis会自动或手动触发AOF重写（rewrite），生成等价但更精简的新AOF文件。恢复时，Redis会从头顺序执行AOF文件中的命令，恢复数据。", "parameters": [{"name": "BGREWRITEAOF", "description": "手动触发AOF重写的命令。"}, {"name": "AOF rewrite buffer", "description": "重写期间用于缓存新写命令的缓冲区。"}], "returnValue": "无返回值", "examples": [{"code": "# 手动触发AOF重写\nBGREWRITEAOF  # Redis后台生成新的AOF文件\n\n# 恢复过程\n# Redis启动时自动加载AOF文件，顺序执行命令恢复数据\n", "explanation": "通过BGREWRITEAOF命令手动重写AOF文件，恢复时自动重放AOF命令。"}]}}, {"name": "AOF vs RDB Comparison", "trans": ["AOF与RDB对比"], "usage": {"syntax": "AOF和RDB是Redis两种持久化机制，各有优缺点。", "description": "AOF持久化记录每个写命令，数据恢复更完整，适合高可靠性场景；RDB通过快照保存数据，恢复速度快但可能丢失最近数据。实际生产常结合使用。", "parameters": [{"name": "AOF", "description": "追加日志方式，数据更完整，文件体积大。"}, {"name": "RDB", "description": "快照方式，恢复快，可能丢失部分数据。"}], "returnValue": "无返回值", "examples": [{"code": "# 配置同时开启AOF和RDB\nappendonly yes\nsave 900 1\n\n# AOF适合高可靠性，RDB适合快速备份恢复\n", "explanation": "AOF和RDB可以同时开启，互为补充，提升数据安全性。"}]}}, {"name": "AOF持久化作业", "trans": ["作业：AOF持久化"], "usage": {"syntax": "请完成以下任务：\n1. 配置Redis开启AOF持久化。\n2. 手动执行BGREWRITEAOF命令。\n3. 比较AOF和RDB文件内容的不同。", "description": "通过实际操作理解AOF持久化的原理和与RDB的区别。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 作业示例\n# 1. 修改redis.conf，设置appendonly yes\n# 2. 启动Redis并执行写操作\n# 3. 执行BGREWRITEAOF\n# 4. 查看AOF和RDB文件内容差异\n", "explanation": "通过动手实验掌握AOF持久化的配置和原理。"}]}}, {"name": "AOF正确实现示例", "trans": ["正确实现卡片"], "usage": {"syntax": "AOF配置与恢复的标准流程。", "description": "展示如何标准配置AOF持久化，并通过AOF文件恢复数据，确保数据安全。", "parameters": [{"name": "appendonly", "description": "设置为yes以开启AOF。"}, {"name": "appendfsync", "description": "推荐设置为everysec，兼顾性能与安全。"}], "returnValue": "无返回值", "examples": [{"code": "# redis.conf标准配置\nappendonly yes\nappendfsync everysec\n\n# 恢复流程\n# 1. 停止Redis服务\n# 2. 替换AOF文件\n# 3. 启动Redis，自动恢复数据\n", "explanation": "标准AOF配置和恢复流程，保证数据安全可靠。"}]}}]}