{"name": "Multi-table Queries", "trans": ["多表查询"], "methods": [{"name": "Inner Join", "trans": ["内连接（INNER JOIN）"], "usage": {"syntax": "SELECT 列名 FROM 表1 INNER JOIN 表2 ON 表1.列 = 表2.列;", "description": "内连接返回两个表中满足连接条件的行，不满足条件的行将被排除在结果之外。", "parameters": [{"name": "表1", "description": "左侧表名"}, {"name": "表2", "description": "右侧表名"}, {"name": "连接条件", "description": "指定如何匹配两个表的行"}], "returnValue": "满足连接条件的结果集", "examples": [{"code": "-- 假设有学生表和班级表\nSELECT s.name, c.class_name\nFROM students s\nINNER JOIN classes c ON s.class_id = c.id;\n-- 查询每个学生及其所在班级", "explanation": "只返回学生表和班级表中能够匹配的记录，即有班级的学生和有学生的班级。"}]}}, {"name": "Left Join", "trans": ["左连接（LEFT JOIN）"], "usage": {"syntax": "SELECT 列名 FROM 表1 LEFT JOIN 表2 ON 表1.列 = 表2.列;", "description": "左连接返回左表中的所有行，即使右表中没有匹配的行。如果右表没有匹配，结果中右表的列将为NULL。", "parameters": [{"name": "表1", "description": "左侧表名，结果将包含此表的所有行"}, {"name": "表2", "description": "右侧表名"}, {"name": "连接条件", "description": "指定如何匹配两个表的行"}], "returnValue": "左表所有行与右表匹配行的结果集", "examples": [{"code": "-- 查询所有学生及其班级，包括没有班级的学生\nSELECT s.name, c.class_name\nFROM students s\nLEFT JOIN classes c ON s.class_id = c.id;", "explanation": "返回所有学生记录，即使学生没有对应的班级。没有班级的学生，class_name将显示为NULL。"}]}}, {"name": "Right Join", "trans": ["右连接（RIGHT JOIN）"], "usage": {"syntax": "SELECT 列名 FROM 表1 RIGHT JOIN 表2 ON 表1.列 = 表2.列;", "description": "右连接返回右表中的所有行，即使左表中没有匹配的行。如果左表没有匹配，结果中左表的列将为NULL。", "parameters": [{"name": "表1", "description": "左侧表名"}, {"name": "表2", "description": "右侧表名，结果将包含此表的所有行"}, {"name": "连接条件", "description": "指定如何匹配两个表的行"}], "returnValue": "右表所有行与左表匹配行的结果集", "examples": [{"code": "-- 查询所有班级及其学生，包括没有学生的班级\nSELECT s.name, c.class_name\nFROM students s\nRIGHT JOIN classes c ON s.class_id = c.id;", "explanation": "返回所有班级记录，即使班级没有对应的学生。没有学生的班级，name将显示为NULL。"}]}}, {"name": "Self Join", "trans": ["自连接（SELF JOIN）"], "usage": {"syntax": "SELECT 列名 FROM 表1 a JOIN 表1 b ON a.列 = b.列;", "description": "自连接是表与自身进行连接，通常需要使用不同的别名来区分同一个表的不同实例。", "parameters": [{"name": "表1 a", "description": "表的第一个别名实例"}, {"name": "表1 b", "description": "同一个表的第二个别名实例"}, {"name": "连接条件", "description": "指定如何匹配表的两个实例"}], "returnValue": "满足连接条件的结果集", "examples": [{"code": "-- 假设有员工表，包含id、name和manager_id字段\nSELECT e.name AS employee, m.name AS manager\nFROM employees e\nJOIN employees m ON e.manager_id = m.id;\n-- 查询每个员工及其经理", "explanation": "通过自连接，可以在同一个表中查询员工与其经理的关系。e表示员工，m表示经理。"}]}}, {"name": "Multi-table Queries Assignment", "trans": ["多表查询练习"], "usage": {"syntax": "# 多表查询练习", "description": "完成以下练习，巩固多表查询的使用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n假设有以下两个表：\n\n-- 学生表\nCREATE TABLE students (\n  id INT PRIMARY KEY,\n  name VARCHAR(20),\n  class_id INT\n);\n\n-- 班级表\nCREATE TABLE classes (\n  id INT PRIMARY KEY,\n  class_name VARCHAR(20)\n);\n\n-- 插入数据\nINSERT INTO classes VALUES (1, '一班'), (2, '二班'), (3, '三班');\nINSERT INTO students VALUES (1, '张三', 1), (2, '李四', 1), (3, '王五', 2), (4, '赵六', NULL);\n\n请完成以下查询：\n1. 使用内连接查询每个学生及其所在班级。\n2. 使用左连接查询所有学生及其班级，包括没有班级的学生。\n3. 使用右连接查询所有班级及其学生，包括没有学生的班级。", "explanation": "通过实际操作掌握内连接、左连接和右连接的区别和使用场景。"}]}}]}