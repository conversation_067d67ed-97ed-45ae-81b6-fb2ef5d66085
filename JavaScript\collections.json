{"name": "ES6 Collections", "trans": ["ES6集合类型"], "methods": [{"name": "Set & WeakSet", "trans": ["Set与WeakSet"], "usage": {"syntax": "const s = new Set([1,2,3]);\ns.add(4);\ns.delete(2);\ns.has(1);\ns.size;\nconst ws = new WeakSet();\nws.add({a:1});", "description": "Set存储唯一值，支持基本类型和对象。WeakSet只存对象且为弱引用。", "parameters": [{"name": "add", "description": "添加元素。"}, {"name": "delete", "description": "删除元素。"}, {"name": "has", "description": "判断元素是否存在。"}, {"name": "size", "description": "元素个数。"}], "returnValue": "返回集合本身或布尔值。", "examples": [{"code": "const set = new Set([1,2,2,3]);\nset.add(4);\nset.delete(1);\nconsole.log(set.has(2)); // true\nconsole.log(set.size); // 3\nconst ws = new WeakSet();\nws.add({x:1});", "explanation": "Set去重、添加、删除、判断和WeakSet用法。"}]}}, {"name": "Map & WeakMap", "trans": ["Map与WeakMap"], "usage": {"syntax": "const m = new Map([[1,'a'],[2,'b']]);\nm.set(3,'c');\nm.get(1);\nm.has(2);\nm.delete(1);\nm.size;\nconst wm = new WeakMap();\nwm.set({}, 123);", "description": "Map是键值对集合，键可为任意类型。WeakMap只接受对象为键且为弱引用。", "parameters": [{"name": "set", "description": "设置键值对。"}, {"name": "get", "description": "获取值。"}, {"name": "has", "description": "判断键是否存在。"}, {"name": "delete", "description": "删除键值对。"}, {"name": "size", "description": "键值对数量。"}], "returnValue": "返回集合本身、值或布尔值。", "examples": [{"code": "const map = new Map([[1,'a'],[2,'b']]);\nmap.set(3,'c');\nconsole.log(map.get(1)); // 'a'\nconsole.log(map.has(2)); // true\nmap.delete(1);\nconsole.log(map.size); // 2\nconst wm = new WeakMap();\nwm.set({}, 456);", "explanation": "Map的增删查和WeakMap用法。"}]}}, {"name": "Common Collection Operations", "trans": ["集合的常用操作"], "usage": {"syntax": "set.forEach(fn);\nmap.forEach((v, k) => {});\nset.clear();\nmap.clear();\nArray.from(set);\n[...map];", "description": "集合可遍历、清空、转换为数组等。forEach遍历元素，clear清空集合，Array.from和扩展运算符转换为数组。", "parameters": [{"name": "for<PERSON>ach", "description": "遍历集合。"}, {"name": "clear", "description": "清空集合。"}, {"name": "Array.from", "description": "集合转数组。"}, {"name": "...", "description": "扩展运算符转数组。"}], "returnValue": "返回undefined或新数组。", "examples": [{"code": "const set = new Set([1,2,3]);\nset.forEach(x=>console.log(x));\nset.clear();\nconst map = new Map([[1,'a']]);\nmap.forEach((v,k)=>console.log(k,v));\nmap.clear();\nconst arr = Array.from(new Set([1,2,3]));\nconst arr2 = [...new Map([[1,'a']])];", "explanation": "集合遍历、清空、转数组等常用操作。"}]}}, {"name": "作业：ES6集合类型实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 使用Set/Map等集合类型\n// 2. 增删查和遍历集合\n// 3. 集合与数组互转", "description": "通过实践Set、Map等集合类型的声明、操作和转换，掌握ES6集合基础。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. Set/Map声明和操作\n// 2. 遍历/清空/转数组", "explanation": "作业提示，需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nconst set = new Set([1,2,3]); set.add(4); set.delete(1);\nconst arr = Array.from(set);\nconst map = new Map([[1,'a']]); map.set(2,'b'); map.delete(1);\nmap.forEach((v,k)=>console.log(k,v));\nconsole.log(arr);", "explanation": "涵盖Set/Map声明、操作、遍历、转数组的正确实现。"}]}}]}