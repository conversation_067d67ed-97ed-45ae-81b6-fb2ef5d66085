{"name": "Events", "trans": ["事件调度器"], "methods": [{"name": "Create Event", "trans": ["创建事件"], "usage": {"syntax": "CREATE EVENT event_name\nON SCHEDULE EVERY 时间间隔\nDO\n  SQL语句;", "description": "用于创建定时自动执行的事件任务。事件调度器可定期执行SQL语句，实现自动化维护、数据归档等。", "parameters": [{"name": "event_name", "description": "事件名称"}, {"name": "时间间隔", "description": "如1 DAY、5 MINUTE等"}, {"name": "SQL语句", "description": "事件要执行的SQL内容"}], "returnValue": "无返回值", "examples": [{"code": "-- 每天凌晨清理日志表\nCREATE EVENT clean_logs\nON SCHEDULE EVERY 1 DAY\nSTARTS '2024-01-01 00:00:00'\nDO\n  DELETE FROM logs WHERE log_time < NOW() - INTERVAL 30 DAY;", "explanation": "本例创建了一个每天自动清理30天前日志的事件。"}]}}, {"name": "Scheduled Tasks", "trans": ["定时任务"], "usage": {"syntax": "-- 启用事件调度器\nSET GLOBAL event_scheduler = ON;\n\n-- 查看所有事件\nSHOW EVENTS;\n\n-- 删除事件\nDROP EVENT event_name;", "description": "事件调度器需开启后才能生效。可通过SHOW EVENTS查看所有事件，通过DROP EVENT删除事件。", "parameters": [{"name": "event_scheduler", "description": "事件调度器开关"}, {"name": "event_name", "description": "事件名称"}], "returnValue": "无返回值", "examples": [{"code": "-- 启用事件调度器\nSET GLOBAL event_scheduler = ON;\n\n-- 查看所有事件\nSHOW EVENTS;\n\n-- 删除clean_logs事件\nDROP EVENT clean_logs;", "explanation": "本例展示了如何启用事件调度器、查看和删除事件。"}]}}, {"name": "Events Assignment", "trans": ["事件调度器练习"], "usage": {"syntax": "# 事件调度器练习", "description": "完成以下练习，巩固MySQL事件调度器的基本用法和自动化运维能力。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 创建一个事件，每隔1小时自动统计student表的总人数并写入统计表。\n2. 启用事件调度器，验证事件是否自动执行。\n3. 删除该事件并验证。", "explanation": "通过这些练习，你将掌握MySQL事件调度器的创建、管理和自动化运维能力。"}]}}]}