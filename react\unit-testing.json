{"name": "Unit Testing", "trans": ["单元测试"], "methods": [{"name": "Test Environment Setup", "trans": ["测试环境设置"], "usage": {"syntax": "// 安装依赖\nnpm install --save-dev jest @testing-library/react @testing-library/jest-dom\n// 配置package.json或jest.config.js", "description": "设置单元测试环境通常包括安装Jest、React Testing Library等依赖，并进行基础配置，确保测试可运行。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// package.json配置\n{\n  \"scripts\": {\n    \"test\": \"jest\"\n  }\n}\n// 运行测试\nnpm test", "explanation": "通过配置和命令行运行Jest测试环境。"}]}}, {"name": "Test Library Selection", "trans": ["测试库选择"], "usage": {"syntax": "// 常用测试库\n// Jest: 断言、mock、快照\n// React Testing Library: 组件测试\n// @testing-library/user-event: 用户交互模拟", "description": "选择合适的测试库是高效测试的基础。Jest用于断言和mock，React Testing Library专注于组件行为测试。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 安装\nnpm install --save-dev jest @testing-library/react @testing-library/user-event", "explanation": "常用测试库的安装命令。"}]}}, {"name": "Component Unit Testing", "trans": ["组件单元测试"], "usage": {"syntax": "import { render, screen } from '@testing-library/react';\nrender(<MyComponent />);\nexpect(screen.getByText('内容')).toBeInTheDocument();", "description": "组件单元测试关注组件的渲染、交互和输出，常用React Testing Library进行DOM断言和事件模拟。", "parameters": [{"name": "component", "description": "待测试的React组件。"}], "returnValue": "无返回值，断言通过即测试通过。", "examples": [{"code": "import { render, screen } from '@testing-library/react';\nfunction MyComponent() { return <div>你好</div>; }\ntest('渲染内容', () => {\n  render(<MyComponent />);\n  expect(screen.getByText('你好')).toBeInTheDocument();\n});", "explanation": "测试组件是否正确渲染指定内容。"}]}}, {"name": "Hooks Testing", "trans": ["Hooks测试"], "usage": {"syntax": "import { renderHook, act } from '@testing-library/react';\nconst { result } = renderHook(() => useMyHook());", "description": "Hooks测试通过renderHook执行自定义Hook，结合act模拟状态变化，断言返回值和副作用。", "parameters": [{"name": "hook", "description": "待测试的自定义Hook函数。"}], "returnValue": "无返回值，断言通过即测试通过。", "examples": [{"code": "import { renderHook, act } from '@testing-library/react';\nfunction useCounter() {\n  const [count, setCount] = useState(0);\n  const inc = () => setCount(c => c + 1);\n  return { count, inc };\n}\ntest('计数器递增', () => {\n  const { result } = renderHook(() => useCounter());\n  act(() => { result.current.inc(); });\n  expect(result.current.count).toBe(1);\n});", "explanation": "测试自定义Hook的状态变化和返回值。"}]}}, {"name": "Function Testing", "trans": ["函数测试"], "usage": {"syntax": "test('描述', () => {\n  expect(fn(参数)).toBe(期望值);\n});", "description": "函数测试关注纯函数的输入输出，直接断言返回值是否符合预期。", "parameters": [{"name": "fn", "description": "待测试的函数。"}, {"name": "参数", "description": "传入函数的参数。"}], "returnValue": "无返回值，断言通过即测试通过。", "examples": [{"code": "function sum(a, b) { return a + b; }\ntest('加法函数', () => {\n  expect(sum(1, 2)).toBe(3);\n});", "explanation": "测试sum函数的返回值是否正确。"}]}}, {"name": "Async Code Testing", "trans": ["异步代码测试"], "usage": {"syntax": "test('异步', async () => {\n  await expect(fetchData()).resolves.toBe(期望值);\n});", "description": "异步代码测试需结合async/await和Jest的异步断言，确保异步操作完成后再断言结果。", "parameters": [{"name": "异步函数", "description": "待测试的异步函数。"}], "returnValue": "无返回值，断言通过即测试通过。", "examples": [{"code": "function fetchData() { return Promise.resolve(42); }\ntest('异步函数', async () => {\n  await expect(fetchData()).resolves.toBe(42);\n});", "explanation": "测试异步函数的返回值。"}]}}, {"name": "Mocking External Dependencies", "trans": ["模拟外部依赖"], "usage": {"syntax": "jest.mock('模块名');\n// 或使用jest.fn()模拟函数", "description": "通过jest.mock和jest.fn可模拟外部模块、API请求等依赖，隔离测试环境，保证测试纯粹。", "parameters": [{"name": "模块名", "description": "需要mock的模块路径。"}], "returnValue": "无返回值，mock后可断言调用和结果。", "examples": [{"code": "jest.mock('./api');\nimport { fetchUser } from './api';\nfetchUser.mockResolvedValue({ name: '<PERSON>' });\ntest('mock API', async () => {\n  const user = await fetchUser();\n  expect(user.name).toBe('Tom');\n});", "explanation": "通过jest.mock模拟API请求，断言返回值。"}]}}, {"name": "作业：单元测试实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 配置测试环境\n// 2. 编写组件、Hook、函数的单元测试\n// 3. 测试异步代码和mock外部依赖", "description": "通过实践测试环境搭建、组件/Hook/函数/异步代码测试和mock依赖，掌握React单元测试方法。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 配置Jest和Testing Library\n// 2. 编写组件、Hook、函数测试\n// 3. 测试异步和mock依赖", "explanation": "作业提示，学生需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nimport { render, screen } from '@testing-library/react';\nfunction Hello() { return <div>Hi</div>; }\ntest('渲染', () => {\n  render(<Hello />);\n  expect(screen.getByText('Hi')).toBeInTheDocument();\n});", "explanation": "组件单元测试的正确实现示例。"}]}}]}