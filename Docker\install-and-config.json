{"name": "Docker Installation and Configuration", "trans": ["安装与环境配置"], "methods": [{"name": "Windows/Mac/Linux Installation", "trans": ["Windows/Mac/Linux安装"], "usage": {"syntax": "根据不同操作系统，安装Docker的方法有所不同。", "description": "Docker提供了适用于Windows、Mac和各种Linux发行版的安装包。Windows和Mac用户可以安装Docker Desktop，它包含了Docker Engine、Docker CLI、Docker Compose等组件；Linux用户则需要根据发行版选择合适的安装方法。", "parameters": [{"name": "<PERSON><PERSON> Des<PERSON>", "description": "Windows和Mac上的完整Docker开发环境。"}, {"name": "Docker Engine", "description": "Linux上的Docker核心组件。"}], "returnValue": "无返回值", "examples": [{"code": "# Windows安装Docker Desktop\n# 1. 访问Docker官网下载Docker Desktop for Windows\n# 2. 双击安装程序运行\n# 3. 确保启用Hyper-V或WSL 2\n# 4. 完成安装后启动Docker Desktop\n\n# Mac安装Docker Desktop\n# 1. 访问Docker官网下载Docker Desktop for Mac\n# 2. 将下载的.dmg文件拖到Applications文件夹\n# 3. 从应用程序启动Docker\n\n# Ubuntu Linux安装Docker Engine\n# 安装依赖包\nsudo apt-get update\nsudo apt-get install apt-transport-https ca-certificates curl gnupg lsb-release\n\n# 添加Docker官方GPG密钥\ncurl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg\n\n# 设置稳定版仓库\necho \"deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable\" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null\n\n# 安装Docker Engine\nsudo apt-get update\nsudo apt-get install docker-ce docker-ce-cli containerd.io\n\n# CentOS Linux安装Docker Engine\n# 安装依赖包\nsudo yum install -y yum-utils\n\n# 设置仓库\nsudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo\n\n# 安装Docker Engine\nsudo yum install docker-ce docker-ce-cli containerd.io\n\n# 启动Docker\nsudo systemctl start docker\n\n# 验证安装\nsudo docker run hello-world\n", "explanation": "分别展示了在Windows、Mac和Linux系统上安装Docker的详细步骤，包括安装依赖、配置仓库、安装Docker组件和验证安装。"}]}}, {"name": "Docker Desktop Introduction", "trans": ["Docker Desktop简介"], "usage": {"syntax": "Docker Desktop是一个适用于Windows和Mac的应用程序，提供了完整的Docker开发环境。", "description": "Docker Desktop集成了Docker Engine、Docker CLI、Docker Compose、Kubernetes等组件，提供了图形化界面，简化了容器开发环境的管理。它支持容器构建、运行、共享，以及多容器应用的管理。", "parameters": [{"name": "图形界面", "description": "管理容器、镜像、卷和网络的界面。"}, {"name": "设置选项", "description": "资源分配、网络、代理等配置。"}], "returnValue": "无返回值", "examples": [{"code": "# Docker Desktop主要功能\n\n# 1. 容器和镜像管理\n# +------------------+\n# | Docker Desktop   |  ← 可视化界面查看所有容器和镜像\n# |                  |\n# | 容器 状态 端口映射  |  ← 容器运行状态、端口映射一目了然\n# | app1 运行中 80:80  |\n# | app2 已停止 -     |  ← 一键启动/停止/删除容器\n# |                  |\n# | 镜像 大小 创建时间  |  ← 镜像信息清晰展示\n# | nginx 120MB 2小时前|\n# +------------------+\n\n# 2. 资源配置\n# +------------------+\n# | 资源设置          |  ← 调整分配给Docker的资源\n# | CPU: 2核         |\n# | 内存: 2GB        |  ← 限制Docker使用的内存\n# | 交换: 1GB        |\n# | 磁盘: 50GB       |  ← 设置镜像存储位置和大小\n# +------------------+\n\n# 3. 首选项设置\n# +------------------+\n# | Docker Engine配置 |  ← 直接编辑daemon.json配置\n# | {                |\n# |  \"registry-mirrors\": [\"https://mirror.example.com\"],\n# |  \"insecure-registries\": [],\n# |  \"debug\": false   |\n# | }                |\n# +------------------+\n\n# 4. 扩展功能\n# - Kubernetes集成   ← 一键启用Kubernetes\n# - 开发工具集成     ← 与VS Code等工具集成\n# - 容器文件浏览     ← 直接查看容器内文件\n# - 日志查看         ← 实时查看容器日志\n", "explanation": "详细展示了Docker Desktop的主要功能，包括容器和镜像管理界面、资源配置选项、Docker Engine配置和扩展功能，使用户能够直观地了解和管理Docker环境。"}]}}, {"name": "Registry Mirror Configuration", "trans": ["配置国内镜像加速"], "usage": {"syntax": "通过配置registry-mirrors参数，可以使用国内镜像源加速Docker镜像下载。", "description": "由于网络原因，从Docker Hub下载镜像可能较慢，配置国内镜像源可以显著提升下载速度。常用的国内镜像源包括阿里云、腾讯云、网易云等提供的Docker镜像仓库加速服务。", "parameters": [{"name": "registry-mirrors", "description": "Docker配置文件中的镜像源列表。"}], "returnValue": "无返回值", "examples": [{"code": "# Windows/Mac (Docker Desktop)配置镜像加速\n# 1. 点击Docker Desktop右上角的设置图标\n# 2. 选择「Docker Engine」\n# 3. 在JSON配置中添加或修改registry-mirrors字段\n{\n  \"registry-mirrors\": [\n    \"https://registry.docker-cn.com\",\n    \"https://docker.mirrors.ustc.edu.cn\",\n    \"https://hub-mirror.c.163.com\"\n  ]\n}\n# 4. 点击「Apply & Restart」重启Docker服务\n\n# Linux配置镜像加速\n# 1. 创建或编辑daemon.json文件\nsudo mkdir -p /etc/docker\nsudo vim /etc/docker/daemon.json\n\n# 2. 添加以下内容\n{\n  \"registry-mirrors\": [\n    \"https://registry.docker-cn.com\",\n    \"https://docker.mirrors.ustc.edu.cn\",\n    \"https://hub-mirror.c.163.com\"\n  ]\n}\n\n# 3. 重启Docker服务\nsudo systemctl daemon-reload\nsudo systemctl restart docker\n\n# 4. 验证配置是否生效\ndocker info\n# 输出中应包含配置的镜像地址\n# Registry Mirrors:\n#  https://registry.docker-cn.com/\n#  https://docker.mirrors.ustc.edu.cn/\n#  https://hub-mirror.c.163.com/\n", "explanation": "详细介绍了在Windows/Mac的Docker Desktop和Linux系统上配置Docker镜像加速的方法，包括修改配置文件、重启服务和验证配置是否生效的步骤。"}]}}, {"name": "Basic Command Line Tools", "trans": ["基本命令行工具"], "usage": {"syntax": "docker [OPTIONS] COMMAND", "description": "Docker提供了丰富的命令行工具，用于管理容器、镜像、网络和存储等资源。掌握基本的Docker命令是使用Docker的基础，也是后续进行Docker开发和运维的前提。", "parameters": [{"name": "docker", "description": "Docker CLI主命令。"}, {"name": "COMMAND", "description": "要执行的Docker子命令。"}, {"name": "OPTIONS", "description": "命令选项，影响命令行为。"}], "returnValue": "无返回值", "examples": [{"code": "# 1. 容器管理命令\ndocker run -d -p 80:80 --name webserver nginx   # 创建并启动容器，-d后台运行，-p端口映射，--name指定名称\ndocker ps                                     # 列出运行中的容器\ndocker ps -a                                  # 列出所有容器，包括已停止的\ndocker start webserver                        # 启动已停止的容器\ndocker stop webserver                         # 停止运行中的容器\ndocker restart webserver                      # 重启容器\ndocker rm webserver                          # 删除容器\ndocker logs webserver                        # 查看容器日志\ndocker exec -it webserver bash               # 进入容器执行命令，-i交互式，-t分配伪终端\n\n# 2. 镜像管理命令\ndocker images                                # 列出本地所有镜像\ndocker pull nginx:latest                     # 拉取镜像，指定标签\ndocker build -t myapp:1.0 .                  # 从当前目录的Dockerfile构建镜像\ndocker rmi nginx:latest                      # 删除镜像\ndocker tag myapp:1.0 username/myapp:latest   # 给镜像打标签\ndocker push username/myapp:latest            # 推送镜像到仓库\ndocker save -o myapp.tar myapp:1.0           # 将镜像保存为tar文件\ndocker load -i myapp.tar                     # 从tar文件加载镜像\n\n# 3. 系统和信息命令\ndocker info                                  # 显示Docker系统信息\ndocker version                               # 显示Docker版本信息\ndocker stats                                 # 显示容器资源使用统计\ndocker system df                             # 显示Docker磁盘使用情况\ndocker system prune                          # 清理未使用的数据\n\n# 4. 网络和卷管理\ndocker network ls                            # 列出所有网络\ndocker network create mynetwork              # 创建自定义网络\ndocker volume ls                             # 列出所有卷\ndocker volume create myvolume                # 创建数据卷\n\n# 5. Docker Compose命令\ndocker-compose up -d                         # 启动定义在docker-compose.yml中的所有服务\ndocker-compose down                          # 停止并删除所有服务\ndocker-compose logs                          # 查看所有服务的日志\n", "explanation": "全面展示了Docker的基本命令行工具，包括容器管理、镜像管理、系统信息查看、网络和卷管理以及Docker Compose命令，涵盖了日常Docker使用的大部分场景。"}]}}, {"name": "Upgrade and Uninstall", "trans": ["升级与卸载"], "usage": {"syntax": "根据不同操作系统，升级和卸载Docker的方法有所不同。", "description": "随着Docker版本的更新，可能需要升级Docker以获取新特性或修复安全漏洞。在某些情况下，也可能需要完全卸载Docker。不同操作系统下的升级和卸载方法略有差异。", "parameters": [{"name": "升级", "description": "更新Docker到最新版本。"}, {"name": "卸载", "description": "完全移除Docker及其组件。"}], "returnValue": "无返回值", "examples": [{"code": "# Windows上升级Docker Desktop\n# 1. 从Docker Hub下载最新版安装程序\n# 2. 运行安装程序，它会自动升级现有安装\n\n# Windows上卸载Docker Desktop\n# 1. 在Windows控制面板中找到「程序和功能」\n# 2. 选择Docker Desktop，点击「卸载」\n# 3. 按照向导完成卸载\n# 4. 可选：删除用户目录下的Docker相关数据\n# 删除 %USERPROFILE%\\.docker 目录\n\n# Mac上升级Docker Desktop\n# 1. 从Docker Hub下载最新版DMG文件\n# 2. 安装新版本，它会替换现有安装\n\n# Mac上卸载Docker Desktop\n# 1. 从应用程序文件夹拖动Docker.app到废纸篓\n# 2. 可选：删除Docker相关数据和配置\nsudo rm -rf /Applications/Docker.app\nrm -rf ~/Library/Group\\ Containers/group.com.docker\nrm -rf ~/Library/Containers/com.docker.docker\nrm -rf ~/.docker\n\n# Ubuntu Linux上升级Docker Engine\nsudo apt-get update\nsudo apt-get install docker-ce docker-ce-cli containerd.io\n\n# Ubuntu Linux上卸载Docker Engine\n# 1. 卸载Docker包\nsudo apt-get purge docker-ce docker-ce-cli containerd.io\n\n# 2. 删除所有镜像、容器和卷\nsudo rm -rf /var/lib/docker\nsudo rm -rf /var/lib/containerd\n\n# CentOS Linux上升级Docker Engine\nsudo yum update docker-ce docker-ce-cli containerd.io\n\n# CentOS Linux上卸载Docker Engine\n# 1. 卸载Docker包\nsudo yum remove docker-ce docker-ce-cli containerd.io\n\n# 2. 删除所有镜像、容器和卷\nsudo rm -rf /var/lib/docker\nsudo rm -rf /var/lib/containerd\n", "explanation": "详细介绍了在Windows、Mac和Linux系统上升级和卸载Docker的方法，包括如何获取新版本、如何删除现有安装以及如何清理Docker数据，确保用户能够正确管理Docker的生命周期。"}]}}, {"name": "安装与环境配置作业", "trans": ["作业：安装与环境配置"], "usage": {"syntax": "请完成以下任务：\n1. 根据您的操作系统安装Docker。\n2. 配置国内镜像源加速。\n3. 运行hello-world容器验证安装。\n4. 尝试使用5个基本Docker命令。\n5. 了解如何查看Docker的系统资源使用情况。", "description": "通过实际操作完成Docker的安装、配置和基本使用，为后续学习打下基础。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 作业示例\n\n# 1. 安装Docker（以Ubuntu为例）\nsudo apt-get update\nsudo apt-get install apt-transport-https ca-certificates curl gnupg lsb-release\ncurl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg\necho \"deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable\" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null\nsudo apt-get update\nsudo apt-get install docker-ce docker-ce-cli containerd.io\n\n# 2. 配置国内镜像源\nsudo mkdir -p /etc/docker\nsudo tee /etc/docker/daemon.json <<-'EOF'\n{\n  \"registry-mirrors\": [\"https://hub-mirror.c.163.com\", \"https://mirror.baidubce.com\"]\n}\nEOF\nsudo systemctl daemon-reload\nsudo systemctl restart docker\n\n# 3. 验证安装\nsudo docker run hello-world\n\n# 4. 尝试基本命令\n# 列出镜像\ndocker images\n# 拉取nginx镜像\ndocker pull nginx\n# 运行nginx容器\ndocker run -d -p 8080:80 --name mynginx nginx\n# 查看运行中的容器\ndocker ps\n# 查看容器日志\ndocker logs mynginx\n\n# 5. 查看资源使用情况\n# 查看所有容器的资源使用\ndocker stats\n# 查看Docker系统信息\ndocker info\n# 查看Docker磁盘使用\ndocker system df\n", "explanation": "通过实际操作完成Docker的安装、配置镜像加速、验证安装、使用基本命令和查看资源使用情况，全面掌握Docker环境的初始设置。"}]}}, {"name": "安装与环境配置正确实现示例", "trans": ["正确实现卡片"], "usage": {"syntax": "Docker安装与配置的标准流程。", "description": "展示Docker在各系统上的正确安装方法、镜像加速配置、常用命令和资源管理，确保用户能够建立一个正常运行的Docker环境。", "parameters": [{"name": "安装验证", "description": "确认Docker安装成功。"}, {"name": "镜像加速", "description": "正确配置国内镜像源。"}, {"name": "基本操作", "description": "掌握核心命令。"}], "returnValue": "无返回值", "examples": [{"code": "# 完整安装流程示例（Ubuntu系统）\n\n# 1. 准备工作\n# 卸载旧版本（如有）\nsudo apt-get remove docker docker-engine docker.io containerd runc\n\n# 2. 设置仓库\nsudo apt-get update\nsudo apt-get install -y apt-transport-https ca-certificates curl gnupg lsb-release\n\n# 添加Docker官方GPG密钥\ncurl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg\n\n# 设置稳定版仓库\necho \"deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable\" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null\n\n# 3. 安装Docker Engine\nsudo apt-get update\nsudo apt-get install -y docker-ce docker-ce-cli containerd.io\n\n# 4. 将当前用户添加到docker组（免sudo运行docker）\nsudo usermod -aG docker $USER\nnewgrp docker  # 应用组更改\n\n# 5. 配置镜像加速\nsudo mkdir -p /etc/docker\nsudo tee /etc/docker/daemon.json <<-'EOF'\n{\n  \"registry-mirrors\": [\n    \"https://hub-mirror.c.163.com\",\n    \"https://mirror.baidubce.com\",\n    \"https://docker.mirrors.ustc.edu.cn\"\n  ],\n  \"log-driver\": \"json-file\",\n  \"log-opts\": {\n    \"max-size\": \"100m\",\n    \"max-file\": \"3\"\n  }\n}\nEOF\n\n# 重启Docker服务\nsudo systemctl daemon-reload\nsudo systemctl restart docker\n\n# 6. 验证安装\ndocker version\ndocker info  # 检查镜像加速配置是否生效\ndocker run hello-world\n\n# 7. 基本命令使用\n# 拉取常用镜像\ndocker pull nginx:alpine\ndocker pull redis:alpine\n\n# 运行容器\ndocker run -d --name web -p 80:80 nginx:alpine\ndocker ps\ndocker logs web\n\n# 容器管理\ndocker exec -it web sh\n# 在容器内执行: echo \"Hello Docker\" > /usr/share/nginx/html/index.html\n# 退出容器: exit\n\n# 访问服务\n# 在浏览器访问 http://localhost 应该显示\"Hello Docker\"\n\n# 8. 资源监控\ndocker stats web\n\n# 9. 清理资源\ndocker stop web\ndocker rm web\ndocker system prune -a  # 清理所有未使用的容器、网络、镜像和构建缓存\n", "explanation": "提供了一个完整的Docker安装和配置流程，包括安装Docker、配置用户权限、设置镜像加速、验证安装、基本容器操作和资源管理，是Docker环境配置的标准实践。"}]}}]}