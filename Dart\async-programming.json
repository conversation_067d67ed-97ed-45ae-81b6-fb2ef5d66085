{"name": "Asynchronous Programming", "trans": ["异步编程"], "methods": [{"name": "Future Basics", "trans": ["Future基本用法"], "usage": {"syntax": "// 创建Future\nFuture<返回类型> 变量名 = Future<返回类型>(() {\n  // 异步执行的代码\n  return 结果;\n});\n\n// 使用then处理Future结果\nFuture<返回类型> 变量名 = 异步操作.then((结果) {\n  // 处理结果\n}).catchError((错误) {\n  // 处理错误\n});\n\n// 使用Future.delayed创建延迟执行的Future\nFuture<返回类型> 变量名 = Future.delayed(Duration(seconds: 秒数), () {\n  // 延迟执行的代码\n  return 结果;\n});", "description": "Future是Dart中表示异步操作结果的对象，用于处理可能需要一段时间才能完成的操作，如网络请求、文件读写、数据库查询等。Future可以处于两种状态：未完成(incomplete)或已完成(completed)。当异步操作完成时，Future会持有一个值或一个错误。\n\nDart提供了多种创建Future的方法：使用Future构造函数创建需要立即执行的异步操作；使用Future.delayed创建延迟执行的异步操作；使用Future.value创建一个已完成的Future，包含一个值；使用Future.error创建一个已完成的Future，包含一个错误。\n\nFuture提供了多种方法来处理异步操作的结果：使用then()方法注册一个回调函数，当Future完成时调用；使用catchError()方法注册一个错误处理函数，当Future发生错误时调用；使用whenComplete()方法注册一个无论Future成功还是失败都会调用的回调函数。\n\nFuture还提供了其他实用方法：使用timeout()设置超时时间；使用Future.wait()等待多个Future全部完成；使用Future.any()等待多个Future中的任意一个完成。\n\n在Dart中，Future是单次完成的，一旦完成就不能再改变其状态或结果。如果需要多次产生结果，应该使用Stream。", "parameters": [], "returnValue": "一个Future对象，表示异步操作的最终结果", "examples": [{"code": "import 'dart:async';\n\nvoid main() {\n  print('开始执行');\n  \n  // 创建一个简单的Future\n  Future<String> future = Future(() {\n    // 模拟耗时操作\n    print('  执行异步操作中...');\n    return '操作完成';\n  });\n  \n  // 使用then处理Future结果\n  future.then((result) {\n    print('  异步结果: $result');\n  }).catchError((error) {\n    print('  发生错误: $error');\n  }).whenComplete(() {\n    print('  Future执行结束');\n  });\n  \n  // 创建一个延迟执行的Future\n  Future.delayed(Duration(seconds: 2), () {\n    print('  延迟2秒后执行');\n    return '延迟操作完成';\n  }).then((result) {\n    print('  延迟操作结果: $result');\n  });\n  \n  // 创建一个已完成的Future\n  Future<int>.value(42).then((value) {\n    print('  已完成Future的值: $value');\n  });\n  \n  // 创建一个包含错误的Future\n  Future<int>.error('发生了一个错误').catchError((error) {\n    print('  错误Future: $error');\n  });\n  \n  // 链式调用\n  Future<String>.value('初始值')\n      .then((value) {\n        print('  处理1: $value');\n        return '$value -> 步骤1';\n      })\n      .then((value) {\n        print('  处理2: $value');\n        return '$value -> 步骤2';\n      })\n      .then((value) {\n        print('  处理3: $value');\n      });\n  \n  // 处理多个Future\n  var future1 = Future.delayed(Duration(seconds: 1), () => '结果1');\n  var future2 = Future.delayed(Duration(milliseconds: 800), () => '结果2');\n  var future3 = Future.delayed(Duration(milliseconds: 1200), () => '结果3');\n  \n  // 等待所有Future完成\n  Future.wait([future1, future2, future3]).then((results) {\n    print('  所有Future完成: $results');\n  });\n  \n  // 等待任意一个Future完成\n  Future.any([future1, future2, future3]).then((result) {\n    print('  最先完成的Future: $result');\n  });\n  \n  // 设置超时\n  future1.timeout(Duration(milliseconds: 500), onTimeout: () {\n    return '超时后的默认值';\n  }).then((result) {\n    print('  带超时的Future结果: $result');\n  }).catchError((error) {\n    print('  超时错误: $error');\n  });\n  \n  print('同步代码执行结束');\n  \n  // 注意：即使我们已经到达了main函数的末尾，异步代码仍然会继续执行\n  // 这是因为Dart的事件循环会持续处理未完成的Future\n}", "explanation": "这个示例展示了Dart中Future的基本用法。首先演示了如何创建基本的Future和使用then、catchError、whenComplete处理其结果。然后展示了创建延迟执行的Future、已完成的Future和包含错误的Future。接着演示了Future的链式调用，以及如何使用Future.wait等待多个Future全部完成，使用Future.any等待多个Future中的任意一个完成。最后展示了如何为Future设置超时。示例中特别注意到了异步代码和同步代码执行顺序的区别，同步代码总是先执行完，然后才执行异步代码。"}]}}, {"name": "Async/Await Syntax", "trans": ["async/await语法"], "usage": {"syntax": "// 声明异步函数\nFuture<返回类型> 函数名() async {\n  // 异步函数体\n  var 结果 = await 异步操作;\n  return 返回值;\n}\n\n// 调用异步函数\nvoid main() async {\n  var 结果 = await 异步函数();\n  print(结果);\n}\n\n// 在非异步函数中处理异步函数的结果\nvoid main() {\n  异步函数().then((结果) {\n    print(结果);\n  });\n}", "description": "async/await是Dart中处理异步操作的语法糖，它使异步代码看起来更像同步代码，提高了代码的可读性和可维护性。async/await是基于Future的，本质上是对Future的链式调用进行了封装。\n\n异步函数用async关键字标记，这样的函数总是返回一个Future。在异步函数内部，可以使用await关键字等待一个Future完成，并获取其结果。await只能在async函数内部使用。\n\n当函数中使用了await，函数的执行会在await处暂停，等待Future完成后再继续执行。这使得代码看起来像是同步执行的，但实际上并不会阻塞线程。await表达式的结果是Future完成后的值。\n\n使用async/await时，异常处理变得更加直观，可以使用普通的try/catch语句捕获异步操作中的异常。这比使用Future的catchError更加自然和易读。\n\nasync/await特别适合处理有依赖关系的异步操作序列，使得代码结构更加清晰。如果需要并行执行多个独立的异步操作，仍然可以使用Future.wait等方法。\n\n需要注意的是，虽然使用async/await的代码看起来是同步执行的，但它仍然是异步的。异步函数内部的代码不会立即执行，而是被封装在一个Future中。", "parameters": [], "returnValue": "异步函数总是返回一个Future对象", "examples": [{"code": "import 'dart:async';\n\nvoid main() async {\n  print('开始执行');\n  \n  // 调用简单的异步函数\n  var result = await fetchData();\n  print('获取到的数据: $result');\n  \n  // 在异步函数中使用try/catch处理异常\n  try {\n    var result = await fetchDataWithError();\n    print('这行代码不会执行，因为上面会抛出异常');\n  } catch (e) {\n    print('捕获到异常: $e');\n  } finally {\n    print('无论成功还是失败，finally都会执行');\n  }\n  \n  // 串行执行多个异步操作（有依赖关系）\n  print('\\n串行执行异步操作:');\n  await serialAsyncOperations();\n  \n  // 并行执行多个异步操作（无依赖关系）\n  print('\\n并行执行异步操作:');\n  await parallelAsyncOperations();\n  \n  // 在循环中使用await\n  print('\\n在循环中使用await:');\n  await usingAwaitInLoop();\n  \n  // 展示async/await与Future链式调用的对比\n  print('\\n对比async/await与Future链式调用:');\n  await compareAsyncAwaitWithFutureChain();\n  \n  print('所有操作完成');\n}\n\n// 简单的异步函数\nFuture<String> fetchData() async {\n  // 模拟网络请求延迟\n  await Future.delayed(Duration(seconds: 1));\n  return '这是获取到的数据';\n}\n\n// 会抛出异常的异步函数\nFuture<String> fetchDataWithError() async {\n  await Future.delayed(Duration(milliseconds: 500));\n  throw Exception('网络请求失败');\n}\n\n// 串行执行多个异步操作的示例\nFuture<void> serialAsyncOperations() async {\n  print('  开始第一个操作');\n  var result1 = await Future.delayed(Duration(seconds: 1), () => '操作1结果');\n  print('  第一个操作完成: $result1');\n  \n  print('  开始第二个操作，使用第一个操作的结果');\n  var result2 = await Future.delayed(Duration(seconds: 1), () => '$result1 -> 操作2结果');\n  print('  第二个操作完成: $result2');\n  \n  print('  开始第三个操作，使用第二个操作的结果');\n  var result3 = await Future.delayed(Duration(seconds: 1), () => '$result2 -> 操作3结果');\n  print('  第三个操作完成: $result3');\n  \n  print('  所有串行操作完成，最终结果: $result3');\n}\n\n// 并行执行多个异步操作的示例\nFuture<void> parallelAsyncOperations() async {\n  print('  开始并行操作');\n  \n  // 创建多个异步操作，但不等待它们\n  final future1 = Future.delayed(Duration(seconds: 2), () => '操作A结果');\n  final future2 = Future.delayed(Duration(seconds: 1), () => '操作B结果');\n  final future3 = Future.delayed(Duration(milliseconds: 1500), () => '操作C结果');\n  \n  // 并行等待所有操作完成\n  final results = await Future.wait([future1, future2, future3]);\n  \n  print('  所有并行操作完成: $results');\n}\n\n// 在循环中使用await的示例\nFuture<void> usingAwaitInLoop() async {\n  // 创建要处理的项目列表\n  final items = ['项目1', '项目2', '项目3'];\n  \n  // 串行处理每个项目（一个接一个）\n  print('  串行处理项目:');\n  for (var item in items) {\n    await processItem(item);\n  }\n  \n  // 并行处理所有项目（同时处理）\n  print('\\n  并行处理项目:');\n  await Future.wait(\n    items.map((item) => processItem(item, prefix: '并行')),\n  );\n}\n\n// 处理单个项目的异步函数\nFuture<void> processItem(String item, {String prefix = '串行'}) async {\n  await Future.delayed(Duration(milliseconds: 800));\n  print('  $prefix处理: $item 完成');\n}\n\n// 对比async/await与Future链式调用的示例\nFuture<void> compareAsyncAwaitWithFutureChain() async {\n  print('  使用async/await:');\n  try {\n    // 使用async/await方式\n    var data = await fetchData();\n    var processed = await processData(data);\n    var result = await finalizeData(processed);\n    print('  最终结果: $result');\n  } catch (e) {\n    print('  发生错误: $e');\n  }\n  \n  print('\\n  使用Future链式调用:');\n  // 使用Future链式调用方式\n  fetchData()\n      .then((data) => processData(data))\n      .then((processed) => finalizeData(processed))\n      .then((result) => print('  最终结果: $result'))\n      .catchError((e) => print('  发生错误: $e'));\n  \n  // 等待上面的链式调用完成，仅为了示例展示\n  await Future.delayed(Duration(seconds: 3));\n}\n\n// 用于对比示例的辅助函数\nFuture<String> processData(String data) async {\n  await Future.delayed(Duration(milliseconds: 500));\n  return '$data -> 已处理';\n}\n\nFuture<String> finalizeData(String processed) async {\n  await Future.delayed(Duration(milliseconds: 500));\n  return '$processed -> 已完成';\n}", "explanation": "这个示例全面展示了Dart中async/await的用法。首先演示了如何定义和调用异步函数，以及如何在异步函数中使用try/catch处理异常。然后展示了串行执行多个有依赖关系的异步操作，以及并行执行多个无依赖关系的异步操作。接着演示了在循环中使用await的两种模式：串行处理（一个接一个）和并行处理（同时处理所有项目）。最后通过一个对比示例，展示了使用async/await和使用Future链式调用两种方式处理异步操作的区别，突出了async/await代码的可读性优势。"}]}}, {"name": "Asynchronous Exception Handling", "trans": ["异步异常处理"], "usage": {"syntax": "// 使用Future的catchError处理异常\nFuture<返回类型> 变量名 = 异步操作\n  .then((result) {\n    // 处理结果\n  })\n  .catchError((error) {\n    // 处理错误\n  });\n\n// 使用async/await和try-catch处理异常\nFuture<返回类型> 函数名() async {\n  try {\n    var 结果 = await 可能抛出异常的异步操作;\n    return 结果;\n  } catch (e) {\n    // 处理异常\n    throw 新异常; // 可以重新抛出异常\n  } finally {\n    // 清理代码，无论是否发生异常都会执行\n  }\n}\n\n// 使用Future.catchError处理特定类型的异常\nFuture<返回类型> 变量名 = 异步操作\n  .catchError((error) {\n    // 处理特定类型的错误\n  }, test: (error) => error is 特定异常类型);", "description": "在Dart中，异步代码的异常处理与同步代码有所不同。异步操作中的异常不会立即被抛出，而是被封装在Future或Stream中。有两种主要方式处理异步异常：使用Future/Stream的API和使用async/await与try-catch。\n\n使用Future的API处理异常时，主要使用catchError方法捕获异常。catchError方法可以指定一个test函数，用于筛选特定类型的异常。whenComplete方法类似于finally子句，无论操作成功还是失败都会执行。\n\n使用async/await时，可以使用传统的try-catch-finally语句处理异常。这种方式更加直观，与同步代码的异常处理一致。在async函数中抛出的异常会自动被包装在返回的Future中。\n\n在处理链式异步操作时，异常会沿着调用链向下传播，直到被捕获。如果异常没有被捕获，它会导致整个异步链终止，并且可能导致未处理的异常警告。\n\n在处理多个并行异步操作时（如使用Future.wait），如果任何一个操作抛出异常，Future.wait返回的Future会立即完成并包含该异常。可以使用catchError来处理这种情况。\n\n对于需要恢复并继续执行的场景，可以在捕获异常后返回一个默认值或替代结果。对于需要记录但不中断执行的异常，可以使用onError回调或在捕获异常后重新抛出。", "parameters": [], "returnValue": "根据异常处理方式不同而不同", "examples": [{"code": "import 'dart:async';\n\nvoid main() async {\n  print('开始异步异常处理示例');\n  \n  // 1. 使用Future的catchError处理异常\n  print('\\n使用Future的catchError:');\n  fetchDataWithError()\n      .then((data) => print('  获取到数据: $data'))\n      .catchError((e) => print('  捕获到错误: $e'))\n      .whenComplete(() => print('  操作完成（成功或失败）'));\n  \n  // 等待上面的异步操作完成\n  await Future.delayed(Duration(milliseconds: 800));\n  \n  // 2. 使用async/await和try-catch处理异常\n  print('\\n使用async/await和try-catch:');\n  try {\n    var data = await fetchDataWithError();\n    print('  获取到数据: $data'); // 不会执行到这里\n  } catch (e) {\n    print('  捕获到错误: $e');\n  } finally {\n    print('  操作完成（成功或失败）');\n  }\n  \n  // 3. 处理特定类型的异常\n  print('\\n处理特定类型的异常:');\n  try {\n    await fetchWithSpecificError();\n  } on FormatException catch (e) {\n    print('  捕获到格式异常: $e');\n  } on TimeoutException catch (e) {\n    print('  捕获到超时异常: $e');\n  } catch (e) {\n    print('  捕获到其他类型异常: $e');\n  }\n  \n  // 4. 使用Future的catchError处理特定类型的异常\n  print('\\n使用Future的catchError处理特定类型的异常:');\n  fetchWithSpecificError()\n      .catchError(\n        (e) => print('  捕获到格式异常: $e'),\n        test: (e) => e is FormatException,\n      )\n      .catchError(\n        (e) => print('  捕获到超时异常: $e'),\n        test: (e) => e is TimeoutException,\n      )\n      .catchError((e) => print('  捕获到其他类型异常: $e'));\n  \n  // 等待上面的异步操作完成\n  await Future.delayed(Duration(milliseconds: 800));\n  \n  // 5. 链式异步操作中的异常处理\n  print('\\n链式异步操作中的异常处理:');\n  fetchData()\n      .then((step1Result) {\n        print('  步骤1成功: $step1Result');\n        return processDataWithError(step1Result); // 这一步会抛出异常\n      })\n      .then((step2Result) {\n        print('  步骤2成功: $step2Result'); // 不会执行到这里\n        return finalizeData(step2Result);\n      })\n      .then((finalResult) {\n        print('  最终结果: $finalResult'); // 不会执行到这里\n      })\n      .catchError((e) {\n        print('  链中的某一步出错: $e');\n        return '恢复后的默认值'; // 提供一个默认值以便继续执行\n      })\n      .then((result) {\n        print('  异常处理后继续执行，结果: $result');\n      });\n  \n  // 等待上面的异步操作完成\n  await Future.delayed(Duration(seconds: 2));\n  \n  // 6. 使用async/await处理链式异步操作中的异常\n  print('\\n使用async/await处理链式异步操作中的异常:');\n  try {\n    var step1Result = await fetchData();\n    print('  步骤1成功: $step1Result');\n    \n    var step2Result = await processDataWithError(step1Result);\n    print('  步骤2成功: $step2Result'); // 不会执行到这里\n    \n    var finalResult = await finalizeData(step2Result);\n    print('  最终结果: $finalResult'); // 不会执行到这里\n  } catch (e) {\n    print('  链中的某一步出错: $e');\n    var defaultResult = '恢复后的默认值';\n    print('  异常处理后继续执行，结果: $defaultResult');\n  }\n  \n  // 7. 处理并行异步操作中的异常\n  print('\\n处理并行异步操作中的异常:');\n  try {\n    var results = await Future.wait([\n      fetchData(),\n      Future.delayed(Duration(milliseconds: 300), () => 'Operation 2 result'),\n      Future.delayed(Duration(milliseconds: 200), () => throw Exception('Operation 3 failed')),\n    ]);\n    print('  所有操作成功: $results'); // 不会执行到这里\n  } catch (e) {\n    print('  某个并行操作失败: $e');\n  }\n  \n  // 8. 使用Future.wait的allSettled模式\n  print('\\n实现类似Future.allSettled的效果:');\n  var futures = [\n    fetchData().then((value) => {'status': 'fulfilled', 'value': value}).catchError((e) => {'status': 'rejected', 'reason': e.toString()}),\n    Future.delayed(Duration(milliseconds: 300), () => 'Operation 2 result').then((value) => {'status': 'fulfilled', 'value': value}).catchError((e) => {'status': 'rejected', 'reason': e.toString()}),\n    Future.delayed(Duration(milliseconds: 200), () => throw Exception('Operation 3 failed')).then((value) => {'status': 'fulfilled', 'value': value}).catchError((e) => {'status': 'rejected', 'reason': e.toString()}),\n  ];\n  \n  var allResults = await Future.wait(futures);\n  print('  所有操作的结果（成功或失败）:');\n  for (var i = 0; i < allResults.length; i++) {\n    print('  操作${i + 1}: ${allResults[i]}');\n  }\n  \n  // 9. Zone捕获未处理的异步错误\n  print('\\n使用Zone捕获未处理的异步错误:');\n  await runZonedGuarded(() async {\n    print('  在自定义Zone中执行代码');\n    // 制造一个未捕获的异步错误\n    Future.delayed(Duration(milliseconds: 100), () {\n      throw Exception('这是一个未捕获的异步错误');\n    });\n    \n    await Future.delayed(Duration(milliseconds: 200));\n    print('  Zone中的其他代码继续执行');\n  }, (error, stackTrace) {\n    print('  Zone捕获到未处理的异步错误: $error');\n    print('  Stack trace: $stackTrace');\n  });\n  \n  print('\\n所有异步异常处理示例完成');\n}\n\n// 一个会返回正常结果的异步函数\nFuture<String> fetchData() async {\n  await Future.delayed(Duration(milliseconds: 500));\n  return '成功获取的数据';\n}\n\n// 一个会抛出一般异常的异步函数\nFuture<String> fetchDataWithError() async {\n  await Future.delayed(Duration(milliseconds: 500));\n  throw Exception('网络请求失败');\n}\n\n// 一个会抛出特定类型异常的异步函数\nFuture<String> fetchWithSpecificError() async {\n  await Future.delayed(Duration(milliseconds: 500));\n  // 随机抛出不同类型的异常，为了示例目的\n  var random = DateTime.now().millisecondsSinceEpoch % 3;\n  if (random == 0) {\n    throw FormatException('数据格式错误');\n  } else if (random == 1) {\n    throw TimeoutException('请求超时', Duration(seconds: 10));\n  } else {\n    throw Exception('未知错误');\n  }\n}\n\n// 用于示例的数据处理函数（会抛出异常）\nFuture<String> processDataWithError(String data) async {\n  await Future.delayed(Duration(milliseconds: 500));\n  throw Exception('数据处理失败');\n}\n\n// 用于示例的数据最终处理函数\nFuture<String> finalizeData(String processed) async {\n  await Future.delayed(Duration(milliseconds: 500));\n  return '$processed -> 已完成';\n}", "explanation": "这个示例全面展示了Dart中异步异常处理的各种方法。首先展示了使用Future的catchError和whenComplete方法处理异常，然后演示了使用async/await与try-catch处理异常的方式。接着展示了如何处理特定类型的异常，包括使用on关键字和使用Future的catchError方法的test参数。然后演示了在链式异步操作中如何处理异常，以及如何在异常发生后恢复并继续执行。示例还展示了如何处理并行异步操作中的异常，包括实现类似JavaScript中Promise.allSettled的效果，即使某些操作失败也能获取所有操作的结果。最后，展示了如何使用Zone捕获未处理的异步错误，这在处理那些不在当前调用栈中抛出的异常时非常有用。"}]}}]}