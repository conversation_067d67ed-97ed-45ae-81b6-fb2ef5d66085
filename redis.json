{"topics": [{"id": "intro-and-scenarios", "name": "介绍与应用场景", "file": "Redis/intro-and-scenarios.json", "description": "Redis的基本介绍、核心特点、典型应用场景及与Memcached的对比，适合初学者快速了解Redis。"}, {"id": "install-and-config", "name": "安装与环境配置", "file": "Redis/install-and-config.json", "description": "Redis在不同操作系统下的安装方法、配置文件详解、服务启动与关闭、客户端工具选择等基础运维知识。"}, {"id": "architecture-and-principles", "name": "基本架构与原理", "file": "Redis/architecture-and-principles.json", "description": "Redis单线程模型、内存管理、网络通信、数据持久化等核心原理，帮助理解其高性能和可靠性基础。"}, {"id": "string", "name": "字符串类型", "file": "Redis/string.json", "description": "Redis字符串类型的基本命令、常用应用、计数与限流等核心用法，适合掌握最常用的数据结构。"}, {"id": "list", "name": "列表类型", "file": "Redis/list.json", "description": "Redis列表类型的基本命令、队列与消息队列实现等核心用法，适合实现任务队列和消息系统。"}, {"id": "hash", "name": "哈希类型", "file": "Redis/hash.json", "description": "Redis哈希类型的基本命令、对象存储与缓存等核心用法，适合存储结构化数据。"}, {"id": "set", "name": "集合类型", "file": "Redis/set.json", "description": "Redis集合类型的基本命令、去重与标签系统等核心用法，适合实现标签、兴趣、好友等无序集合。"}, {"id": "sorted-set", "name": "有序集合类型", "file": "Redis/sorted-set.json", "description": "Redis有序集合类型的基本命令、排行榜与优先队列等核心用法，适合实现积分榜、排名等场景。"}, {"id": "hyperloglog-bitmap-geo", "name": "基数统计、位图、地理位置类型", "file": "Redis/hyperloglog-bitmap-geo.json", "description": "Redis HyperLogLog基数统计、Bitmap位图、Geo地理位置等特殊数据结构的核心用法，适合大规模去重、签到、LBS等场景。"}, {"id": "transaction", "name": "事务机制", "file": "Redis/transaction.json", "description": "Redis事务机制的MULTI/EXEC/DISCARD/WATCH命令、事务特性与乐观锁等核心用法，适合批量操作和并发控制。"}, {"id": "lua", "name": "<PERSON>a脚本", "file": "Redis/lua.json", "description": "Redis Lua脚本的EVAL命令、原子性、常见应用等核心用法，适合实现复杂原子操作和分布式锁。"}, {"id": "rdb", "name": "RDB持久化", "file": "Redis/rdb.json", "description": "Redis RDB持久化的快照原理、配置与触发时机、RDB文件管理等核心用法，适合数据定期备份与恢复。"}, {"id": "aof", "name": "AOF持久化", "file": "Redis/aof.json", "description": "Redis AOF持久化的日志追加原理、AOF重写与恢复、AOF与RDB对比等核心用法，适合理解高可靠性数据持久化机制。"}, {"id": "hybrid-persistence", "name": "混合持久化", "file": "Redis/hybrid-persistence.json", "description": "Redis混合持久化的配置与原理、典型应用场景，适合对数据安全性和恢复速度有高要求的场景。"}, {"id": "replication", "name": "主从复制", "file": "Redis/replication.json", "description": "Redis主从复制的原理、配置与管理、延迟与一致性，适合实现高可用和读写分离架构。"}, {"id": "sentinel", "name": "Sentinel高可用", "file": "Redis/sentinel.json", "description": "Redis Sentinel高可用的架构、自动故障转移、配置与监控，适合实现主从自动切换和高可用集群。"}, {"id": "cluster", "name": "Redis Cluster集群", "file": "Redis/cluster.json", "description": "Redis Cluster集群的分片原理、节点管理与扩容、数据一致性，适合大规模分布式和高可用场景。"}, {"id": "performance", "name": "性能优化", "file": "Redis/performance.json", "description": "Redis性能优化的内存管理、慢查询分析、热点key与大key处理、网络与IO优化，适合高性能场景调优。"}, {"id": "operation", "name": "运维管理", "file": "Redis/operation.json", "description": "Redis运维管理的备份与恢复、日志与监控、常用运维命令、升级与迁移，适合日常管理和故障排查。"}, {"id": "security", "name": "认证与权限", "file": "Redis/security.json", "description": "Redis认证与权限的requirepass配置、ACL访问控制、加密与安全建议，适合安全加固和权限管理。"}, {"id": "network-security", "name": "网络安全", "file": "Redis/network-security.json", "description": "Redis网络安全的端口与防火墙、只绑定本地/内网、防止未授权访问，适合网络隔离和安全防护。"}, {"id": "errors", "name": "常见错误", "file": "Redis/errors.json", "description": "Redis常见错误的连接超时、内存溢出、持久化失败、主从同步异常，适合排查和处理常见问题。"}, {"id": "debug", "name": "调试技巧", "file": "Redis/debug.json", "description": "Redis调试技巧的日志分析、redis-cli调试、慢查询与监控，适合故障排查和性能优化。"}, {"id": "cases", "name": "实战案例", "file": "Redis/cases.json", "description": "Redis实战案例的分布式锁、缓存防护、消息队列、Session共享、限流计数，适合实际项目应用。"}, {"id": "projects", "name": "练习与项目", "file": "Redis/projects.json", "description": "Redis练习与项目包含缓存系统实现、Lua脚本、主从复制与Sentinel、持久化备份、性能优化和安全加固，适合综合应用实战。"}]}