{"name": "Docker Introduction", "trans": ["Docker简介"], "methods": [{"name": "What is Docker", "trans": ["Docker是什么"], "usage": {"syntax": "Docker是一个开源的容器化平台，用于开发、打包和运行应用程序。", "description": "Docker是一种轻量级虚拟化技术，通过容器实现应用程序的打包、分发和运行。它使应用程序能够在隔离的环境中运行，同时共享主机操作系统的内核，避免了传统虚拟机的额外开销。", "parameters": [{"name": "docker", "description": "Docker命令行工具，用于管理容器和镜像。"}, {"name": "dockerd", "description": "Docker守护进程，管理容器生命周期。"}], "returnValue": "无返回值", "examples": [{"code": "# 检查Docker版本\ndocker --version\n\n# Docker架构示例\n# +-------------+\n# | Docker CLI  |  ← 用户通过命令行接口与Docker交互\n# +-------------+\n# |             |\n# | Docker 守护进程 |  ← 管理容器、镜像、网络和存储\n# |             |\n# +-------------+\n# | 容器 | 容器 | 容器 |  ← 隔离运行的应用程序环境\n# +-------------+\n# |    主机操作系统   |  ← 所有容器共享同一内核\n# +-------------+\n# |    硬件      |  ← 物理或虚拟服务器\n# +-------------+\n", "explanation": "展示Docker的版本和基本架构，Docker由客户端、守护进程和容器组成，所有容器共享主机操作系统内核。"}]}}, {"name": "Containers vs Virtual Machines", "trans": ["容器与虚拟机的区别"], "usage": {"syntax": "容器共享操作系统内核，而虚拟机需要完整的操作系统副本。", "description": "容器与虚拟机是两种不同的虚拟化技术。容器仅包含应用程序及其依赖，共享主机内核，启动快速且资源占用少；虚拟机则包含完整的操作系统副本和虚拟化硬件，提供更强的隔离但资源消耗更大。", "parameters": [{"name": "容器", "description": "轻量级、共享内核、秒级启动、MB级大小。"}, {"name": "虚拟机", "description": "完整OS、独立内核、分钟级启动、GB级大小。"}], "returnValue": "无返回值", "examples": [{"code": "# 容器架构\n# +--------+ +--------+ +--------+\n# | 应用 1  | | 应用 2  | | 应用 3  |  ← 只包含应用及其依赖\n# +--------+ +--------+ +--------+\n# | Docker 引擎（容器运行时）    |  ← 管理容器生命周期\n# +------------------------+\n# |      主机操作系统        |  ← 所有容器共享一个内核\n# +------------------------+\n# |         硬件           |  ← 物理或虚拟服务器\n# +------------------------+\n\n# 虚拟机架构\n# +--------+ +--------+ +--------+\n# | 应用 1  | | 应用 2  | | 应用 3  |  ← 应用程序\n# +--------+ +--------+ +--------+\n# | Guest OS| | Guest OS| | Guest OS|  ← 每个VM需要完整的操作系统\n# +--------+ +--------+ +--------+\n# |    虚拟机监控器（Hypervisor） |  ← 管理虚拟机\n# +------------------------+\n# |      主机操作系统        |  ← 宿主操作系统\n# +------------------------+\n# |         硬件           |  ← 物理服务器\n# +------------------------+\n\n# 启动时间和资源占用对比\n# 容器：秒级启动，MB级内存占用\n# 虚拟机：分钟级启动，GB级内存占用\n", "explanation": "展示容器和虚拟机架构的区别，容器共享主机内核，资源占用小且启动迅速；虚拟机拥有独立的操作系统，隔离性更强但资源消耗大。"}]}}, {"name": "Core Concepts", "trans": ["Docker的核心概念（镜像、容器、仓库）"], "usage": {"syntax": "Docker由镜像(Image)、容器(Container)和仓库(Registry)三个核心概念组成。", "description": "Docker镜像是容器的只读模板，包含运行应用所需的所有文件和配置；容器是镜像的运行实例，可以被启动、停止和删除；仓库是存储和分发镜像的服务，如Docker Hub。", "parameters": [{"name": "镜像(Image)", "description": "应用的静态模板，包含代码、依赖和环境。"}, {"name": "容器(Container)", "description": "镜像的运行实例，具有独立的文件系统和网络。"}, {"name": "仓库(Registry)", "description": "存储和分发镜像的服务。"}], "returnValue": "无返回值", "examples": [{"code": "# 镜像操作示例\ndocker pull nginx:latest        # 从Docker Hub拉取Nginx镜像\ndocker images                  # 列出本地所有镜像\n\n# 容器操作示例\ndocker run -d -p 80:80 nginx   # 运行Nginx容器，-d表示后台运行，-p映射端口\ndocker ps                      # 查看运行中的容器\ndocker stop <container_id>     # 停止容器\ndocker rm <container_id>       # 删除容器\n\n# 仓库操作示例\ndocker login                   # 登录Docker Hub\ndocker tag nginx:latest username/nginx:v1  # 给镜像打标签\ndocker push username/nginx:v1   # 推送镜像到Docker Hub\n\n# 核心概念关系\n# 1. 从仓库拉取镜像：docker pull\n# 2. 基于镜像创建容器：docker run\n# 3. 将自定义镜像推送到仓库：docker push\n", "explanation": "展示Docker三个核心概念的具体操作：从仓库拉取镜像、运行容器、管理容器生命周期，以及如何将自定义镜像推送到仓库。"}]}}, {"name": "Application Scenarios", "trans": ["Docker的应用场景"], "usage": {"syntax": "Docker适用于开发环境统一、持续集成/部署、微服务架构和快速扩展部署等场景。", "description": "Docker可用于多种场景：统一开发和生产环境，避免\"我机器上能运行\"问题；简化CI/CD流程；支持微服务架构，每个服务独立容器化；实现应用的快速部署和横向扩展。", "parameters": [{"name": "开发环境统一", "description": "消除环境差异引起的问题。"}, {"name": "CI/CD", "description": "简化持续集成和部署流程。"}, {"name": "微服务架构", "description": "支持服务独立打包和部署。"}, {"name": "横向扩展", "description": "快速复制和部署相同容器。"}], "returnValue": "无返回值", "examples": [{"code": "# 开发环境统一示例\n# 创建Dockerfile定义环境\nFROM node:14                # 基础镜像：Node.js 14版本\nWORKDIR /app                # 设置工作目录\nCOPY package*.json ./       # 复制项目依赖文件\nRUN npm install             # 安装依赖\nCOPY . .                    # 复制项目代码\nCMD [\"npm\", \"start\"]       # 启动命令\n\n# 团队所有成员和CI服务器都使用同一个Docker镜像，确保环境一致\n\n# CI/CD场景示例\n# .gitlab-ci.yml示例\nstages:\n  - build\n  - test\n  - deploy\n\nbuild:\n  stage: build\n  script:\n    - docker build -t myapp:$CI_COMMIT_SHA .  # 构建Docker镜像\n\ntest:\n  stage: test\n  script:\n    - docker run myapp:$CI_COMMIT_SHA npm test  # 在容器中运行测试\n\ndeploy:\n  stage: deploy\n  script:\n    - docker push myapp:$CI_COMMIT_SHA  # 推送镜像\n    - kubectl set image deployment/myapp container=myapp:$CI_COMMIT_SHA  # 部署新版本\n\n# 微服务架构示例\n# docker-compose.yml\nversion: '3'\nservices:\n  frontend:                 # 前端服务\n    image: myapp/frontend\n    ports:\n      - \"80:80\"\n  backend:                  # 后端API服务\n    image: myapp/backend\n    ports:\n      - \"8080:8080\"\n  database:                 # 数据库服务\n    image: postgres\n    environment:\n      POSTGRES_PASSWORD: password\n\n# 横向扩展示例\ndocker-compose up -d --scale backend=3  # 启动3个后端服务实例\n", "explanation": "展示Docker在不同场景的应用：通过Dockerfile统一开发环境，在CI/CD流程中自动构建和测试，使用docker-compose组织微服务架构，以及利用Docker快速横向扩展服务实例。"}]}}, {"name": "Docker Ecosystem", "trans": ["Docker生态系统"], "usage": {"syntax": "Docker生态系统包括Docker Engine、Docker Compose、Docker Swarm、Docker Hub等组件和工具。", "description": "Docker生态系统由多个组件组成：Docker Engine是核心运行时；Docker Compose用于定义多容器应用；Docker Swarm提供集群和编排功能；Docker Hub是官方镜像仓库。此外还有Kubernetes等第三方工具集成。", "parameters": [{"name": "Docker Engine", "description": "Docker的核心组件，包含守护进程、API和CLI。"}, {"name": "<PERSON><PERSON>", "description": "定义和运行多容器应用的工具。"}, {"name": "Docker Swarm", "description": "Docker原生的容器编排工具。"}, {"name": "<PERSON><PERSON>", "description": "官方的镜像仓库和分享平台。"}], "returnValue": "无返回值", "examples": [{"code": "# Docker Engine示例\ndocker info                   # 显示Docker系统信息\ndocker version                # 显示Docker版本\n\n# Docker Compose示例 (docker-compose.yml)\nversion: '3'\nservices:\n  web:                      # Web服务\n    image: nginx\n    ports:\n      - \"80:80\"\n    volumes:                # 挂载本地目录到容器\n      - ./html:/usr/share/nginx/html\n  db:                       # 数据库服务\n    image: mysql\n    environment:            # 环境变量配置\n      MYSQL_ROOT_PASSWORD: example\n\n# 使用Docker Compose命令\ndocker-compose up -d         # 后台启动所有服务\ndocker-compose down          # 停止并移除所有容器\n\n# Docker Swarm示例\ndocker swarm init            # 初始化Swarm集群\ndocker service create --replicas 3 --name web nginx  # 创建3个副本的服务\ndocker service ls            # 列出所有服务\ndocker service scale web=5   # 将服务扩展到5个副本\n\n# Docker Hub交互示例\ndocker search nginx          # 搜索Nginx相关镜像\ndocker pull nginx:alpine     # 拉取轻量级Nginx镜像\ndocker login                 # 登录Docker Hub\ndocker push username/myapp   # 推送自定义镜像\n\n# 与Kubernetes集成示例\n# 使用Docker构建镜像，然后在Kubernetes中部署\ndocker build -t myapp:v1 .\nkubectl create deployment myapp --image=myapp:v1\nkubectl expose deployment myapp --port=80 --type=LoadBalancer\n", "explanation": "展示Docker生态系统的各个组件：使用Docker Engine管理容器，通过Docker Compose定义多容器应用，利用Docker Swarm创建容器集群，与Docker Hub交互管理镜像，以及与Kubernetes等外部工具的集成。"}]}}, {"name": "Docker简介作业", "trans": ["作业：Docker简介"], "usage": {"syntax": "请完成以下任务：\n1. 安装Docker并运行hello-world容器。\n2. 分析容器和虚拟机在资源使用上的区别。\n3. 使用Docker运行Nginx并访问网页。\n4. 设计一个简单的多容器应用（如前端+后端）。\n5. 探索Docker Hub并找到三个有用的官方镜像。", "description": "通过实际操作理解Docker的基本概念和使用方法。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 作业示例\n\n# 1. 安装Docker并运行hello-world\n# 安装Docker（Ubuntu示例）\nsudo apt-get update\nsudo apt-get install docker-ce docker-ce-cli containerd.io\n# 运行hello-world容器\ndocker run hello-world\n\n# 2. 容器与虚拟机对比\n# 启动一个容器并查看资源使用\ndocker run -d --name nginx nginx\ndocker stats nginx  # 观察内存和CPU使用\n\n# 3. 运行Nginx并访问\ndocker run -d -p 8080:80 --name my-nginx nginx\n# 在浏览器访问 http://localhost:8080\n\n# 4. 设计多容器应用\n# 创建docker-compose.yml文件\nversion: '3'\nservices:\n  frontend:\n    image: nginx\n    ports:\n      - \"8080:80\"\n  backend:\n    image: node:14\n    volumes:\n      - ./app:/app\n    working_dir: /app\n    command: node server.js\n\n# 5. 探索Docker Hub\n# 搜索并拉取有用的官方镜像\ndocker pull mysql\ndocker pull redis\ndocker pull python\n", "explanation": "通过安装Docker、运行容器、比较资源使用、设计多容器应用和探索镜像仓库，全面了解Docker的基本概念和使用方法。"}]}}, {"name": "Docker简介正确实现示例", "trans": ["正确实现卡片"], "usage": {"syntax": "Docker基础概念的正确理解与实现。", "description": "展示如何正确理解Docker核心概念并应用到实际场景中，包括安装、使用镜像、运行容器、创建多容器应用等。", "parameters": [{"name": "安装与验证", "description": "正确安装Docker并验证。"}, {"name": "镜像与容器", "description": "正确使用镜像和容器。"}, {"name": "多容器应用", "description": "正确设计多容器应用。"}], "returnValue": "无返回值", "examples": [{"code": "# 正确安装Docker (Ubuntu示例)\nsudo apt-get update\nsudo apt-get install apt-transport-https ca-certificates curl software-properties-common\ncurl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -\nsudo add-apt-repository \"deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable\"\nsudo apt-get update\nsudo apt-get install docker-ce docker-ce-cli containerd.io\n\n# 验证安装\ndocker version\ndocker run hello-world\n\n# 正确使用镜像和容器\n# 拉取镜像\ndocker pull nginx:latest\n# 运行容器，映射端口，设置名称和后台运行\ndocker run -d -p 80:80 --name my-nginx nginx\n# 查看容器\ndocker ps\n# 查看容器日志\ndocker logs my-nginx\n# 进入容器执行命令\ndocker exec -it my-nginx bash\n# 停止和删除容器\ndocker stop my-nginx\ndocker rm my-nginx\n\n# 正确设计多容器应用\n# 创建docker-compose.yml\nversion: '3'\nservices:\n  web:\n    image: nginx:alpine\n    ports:\n      - \"80:80\"\n    volumes:\n      - ./website:/usr/share/nginx/html\n    depends_on:\n      - api\n  api:\n    image: node:14-alpine\n    volumes:\n      - ./api:/app\n    working_dir: /app\n    command: npm start\n    environment:\n      - NODE_ENV=production\n    ports:\n      - \"3000:3000\"\n  db:\n    image: mongo:4\n    volumes:\n      - db-data:/data/db\n\nvolumes:\n  db-data:\n\n# 启动多容器应用\ndocker-compose up -d\n# 查看所有容器状态\ndocker-compose ps\n# 查看服务日志\ndocker-compose logs\n# 停止所有服务\ndocker-compose down\n", "explanation": "展示了Docker的标准安装过程、基本容器操作和多容器应用的正确配置方法，涵盖了端口映射、卷挂载、环境变量设置和容器依赖关系等重要概念。"}]}}]}