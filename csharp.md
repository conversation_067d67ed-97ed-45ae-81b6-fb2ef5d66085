# C#语言学习路线与核心内容

## 1. C#基础

### 语言简介
- C#发展历史
- C#的特点
- C#与C++/Java的区别
- C#的应用领域

### 开发环境搭建
- Visual Studio/VS Code安装
- .NET SDK安装
- Windows/Linux/Mac环境配置
- 第一个C#程序
- 项目结构与编译运行

### 基本语法
- Main方法结构
- 语句与分号
- 注释写法
- 标识符与关键字
- 代码风格规范

## 2. 数据类型与运算符

### 基本数据类型
- int、float、double、char、bool、decimal
- string类型
- var隐式类型
- 类型转换
- sizeof与typeof

### 常量与变量
- 变量声明与初始化
- const与readonly
- 作用域与生命周期

### 运算符
- 算术运算符
- 关系运算符
- 逻辑运算符
- 位运算符
- 赋值运算符
- 其他运算符（条件、null合并、is/as等）

## 3. 流程控制

### 条件语句
- if/else语句
- switch语句
- 嵌套条件判断

### 循环语句
- for循环
- while循环
- do...while循环
- foreach循环
- break与continue
- 循环嵌套

### 跳转语句
- goto语句
- return语句

## 4. 方法

### 方法定义与调用
- 方法的基本结构
- 参数与返回值
- 可选参数与命名参数
- 方法重载
- 递归方法
- 局部函数

### 参数传递方式
- 值传递
- 引用传递（ref/out）
- params参数

## 5. 面向对象编程

### 类与对象
- 类的定义与声明
- 对象的创建与使用
- 构造函数与析构函数
- 静态成员
- this关键字

### 封装、继承与多态
- 访问修饰符（public/private/protected/internal）
- 继承与派生类
- 抽象类与接口
- 虚方法与重写
- 多态与类型转换
- 密封类与方法

### 属性与索引器
- 自动属性
- 只读/写属性
- 索引器用法

## 6. 委托、事件与Lambda

### 委托
- 委托定义与使用
- 多播委托
- 匿名方法

### 事件
- 事件声明与订阅
- 事件发布与处理
- 事件与委托关系

### Lambda表达式
- 基本语法
- 与委托/事件结合

## 7. 集合与LINQ

### 集合类型
- 数组
- List、Dictionary、HashSet等
- 集合初始化器
- 集合遍历

### LINQ基础
- 查询表达式语法
- 方法链式语法
- 投影、筛选、排序、分组
- 聚合操作
- 延迟执行

## 8. 文件与输入输出

### 文件操作
- 文件读写（File、StreamReader/Writer）
- 路径与目录操作
- 二进制文件处理

### 控制台输入输出
- Console.Write/WriteLine
- Console.Read/ReadLine
- 格式化输出

## 9. 异常处理

### 异常机制
- try/catch/finally
- throw与自定义异常
- 异常链与内嵌异常
- 标准异常类

## 10. 内存与资源管理

### 垃圾回收机制
- GC基本原理
- IDisposable与using
- 内存泄漏与检测
- 非托管资源处理

## 11. 异步与多线程编程

### 异步编程
- async/await
- Task与并发
- 线程与Thread类
- 线程池与定时器
- 锁与线程安全

## 12. 调试与常见错误

### 编译错误与警告
- 语法错误
- 类型不匹配
- 空引用异常
- 索引越界

### 调试技巧
- Visual Studio调试
- 断点与单步执行
- 日志与输出窗口
- 常用调试工具

## 13. 项目实战与练习

### 实战案例
- 简单计算器实现
- 学生成绩管理系统
- 文件批量处理工具
- 字符串查找与替换
- 集合与LINQ综合应用

### 练习与项目
- 编写九九乘法表
- 实现冒泡排序与二分查找
- 编写自定义字符串函数
- 文件复制工具
- 异步文件下载器
- 事件驱动小项目 