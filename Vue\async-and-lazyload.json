{"name": "Async Components and Lazy Loading", "trans": ["异步组件与懒加载"], "methods": [{"name": "Async Component Loading", "trans": ["异步组件加载"], "usage": {"syntax": "import { defineAsyncComponent } from 'vue'\nconst AsyncComp = defineAsyncComponent(() => import('./Comp.vue'))", "description": "通过defineAsyncComponent实现组件按需异步加载，减小首屏体积，提升加载速度。", "parameters": [{"name": "defineAsyncComponent", "description": "定义异步组件的方法"}, {"name": "import", "description": "动态导入组件的函数"}], "returnValue": "异步加载的组件实例", "examples": [{"code": "import { defineAsyncComponent } from 'vue'\nconst AsyncComp = defineAsyncComponent(() => import('./Comp.vue'))", "explanation": "按需加载Comp组件，提升性能。"}]}}, {"name": "Route Lazy Loading", "trans": ["路由懒加载"], "usage": {"syntax": "component: () => import('./Page.vue')", "description": "路由配置中通过函数返回import实现页面组件懒加载，只有访问路由时才加载对应页面。", "parameters": [{"name": "component", "description": "懒加载组件的函数"}], "returnValue": "按需加载的路由页面组件", "examples": [{"code": "{ path: '/about', component: () => import('./About.vue') }", "explanation": "访问/about时才加载About页面。"}]}}, {"name": "Image Lazy Loading", "trans": ["图片懒加载"], "usage": {"syntax": "<img v-lazy=\"url\" /> 或 loading=\"lazy\"", "description": "图片懒加载可用v-lazy指令或原生loading='lazy'属性，图片进入可视区时才加载，减少带宽消耗。", "parameters": [{"name": "v-lazy", "description": "图片懒加载指令"}, {"name": "loading='lazy'", "description": "原生懒加载属性"}], "returnValue": "按需加载的图片资源", "examples": [{"code": "<img v-lazy=\"url\" />\n<img :src=\"url\" loading=\"lazy\" />", "explanation": "两种图片懒加载方式。"}]}}, {"name": "Resource Preloading", "trans": ["资源预加载"], "usage": {"syntax": "<link rel=\"preload\" as=\"image\" href=\"url\">", "description": "通过link rel='preload'或rel='prefetch'标签提前加载关键资源，提升页面响应速度。", "parameters": [{"name": "rel='preload'", "description": "预加载关键资源"}, {"name": "rel='prefetch'", "description": "预取未来可能用到的资源"}], "returnValue": "提前加载的资源，提高首屏和交互性能", "examples": [{"code": "<link rel=\"preload\" as=\"image\" href=\"/logo.png\">\n<link rel=\"prefetch\" href=\"/next-page.js\">", "explanation": "预加载图片和预取下一个页面资源。"}]}}, {"name": "Async and Lazyload Assignment", "trans": ["异步与懒加载练习"], "usage": {"syntax": "# 异步与懒加载练习", "description": "完成以下练习，巩固异步组件与懒加载相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用defineAsyncComponent实现异步组件加载。\n2. 配置路由懒加载。\n3. 用v-lazy或loading='lazy'实现图片懒加载。\n4. 用preload/prefetch优化资源加载。", "explanation": "通过这些练习掌握异步与懒加载优化。"}]}}]}