{"name": "Introduction and Scenarios", "trans": ["介绍与应用场景"], "methods": [{"name": "What is Redis", "trans": ["Redis是什么"], "usage": {"syntax": "Redis 是一个开源的高性能键值对（Key-Value）数据库。", "description": "Redis 是基于内存的 NoSQL 数据库，支持多种数据结构，常用于缓存、消息队列、排行榜等场景。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# Redis 运行示例\n$ redis-server # 启动Redis服务\n$ redis-cli set key1 value1 # 设置一个键值对\n$ redis-cli get key1 # 获取键值对，返回value1", "explanation": "展示了Redis的基本启动和键值操作。"}]}}, {"name": "Redis Features", "trans": ["Redis的特点"], "usage": {"syntax": "内存存储、丰富数据结构、持久化、主从复制、事务、Lua脚本、集群、发布订阅等", "description": "Redis 以高性能、丰富数据结构和多样功能著称，支持持久化和分布式部署。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# Redis 支持的数据结构示例\nSET key1 value1 # 字符串\nLPUSH list1 a b c # 列表\nHSET user:1 name Tom age 20 # 哈希\nSADD set1 a b c # 集合\nZADD zset1 1 a 2 b # 有序集合", "explanation": "展示了Redis支持的多种数据结构。"}]}}, {"name": "Typical Application Scenarios", "trans": ["典型应用场景"], "usage": {"syntax": "缓存、分布式锁、消息队列、排行榜、会话管理、限流等", "description": "Redis 常用于高性能缓存、分布式锁、消息队列、实时统计、排行榜、会话共享等场景。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 缓存示例\nSET user:1:name <PERSON> EX 60 # 设置带过期时间的缓存\n# 分布式锁示例\nSET lock:order 1 NX EX 10 # 10秒自动释放的分布式锁", "explanation": "展示了缓存和分布式锁的典型用法。"}]}}, {"name": "Redis vs Memcached", "trans": ["Redis与Memcached对比"], "usage": {"syntax": "Redis 支持多种数据结构和持久化，Memcached 仅支持字符串且无持久化。", "description": "Redis 功能更丰富，支持持久化、事务、发布订阅、Lua脚本等，适合多样化场景。Memcached 适合简单缓存。", "parameters": [{"name": "数据结构", "description": "Redis支持多种，Memcached仅字符串"}, {"name": "持久化", "description": "Redis支持，Memcached不支持"}, {"name": "分布式", "description": "两者均支持分布式部署"}], "returnValue": "无返回值", "examples": [{"code": "# Redis 支持持久化和多数据结构\nSAVE # 触发RDB快照\n# Memcached 仅支持字符串\nset key1 value1", "explanation": "对比了两者的主要区别。"}]}}, {"name": "Practice: Redis基础练习", "trans": ["基础练习"], "usage": {"syntax": "# Redis基础练习", "description": "完成以下练习，巩固Redis基础知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 启动Redis服务并用redis-cli连接。\n2. 设置并获取一个字符串键值对。\n3. 使用LPUSH和LRANGE操作一个列表。\n4. 对比Redis和Memcached的主要区别。", "explanation": "通过这些练习掌握Redis的基本操作和应用场景。"}, {"code": "# 正确实现示例\n$ redis-server\n$ redis-cli set hello world\n$ redis-cli get hello\n$ redis-cli lpush mylist a b c\n$ redis-cli lrange mylist 0 -1", "explanation": "展示了练习的标准实现过程。"}]}}]}