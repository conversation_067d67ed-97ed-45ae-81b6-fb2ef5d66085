{"name": "Slots and Scoped Slots", "trans": ["插槽与作用域插槽"], "methods": [{"name": "<PERSON><PERSON><PERSON>", "trans": ["默认插槽"], "usage": {"syntax": "<slot></slot>", "description": "默认插槽用于父组件向子组件传递内容，未指定name属性时为默认插槽。", "parameters": [{"name": "slot标签", "description": "插槽占位，接收父组件内容"}], "returnValue": "渲染父组件传递的内容，无返回值", "examples": [{"code": "// 子组件\n<template>\n  <slot></slot>\n</template>\n// 父组件\n<MyComp>hello</MyComp>", "explanation": "父组件内容通过默认插槽渲染到子组件。"}]}}, {"name": "Named Slot", "trans": ["具名插槽"], "usage": {"syntax": "<slot name=\"header\"></slot>", "description": "具名插槽允许父组件向子组件的指定位置传递内容，通过name属性区分。", "parameters": [{"name": "name属性", "description": "指定插槽名称"}], "returnValue": "渲染父组件指定slot的内容，无返回值", "examples": [{"code": "// 子组件\n<template>\n  <slot name=\"header\"></slot>\n  <slot></slot>\n</template>\n// 父组件\n<MyComp>\n  <template #header>头部</template>\n  默认内容\n</MyComp>", "explanation": "父组件通过#header传递内容到具名插槽。"}]}}, {"name": "<PERSON><PERSON> Slot", "trans": ["作用域插槽"], "usage": {"syntax": "<slot :user=\"user\"></slot>", "description": "作用域插槽允许子组件向插槽内容传递数据，父组件可接收并使用这些数据。", "parameters": [{"name": ":prop", "description": "子组件传递给插槽内容的数据"}], "returnValue": "父组件可访问子组件传递的数据", "examples": [{"code": "// 子组件\n<template>\n  <slot :user=\"user\"></slot>\n</template>\n<script setup>\nconst user = { name: '张三' }\n</script>\n// 父组件\n<MyComp v-slot={ user }>\n  {{ user.name }}\n</MyComp>", "explanation": "子组件通过作用域插槽向父组件传递user对象。"}]}}, {"name": "Dynamic Slot", "trans": ["动态插槽"], "usage": {"syntax": "<slot :name=\"slotName\"></slot>", "description": "动态插槽允许根据变量动态决定插槽名称，实现更灵活的内容分发。", "parameters": [{"name": ":name", "description": "动态绑定插槽名称"}], "returnValue": "根据变量渲染对应插槽内容", "examples": [{"code": "// 子组件\n<template>\n  <slot :name=\"slotName\"></slot>\n</template>\n<script setup>\nconst slotName = 'footer'\n</script>\n// 父组件\n<MyComp>\n  <template #footer>底部内容</template>\n</MyComp>", "explanation": "通过变量slotName动态决定插槽内容渲染位置。"}]}}, {"name": "Slot Content Passing", "trans": ["插槽内容传递"], "usage": {"syntax": "<slot></slot>", "description": "插槽机制实现父组件向子组件传递任意内容，包括文本、HTML、组件等，提升组件灵活性。", "parameters": [{"name": "slot内容", "description": "父组件传递的内容"}], "returnValue": "子组件渲染父组件传递的内容", "examples": [{"code": "// 子组件\n<template>\n  <slot></slot>\n</template>\n// 父组件\n<MyComp>\n  <span style=\"color:red\">高亮内容</span>\n</MyComp>", "explanation": "父组件可传递任意内容到子组件插槽。"}]}}, {"name": "Slots Assignment", "trans": ["插槽练习"], "usage": {"syntax": "# 插槽练习", "description": "完成以下练习，巩固插槽相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 实现一个带默认插槽的组件。\n2. 实现具名插槽和作用域插槽。\n3. 用动态插槽渲染不同内容。\n4. 传递复杂内容到插槽。", "explanation": "通过这些练习掌握插槽的用法和灵活性。"}]}}]}