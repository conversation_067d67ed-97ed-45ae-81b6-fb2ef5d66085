{"name": "Image Build", "trans": ["镜像构建"], "methods": [{"name": "Dockerfile Syntax", "trans": ["Dockerfile基础语法"], "usage": {"syntax": "FROM <基础镜像>\nRUN <命令>\nCOPY <源> <目标>\nCMD [\"命令\"]", "description": "Dockerfile用于定义自定义镜像的构建过程，包含基础镜像、命令执行、文件拷贝、启动命令等指令。", "parameters": [{"name": "FROM", "description": "指定基础镜像。"}, {"name": "RUN", "description": "在构建时执行的命令。"}, {"name": "COPY", "description": "将本地文件复制到镜像中。"}, {"name": "CMD", "description": "容器启动时默认执行的命令。"}], "returnValue": "无返回值，定义镜像构建流程。", "examples": [{"code": "# 一个简单的Dockerfile示例\nFROM alpine:latest\nRUN echo 'Hello Docker' > /hello.txt\nCOPY ./app /app\nCMD [\"cat\", \"/hello.txt\"]", "explanation": "基于alpine镜像，写入hello.txt，复制app目录，启动时输出hello.txt内容。"}]}}, {"name": "docker build", "trans": ["构建镜像"], "usage": {"syntax": "docker build -t <镜像名>:<标签> <路径>", "description": "根据Dockerfile构建自定义镜像，可指定镜像名称和标签。", "parameters": [{"name": "-t", "description": "指定镜像名称和标签。"}, {"name": "<路径>", "description": "Dockerfile所在目录，通常为.。"}], "returnValue": "无返回值，构建完成后本地生成新镜像。", "examples": [{"code": "# 构建镜像并命名为myapp:1.0\ndocker build -t myapp:1.0 .", "explanation": "在当前目录下根据Dockerfile构建myapp:1.0镜像。"}]}}, {"name": "Multi-stage Build", "trans": ["多阶段构建"], "usage": {"syntax": "FROM node:16 AS build\nWORKDIR /app\nCOPY . .\nRUN npm install && npm run build\nFROM nginx:alpine\nCOPY --from=build /app/dist /usr/share/nginx/html", "description": "通过多阶段构建，先用一个镜像编译应用，再用另一个更小的镜像作为最终产物，减少镜像体积。", "parameters": [{"name": "AS", "description": "为阶段命名，便于后续引用。"}, {"name": "COPY --from", "description": "从前一阶段复制文件到新阶段。"}], "returnValue": "无返回值，生成优化后的小体积镜像。", "examples": [{"code": "# 多阶段构建示例\nFROM golang:1.20 AS builder\nWORKDIR /src\nCOPY . .\nRUN go build -o app\nFROM alpine:latest\nCOPY --from=builder /src/app /app\nCMD [\"/app\"]", "explanation": "先用golang镜像编译Go程序，再用alpine镜像只包含最终二进制，极大减小体积。"}]}}, {"name": "Image Optimization Tips", "trans": ["镜像优化技巧"], "usage": {"syntax": "# 常用优化技巧\n1. 合并RUN指令\n2. 清理无用文件\n3. 选择小体积基础镜像\n4. 多阶段构建", "description": "通过合并指令、清理缓存、选择精简基础镜像等方式，减少镜像体积，提高构建效率。", "parameters": [], "returnValue": "无返回值，优化镜像构建过程。", "examples": [{"code": "# 合并RUN指令减少镜像层数\nRUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*", "explanation": "合并命令并清理缓存，减少镜像层和体积。"}]}}, {"name": "Image Size Analysis", "trans": ["镜像体积分析"], "usage": {"syntax": "docker images\ndocker history <镜像名>", "description": "通过docker images和docker history命令分析镜像体积和各层大小，定位优化空间。", "parameters": [{"name": "<镜像名>", "description": "要分析的镜像名称。"}], "returnValue": "无返回值，命令行输出镜像体积和构建历史。", "examples": [{"code": "# 查看镜像体积\ndocker images\n# 查看镜像构建历史及每层大小\ndocker history myapp:1.0", "explanation": "分析镜像整体体积和每一层的大小，便于优化。"}]}}, {"name": "Assignment: 镜像构建实践", "trans": ["作业：镜像构建实践"], "usage": {"syntax": "请完成以下任务：\n1. 编写一个Dockerfile，基于alpine，复制本地app目录到镜像中，启动时输出app目录内容\n2. 使用docker build构建镜像并命名为myalpine-app:latest\n3. 尝试合并RUN指令优化镜像体积\n4. 使用docker history分析镜像层大小", "description": "通过实际操作掌握Dockerfile编写、镜像构建、优化和体积分析。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 1. 编写Dockerfile\nFROM alpine\nCOPY ./app /app\nCMD [\"ls\", \"/app\"]\n# 2. 构建镜像\ndocker build -t myalpine-app:latest .\n# 3. 合并RUN指令（如有需要）\n# 4. 分析镜像体积\ndocker history myalpine-app:latest", "explanation": "依次完成镜像构建、优化和体积分析的核心操作。"}]}}]}