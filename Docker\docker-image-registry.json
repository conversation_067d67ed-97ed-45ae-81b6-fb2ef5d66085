{"name": "Image Registry", "trans": ["镜像仓库"], "methods": [{"name": "<PERSON>er <PERSON><PERSON>", "trans": ["<PERSON>er <PERSON><PERSON>使用"], "usage": {"syntax": "docker login\ndocker search <镜像名>\ndocker pull <镜像名>\ndocker push <镜像名>", "description": "Docker Hub是官方公共镜像仓库，支持镜像的搜索、拉取、推送和管理。需先登录账号。", "parameters": [{"name": "docker login", "description": "登录Docker Hub账号。"}, {"name": "docker search", "description": "搜索公开镜像。"}, {"name": "docker pull", "description": "拉取镜像。"}, {"name": "docker push", "description": "推送镜像。"}], "returnValue": "无返回值，命令行输出操作结果。", "examples": [{"code": "# 登录Docker Hub\ndocker login\n# 搜索nginx镜像\ndocker search nginx\n# 拉取nginx镜像\ndocker pull nginx\n# 推送自定义镜像\ndocker push myrepo/myimage:1.0", "explanation": "登录后可在Docker Hub搜索、拉取和推送镜像。"}]}}, {"name": "Private Registry Setup", "trans": ["私有仓库搭建"], "usage": {"syntax": "docker run -d -p 5000:5000 --name registry registry:2", "description": "通过官方registry镜像快速搭建本地私有镜像仓库，便于企业或团队内部管理镜像。", "parameters": [{"name": "-p 5000:5000", "description": "将本地5000端口映射到容器的5000端口。"}, {"name": "registry:2", "description": "官方私有仓库镜像。"}], "returnValue": "无返回值，命令执行后本地启动私有仓库服务。", "examples": [{"code": "# 启动本地私有仓库\ndocker run -d -p 5000:5000 --name registry registry:2", "explanation": "在本地5000端口启动一个私有镜像仓库服务。"}]}}, {"name": "docker push", "trans": ["镜像推送"], "usage": {"syntax": "docker tag <本地镜像>:<标签> <仓库地址>/<镜像名>:<标签>\ndocker push <仓库地址>/<镜像名>:<标签>", "description": "将本地镜像推送到远程仓库（如Docker Hub或私有仓库），需先打标签。", "parameters": [{"name": "docker tag", "description": "为镜像打上目标仓库标签。"}, {"name": "docker push", "description": "推送镜像到指定仓库。"}], "returnValue": "无返回值，命令行输出推送进度。", "examples": [{"code": "# 给镜像打标签\ndocker tag myapp:1.0 localhost:5000/myapp:1.0\n# 推送到本地私有仓库\ndocker push localhost:5000/myapp:1.0", "explanation": "先打标签再推送镜像到本地私有仓库。"}]}}, {"name": "Image Pull Policy", "trans": ["镜像拉取策略"], "usage": {"syntax": "docker run --pull=always <镜像名>\ndocker run --pull=never <镜像名>", "description": "通过--pull参数控制容器启动时镜像的拉取策略，保证镜像为最新或避免重复拉取。", "parameters": [{"name": "--pull=always", "description": "每次启动都强制拉取最新镜像。"}, {"name": "--pull=never", "description": "只用本地镜像，不拉取远程。"}], "returnValue": "无返回值，影响容器启动时镜像的获取方式。", "examples": [{"code": "# 每次都拉取最新镜像\ndocker run --pull=always nginx", "explanation": "保证启动的nginx容器总是最新镜像。"}, {"code": "# 只用本地镜像\ndocker run --pull=never nginx", "explanation": "只用本地已有镜像，避免网络拉取。"}]}}, {"name": "Image Security Scan", "trans": ["镜像安全扫描"], "usage": {"syntax": "docker scan <镜像名>", "description": "使用docker scan命令对镜像进行安全漏洞扫描，发现潜在风险。部分环境需安装docker scan插件。", "parameters": [{"name": "<镜像名>", "description": "要扫描的镜像名称。"}], "returnValue": "无返回值，命令行输出安全扫描报告。", "examples": [{"code": "# 扫描nginx镜像安全\ndocker scan nginx", "explanation": "输出nginx镜像的安全漏洞报告。"}]}}, {"name": "Assignment: 镜像仓库实践", "trans": ["作业：镜像仓库实践"], "usage": {"syntax": "请完成以下任务：\n1. 登录Docker Hub并拉取nginx镜像\n2. 启动本地私有仓库并推送自定义镜像\n3. 使用--pull=always参数启动容器\n4. 对nginx镜像进行安全扫描", "description": "通过实际操作掌握镜像仓库的使用、推送、拉取策略和安全扫描。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 1. 登录并拉取nginx\ndocker login\ndocker pull nginx\n# 2. 启动私有仓库并推送镜像\ndocker run -d -p 5000:5000 --name registry registry:2\ndocker tag nginx localhost:5000/nginx\ndocker push localhost:5000/nginx\n# 3. 启动容器并强制拉取\ndocker run --pull=always nginx\n# 4. 镜像安全扫描\ndocker scan nginx", "explanation": "依次完成镜像仓库相关的核心操作。"}]}}]}