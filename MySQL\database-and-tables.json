{"name": "Database and Tables", "trans": ["数据库与表"], "methods": [{"name": "Database Concept", "trans": ["数据库概念"], "usage": {"syntax": "-- 无具体SQL，理解数据库是数据的有序集合", "description": "数据库是存储和管理数据的容器，通常用于保存结构化信息。每个数据库可以包含多张表。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "-- 一个MySQL服务器可以有多个数据库，每个数据库下有多张表。\n-- 例如：学生信息数据库、公司业务数据库等。", "explanation": "说明数据库的基本概念和作用。"}]}}, {"name": "Create Database", "trans": ["创建数据库"], "usage": {"syntax": "CREATE DATABASE 数据库名;", "description": "创建一个新的数据库，用于存储相关的数据表。", "parameters": [{"name": "数据库名", "description": "要创建的数据库名称"}], "returnValue": "无返回值，数据库被创建", "examples": [{"code": "CREATE DATABASE school; -- 创建名为school的数据库", "explanation": "创建一个名为school的数据库，后续可在其中建表。"}]}}, {"name": "Drop Database", "trans": ["删除数据库"], "usage": {"syntax": "DROP DATABASE 数据库名;", "description": "删除指定的数据库及其所有数据表，操作不可逆。", "parameters": [{"name": "数据库名", "description": "要删除的数据库名称"}], "returnValue": "无返回值，数据库被删除", "examples": [{"code": "DROP DATABASE school; -- 删除名为school的数据库", "explanation": "彻底删除school数据库及其所有内容，操作需谨慎。"}]}}, {"name": "Create Table", "trans": ["创建表"], "usage": {"syntax": "CREATE TABLE 表名 (列定义1, 列定义2, ...);", "description": "在指定数据库中创建一张新表，定义字段名、类型和约束。", "parameters": [{"name": "表名", "description": "要创建的表名称"}, {"name": "列定义", "description": "每一列的名称、类型及约束"}], "returnValue": "无返回值，表被创建", "examples": [{"code": "CREATE TABLE students (\n  id INT PRIMARY KEY,\n  name VARCHAR(20),\n  gender CHAR(1),\n  age INT\n); -- 创建学生表", "explanation": "创建一个包含学号、姓名、性别、年龄的学生表。"}]}}, {"name": "Drop Table", "trans": ["删除表"], "usage": {"syntax": "DROP TABLE 表名;", "description": "删除指定的数据表，表中所有数据也会被删除。", "parameters": [{"name": "表名", "description": "要删除的表名称"}], "returnValue": "无返回值，表被删除", "examples": [{"code": "DROP TABLE students; -- 删除学生表", "explanation": "删除名为students的表，数据不可恢复。"}]}}, {"name": "Describe Table", "trans": ["查看表结构"], "usage": {"syntax": "DESCRIBE 表名;", "description": "查看指定表的结构，包括字段名、类型、约束等信息。", "parameters": [{"name": "表名", "description": "要查看的表名称"}], "returnValue": "表结构信息", "examples": [{"code": "DESCRIBE students; -- 查看学生表结构", "explanation": "显示students表的所有字段、类型和约束。"}]}}, {"name": "Alter Table", "trans": ["修改表结构"], "usage": {"syntax": "ALTER TABLE 表名 操作;", "description": "修改表结构，如添加、删除、修改字段或约束。", "parameters": [{"name": "表名", "description": "要修改的表名称"}, {"name": "操作", "description": "具体的结构变更操作，如ADD、DROP、MODIFY等"}], "returnValue": "无返回值，表结构被修改", "examples": [{"code": "ALTER TABLE students ADD email VARCHAR(50); -- 添加email字段", "explanation": "为students表增加一个email字段。"}, {"code": "ALTER TABLE students MODIFY age SMALLINT; -- 修改age字段类型", "explanation": "将age字段类型由INT改为SMALLINT。"}]}}, {"name": "Database and Tables Assignment", "trans": ["数据库与表练习"], "usage": {"syntax": "# 数据库与表练习", "description": "完成以下练习，巩固数据库与表的基本操作。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 创建名为school的数据库。\n2. 在school数据库中创建students表，包含id、name、gender、age字段。\n3. 查看students表结构。\n4. 给students表添加email字段。\n5. 删除students表。\n6. 删除school数据库。", "explanation": "通过实际操作掌握数据库和表的创建、修改、删除等基本技能。"}]}}]}