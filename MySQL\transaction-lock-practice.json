{"name": "Transaction and Lock Practice", "trans": ["事务与锁练习"], "methods": [{"name": "Concurrent Update Lock Demo", "trans": ["并发更新锁演示"], "usage": {"syntax": "-- 会话A\nSTART TRANSACTION;\nUPDATE accounts SET balance = balance + 100 WHERE account_id = 1;\n-- 不提交，保持事务开启\n\n-- 会话B\nSTART TRANSACTION;\nUPDATE accounts SET balance = balance - 50 WHERE account_id = 1;\n-- 观察是否被阻塞\n-- 会话A提交后，会话B才能继续\nCOMMIT;", "description": "本练习通过两个会话同时修改同一条数据，演示MySQL InnoDB的行级锁机制。第一个会话对某行加锁未提交时，第二个会话尝试修改同一行会被阻塞，直到第一个会话提交或回滚。", "parameters": [{"name": "account_id", "description": "要操作的账户ID"}, {"name": "balance", "description": "账户余额"}], "returnValue": "无返回值", "examples": [{"code": "-- 建表及插入数据\nCREATE TABLE accounts (\n  account_id INT PRIMARY KEY,\n  account_name VARCHAR(100),\n  balance DECIMAL(10,2)\n);\nINSERT INTO accounts VALUES (1, '张三', 1000.00);\n\n-- 会话A\nSTART TRANSACTION;\nUPDATE accounts SET balance = balance + 100 WHERE account_id = 1;\n-- 此时不要提交，保持事务开启\n\n-- 会话B（新窗口）\nSTART TRANSACTION;\nUPDATE accounts SET balance = balance - 50 WHERE account_id = 1;\n-- 这一步会被阻塞，直到会话A提交或回滚\n\n-- 会话A提交\nCOMMIT;\n\n-- 会话B现在可以继续\nCOMMIT;", "explanation": "本例演示了InnoDB的行级锁。当会话A对account_id=1的行加锁未提交时，会话B对同一行的更新会被阻塞。只有会话A提交或回滚后，会话B才能获得锁并完成操作。这体现了事务隔离和并发控制。"}]}}, {"name": "Transaction and Lock Assignment", "trans": ["事务与锁练习题"], "usage": {"syntax": "# 事务与锁练习题", "description": "完成以下练习，深入理解MySQL事务与锁的并发控制机制。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 按照上例步骤，实际操作两窗口，观察会话B的阻塞现象，并截图说明。\n2. 修改实验，尝试在会话A未提交时，会话B只执行SELECT * FROM accounts WHERE account_id = 1;，观察是否被阻塞，并解释原因。\n3. 尝试将表引擎改为MyISAM，重复上述实验，观察行为有何不同，并分析原因。\n4. 总结InnoDB与MyISAM在并发控制上的主要区别。", "explanation": "通过这些练习，你将深入理解MySQL的行级锁、表级锁、不同存储引擎的并发控制机制，以及事务隔离对并发行为的影响。"}]}}]}