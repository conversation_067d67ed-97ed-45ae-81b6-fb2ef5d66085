{"name": "Bind Mounts", "trans": ["绑定挂载"], "methods": [{"name": "Local Directory Mount", "trans": ["本地目录挂载"], "usage": {"syntax": "docker run -v <主机路径>:<容器路径> <镜像名>", "description": "将主机上的目录直接挂载到容器内，实现数据实时同步和持久化。", "parameters": [{"name": "-v", "description": "挂载参数。"}, {"name": "<主机路径>", "description": "主机上的目录路径。"}, {"name": "<容器路径>", "description": "容器内的挂载点。"}, {"name": "<镜像名>", "description": "要运行的镜像名称。"}], "returnValue": "无返回值，主机与容器目录实时同步。", "examples": [{"code": "# 挂载主机当前目录到容器/app\ndocker run -v $(pwd):/app nginx", "explanation": "主机当前目录与nginx容器的/app目录实时同步。"}]}}, {"name": "Mount Permission Management", "trans": ["挂载权限管理"], "usage": {"syntax": "docker run -v <主机路径>:<容器路径>:ro <镜像名>", "description": "通过:ro或:rw参数控制挂载目录的只读或读写权限，保障数据安全。", "parameters": [{"name": ":ro", "description": "只读挂载，容器内无法修改主机数据。"}, {"name": ":rw", "description": "读写挂载，容器可修改主机数据（默认）。"}, {"name": "<主机路径>", "description": "主机上的目录路径。"}, {"name": "<容器路径>", "description": "容器内的挂载点。"}, {"name": "<镜像名>", "description": "要运行的镜像名称。"}], "returnValue": "无返回值，挂载权限受控。", "examples": [{"code": "# 只读挂载\ndocker run -v $(pwd):/app:ro nginx", "explanation": "主机目录以只读方式挂载到nginx容器，容器内无法修改。"}]}}, {"name": "Config File Injection", "trans": ["配置文件注入"], "usage": {"syntax": "docker run -v <主机配置文件>:<容器路径/配置文件> <镜像名>", "description": "将主机上的配置文件直接挂载到容器内，实现配置热更新和环境隔离。", "parameters": [{"name": "-v", "description": "挂载参数。"}, {"name": "<主机配置文件>", "description": "主机上的配置文件路径。"}, {"name": "<容器路径/配置文件>", "description": "容器内的目标配置文件路径。"}, {"name": "<镜像名>", "description": "要运行的镜像名称。"}], "returnValue": "无返回值，配置文件可热更新。", "examples": [{"code": "# 挂载主机nginx.conf到容器\ndocker run -v /etc/nginx/nginx.conf:/etc/nginx/nginx.conf nginx", "explanation": "主机上的nginx.conf直接覆盖容器内配置，实现热更新。"}]}}, {"name": "Persistence with Bind Mounts", "trans": ["挂载与容器持久化"], "usage": {"syntax": "docker run -v <主机路径>:<容器路径> <镜像名>", "description": "通过绑定挂载实现数据持久化，容器删除后数据仍保留在主机。", "parameters": [{"name": "-v", "description": "挂载参数。"}, {"name": "<主机路径>", "description": "主机上的目录路径。"}, {"name": "<容器路径>", "description": "容器内的挂载点。"}, {"name": "<镜像名>", "description": "要运行的镜像名称。"}], "returnValue": "无返回值，数据持久化于主机。", "examples": [{"code": "# 持久化数据到主机\ndocker run -v /mydata:/data nginx", "explanation": "nginx容器的数据持久化到主机/mydata目录。"}]}}, {"name": "Assignment: 绑定挂载实践", "trans": ["作业：绑定挂载实践"], "usage": {"syntax": "请完成以下任务：\n1. 挂载主机当前目录到nginx容器/app\n2. 以只读方式挂载配置文件到容器\n3. 持久化nginx日志到主机/logs\n4. 修改主机配置文件并观察容器内变化", "description": "通过实际操作掌握本地目录挂载、权限管理、配置注入和数据持久化。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 1. 挂载当前目录\ndocker run -v $(pwd):/app nginx\n# 2. 只读挂载配置\ndocker run -v /etc/nginx/nginx.conf:/etc/nginx/nginx.conf:ro nginx\n# 3. 持久化日志\ndocker run -v /logs:/var/log/nginx nginx\n# 4. 修改主机配置文件后，容器内配置实时变化", "explanation": "依次完成绑定挂载的核心实践。"}]}}]}