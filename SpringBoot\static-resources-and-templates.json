{"name": "Static Resources and Template Engines", "trans": ["静态资源与模板引擎"], "methods": [{"name": "Static Resource Mapping", "trans": ["静态资源映射"], "usage": {"syntax": "src/main/resources/static/ 目录下的文件自动映射为静态资源", "description": "Spring Boot会自动将static、public、resources、META-INF/resources目录下的文件映射为静态资源，可直接通过URL访问。", "parameters": [{"name": "static目录", "description": "存放静态资源（如js、css、img等）"}, {"name": "访问路径", "description": "http://localhost:8080/资源名"}], "returnValue": "可直接访问的静态资源文件", "examples": [{"code": "// src/main/resources/static/logo.png\n// 访问：http://localhost:8080/logo.png", "explanation": "将图片放入static目录即可通过URL直接访问。"}]}}, {"name": "Common Template Engines (<PERSON><PERSON><PERSON><PERSON><PERSON>, Freemarker)", "trans": ["常用模板引擎（Thymeleaf、Freemarker）"], "usage": {"syntax": "引入模板引擎依赖，模板文件放在templates目录下", "description": "Spring Boot支持Thymeleaf、Freemarker等模板引擎，实现服务端渲染动态页面。模板文件默认放在resources/templates目录。", "parameters": [{"name": "Thymeleaf/Freemarker依赖", "description": "在pom.xml中添加依赖"}, {"name": "templates目录", "description": "存放模板文件（.html/.ftl）"}], "returnValue": "动态渲染的HTML页面", "examples": [{"code": "<!-- pom.xml中添加Thymeleaf依赖 -->\n<dependency>\n  <groupId>org.springframework.boot</groupId>\n  <artifactId>spring-boot-starter-thymeleaf</artifactId>\n</dependency>\n\n// Controller示例\n@Controller\npublic class PageController {\n    @GetMapping(\"/hello\")\n    public String hello(Model model) {\n        model.addAttribute(\"name\", \"张三\");\n        return \"hello\"; // 渲染hello.html\n    }\n}\n\n<!-- resources/templates/hello.html -->\n<html>\n<body>\n  <h1 th:text=\"${name}\"></h1>\n</body>\n</html>", "explanation": "Thymeleaf模板引擎结合Controller动态渲染页面。"}]}}, {"name": "Page Rendering Process", "trans": ["页面渲染流程"], "usage": {"syntax": "Controller返回模板名 -> 模板引擎渲染 -> 返回HTML页面", "description": "请求到达Controller后，返回模板名，模板引擎根据数据模型渲染HTML，最终返回给浏览器。", "parameters": [{"name": "Controller", "description": "处理请求并返回模板名"}, {"name": "模板引擎", "description": "根据数据模型渲染HTML"}], "returnValue": "渲染后的HTML页面", "examples": [{"code": "@Controller\npublic class PageController {\n    @GetMapping(\"/hello\")\n    public String hello(Model model) {\n        model.addAttribute(\"name\", \"张三\");\n        return \"hello\"; // 渲染hello.html\n    }\n}\n// hello.html模板渲染后返回给浏览器。", "explanation": "Controller与模板引擎协作完成页面渲染。"}]}}, {"name": "Static Resource Caching", "trans": ["静态资源缓存"], "usage": {"syntax": "可通过application.properties配置静态资源缓存策略", "description": "Spring Boot支持为静态资源设置缓存头，提升访问性能。可通过配置文件自定义缓存时长。", "parameters": [{"name": "spring.web.resources.cache.period", "description": "设置缓存时长（秒）"}], "returnValue": "带有缓存头的静态资源响应", "examples": [{"code": "# application.properties\nspring.web.resources.cache.period=3600", "explanation": "设置静态资源缓存1小时，提升加载速度。"}]}}, {"name": "Static Resources and Templates Assignment", "trans": ["静态资源与模板引擎练习"], "usage": {"syntax": "# 静态资源与模板引擎练习", "description": "完成以下练习，巩固Spring Boot静态资源与模板引擎相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 在static目录下添加图片并通过URL访问。\n2. 集成Thymeleaf实现动态页面渲染。\n3. 配置静态资源缓存策略。\n4. 理解Controller到页面渲染的完整流程。", "explanation": "通过这些练习掌握Spring Boot静态资源与模板引擎的核心用法。"}]}}]}