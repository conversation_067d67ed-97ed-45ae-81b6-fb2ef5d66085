{"name": "Basic Data Types", "trans": ["基本数据类型"], "methods": [{"name": "Numbers", "trans": ["数字类型"], "usage": {"syntax": "// 整数\nint intValue = 42;\n\n// 浮点数\ndouble doubleValue = 3.14;\n\n// 数字(可以是int或double)\nnum numValue = 10;\nnum anotherNum = 10.5;", "description": "Dart提供三种数字类型：1) `int`：表示整数值，如1、2、-10等，在64位平台上范围为-2^63到2^63-1；2) `double`：表示64位双精度浮点数，如3.14、-0.5等；3) `num`：是int和double的父类，可以同时表示整数或浮点数。数字类型提供各种算术运算方法，如加减乘除、整除、取余等，还有各种数学函数，如最大值、最小值、四舍五入等。在Dart 2.1之后，整数字面量在需要时会自动转换为double类型。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "void main() {\n  // 声明整数\n  int age = 30;\n  int hexValue = 0xEADEBAEE; // 十六进制表示\n  int binary = 0b1010; // 二进制表示\n  \n  // 声明浮点数\n  double pi = 3.14159;\n  double exponent = 1.42e5; // 科学计数法：142000.0\n  double negativeDouble = -18.5;\n  \n  // 使用num类型(可以是int或double)\n  num count = 50; // 整数\n  num price = 49.99; // 浮点数\n  \n  // 整数除法总是返回double\n  double result = 5 / 2; // 结果是2.5\n  \n  // 使用整除操作符获取整数结果\n  int intResult = 5 ~/ 2; // 结果是2\n  \n  // 取余操作\n  int remainder = 5 % 2; // 结果是1\n  \n  // 数学运算\n  int sum = age + 5; // 加法\n  int difference = age - 10; // 减法\n  int product = age * 2; // 乘法\n  \n  // 数字类型的方法\n  print(pi.toStringAsFixed(2)); // 输出：3.14\n  print(pi.round()); // 输出：3 (四舍五入)\n  print(pi.floor()); // 输出：3 (向下取整)\n  print(pi.ceil()); // 输出：4 (向上取整)\n  print(pi.abs()); // 输出：3.14159 (绝对值)\n  \n  // 检查数字类型\n  print(age.runtimeType); // 输出：int\n  print(pi.runtimeType); // 输出：double\n  print(count.runtimeType); // 输出：int\n  print(price.runtimeType); // 输出：double\n  \n  // 数字类型转换\n  double ageAsDouble = age.toDouble(); // int转double\n  int piAsInt = pi.toInt(); // double转int\n  String piAsString = pi.toString(); // double转String\n  \n  // 使用parse方法将字符串转换为数字\n  int parsedInt = int.parse('42');\n  double parsedDouble = double.parse('3.14');\n  \n  // 使用tryParse方法安全转换(转换失败返回null)\n  int? safeInt = int.tryParse('not a number'); // 返回null\n  double? safeDouble = double.tryParse('3.14'); // 返回3.14\n  \n  // 数学运算中的类型转换\n  var result1 = 5 / 2; // 结果是double: 2.5\n  var result2 = 5 * 2.0; // 结果是double: 10.0\n  var result3 = 5 + 2; // 结果是int: 7\n  \n  // 打印结果\n  print('整数: $age');\n  print('浮点数: $pi');\n  print('除法结果: $result');\n  print('整除结果: $intResult');\n  print('取余结果: $remainder');\n}", "explanation": "这个示例展示了Dart中数字类型的声明和使用。展示了如何创建int、double和num类型的变量，以及如何进行基本的算术运算。还展示了数字类型提供的各种方法，如四舍五入、向上/向下取整、格式化输出等。同时展示了数字类型之间的转换方法，以及如何使用parse和tryParse方法将字符串转换为数字。"}]}}, {"name": "Strings", "trans": ["字符串类型"], "usage": {"syntax": "// 使用单引号\nString name = 'Dar<PERSON>';\n\n// 使用双引号\nString greeting = \"Hello\";\n\n// 使用三引号(多行字符串)\nString multiLine = '''\n这是一个\n多行字符串\n''';\n\n// 字符串插值\nString message = 'Hello, $name!';\nString calculation = 'The sum is ${2 + 3}';", "description": "String类型在Dart中用于表示UTF-16编码的字符序列。字符串可以用单引号或双引号创建，两者没有功能区别，但推荐使用单引号作为标准风格。三引号(`'''`或`\"\"\"`)用于创建多行字符串。Dart支持字符串插值，使用`$变量名`插入变量值，使用`${表达式}`插入表达式结果。String类提供丰富的方法进行字符串操作，如连接、分割、替换、查找等。字符串在Dart中是不可变的，任何字符串操作都会创建一个新字符串。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "void main() {\n  // 创建字符串的不同方式\n  String name = 'Dart';\n  String language = \"编程语言\";\n  \n  // 多行字符串\n  String multiLine = '''\n  这是一个多行字符串\n  可以包含多行文本\n  不需要使用\\n转义符\n  ''';\n  \n  // 原始字符串(r前缀，不处理转义字符)\n  String path = r'C:\\Program Files\\Dart';\n  print(path); // 输出: C:\\Program Files\\Dart\n  \n  // 字符串插值\n  String greeting = 'Hello, $name!';\n  print(greeting); // 输出: Hello, Dart!\n  \n  int a = 5;\n  int b = 3;\n  String calculation = '${a} + ${b} = ${a + b}';\n  print(calculation); // 输出: 5 + 3 = 8\n  \n  // 字符串连接\n  String concatenated = name + ' ' + language;\n  print(concatenated); // 输出: Dart 编程语言\n  \n  // 使用相邻字符串自动连接(仅适用于字符串字面量)\n  String adjacent = 'Hello '\n      'World';\n  print(adjacent); // 输出: Hello World\n  \n  // 字符串操作\n  \n  // 1. 获取长度\n  print(name.length); // 输出: 4\n  \n  // 2. 获取字符\n  print(name[0]); // 输出: D\n  \n  // 3. 查找子字符串\n  bool containsDart = greeting.contains('Dart');\n  print(containsDart); // 输出: true\n  \n  int indexOf = greeting.indexOf('Dart');\n  print(indexOf); // 输出: 7\n  \n  // 4. 替换\n  String replaced = greeting.replaceAll('Dart', 'Flutter');\n  print(replaced); // 输出: Hello, Flutter!\n  \n  // 5. 分割字符串\n  String csv = 'apple,banana,orange';\n  List<String> fruits = csv.split(',');\n  print(fruits); // 输出: [apple, banana, orange]\n  \n  // 6. 大小写转换\n  print(name.toLowerCase()); // 输出: dart\n  print(name.toUpperCase()); // 输出: DART\n  \n  // 7. 去除空白\n  String withSpaces = '  hello  ';\n  print(withSpaces.trim()); // 输出: hello\n  print(withSpaces.trimLeft()); // 输出: hello  \n  print(withSpaces.trimRight()); // 输出:   hello\n  \n  // 8. 检查前缀和后缀\n  print(greeting.startsWith('Hello')); // 输出: true\n  print(greeting.endsWith('!')); // 输出: true\n  \n  // 9. 子字符串\n  print(name.substring(1)); // 输出: art\n  print(name.substring(1, 3)); // 输出: ar\n  \n  // 10. 字符串构建器(用于高效构建大型字符串)\n  StringBuffer buffer = StringBuffer();\n  buffer.write('Hello');\n  buffer.write(', ');\n  buffer.write('Dart');\n  buffer.write('!');\n  String result = buffer.toString();\n  print(result); // 输出: Hello, Dart!\n  \n  // 11. 字符串比较\n  String str1 = 'apple';\n  String str2 = 'Apple';\n  print(str1 == str2); // 输出: false (区分大小写)\n  print(str1.toLowerCase() == str2.toLowerCase()); // 输出: true\n  \n  // 12. 字符串插值中使用引号\n  String quote = 'He said, \"Hello\"';\n  String withVariable = 'He said, \"$greeting\"';\n  print(quote); // 输出: He said, \"Hello\"\n  print(withVariable); // 输出: He said, \"Hello, Dart!\"\n}", "explanation": "这个示例展示了Dart中字符串的创建和各种操作。展示了如何使用单引号、双引号和三引号创建字符串，以及原始字符串的使用。还展示了字符串插值、连接、查找、替换、分割等操作，以及字符串比较和字符串构建器的使用。这些操作在文本处理中非常常用，Dart的String类提供了丰富的方法来简化这些操作。"}]}}, {"name": "Booleans", "trans": ["布尔类型"], "usage": {"syntax": "// 布尔类型声明\nbool isValid = true;\nbool isFinished = false;", "description": "布尔(bool)类型在Dart中表示逻辑值，只有两个常量值：`true`和`false`。布尔类型主要用于条件判断、逻辑运算和流程控制。Dart是强类型的，不像JavaScript等语言，Dart不会自动将非布尔值(如null、0、空字符串等)转换为布尔值，必须使用显式的条件表达式。布尔值可以使用逻辑运算符(&&、||、!)进行组合和取反操作。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "void main() {\n  // 布尔变量声明\n  bool isActive = true;\n  bool isDisabled = false;\n  \n  // 条件判断\n  if (isActive) {\n    print('活动状态');\n  } else {\n    print('非活动状态');\n  }\n  \n  // 逻辑运算符\n  bool hasPermission = true;\n  bool isAdmin = false;\n  \n  // 与运算(&&) - 两个条件都为true时结果为true\n  bool canEdit = isActive && hasPermission;\n  print('可以编辑: $canEdit'); // 输出: 可以编辑: true\n  \n  // 或运算(||) - 任一条件为true时结果为true\n  bool canAccess = isAdmin || hasPermission;\n  print('可以访问: $canAccess'); // 输出: 可以访问: true\n  \n  // 非运算(!) - 取反\n  bool isInactive = !isActive;\n  print('非活动: $isInactive'); // 输出: 非活动: false\n  \n  // 复合逻辑表达式\n  bool complexCondition = (isActive && hasPermission) || isAdmin;\n  print('复合条件: $complexCondition'); // 输出: 复合条件: true\n  \n  // 比较运算符返回布尔值\n  int age = 25;\n  bool isAdult = age >= 18;\n  print('是成年人: $isAdult'); // 输出: 是成年人: true\n  \n  String name = 'Dart';\n  bool isDart = name == 'Dart';\n  print('是Dart: $isDart'); // 输出: 是Dart: true\n  \n  // 字符串和集合的方法返回布尔值\n  bool isEmpty = name.isEmpty;\n  bool isNotEmpty = name.isNotEmpty;\n  print('字符串为空: $isEmpty'); // 输出: 字符串为空: false\n  print('字符串不为空: $isNotEmpty'); // 输出: 字符串不为空: true\n  \n  List<int> numbers = [1, 2, 3];\n  bool containsTwo = numbers.contains(2);\n  print('包含数字2: $containsTwo'); // 输出: 包含数字2: true\n  \n  // 注意：Dart不会自动将非布尔值转换为布尔值\n  // 以下代码在Dart中会报错\n  // if (0) { ... }  // 错误: 条件必须是布尔类型\n  // if ('') { ... } // 错误: 条件必须是布尔类型\n  // if (null) { ... } // 错误: 条件必须是布尔类型\n  \n  // 必须使用显式条件\n  var count = 0;\n  if (count == 0) { // 正确\n    print('计数为零');\n  }\n  \n  var text = '';\n  if (text.isEmpty) { // 正确\n    print('文本为空');\n  }\n  \n  // 在Dart 2.0及以上版本中的null安全\n  String? nullableString; // 可空类型，初始值为null\n  \n  // 检查nullableString是否为null\n  if (nullableString == null) { // 正确\n    print('字符串为null');\n  }\n  \n  // 条件表达式(三元运算符)\n  bool condition = true;\n  String result = condition ? '条件为真' : '条件为假';\n  print(result); // 输出: 条件为真\n  \n  // ?? 运算符(空值合并)\n  String? nullable;\n  String nonNullable = nullable ?? '默认值';\n  print(nonNullable); // 输出: 默认值\n}", "explanation": "这个示例展示了Dart中布尔类型的使用方式。首先展示了布尔变量的声明，然后演示了各种逻辑运算符(&&、||、!)的使用。示例还展示了如何在条件语句中使用布尔值，以及如何通过比较运算符和字符串/集合的方法获取布尔值。特别强调了Dart作为强类型语言，不会自动将非布尔值转换为布尔值，必须使用显式条件。最后展示了条件表达式(三元运算符)和空值合并运算符(??)的使用。"}]}}, {"name": "Dynamic and Var", "trans": ["动态类型与变量声明"], "usage": {"syntax": "// 使用var声明变量\nvar name = 'Dart';\nvar age = 30;\n\n// 使用dynamic声明动态类型变量\ndynamic value = 'hello';\nvalue = 42; // 可以改变类型", "description": "Dart提供两种主要的变量声明方式：1) `var`：不指定类型，由初始值推断类型，一旦确定类型后不能更改类型；2) `dynamic`：真正的动态类型，可以在运行时更改变量类型。var是Dart中推荐的变量声明方式，符合静态类型检查，而dynamic则更加灵活但缺少编译时类型检查。在不使用var或显式类型的情况下，Dart默认假定变量类型为dynamic。", "parameters": [{"name": "var", "description": "类型推断变量，初始化后类型固定不变"}, {"name": "dynamic", "description": "动态类型变量，可以在运行时改变类型"}], "returnValue": "无返回值", "examples": [{"code": "void main() {\n  // 使用var声明变量\n  var name = 'Dart'; // 推断为String类型\n  var age = 30; // 推断为int类型\n  var isActive = true; // 推断为bool类型\n  var items = [1, 2, 3]; // 推断为List<int>类型\n  \n  // 使用var声明的变量类型确定后不能改变\n  // name = 42; // 错误：不能将int赋值给String类型\n  \n  // 查看var声明变量的类型\n  print('name类型: ${name.runtimeType}'); // 输出: name类型: String\n  print('age类型: ${age.runtimeType}'); // 输出: age类型: int\n  \n  // 使用dynamic声明变量\n  dynamic value = 'hello';\n  print('value初始类型: ${value.runtimeType}'); // 输出: value初始类型: String\n  \n  // dynamic类型可以改变类型\n  value = 42;\n  print('value现在类型: ${value.runtimeType}'); // 输出: value现在类型: int\n  \n  value = true;\n  print('value现在类型: ${value.runtimeType}'); // 输出: value现在类型: bool\n  \n  // dynamic类型在编译时不检查类型错误\n  // 以下代码在编译时不会报错，但运行时可能会出错\n  dynamic dynamicValue = 'string';\n  // dynamicValue.nonExistentMethod(); // 运行时错误\n  \n  // 未指定类型且未初始化的变量默认为dynamic\n  var uninitializedVar;\n  print('未初始化var类型: ${uninitializedVar.runtimeType}'); // 输出: 未初始化var类型: Null\n  \n  uninitializedVar = 'now it is a string';\n  print('赋值后类型: ${uninitializedVar.runtimeType}'); // 输出: 赋值后类型: String\n  \n  // var与dynamic比较\n  var fixedType = 100;\n  dynamic flexibleType = 100;\n  \n  // fixedType = 'string'; // 错误：不能将String赋值给int\n  flexibleType = 'string'; // 正确：dynamic类型可以改变\n  \n  // 显式类型声明与var对比\n  String explicitString = 'explicit';\n  var inferredString = 'inferred';\n  \n  // 两者效果相同，但var更简洁\n  print('显式类型: ${explicitString.runtimeType}'); // 输出: 显式类型: String\n  print('推断类型: ${inferredString.runtimeType}'); // 输出: 推断类型: String\n  \n  // var在函数中特别有用\n  for (var i = 0; i < 3; i++) {\n    print(i);\n  }\n  \n  // 在复杂类型中使用var\n  var complexMap = <String, List<int>>{\n    'first': [1, 2, 3],\n    'second': [4, 5, 6]\n  };\n  print('复杂类型: ${complexMap.runtimeType}');\n}", "explanation": "这个示例展示了var和dynamic的区别和使用方式。var用于声明类型推断的变量，一旦初始化后类型就固定，不能更改；而dynamic用于声明真正的动态类型变量，可以在运行时改变类型。示例还展示了未初始化的var默认为dynamic类型，以及var与显式类型声明的对比。在Dart编程中，建议优先使用var来保持代码简洁同时享受静态类型检查的好处，只在确实需要动态类型时才使用dynamic。"}]}}, {"name": "Type Inference and Conversion", "trans": ["类型推断与转换"], "usage": {"syntax": "// 类型推断\nvar inferredType = 42; // 推断为int类型\n\n// 类型转换\nint intValue = 42;\ndouble doubleValue = intValue.toDouble(); // int转double\nString stringValue = intValue.toString(); // int转String", "description": "Dart是一种强类型语言，但提供了强大的类型推断能力，使代码更简洁。类型推断发生在变量初始化、函数返回值、集合字面量等多种场景。类型转换则是将一种数据类型转换为另一种类型的过程，Dart提供多种方法进行显式类型转换，如数字类型之间的`toInt()`和`toDouble()`方法，以及通用的`toString()`方法等。在Dart中进行类型转换时，推荐使用显式转换方法而非类型转换运算符，以避免运行时错误。", "parameters": [{"name": "类型推断", "description": "编译器自动分析并确定变量类型的过程"}, {"name": "类型转换", "description": "将数据从一种类型转换为另一种类型的过程"}], "returnValue": "无返回值", "examples": [{"code": "void main() {\n  // 类型推断示例\n  \n  // 1. 变量声明中的类型推断\n  var number = 42; // 推断为int\n  var decimal = 3.14; // 推断为double\n  var text = 'Hello'; // 推断为String\n  var flag = true; // 推断为bool\n  var list = [1, 2, 3]; // 推断为List<int>\n  var map = {'key': 'value'}; // 推断为Map<String, String>\n  \n  print('number类型: ${number.runtimeType}'); // int\n  print('list类型: ${list.runtimeType}'); // List<int>\n  \n  // 2. 函数返回值中的类型推断\n  var result = addNumbers(5, 3); // 推断为int\n  print('addNumbers返回类型: ${result.runtimeType}'); // int\n  \n  // 3. 泛型中的类型推断\n  var numbers = <int>[1, 2, 3]; // 明确指定List<int>\n  var inferred = [1, 2, 3]; // 自动推断为List<int>\n  print('numbers类型: ${numbers.runtimeType}'); // List<int>\n  print('inferred类型: ${inferred.runtimeType}'); // List<int>\n  \n  // 4. 复杂泛型中的类型推断\n  var complexMap = {\n    'numbers': [1, 2, 3],\n    'flags': [true, false]\n  }; // 推断为Map<String, List<Object>>\n  print('complexMap类型: ${complexMap.runtimeType}');\n  \n  // 类型转换示例\n  \n  // 1. 数字类型间的转换\n  int intValue = 10;\n  double doubleValue = 3.14;\n  \n  double intToDouble = intValue.toDouble(); // int转double\n  int doubleToInt = doubleValue.toInt(); // double转int (截断小数部分)\n  \n  print('intToDouble: $intToDouble (${intToDouble.runtimeType})'); // 10.0 (double)\n  print('doubleToInt: $doubleToInt (${doubleToInt.runtimeType})'); // 3 (int)\n  \n  // 2. 数字与字符串间的转换\n  String numAsString = intValue.toString(); // int转String\n  String doubleAsString = doubleValue.toString(); // double转String\n  String formattedDouble = doubleValue.toStringAsFixed(1); // 指定小数位数\n  \n  print('numAsString: $numAsString (${numAsString.runtimeType})'); // \"10\" (String)\n  print('formattedDouble: $formattedDouble'); // \"3.1\"\n  \n  // 从字符串转换为数字\n  int parsedInt = int.parse('42');\n  double parsedDouble = double.parse('3.14');\n  \n  print('parsedInt: $parsedInt (${parsedInt.runtimeType})'); // 42 (int)\n  print('parsedDouble: $parsedDouble (${parsedDouble.runtimeType})'); // 3.14 (double)\n  \n  // 安全的字符串转换（避免异常）\n  int? safeInt = int.tryParse('not a number'); // 返回null\n  print('safeInt: $safeInt'); // null\n  \n  // 3. 集合类型的转换\n  List<int> intList = [1, 2, 3];\n  Set<int> intSet = intList.toSet(); // List转Set\n  List<int> backToList = intSet.toList(); // Set转List\n  \n  print('intSet: $intSet (${intSet.runtimeType})'); // {1, 2, 3} (Set<int>)\n  print('backToList: $backToList (${backToList.runtimeType})'); // [1, 2, 3] (List<int>)\n  \n  // 4. as运算符（类型转换运算符）\n  // 注意：as主要用于类型转换，而非基本类型转换\n  // 以下是演示，实际使用中要谨慎\n  Object obj = 'Hello';\n  String str = obj as String; // 将Object转为String\n  print('str: $str (${str.runtimeType})'); // \"Hello\" (String)\n  \n  // 如果类型不兼容，as会抛出异常\n  // Object wrongType = 42;\n  // String willFail = wrongType as String; // 运行时错误\n  \n  // 5. is运算符（类型检查）\n  Object someValue = 'test';\n  if (someValue is String) {\n    // 在这个范围内，someValue被视为String类型\n    print('长度: ${someValue.length}'); // 可以直接访问String方法\n  }\n}\n\n// 函数示例，用于演示返回值类型推断\naddNumbers(int a, int b) {\n  return a + b; // 返回类型被推断为int\n}", "explanation": "这个示例全面展示了Dart中的类型推断和类型转换。首先展示了类型推断在变量声明、函数返回值和泛型中的应用，让开发者能够编写更简洁的代码同时保持类型安全。然后展示了多种类型转换方法，包括数字类型之间的转换、数字与字符串之间的转换、集合类型的转换，以及as运算符和is运算符的使用。Dart提供了丰富的类型转换机制，使开发者能够安全地处理不同类型的数据。"}]}}, {"name": "Constants (const and final)", "trans": ["常量定义（const与final）"], "usage": {"syntax": "// final常量（运行时常量）\nfinal String name = '<PERSON><PERSON>';\nfinal age = 10;\n\n// const常量（编译时常量）\nconst double pi = 3.14159;\nconst gravity = 9.8;", "description": "Dart提供两种方式声明常量：1) `final`：运行时常量，值在第一次使用时确定，可以使用在运行时计算的值；2) `const`：编译时常量，值必须在编译时确定，不能使用运行时计算的值。两种常量一旦初始化后都不能改变值。const还可以用于创建常量值，即使不将变量声明为const。在常量集合中，const关键字可以应用于构造函数或字面量前，创建不可变集合。", "parameters": [{"name": "final", "description": "运行时常量，初始化后值不可更改，值在运行时确定"}, {"name": "const", "description": "编译时常量，初始化后值不可更改，值在编译时确定"}], "returnValue": "无返回值", "examples": [{"code": "void main() {\n  // final常量\n  final String name = 'Dart';\n  final int age = 10;\n  \n  // final可以使用运行时计算的值\n  final currentTime = DateTime.now(); // 运行时计算\n  final random = getRandomNumber(); // 函数调用的结果\n  \n  // final变量一旦初始化后不能修改\n  // name = 'Flutter'; // 错误：final变量只能设置一次\n  \n  // 声明final变量但延迟初始化\n  final String title;\n  // 在使用前必须初始化\n  title = 'Professor'; // 只能初始化一次\n  // title = 'Doctor'; // 错误：final变量只能设置一次\n  \n  // const常量\n  const double pi = 3.14159;\n  const int maxLoginAttempts = 5;\n  \n  // const必须使用编译时已知的值\n  // const currentDate = DateTime.now(); // 错误：不是编译时常量\n  \n  // 编译时常量表达式\n  const double halfPi = pi / 2;\n  const int squared = maxLoginAttempts * maxLoginAttempts;\n  \n  // 声明常量值（即使变量不是const）\n  var constantValue = const [1, 2, 3]; // 常量列表\n  print(constantValue); // [1, 2, 3]\n  \n  // 变量本身不是常量，可以修改引用\n  constantValue = [4, 5, 6]; // 可以更改引用\n  print(constantValue); // [4, 5, 6]\n  \n  // 但不能修改常量对象本身\n  const constList = [1, 2, 3];\n  // constList.add(4); // 错误：不能修改不可变列表\n  \n  // final与const对比\n  \n  // 1. final可以使用运行时值，const不能\n  final runTime = DateTime.now();\n  // const compileTime = DateTime.now(); // 错误\n  \n  // 2. final对象内容可以改变，const不能\n  final finalList = [1, 2, 3];\n  finalList.add(4); // 正确：可以修改内容\n  print('Final列表修改后: $finalList'); // [1, 2, 3, 4]\n  \n  // 3. 创建常量集合\n  const constMap = {\n    'name': 'Dart',\n    'year': 2011\n  };\n  // constMap['version'] = '2.12'; // 错误：不能修改常量Map\n  \n  // 4. 类中的常量\n  const point = Point(2, 3); // 常量构造函数\n  \n  // 5. 在集合中混合使用const\n  var mixed = [1, 2, const [3, 4]]; // 内部列表是常量\n  mixed[0] = 5; // 可以修改外部列表\n  // mixed[2].add(5); // 错误：不能修改内部常量列表\n  \n  // 6. 字面量前的const\n  var list1 = const [1, 2, 3];\n  var list2 = const [1, 2, 3];\n  print(identical(list1, list2)); // true - 相同的常量值是同一个对象\n  \n  // 7. 没有const的字面量\n  var list3 = [1, 2, 3];\n  var list4 = [1, 2, 3];\n  print(identical(list3, list4)); // false - 不同对象\n  \n  // 打印结果\n  print('Final常量: $name, $age');\n  print('当前时间: $currentTime');\n  print('随机数: $random');\n  print('Const常量: $pi, $maxLoginAttempts');\n  print('派生常量: $halfPi, $squared');\n  print('Point: (${point.x}, ${point.y})');\n}\n\n// 用于演示final的函数\nint getRandomNumber() {\n  return 42; // 假设这是随机生成的\n}\n\n// 常量构造函数示例\nclass Point {\n  final int x;\n  final int y;\n  \n  // 常量构造函数\n  const Point(this.x, this.y);\n}", "explanation": "这个示例详细展示了Dart中两种常量声明方式（const和final）的用法和区别。final是运行时常量，值在运行时确定，可以使用运行时计算的值；const是编译时常量，值必须在编译时确定。两者都不能在初始化后更改值，但final对象的内容可以修改，而const对象完全不可变。示例还展示了const关键字用于创建常量值、常量集合和常量构造函数的用法，以及identical函数验证相同常量值是同一个对象的特性。在Dart编程中，推荐使用final声明不会更改的变量，使用const声明编译时常量和不可变对象。"}]}}, {"name": "Assignment", "trans": ["实践作业"], "usage": {"syntax": "// 基本数据类型实践", "description": "完成以下任务，练习Dart基本数据类型：1) 创建并使用各种数字类型(int, double, num)并进行算术运算；2) 使用字符串创建、操作和格式化；3) 使用布尔类型和逻辑运算符进行条件判断；4) 使用var和dynamic声明变量并观察类型推断；5) 进行不同类型之间的转换；6) 使用final和const创建不同类型的常量。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "void main() {\n  // 1. 数字类型\n  int age = 25;\n  double height = 1.75;\n  num weight = 70;\n  \n  print('年龄: $age岁');\n  print('身高: ${height}m');\n  print('体重: ${weight}kg');\n  \n  // 算术运算\n  double bmi = weight / (height * height);\n  print('BMI指数: ${bmi.toStringAsFixed(2)}');\n  \n  // 2. 字符串操作\n  String firstName = 'John';\n  String lastName = 'Doe';\n  String fullName = '$firstName $lastName';\n  \n  print('全名: $fullName');\n  print('长度: ${fullName.length}');\n  print('大写: ${fullName.toUpperCase()}');\n  print('是否包含\"ohn\": ${fullName.contains(\"ohn\")}');\n  \n  // 3. 布尔类型\n  bool isAdult = age >= 18;\n  bool hasValidHeight = height > 0 && height < 2.5;\n  \n  print('是成年人: $isAdult');\n  print('身高有效: $hasValidHeight');\n  \n  if (isAdult && hasValidHeight) {\n    print('成年人，身高正常');\n  } else if (isAdult) {\n    print('成年人，身高异常');\n  } else {\n    print('未成年人');\n  }\n  \n  // 4. var和dynamic\n  var inferredString = 'Hello';\n  var inferredInt = 42;\n  dynamic dynamicVar = 'World';\n  \n  print('inferredString类型: ${inferredString.runtimeType}');\n  print('inferredInt类型: ${inferredInt.runtimeType}');\n  \n  // dynamic可以改变类型\n  dynamicVar = 100;\n  print('dynamicVar类型: ${dynamicVar.runtimeType}');\n  \n  // 5. 类型转换\n  String ageString = age.toString();\n  int parsedInt = int.parse('42');\n  double parsedDouble = double.parse('3.14');\n  \n  print('ageString: $ageString (${ageString.runtimeType})');\n  print('parsedInt: $parsedInt (${parsedInt.runtimeType})');\n  print('parsedDouble: $parsedDouble (${parsedDouble.runtimeType})');\n  \n  // 6. 常量\n  final currentYear = DateTime.now().year;\n  const double pi = 3.14159;\n  final List<String> names = ['Alice', 'Bob', 'Charlie'];\n  const List<int> primeNumbers = [2, 3, 5, 7, 11];\n  \n  print('当前年份: $currentYear');\n  print('圆周率: $pi');\n  print('名字列表: $names');\n  print('质数列表: $primeNumbers');\n  \n  // 修改final列表内容（允许）\n  names.add('David');\n  print('修改后的名字列表: $names');\n  \n  // 不能修改const列表\n  // primeNumbers.add(13); // 错误：不能修改不可变列表\n}", "explanation": "这个参考实现展示了Dart中基本数据类型的使用。首先使用了数字类型(int, double, num)进行算术运算和BMI计算；然后展示了字符串的创建、拼接、操作和查询；之后使用布尔类型和逻辑运算进行条件判断；演示了var和dynamic的类型推断和类型变化；展示了不同类型之间的转换方法；最后使用final和const创建了不同类型的常量，并演示了它们在可变性上的区别。这个示例涵盖了Dart基本数据类型的主要用法，是一个很好的实践练习。"}]}}]}