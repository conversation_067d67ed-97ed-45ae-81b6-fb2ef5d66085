{"name": "Spring Data JPA", "trans": ["Spring Data JPA"], "methods": [{"name": "JPA Dependency and Configuration", "trans": ["JPA依赖与配置"], "usage": {"syntax": "pom.xml中添加spring-boot-starter-data-jpa依赖，配置数据源参数", "description": "Spring Boot通过spring-boot-starter-data-jpa集成JPA，简化ORM开发，需配置数据源和JPA相关参数。", "parameters": [{"name": "spring-boot-starter-data-jpa", "description": "JPA核心依赖"}, {"name": "spring.datasource.*", "description": "数据库连接参数"}, {"name": "spring.jpa.*", "description": "JPA相关配置"}], "returnValue": "自动配置的JPA环境", "examples": [{"code": "<!-- pom.xml -->\n<dependency>\n  <groupId>org.springframework.boot</groupId>\n  <artifactId>spring-boot-starter-data-jpa</artifactId>\n</dependency>\n\n# application.properties\nspring.datasource.url=********************************************************************************************************************************************************************", "explanation": "引入依赖并配置参数即可使用JPA。"}]}}, {"name": "Entity and Repository", "trans": ["实体类与Repository"], "usage": {"syntax": "@Entity声明实体类，继承JpaRepository定义数据访问接口", "description": "@Entity注解声明数据库表映射，Repository接口继承JpaRepository实现CRUD操作。", "parameters": [{"name": "@Entity", "description": "声明实体类"}, {"name": "@Id", "description": "主键字段"}, {"name": "JpaRepository", "description": "JPA数据访问接口"}], "returnValue": "自动映射的数据库表和数据访问对象", "examples": [{"code": "@Entity\npublic class User {\n    @Id\n    @GeneratedValue\n    private Long id;\n    private String name;\n    // getter/setter\n}\n\npublic interface UserRepository extends JpaRepository<User, Long> {}", "explanation": "实体类与Repository接口配合实现ORM映射和数据操作。"}]}}, {"name": "Common Query Methods", "trans": ["常用查询方法"], "usage": {"syntax": "findByXxx、countByXxx等方法命名规则自动生成SQL", "description": "JpaRepository支持通过方法名自动生成SQL，如findByName、countByEmail等，无需手写SQL。", "parameters": [{"name": "findByXxx", "description": "按字段查询"}, {"name": "countByXxx", "description": "按字段计数"}], "returnValue": "自动生成的查询结果", "examples": [{"code": "List<User> users = userRepository.findByName(\"张三\");\nlong count = userRepository.countByEmail(\"<EMAIL>\");", "explanation": "通过方法名规则自动生成SQL，简化开发。"}]}}, {"name": "Pagination and Sorting", "trans": ["分页与排序"], "usage": {"syntax": "Pageable和Sort参数实现分页与排序", "description": "JpaRepository支持Pageable和Sort参数，轻松实现分页和排序查询。", "parameters": [{"name": "Pageable", "description": "分页参数对象"}, {"name": "Sort", "description": "排序参数对象"}], "returnValue": "分页和排序后的查询结果", "examples": [{"code": "Page<User> page = userRepository.findAll(PageRequest.of(0, 10, Sort.by(\"id\").descending()));", "explanation": "Pageable和Sort可灵活组合，实现复杂查询。"}]}}, {"name": "Custom SQL and @Query", "trans": ["自定义SQL与@Query"], "usage": {"syntax": "@Query注解自定义JPQL或原生SQL", "description": "@Query可在Repository接口方法上自定义JPQL或原生SQL，满足复杂查询需求。", "parameters": [{"name": "@Query", "description": "自定义查询注解"}, {"name": "nativeQuery", "description": "是否使用原生SQL"}], "returnValue": "自定义SQL查询结果", "examples": [{"code": "@Query(\"select u from User u where u.name = ?1\")\nList<User> findByName(String name);\n@Query(value = \"select * from user where email = ?1\", nativeQuery = true)\nUser findByEmail(String email);", "explanation": "@Query支持JPQL和原生SQL，参数用?1、?2等占位。"}]}}, {"name": "Transactional Annotation (@Transactional)", "trans": ["事务注解（@Transactional）"], "usage": {"syntax": "@Transactional注解在Service层实现事务管理", "description": "JPA操作建议在Service层用@Transactional声明事务，保证数据一致性和回滚。", "parameters": [{"name": "@Transactional", "description": "声明事务范围"}], "returnValue": "自动提交或回滚的JPA操作", "examples": [{"code": "@Service\npublic class UserService {\n    @Autowired\n    private UserRepository userRepository;\n    @Transactional\n    public void addUser(User user) {\n        userRepository.save(user);\n        // 发生异常自动回滚\n    }\n}", "explanation": "@Transactional保证JPA操作的原子性和一致性。"}]}}, {"name": "Spring Data JPA Assignment", "trans": ["Spring Data JPA练习"], "usage": {"syntax": "# Spring Data JPA练习", "description": "完成以下练习，巩固Spring Boot JPA相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 配置JPA依赖和参数。\n2. 定义实体类和Repository接口。\n3. 实现常用查询、分页与排序。\n4. 用@Query实现自定义SQL。\n5. 用@Transactional实现JPA事务。", "explanation": "通过这些练习掌握Spring Boot JPA开发的核心用法。"}]}}]}