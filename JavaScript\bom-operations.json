{"name": "BOM Operations", "trans": ["BOM操作"], "methods": [{"name": "Window Object", "trans": ["window对象"], "usage": {"syntax": "window.property;\nwindow.method();\n// 或直接省略window\nproperty;\nmethod();", "description": "window对象是浏览器中的全局对象，代表浏览器窗口。所有全局变量和函数都是window对象的属性和方法，可以直接访问而无需前缀。", "parameters": [], "returnValue": "根据具体属性或方法而定。", "examples": [{"code": "// window对象的基本属性\nconsole.log(window.innerWidth); // 窗口内部宽度\nconsole.log(window.innerHeight); // 窗口内部高度\nconsole.log(window.outerWidth); // 窗口外部宽度（包括边框和工具栏）\nconsole.log(window.outerHeight); // 窗口外部高度\n\n// 窗口位置\nconsole.log(window.screenX || window.screenLeft); // 窗口左上角X坐标\nconsole.log(window.screenY || window.screenTop); // 窗口左上角Y坐标\n\n// 全局变量实际上是window的属性\nvar globalVar = 'hello';\nconsole.log(window.globalVar); // 'hello'\n\n// 全局函数实际上是window的方法\nfunction globalFunc() { return 'world'; }\nconsole.log(window.globalFunc()); // 'world'", "explanation": "展示window对象的基本属性和全局变量/函数与window的关系。"}, {"code": "// 窗口操作\n// 打开新窗口\nconst newWindow = window.open('https://example.com', '_blank', 'width=500,height=400');\n\n// 关闭窗口\n// newWindow.close();\n\n// 调整窗口大小\n// window.resizeTo(800, 600);\n\n// 移动窗口\n// window.moveTo(100, 100);\n\n// 滚动\nwindow.scrollTo(0, 100); // 滚动到指定位置\nwindow.scrollBy(0, 50); // 相对当前位置滚动\n\n// 检测设备\nconst isMobile = window.innerWidth <= 768;\nconsole.log('是否为移动设备:', isMobile);", "explanation": "展示window对象的窗口操作方法，包括打开、关闭、调整大小、移动和滚动。"}]}}, {"name": "Location, History & Navigator", "trans": ["location、history、navigator"], "usage": {"syntax": "// location对象\nwindow.location.href;\nwindow.location.assign(url);\n// history对象\nwindow.history.back();\nwindow.history.forward();\n// navigator对象\nwindow.navigator.userAgent;", "description": "location对象提供当前URL信息和导航方法，history对象管理浏览历史，navigator对象提供浏览器和系统信息。", "parameters": [{"name": "url", "description": "导航目标URL。"}], "returnValue": "根据具体属性或方法而定。", "examples": [{"code": "// location对象\nconsole.log(location.href); // 完整URL\nconsole.log(location.protocol); // 协议（如'http:'）\nconsole.log(location.host); // 主机名+端口\nconsole.log(location.hostname); // 主机名\nconsole.log(location.port); // 端口\nconsole.log(location.pathname); // 路径\nconsole.log(location.search); // 查询字符串\nconsole.log(location.hash); // 片段标识符（#后面的部分）\n\n// 导航方法\n// location.assign('https://example.com'); // 导航到新URL，保留历史记录\n// location.replace('https://example.com'); // 导航到新URL，替换当前历史记录\n// location.reload(); // 重新加载当前页面\n\n// 解析URL参数\nfunction getUrlParams() {\n  const params = {};\n  const queryString = location.search.substring(1);\n  const pairs = queryString.split('&');\n  \n  for (const pair of pairs) {\n    if (pair === '') continue;\n    const [key, value] = pair.split('=');\n    params[decodeURIComponent(key)] = decodeURIComponent(value || '');\n  }\n  \n  return params;\n}\n\nconsole.log(getUrlParams());", "explanation": "展示location对象的属性和方法，以及如何解析URL参数。"}, {"code": "// history对象\nconsole.log(history.length); // 历史记录数量\n\n// 导航方法\n// history.back(); // 后退一页\n// history.forward(); // 前进一页\n// history.go(-2); // 后退两页\n// history.go(1); // 前进一页\n\n// HTML5 History API\n// 添加历史记录，不刷新页面\n// history.pushState({page: 1}, 'Title 1', '/page1');\n\n// 替换当前历史记录，不刷新页面\n// history.replaceState({page: 2}, 'Title 2', '/page2');\n\n// popstate事件，当用户点击前进/后退按钮时触发\nwindow.addEventListener('popstate', function(event) {\n  console.log('状态:', event.state);\n  // 根据state更新UI\n});\n\n// navigator对象\nconsole.log(navigator.userAgent); // 用户代理字符串\nconsole.log(navigator.platform); // 操作系统平台\nconsole.log(navigator.language); // 首选语言\nconsole.log(navigator.languages); // 语言偏好列表\nconsole.log(navigator.onLine); // 是否在线\nconsole.log(navigator.cookieEnabled); // 是否启用cookie\n\n// 检测浏览器\nfunction detectBrowser() {\n  const ua = navigator.userAgent;\n  if (ua.indexOf('Chrome') > -1) return 'Chrome';\n  if (ua.indexOf('Firefox') > -1) return 'Firefox';\n  if (ua.indexOf('Safari') > -1) return 'Safari';\n  if (ua.indexOf('Edge') > -1) return 'Edge';\n  if (ua.indexOf('MSIE') > -1 || ua.indexOf('Trident/') > -1) return 'IE';\n  return 'Unknown';\n}\n\nconsole.log('浏览器:', detectBrowser());", "explanation": "展示history对象的方法和HTML5 History API，以及navigator对象的属性和浏览器检测。"}]}}, {"name": "Local Storage", "trans": ["本地存储"], "usage": {"syntax": "// localStorage\nlocalStorage.setItem('key', 'value');\nconst value = localStorage.getItem('key');\n// sessionStorage\nsessionStorage.setItem('key', 'value');\n// cookie\ndocument.cookie = 'key=value; expires=date; path=/';", "description": "浏览器提供三种主要的客户端存储机制：localStorage（永久存储）、sessionStorage（会话存储）和cookie（可设置过期时间的小型文本数据）。", "parameters": [{"name": "key", "description": "存储项的键名。"}, {"name": "value", "description": "存储项的值。"}], "returnValue": "getItem方法返回存储的字符串值或null。", "examples": [{"code": "// localStorage - 永久存储，除非手动清除\n// 存储数据\nlocalStorage.setItem('username', 'john_doe');\nlocalStorage.setItem('theme', 'dark');\n\n// 读取数据\nconst username = localStorage.getItem('username');\nconsole.log('用户名:', username);\n\n// 删除特定项\nlocalStorage.removeItem('theme');\n\n// 清除所有数据\n// localStorage.clear();\n\n// 存储对象（需要序列化）\nconst user = { id: 1, name: '<PERSON>', role: 'admin' };\nlocalStorage.setItem('user', JSON.stringify(user));\n\n// 读取对象（需要解析）\nconst storedUser = JSON.parse(localStorage.getItem('user'));\nconsole.log('存储的用户:', storedUser);\n\n// sessionStorage - 会话存储，关闭标签页后清除\nsessionStorage.setItem('tempData', 'some value');\nconst tempData = sessionStorage.getItem('tempData');\nconsole.log('临时数据:', tempData);\n\n// 遍历所有存储项\nconsole.log('所有localStorage项:');\nfor (let i = 0; i < localStorage.length; i++) {\n  const key = localStorage.key(i);\n  console.log(key + ':', localStorage.getItem(key));\n}", "explanation": "展示localStorage和sessionStorage的基本用法，包括存储、读取、删除和遍历。"}, {"code": "// Cookie操作\n// 设置cookie\nfunction setCookie(name, value, days) {\n  let expires = '';\n  if (days) {\n    const date = new Date();\n    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));\n    expires = '; expires=' + date.toUTCString();\n  }\n  document.cookie = name + '=' + encodeURIComponent(value) + expires + '; path=/';\n}\n\n// 获取cookie\nfunction getCookie(name) {\n  const nameEQ = name + '=';\n  const ca = document.cookie.split(';');\n  for (let i = 0; i < ca.length; i++) {\n    let c = ca[i];\n    while (c.charAt(0) === ' ') c = c.substring(1, c.length);\n    if (c.indexOf(nameEQ) === 0) return decodeURIComponent(c.substring(nameEQ.length, c.length));\n  }\n  return null;\n}\n\n// 删除cookie\nfunction deleteCookie(name) {\n  setCookie(name, '', -1);\n}\n\n// 使用示例\nsetCookie('user_id', '12345', 7); // 设置7天过期\nsetCookie('last_visit', new Date().toISOString(), 30);\n\nconsole.log('用户ID:', getCookie('user_id'));\nconsole.log('上次访问:', getCookie('last_visit'));\n\n// deleteCookie('user_id'); // 删除cookie", "explanation": "展示cookie的设置、获取和删除方法，包括处理过期时间和编码/解码。"}]}}, {"name": "Dialogs & Timers", "trans": ["弹窗与定时器"], "usage": {"syntax": "// 弹窗\nalert(message);\nconst result = confirm(message);\nconst input = prompt(message, defaultValue);\n// 定时器\nconst timeoutId = setTimeout(callback, delay);\nconst intervalId = setInterval(callback, delay);\nclearTimeout(timeoutId);\nclearInterval(intervalId);", "description": "浏览器提供三种基本弹窗：alert（提示）、confirm（确认）、prompt（输入）。定时器包括setTimeout（延时执行）和setInterval（定期执行）。", "parameters": [{"name": "message", "description": "弹窗显示的消息。"}, {"name": "defaultValue", "description": "prompt弹窗的默认输入值。"}, {"name": "callback", "description": "定时器执行的回调函数。"}, {"name": "delay", "description": "延迟或间隔时间，单位毫秒。"}], "returnValue": "confirm返回布尔值，prompt返回字符串或null，setTimeout和setInterval返回定时器ID。", "examples": [{"code": "// 基本弹窗\n// alert('这是一条提示信息'); // 显示消息\n\n// 确认弹窗\nfunction deleteItem() {\n  const confirmed = confirm('确定要删除此项吗？');\n  if (confirmed) {\n    console.log('用户确认删除');\n    // 执行删除操作\n  } else {\n    console.log('用户取消删除');\n  }\n}\n\n// 输入弹窗\nfunction getUserName() {\n  const name = prompt('请输入您的名字', '访客');\n  if (name !== null) {\n    console.log('欢迎,', name);\n  } else {\n    console.log('用户取消输入');\n  }\n}\n\n// 调用示例\n// deleteItem();\n// getUserName();", "explanation": "展示三种基本弹窗的用法及返回值处理。"}, {"code": "// setTimeout - 延时执行一次\nconst timeoutId = setTimeout(() => {\n  console.log('3秒后执行');\n}, 3000);\n\n// 取消定时器\n// clearTimeout(timeoutId);\n\n// 带参数的setTimeout\nfunction greet(name, role) {\n  console.log(`你好, ${name}! 你是${role}。`);\n}\n\nsetTimeout(greet, 1000, '张三', '管理员');\n\n// setInterval - 定期重复执行\nlet counter = 0;\nconst intervalId = setInterval(() => {\n  counter++;\n  console.log(`计数: ${counter}`);\n  \n  if (counter >= 5) {\n    console.log('停止计数');\n    clearInterval(intervalId);\n  }\n}, 1000);\n\n// 倒计时示例\nfunction countdown(seconds) {\n  let remainingSeconds = seconds;\n  \n  const timerId = setInterval(() => {\n    console.log(remainingSeconds);\n    remainingSeconds--;\n    \n    if (remainingSeconds < 0) {\n      console.log('倒计时结束!');\n      clearInterval(timerId);\n    }\n  }, 1000);\n}\n\n// 开始5秒倒计时\n// countdown(5);", "explanation": "展示setTimeout和setInterval的用法，包括传递参数、清除定时器和实现倒计时。"}]}}, {"name": "作业：本地存储记事本", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 创建简单记事本，使用localStorage存储笔记\n// 2. 实现添加、编辑、删除、列出笔记功能\n// 3. 实现自动保存功能", "description": "通过实现本地存储记事本，综合应用BOM操作、本地存储、定时器等知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 创建HTML结构\n// 2. 实现笔记的CRUD操作\n// 3. 使用localStorage存储笔记\n// 4. 添加自动保存功能", "explanation": "作业提示，需结合本节内容完成实现。"}, {"code": "// 正确实现示例\n// HTML结构\n/*\n<div class=\"notepad\">\n  <h2>本地记事本</h2>\n  <div class=\"note-list\">\n    <h3>我的笔记</h3>\n    <ul id=\"notes-container\"></ul>\n    <button id=\"add-note-btn\">添加新笔记</button>\n  </div>\n  <div class=\"note-editor\">\n    <input type=\"text\" id=\"note-title\" placeholder=\"笔记标题\">\n    <textarea id=\"note-content\" placeholder=\"笔记内容\"></textarea>\n    <div class=\"note-actions\">\n      <span id=\"save-status\">已保存</span>\n      <button id=\"save-note-btn\">保存</button>\n      <button id=\"delete-note-btn\">删除</button>\n    </div>\n  </div>\n</div>\n*/\n\n// JavaScript实现\nclass Notepad {\n  constructor() {\n    this.notes = JSON.parse(localStorage.getItem('notes')) || [];\n    this.currentNoteId = null;\n    this.autoSaveTimer = null;\n    this.autoSaveDelay = 2000; // 2秒后自动保存\n    \n    // DOM元素\n    this.notesContainer = document.getElementById('notes-container');\n    this.titleInput = document.getElementById('note-title');\n    this.contentInput = document.getElementById('note-content');\n    this.saveStatusSpan = document.getElementById('save-status');\n    \n    // 绑定事件\n    document.getElementById('add-note-btn').addEventListener('click', () => this.createNewNote());\n    document.getElementById('save-note-btn').addEventListener('click', () => this.saveCurrentNote());\n    document.getElementById('delete-note-btn').addEventListener('click', () => this.deleteCurrentNote());\n    \n    // 输入事件 - 触发自动保存\n    this.titleInput.addEventListener('input', () => this.startAutoSave());\n    this.contentInput.addEventListener('input', () => this.startAutoSave());\n    \n    // 初始化\n    this.renderNotesList();\n    if (this.notes.length > 0) {\n      this.openNote(this.notes[0].id);\n    } else {\n      this.createNewNote();\n    }\n  }\n  \n  // 渲染笔记列表\n  renderNotesList() {\n    this.notesContainer.innerHTML = '';\n    \n    this.notes.forEach(note => {\n      const li = document.createElement('li');\n      li.textContent = note.title || '无标题笔记';\n      li.dataset.id = note.id;\n      \n      if (note.id === this.currentNoteId) {\n        li.classList.add('active');\n      }\n      \n      li.addEventListener('click', () => this.openNote(note.id));\n      this.notesContainer.appendChild(li);\n    });\n  }\n  \n  // 创建新笔记\n  createNewNote() {\n    // 先保存当前笔记\n    if (this.currentNoteId) {\n      this.saveCurrentNote();\n    }\n    \n    const newNote = {\n      id: Date.now().toString(),\n      title: '',\n      content: '',\n      createdAt: new Date().toISOString()\n    };\n    \n    this.notes.unshift(newNote);\n    this.saveToLocalStorage();\n    this.renderNotesList();\n    this.openNote(newNote.id);\n  }\n  \n  // 打开笔记\n  openNote(id) {\n    const note = this.notes.find(note => note.id === id);\n    if (!note) return;\n    \n    this.currentNoteId = id;\n    this.titleInput.value = note.title;\n    this.contentInput.value = note.content;\n    this.renderNotesList();\n    this.saveStatusSpan.textContent = '已保存';\n  }\n  \n  // 保存当前笔记\n  saveCurrentNote() {\n    if (!this.currentNoteId) return;\n    \n    const noteIndex = this.notes.findIndex(note => note.id === this.currentNoteId);\n    if (noteIndex === -1) return;\n    \n    this.notes[noteIndex].title = this.titleInput.value;\n    this.notes[noteIndex].content = this.contentInput.value;\n    this.notes[noteIndex].updatedAt = new Date().toISOString();\n    \n    this.saveToLocalStorage();\n    this.renderNotesList();\n    this.saveStatusSpan.textContent = '已保存';\n    \n    // 清除自动保存定时器\n    if (this.autoSaveTimer) {\n      clearTimeout(this.autoSaveTimer);\n      this.autoSaveTimer = null;\n    }\n  }\n  \n  // 删除当前笔记\n  deleteCurrentNote() {\n    if (!this.currentNoteId) return;\n    \n    if (confirm('确定要删除这个笔记吗？')) {\n      this.notes = this.notes.filter(note => note.id !== this.currentNoteId);\n      this.saveToLocalStorage();\n      \n      if (this.notes.length > 0) {\n        this.openNote(this.notes[0].id);\n      } else {\n        this.currentNoteId = null;\n        this.titleInput.value = '';\n        this.contentInput.value = '';\n        this.createNewNote();\n      }\n      \n      this.renderNotesList();\n    }\n  }\n  \n  // 启动自动保存\n  startAutoSave() {\n    this.saveStatusSpan.textContent = '正在编辑...';\n    \n    if (this.autoSaveTimer) {\n      clearTimeout(this.autoSaveTimer);\n    }\n    \n    this.autoSaveTimer = setTimeout(() => {\n      this.saveCurrentNote();\n    }, this.autoSaveDelay);\n  }\n  \n  // 保存到localStorage\n  saveToLocalStorage() {\n    localStorage.setItem('notes', JSON.stringify(this.notes));\n  }\n}\n\n// 初始化应用\ndocument.addEventListener('DOMContentLoaded', () => {\n  const notepad = new Notepad();\n});", "explanation": "完整实现本地存储记事本，包含添加、编辑、删除、列出笔记功能，并使用localStorage存储数据，实现自动保存功能。"}]}}]}