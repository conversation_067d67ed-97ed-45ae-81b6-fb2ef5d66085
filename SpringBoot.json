{"topics": [{"id": "getting-started", "name": "入门与环境搭建", "file": "SpringBoot/getting-started.json", "description": "Spring Boot的基础介绍、版本选择、开发环境搭建、首个项目、目录结构与启动原理，适合初学者快速上手。"}, {"id": "configuration-management", "name": "配置管理", "file": "SpringBoot/configuration-management.json", "description": "Spring Boot配置文件格式、优先级、外部化、Profile多环境、热更新、类型安全与加密等配置管理核心知识。"}, {"id": "auto-configuration", "name": "自动配置原理", "file": "SpringBoot/auto-configuration.json", "description": "Spring Boot自动配置注解、自动装配机制、条件装配、自定义Starter与禁用排除自动配置等核心原理。"}, {"id": "restful-api", "name": "RESTful API开发", "file": "SpringBoot/restful-api.json", "description": "Spring Boot RESTful控制器、请求方法、参数绑定、请求体响应体、JSON序列化、异常与全局异常处理等核心API开发知识。"}, {"id": "static-resources-and-templates", "name": "静态资源与模板引擎", "file": "SpringBoot/static-resources-and-templates.json", "description": "Spring Boot静态资源映射、模板引擎集成、页面渲染流程与静态资源缓存等服务端渲染与前端资源管理知识。"}, {"id": "cors-and-file-upload", "name": "跨域与文件上传", "file": "SpringBoot/cors-and-file-upload.json", "description": "Spring Boot CORS跨域配置、文件上传与下载、Multipart参数配置等前后端交互与文件处理核心知识。"}, {"id": "interceptor-and-filter", "name": "拦截器与过滤器", "file": "SpringBoot/interceptor-and-filter.json", "description": "Spring Boot拦截器与过滤器、全局请求日志、参数校验等请求处理与安全控制核心知识。"}, {"id": "datasource-and-jdbc", "name": "数据源与JDBC", "file": "SpringBoot/datasource-and-jdbc.json", "description": "Spring Boot数据源配置、多数据源管理、JdbcTemplate用法与事务管理等数据库操作核心知识。"}, {"id": "spring-data-jpa", "name": "Spring Data JPA", "file": "SpringBoot/spring-data-jpa.json", "description": "Spring Boot JPA依赖配置、实体与Repository、常用查询、分页排序、自定义SQL与事务注解等ORM开发核心知识。"}, {"id": "mybatis-integration", "name": "MyBatis集成", "file": "SpringBoot/mybatis-integration.json", "description": "Spring Boot MyBatis依赖配置、Mapper接口与XML、动态SQL、分页插件、多表关联查询等ORM集成核心知识。"}, {"id": "redis-and-cache", "name": "Redis与缓存", "file": "SpringBoot/redis-and-cache.json", "description": "Spring Boot Redis集成与配置、常用数据结构操作、缓存注解、分布式锁与缓存雪崩防护等高性能缓存核心知识。"}, {"id": "spring-security", "name": "Spring Security", "file": "SpringBoot/spring-security.json", "description": "Spring Boot Security依赖配置、用户认证与授权、自定义登录登出、权限注解、密码加密、会话管理与CSRF防护等安全开发核心知识。"}, {"id": "oauth2-and-jwt", "name": "OAuth2与JWT", "file": "SpringBoot/oauth2-and-jwt.json", "description": "Spring Boot OAuth2授权模式、JWT原理与集成、单点登录、Token刷新与失效等分布式认证与安全核心知识。"}, {"id": "microservice-communication", "name": "微服务通信", "file": "SpringBoot/microservice-communication.json", "description": "Spring Boot RestTemplate客户端、WebClient响应式客户端、Feign声明式客户端、服务调用最佳实践等微服务通信核心知识。"}, {"id": "message-queue", "name": "消息队列集成", "file": "SpringBoot/message-queue.json", "description": "Spring Boot集成RabbitMQ、Kafka、ActiveMQ，消息可靠性与事务等主流消息队列核心知识。"}, {"id": "spring-cloud-basic", "name": "Spring Cloud基础", "file": "SpringBoot/spring-cloud-basic.json", "description": "Spring Cloud服务注册与发现、负载均衡、配置中心、熔断降级、网关、分布式链路追踪等微服务基础能力。"}, {"id": "async-and-schedule", "name": "异步任务与定时任务", "file": "SpringBoot/async-and-schedule.json", "description": "Spring Boot @Async异步方法、线程池配置、@Scheduled定时任务、分布式定时任务（xxl-job）等任务调度核心知识。"}, {"id": "unit-test", "name": "单元测试", "file": "SpringBoot/unit-test.json", "description": "Spring Boot JUnit5集成、MockMvc测试Web接口、Mockito/MockBean用法、数据库测试、参数化测试等单元测试核心知识。"}, {"id": "integration-test", "name": "集成测试", "file": "SpringBoot/integration-test.json", "description": "Spring Boot @SpringBootTest用法、测试环境隔离、测试数据准备、容器化测试（Testcontainers）等集成测试核心知识。"}, {"id": "package-and-deploy", "name": "打包与部署", "file": "SpringBoot/package-and-deploy.json", "description": "Spring Boot Maven/Gradle打包、可执行JAR/WAR、外部配置与环境变量、日志管理、进程管理等部署核心知识。"}, {"id": "container-and-cloudnative", "name": "容器化与云原生", "file": "SpringBoot/container-and-cloudnative.json", "description": "Spring Boot Dockerfile编写、镜像构建与推送、K8s部署与配置、配置中心与密钥管理、健康检查与自动重启等云原生核心知识。"}, {"id": "monitor-and-alert", "name": "监控与告警", "file": "SpringBoot/monitor-and-alert.json", "description": "Spring Boot Actuator健康检查、自定义监控指标、Prometheus/Grafana集成、日志采集与分析、告警通知等监控核心知识。"}, {"id": "performance-tuning", "name": "性能调优", "file": "SpringBoot/performance-tuning.json", "description": "Spring Boot启动速度优化、内存与GC调优、数据库性能优化、连接池优化、缓存优化、热点数据与接口优化等性能调优核心知识。"}, {"id": "best-practices", "name": "代码与架构最佳实践", "file": "SpringBoot/best-practices.json", "description": "Spring Boot分层架构设计、领域驱动设计、统一异常与日志处理、配置与代码分离、依赖注入与解耦、安全防护、版本升级等最佳实践。"}, {"id": "spring-ecosystem", "name": "常用Spring生态组件", "file": "SpringBoot/spring-ecosystem.json", "description": "Spring Boot Admin、Spring Batch、Spring Session、Spring AMQP、WebFlux、Native等常用生态组件用法。"}, {"id": "project-practice", "name": "项目实战与案例", "file": "SpringBoot/project-practice.json", "description": "Spring Boot多模块项目结构、典型业务实战、故障排查、真实部署流程、代码规范与文档等项目案例。"}]}