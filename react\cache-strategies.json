{"name": "Cache Strategies", "trans": ["缓存策略"], "methods": [{"name": "Data Caching", "trans": ["数据缓存"], "usage": {"syntax": "const cache = new Map();\ncache.set(key, value);\nconst value = cache.get(key);", "description": "数据缓存是将常用数据存储在内存、全局变量或专用缓存对象中，减少重复计算和请求，提升性能。常用Map、对象、第三方库等实现。", "parameters": [{"name": "key", "description": "缓存项的唯一标识"}, {"name": "value", "description": "需要缓存的数据"}], "returnValue": "返回缓存的数据或undefined。", "examples": [{"code": "// 基于Map的数据缓存\nconst cache = new Map();\nfunction getData(key) {\n  if (cache.has(key)) {\n    return cache.get(key); // 命中缓存\n  }\n  const value = computeExpensiveValue(key);\n  cache.set(key, value);\n  return value;\n}", "explanation": "通过Map对象缓存计算结果，避免重复计算。"}]}}, {"name": "Request Caching", "trans": ["请求缓存"], "usage": {"syntax": "const cache = {};\nasync function fetchWithCache(url) {\n  if (cache[url]) return cache[url];\n  const resp = await fetch(url);\n  const data = await resp.json();\n  cache[url] = data;\n  return data;\n}", "description": "请求缓存用于存储接口请求结果，避免重复请求同一资源，常用于数据获取、分页、搜索等场景。", "parameters": [{"name": "url", "description": "请求的唯一标识或地址"}], "returnValue": "返回缓存的请求结果。", "examples": [{"code": "// 简单请求缓存\nconst cache = {};\nasync function fetchWithCache(url) {\n  if (cache[url]) return cache[url];\n  const resp = await fetch(url);\n  const data = await resp.json();\n  cache[url] = data;\n  return data;\n}\n// 使用\nfetchWithCache('/api/user').then(data => ...);", "explanation": "请求结果缓存到对象，后续相同请求直接返回缓存。"}]}}, {"name": "Component Caching", "trans": ["组件缓存"], "usage": {"syntax": "// 组件缓存思路：\n// 1. 用React.memo缓存组件渲染结果\n// 2. 用keep-alive等库缓存组件状态\n// 3. 用localStorage/sessionStorage持久化组件数据", "description": "组件缓存通过记忆化、状态保存等方式，避免组件重复渲染和状态丢失，提升页面切换和多标签体验。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// React.memo缓存组件渲染\nconst MemoComponent = React.memo(MyComponent);\n\n// keep-alive实现组件缓存（以react-activation为例）\nimport { KeepAlive } from 'react-activation';\n<KeepAlive><MyComponent /></KeepAlive>;\n\n// localStorage持久化组件状态\nuseEffect(() => {\n  localStorage.setItem('form', JSON.stringify(form));\n}, [form]);", "explanation": "通过多种方式缓存组件渲染和状态，提升用户体验。"}]}}, {"name": "Computation Result Caching", "trans": ["计算结果缓存"], "usage": {"syntax": "const result = useMemo(() => computeExpensive(), [deps]);", "description": "计算结果缓存通过useMemo、memoization等手段缓存函数计算结果，避免重复计算，常用于复杂计算、过滤、排序等。", "parameters": [{"name": "computeExpensive", "description": "需要缓存的计算函数"}, {"name": "deps", "description": "依赖项数组，变化时重新计算"}], "returnValue": "返回缓存的计算结果。", "examples": [{"code": "// useMemo缓存计算结果\nconst filtered = useMemo(() => items.filter(i => i.active), [items]);", "explanation": "只有items变化时才重新计算filtered，提升性能。"}]}}, {"name": "Cache Invalidation Strategies", "trans": ["缓存失效策略"], "usage": {"syntax": "// 缓存失效思路：\n// 1. 设置过期时间\n// 2. 主动清理缓存\n// 3. 依赖变化时失效", "description": "缓存失效策略用于保证数据新鲜度和一致性，常见方式有TTL（过期时间）、手动清理、依赖变化等。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 过期时间失效\nconst cache = new Map();\nfunction setCache(key, value, ttl = 60000) {\n  cache.set(key, { value, expire: Date.now() + ttl });\n}\nfunction getCache(key) {\n  const entry = cache.get(key);\n  if (!entry) return undefined;\n  if (Date.now() > entry.expire) {\n    cache.delete(key);\n    return undefined;\n  }\n  return entry.value;\n}", "explanation": "通过设置过期时间自动失效，保证缓存数据新鲜。"}]}}, {"name": "Persistent Caching", "trans": ["持久化缓存"], "usage": {"syntax": "localStorage.setItem(key, JSON.stringify(data));\nconst data = JSON.parse(localStorage.getItem(key));", "description": "持久化缓存将数据存储到localStorage、IndexedDB等本地存储，页面刷新或关闭后依然可用，适合长期缓存。", "parameters": [{"name": "key", "description": "本地存储的唯一标识"}, {"name": "data", "description": "需要持久化的数据"}], "returnValue": "返回本地存储的数据。", "examples": [{"code": "// localStorage持久化缓存\nfunction saveData(key, data) {\n  localStorage.setItem(key, JSON.stringify(data));\n}\nfunction loadData(key) {\n  const str = localStorage.getItem(key);\n  return str ? JSON.parse(str) : null;\n}", "explanation": "通过localStorage保存和读取数据，实现持久化缓存。"}]}}, {"name": "作业：实现多种缓存策略", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 实现数据缓存和请求缓存\n// 2. 用useMemo缓存计算结果\n// 3. 支持缓存失效和持久化\n// 4. 分析不同缓存策略的适用场景", "description": "通过实践多种缓存策略，掌握数据、请求、组件、计算结果等缓存的实现与优化。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 用Map或对象实现数据缓存\n// 2. 用对象缓存请求结果\n// 3. 用useMemo缓存复杂计算\n// 4. 用localStorage实现持久化\n// 5. 设置过期时间实现失效", "explanation": "作业提示，学生需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nimport React, { useMemo, useState, useEffect } from 'react';\n// 1. 数据缓存\nconst dataCache = new Map();\nfunction getData(key) {\n  if (dataCache.has(key)) return dataCache.get(key);\n  const value = key + '-data';\n  dataCache.set(key, value);\n  return value;\n}\n// 2. 请求缓存\nconst reqCache = {};\nasync function fetchWithCache(url) {\n  if (reqCache[url]) return reqCache[url];\n  const resp = await fetch(url);\n  const data = await resp.json();\n  reqCache[url] = data;\n  return data;\n}\n// 3. 计算结果缓存\nfunction Expensive({ n }) {\n  const result = useMemo(() => n * n, [n]);\n  return <div>平方: {result}</div>;\n}\n// 4. 持久化缓存\nfunction save(key, data) {\n  localStorage.setItem(key, JSON.stringify(data));\n}\nfunction load(key) {\n  const str = localStorage.getItem(key);\n  return str ? JSON.parse(str) : null;\n}\n// 5. 缓存失效\nfunction setCache(key, value, ttl = 10000) {\n  reqCache[key] = { value, expire: Date.now() + ttl };\n}\nfunction getCache(key) {\n  const entry = reqCache[key];\n  if (!entry) return undefined;\n  if (Date.now() > entry.expire) { delete reqCache[key]; return undefined; }\n  return entry.value;\n}\nexport default function App() {\n  // ...综合使用上述缓存...\n  return <div>缓存策略综合演示</div>;\n}", "explanation": "完整实现了多种缓存策略，涵盖数据、请求、计算、持久化和失效。"}]}}]}