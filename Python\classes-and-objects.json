{"name": "Classes and Objects", "trans": ["类与对象"], "methods": [{"name": "Class Definition", "trans": ["类的定义"], "usage": {"syntax": "class 类名:\n    代码块", "description": "使用class关键字定义类，类名通常首字母大写。类体可包含属性和方法。", "parameters": [{"name": "类名", "description": "自定义的类名称"}], "returnValue": "类对象", "examples": [{"code": "class Person:\n    pass\nprint(Person)  # <class '__main__.Person'>", "explanation": "演示了最简单的类定义。"}]}}, {"name": "Instance and Attributes", "trans": ["实例与属性"], "usage": {"syntax": "对象 = 类名()\n对象.属性 = 值", "description": "通过类名()创建实例，可动态添加属性。属性用于存储对象的状态。", "parameters": [{"name": "对象", "description": "类的实例对象"}, {"name": "属性", "description": "对象的状态信息"}], "returnValue": "实例对象", "examples": [{"code": "class Dog:\n    pass\nd = Dog()\nd.name = '旺财'\nprint(d.name)  # 旺财", "explanation": "演示了实例的创建和属性的赋值。"}]}}, {"name": "Methods and self", "trans": ["方法与self"], "usage": {"syntax": "def 方法名(self, ...):\n    代码块", "description": "类中定义的方法第一个参数必须是self，代表实例本身。通过self访问属性和其他方法。", "parameters": [{"name": "self", "description": "实例自身的引用"}], "returnValue": "方法的返回值，若无return则为None", "examples": [{"code": "class Cat:\n    def speak(self):\n        print('喵')\nc = Cat()\nc.speak()  # 喵", "explanation": "演示了方法定义和self的用法。"}]}}, {"name": "Constructor (__init__)", "trans": ["构造方法__init__"], "usage": {"syntax": "def __init__(self, ...):\n    代码块", "description": "__init__是构造方法，创建实例时自动调用，用于初始化属性。", "parameters": [{"name": "self", "description": "实例自身的引用"}, {"name": "...", "description": "初始化参数"}], "returnValue": "无返回值", "examples": [{"code": "class Student:\n    def __init__(self, name):\n        self.name = name\ns = Student('小明')\nprint(s.name)  # 小明", "explanation": "演示了__init__方法的用法。"}]}}, {"name": "Class Attributes and Instance Attributes", "trans": ["类属性与实例属性"], "usage": {"syntax": "class 类名:\n    类属性 = 值\n    def __init__(self):\n        self.实例属性 = 值", "description": "类属性属于类本身，所有实例共享。实例属性属于每个对象，互不影响。", "parameters": [{"name": "类属性", "description": "定义在类体内的属性"}, {"name": "实例属性", "description": "定义在__init__中的属性"}], "returnValue": "属性值", "examples": [{"code": "class Car:\n    wheels = 4  # 类属性\n    def __init__(self, color):\n        self.color = color  # 实例属性\nc1 = Car('red')\nc2 = Car('blue')\nprint(Car.wheels, c1.color, c2.color)  # 4 red blue", "explanation": "演示了类属性和实例属性的区别。"}]}}, {"name": "Class Method and Static Method", "trans": ["类方法与静态方法"], "usage": {"syntax": "@classmethod\ndef 方法名(cls, ...):\n    代码块\n@staticmethod\ndef 方法名(...):\n    代码块", "description": "类方法用@classmethod装饰，第一个参数是cls。静态方法用@staticmethod装饰，无默认参数。", "parameters": [{"name": "cls", "description": "类本身的引用（类方法）"}], "returnValue": "方法的返回值，若无return则为None", "examples": [{"code": "class Tool:\n    count = 0\n    @classmethod\n    def show_count(cls):\n        print(cls.count)\n    @staticmethod\n    def add(a, b):\n        return a + b\nTool.show_count()\nprint(Tool.add(1, 2))  # 3", "explanation": "演示了类方法和静态方法的定义和调用。"}]}}, {"name": "Magic Methods (__str__, __repr__, etc.)", "trans": ["魔术方法（__str__、__repr__等）"], "usage": {"syntax": "def __str__(self):\n    return 字符串\ndef __repr__(self):\n    return 字符串", "description": "魔术方法是特殊命名的方法，如__str__、__repr__等，用于定制对象行为。", "parameters": [{"name": "self", "description": "实例自身的引用"}], "returnValue": "方法的返回值，通常为字符串或特定类型", "examples": [{"code": "class Book:\n    def __init__(self, title):\n        self.title = title\n    def __str__(self):\n        return f'书名：{self.title}'\n    def __repr__(self):\n        return f'Book({self.title!r})'\nb = Book('Python')\nprint(str(b))    # 书名：Python\nprint(repr(b))  # Book('Python')", "explanation": "演示了__str__和__repr__魔术方法的用法。"}]}}, {"name": "Classes and Objects Assignment", "trans": ["类与对象练习"], "usage": {"syntax": "# 类与对象练习", "description": "完成以下练习，巩固类与对象相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 定义一个Person类，包含姓名和年龄属性。\n2. 给Person类添加一个say_hello方法，输出自我介绍。\n3. 定义一个类属性和实例属性，并演示区别。\n4. 实现__str__方法美化对象输出。", "explanation": "练习要求动手实践类定义、属性、方法、构造方法、魔术方法等。"}]}}]}