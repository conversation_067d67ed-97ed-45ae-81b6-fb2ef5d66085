{"name": "JavaScript Events", "trans": ["JavaScript事件"], "methods": [{"name": "Mouse Events", "trans": ["鼠标事件"], "usage": {"syntax": "element.addEventListener('eventName', callback)", "description": "鼠标事件用于监听用户与页面元素之间的鼠标交互，包括点击、移动、悬停等操作。", "parameters": [{"name": "eventName", "description": "事件名称，如'click'、'mouseover'等"}, {"name": "callback", "description": "事件触发时执行的回调函数，接收event对象作为参数"}], "returnValue": "无返回值", "examples": [{"code": "// 为按钮添加点击事件\nconst button = document.querySelector('#myButton');\nbutton.addEventListener('click', function(event) {\n  // event.target 指向触发事件的元素\n  console.log('按钮被点击了!');\n  // 阻止默认行为\n  event.preventDefault();\n});\n\n// 监听鼠标移动\ndocument.addEventListener('mousemove', function(event) {\n  // event.clientX 和 event.clientY 包含鼠标指针的坐标\n  console.log(`鼠标位置: X=${event.clientX}, Y=${event.clientY}`);\n});", "explanation": "这个示例展示了如何为按钮添加点击事件监听器，以及如何监听整个文档上的鼠标移动事件。event对象包含了事件的详细信息，如触发事件的元素和鼠标坐标。"}]}}, {"name": "Keyboard Events", "trans": ["键盘事件"], "usage": {"syntax": "element.addEventListener('eventName', callback)", "description": "键盘事件用于监听用户的键盘输入，可以检测按键的按下和释放。", "parameters": [{"name": "eventName", "description": "事件名称，如'keydown'、'keyup'等"}, {"name": "callback", "description": "事件触发时执行的回调函数，接收event对象作为参数"}], "returnValue": "无返回值", "examples": [{"code": "// 监听键盘按下事件\ndocument.addEventListener('keydown', function(event) {\n  // event.key 包含按下的键名\n  // event.keyCode 包含键码（已弃用）\n  console.log(`按下的键: ${event.key}`);\n  \n  // 检测特定按键\n  if (event.key === 'Enter') {\n    console.log('按下了回车键!');\n  }\n  \n  // 检测组合键\n  if (event.ctrlKey && event.key === 's') {\n    console.log('按下了Ctrl+S组合键!');\n    event.preventDefault(); // 阻止浏览器默认的保存行为\n  }\n});", "explanation": "这个示例展示了如何监听键盘按下事件，以及如何检测特定按键和组合键。通过event.key可以获取按下的键名，event.ctrlKey、event.shiftKey等属性可以检测修饰键是否被按下。"}]}}, {"name": "Form Events", "trans": ["表单事件"], "usage": {"syntax": "formElement.addEventListener('eventName', callback)", "description": "表单事件用于监听表单元素的状态变化，如提交、重置、输入、焦点变化等。", "parameters": [{"name": "eventName", "description": "事件名称，如'submit'、'change'、'input'等"}, {"name": "callback", "description": "事件触发时执行的回调函数，接收event对象作为参数"}], "returnValue": "无返回值", "examples": [{"code": "// 监听表单提交事件\nconst form = document.querySelector('#myForm');\nform.addEventListener('submit', function(event) {\n  // 阻止表单默认提交行为\n  event.preventDefault();\n  \n  // 获取表单数据\n  const formData = new FormData(form);\n  console.log('表单数据:', Object.fromEntries(formData));\n  \n  // 进行表单验证\n  const email = formData.get('email');\n  if (!email.includes('@')) {\n    console.error('邮箱格式不正确!');\n    return;\n  }\n  \n  // 提交表单\n  console.log('表单验证通过，准备提交...');\n});\n\n// 监听输入变化\nconst input = document.querySelector('#username');\ninput.addEventListener('input', function(event) {\n  // 实时获取输入值\n  console.log('当前输入:', event.target.value);\n});\n\n// 监听选择框变化\nconst select = document.querySelector('#country');\nselect.addEventListener('change', function(event) {\n  console.log('选择了:', event.target.value);\n});", "explanation": "这个示例展示了如何监听表单提交事件并进行表单验证，以及如何监听输入框和选择框的值变化。通过FormData可以方便地获取表单数据，通过event.target.value可以获取输入元素的当前值。"}]}}, {"name": "Touch Events", "trans": ["触摸与手势事件"], "usage": {"syntax": "element.addEventListener('eventName', callback)", "description": "触摸事件用于监听移动设备上的触摸操作，如点击、滑动、捏合等。这些事件在移动端开发中非常重要。", "parameters": [{"name": "eventName", "description": "事件名称，如'touchstart'、'touchmove'、'touchend'等"}, {"name": "callback", "description": "事件触发时执行的回调函数，接收event对象作为参数"}], "returnValue": "无返回值", "examples": [{"code": "// 监听触摸开始事件\nconst touchArea = document.querySelector('#touchArea');\ntouchArea.addEventListener('touchstart', function(event) {\n  // 阻止默认行为（如滚动）\n  event.preventDefault();\n  \n  // touches 包含当前所有触摸点\n  const touch = event.touches[0];\n  console.log(`触摸开始: X=${touch.clientX}, Y=${touch.clientY}`);\n});\n\n// 监听触摸移动事件\ntouchArea.addEventListener('touchmove', function(event) {\n  // 获取第一个触摸点\n  const touch = event.touches[0];\n  console.log(`触摸移动: X=${touch.clientX}, Y=${touch.clientY}`);\n  \n  // 实现简单的拖拽效果\n  touchArea.style.left = touch.clientX + 'px';\n  touchArea.style.top = touch.clientY + 'px';\n});\n\n// 实现简单的滑动检测\nlet startX, startY;\ntouchArea.addEventListener('touchstart', function(event) {\n  startX = event.touches[0].clientX;\n  startY = event.touches[0].clientY;\n});\n\ntouchArea.addEventListener('touchend', function(event) {\n  const endX = event.changedTouches[0].clientX;\n  const endY = event.changedTouches[0].clientY;\n  \n  const diffX = endX - startX;\n  const diffY = endY - startY;\n  \n  // 判断滑动方向\n  if (Math.abs(diffX) > Math.abs(diffY)) {\n    // 水平滑动\n    if (diffX > 0) {\n      console.log('向右滑动');\n    } else {\n      console.log('向左滑动');\n    }\n  } else {\n    // 垂直滑动\n    if (diffY > 0) {\n      console.log('向下滑动');\n    } else {\n      console.log('向上滑动');\n    }\n  }\n});", "explanation": "这个示例展示了如何监听触摸事件并实现简单的触摸交互，包括获取触摸坐标、实现拖拽效果和检测滑动方向。event.touches包含当前所有触摸点，event.changedTouches包含引起当前事件的触摸点。"}]}}, {"name": "Event Assignment", "trans": ["事件练习"], "usage": {"syntax": "// 事件练习", "description": "完成以下练习来巩固对JavaScript事件的理解和应用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "/*\n练习1: 创建一个简单的待办事项列表\n要求:\n1. 创建一个输入框和一个添加按钮\n2. 点击添加按钮时，将输入框中的内容添加到列表中\n3. 每个列表项有一个删除按钮，点击可以删除该项\n4. 双击列表项可以标记为已完成（添加删除线样式）\n*/\n\n// HTML结构参考\n/*\n<div id=\"todo-app\">\n  <input type=\"text\" id=\"todo-input\" placeholder=\"输入待办事项...\">\n  <button id=\"add-btn\">添加</button>\n  <ul id=\"todo-list\"></ul>\n</div>\n*/\n\n// 你的代码：\n// 获取DOM元素\nconst todoInput = document.getElementById('todo-input');\nconst addBtn = document.getElementById('add-btn');\nconst todoList = document.getElementById('todo-list');\n\n// 添加事项函数\nfunction addTodo() {\n  const text = todoInput.value.trim();\n  if (text === '') return;\n  \n  // 创建新的列表项\n  const li = document.createElement('li');\n  li.textContent = text;\n  \n  // 创建删除按钮\n  const deleteBtn = document.createElement('button');\n  deleteBtn.textContent = '删除';\n  deleteBtn.classList.add('delete-btn');\n  li.appendChild(deleteBtn);\n  \n  // 添加到列表\n  todoList.appendChild(li);\n  \n  // 清空输入框\n  todoInput.value = '';\n}\n\n// 事件监听\naddBtn.addEventListener('click', addTodo);\n\n// 回车键添加\ntodoInput.addEventListener('keydown', function(event) {\n  if (event.key === 'Enter') {\n    addTodo();\n  }\n});\n\n// 事件委托处理列表项的点击事件\ntodoList.addEventListener('click', function(event) {\n  // 如果点击的是删除按钮\n  if (event.target.classList.contains('delete-btn')) {\n    const li = event.target.parentElement;\n    todoList.removeChild(li);\n  }\n});\n\n// 双击标记为已完成\ntodoList.addEventListener('dblclick', function(event) {\n  // 如果点击的是列表项\n  if (event.target.tagName === 'LI') {\n    event.target.classList.toggle('completed');\n  }\n});", "explanation": "这个练习要求实现一个简单的待办事项列表，包括添加、删除和标记已完成功能。通过这个练习，可以综合运用点击事件、键盘事件和事件委托等知识。"}]}}]}