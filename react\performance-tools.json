{"name": "Performance Analysis Tools", "trans": ["性能分析工具"], "methods": [{"name": "React Profiler", "trans": ["React性能分析器"], "usage": {"syntax": "import { Profiler } from 'react';\n<Profiler id=\"App\" onRender={callback}>...</Profiler>", "description": "React Profiler用于分析组件渲染的性能瓶颈，记录每次渲染的耗时、原因和影响范围，帮助定位性能问题。", "parameters": [{"name": "id", "description": "唯一标识该Profiler区域的字符串。"}, {"name": "onRender", "description": "渲染回调函数，接收渲染信息参数。"}], "returnValue": "无返回值，回调函数用于分析渲染数据。", "examples": [{"code": "import { Profiler } from 'react';\nfunction onRender(id, phase, actualDuration) {\n  console.log(id, phase, actualDuration);\n}\n<Profiler id=\"App\" onRender={onRender}>\n  <App />\n</Profiler>;", "explanation": "通过Profiler分析App组件的每次渲染耗时。"}]}}, {"name": "Chrome Performance Panel", "trans": ["Chrome性能面板"], "usage": {"syntax": "// 打开Chrome DevTools > Performance\n// 点击Record录制性能\n// 进行操作后停止录制，分析时间线和调用栈", "description": "Chrome浏览器的Performance面板可用于分析页面的渲染、脚本执行、帧率、内存等性能瓶颈，适合定位慢操作和卡顿。", "parameters": [], "returnValue": "无返回值，分析结果在面板中展示。", "examples": [{"code": "// 步骤：\n// 1. 打开DevTools，切换到Performance\n// 2. 点击Record，操作页面\n// 3. 停止后分析Main、Scripting、Rendering等耗时区块", "explanation": "通过Performance面板分析页面各阶段的性能瓶颈。"}]}}, {"name": "Lighthouse", "trans": ["Lighthouse性能评测"], "usage": {"syntax": "// Chrome DevTools > Lighthouse\n// 选择性能分析类型，点击生成报告", "description": "Lighthouse是Chrome内置的自动化性能评测工具，可生成页面性能、可访问性、最佳实践等报告，给出优化建议。", "parameters": [], "returnValue": "返回详细的性能评测报告。", "examples": [{"code": "// 步骤：\n// 1. 打开DevTools，切换到Lighthouse\n// 2. 选择Performance，点击Analyze page load\n// 3. 查看报告和优化建议", "explanation": "通过Lighthouse自动生成页面性能报告。"}]}}, {"name": "Network Request Analysis", "trans": ["网络请求分析"], "usage": {"syntax": "// Chrome DevTools > Network\n// 观察请求耗时、响应体、缓存命中等", "description": "通过Network面板可分析接口请求的耗时、响应内容、缓存命中、失败原因等，定位数据加载慢或异常。", "parameters": [], "returnValue": "无返回值，分析结果在面板中展示。", "examples": [{"code": "// 步骤：\n// 1. 打开DevTools，切换到Network\n// 2. 刷新页面，观察各请求的耗时和状态码\n// 3. 点击请求查看详细信息", "explanation": "通过Network面板定位慢接口和异常请求。"}]}}, {"name": "Rendering Performance Analysis", "trans": ["渲染性能分析"], "usage": {"syntax": "// 使用Performance、React Profiler等工具分析渲染耗时\n// 关注Recalculate Style、Layout、Paint等阶段", "description": "渲染性能分析关注页面的样式计算、布局、绘制等阶段，结合Performance和Profiler工具定位慢渲染原因。", "parameters": [], "returnValue": "无返回值，分析结果在工具面板中展示。", "examples": [{"code": "// 步骤：\n// 1. Performance面板查看Recalculate Style、Layout、Paint耗时\n// 2. Profiler分析组件渲染耗时", "explanation": "结合多工具定位渲染慢的具体环节。"}]}}, {"name": "Memory Leak Detection", "trans": ["内存泄漏排查"], "usage": {"syntax": "// Chrome DevTools > Memory\n// 拍摄快照、分析内存分配、查找未释放对象", "description": "通过Memory面板可拍摄堆快照、分析内存分配、查找未释放的对象，定位内存泄漏问题。", "parameters": [], "returnValue": "无返回值，分析结果在面板中展示。", "examples": [{"code": "// 步骤：\n// 1. 打开DevTools，切换到Memory\n// 2. 拍摄Heap snapshot\n// 3. 分析Detached DOM trees等未释放对象", "explanation": "通过Memory面板定位内存泄漏。"}]}}, {"name": "作业：性能分析实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 用Profiler分析组件渲染性能\n// 2. 用Performance面板分析页面瓶颈\n// 3. 用Network面板分析接口耗时\n// 4. 用Memory面板排查内存泄漏", "description": "通过实践各类性能分析工具，掌握定位和优化React应用性能的方法。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. Profiler分析渲染\n// 2. Performance分析瓶颈\n// 3. Network分析请求\n// 4. Memory排查泄漏", "explanation": "作业提示，学生需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nimport React, { Profiler, useState } from 'react';\nfunction onRender(id, phase, duration) {\n  console.log(id, phase, duration);\n}\nfunction App() {\n  const [count, setCount] = useState(0);\n  return (\n    <Profiler id=\"App\" onRender={onRender}>\n      <button onClick={() => setCount(c => c + 1)}>增加</button>\n      <div>计数: {count}</div>\n    </Profiler>\n  );\n}\nexport default App;", "explanation": "通过Profiler分析渲染性能，结合浏览器工具完成性能分析。"}]}}]}