{"name": "Custom Hooks", "trans": ["自定义Hooks"], "methods": [{"name": "Creating Custom Hook", "trans": ["创建自定义Hook"], "usage": {"syntax": "function useMyHook(param) {\n  // 逻辑...\n  return value;\n}", "description": "自定义Hook是以use开头的函数，可以调用其他Hook，实现逻辑复用。自定义Hook内部可以使用useState、useEffect等内置Hook，也可以组合多个Hook。自定义Hook通常返回状态、方法或对象，供组件使用。", "parameters": [{"name": "param", "description": "自定义Hook的参数，根据实际需求定义"}], "returnValue": "返回自定义Hook需要暴露的状态、方法或对象", "examples": [{"code": "// 一个简单的自定义计数器Hook\nimport { useState } from 'react';\nfunction useCounter(initialValue = 0) {\n  const [count, setCount] = useState(initialValue);\n  const increment = () => setCount(c => c + 1);\n  const decrement = () => setCount(c => c - 1);\n  const reset = () => setCount(initialValue);\n  return { count, increment, decrement, reset };\n}\n// 在组件中使用\nfunction Counter() {\n  const { count, increment, decrement, reset } = useCounter(10);\n  return (\n    <div>\n      <p>计数: {count}</p>\n      <button onClick={increment}>加一</button>\n      <button onClick={decrement}>减一</button>\n      <button onClick={reset}>重置</button>\n    </div>\n  );\n}", "explanation": "这个例子展示了如何创建和使用一个自定义计数器Hook，实现了计数、加一、减一和重置功能。"}]}}, {"name": "Logic Reuse", "trans": ["逻辑复用"], "usage": {"syntax": "function useSharedLogic() {\n  // 复用逻辑\n  return value;\n}", "description": "自定义Hook的最大价值在于逻辑复用。可以将组件间重复的副作用、状态管理、事件处理等抽离到自定义Hook中，提升代码可维护性和复用性。", "parameters": [], "returnValue": "返回复用的状态、方法或对象", "examples": [{"code": "// 复用窗口尺寸监听逻辑\nimport { useState, useEffect } from 'react';\nfunction useWindowSize() {\n  const [size, setSize] = useState({ width: window.innerWidth, height: window.innerHeight });\n  useEffect(() => {\n    const onResize = () => setSize({ width: window.innerWidth, height: window.innerHeight });\n    window.addEventListener('resize', onResize);\n    return () => window.removeEventListener('resize', onResize);\n  }, []);\n  return size;\n}\n// 在多个组件中都可以直接使用 useWindowSize() 获取窗口尺寸。", "explanation": "这个例子展示了如何通过自定义Hook实现窗口尺寸监听逻辑的复用。"}]}}, {"name": "State Isolation", "trans": ["状态隔离"], "usage": {"syntax": "function useSomething() {\n  const [state, setState] = useState();\n  // ...\n  return { state, setState };\n}", "description": "每次调用自定义Hook都会生成独立的状态。即使在同一个组件中多次调用同一个自定义Hook，彼此之间的状态也是隔离的。", "parameters": [], "returnValue": "返回独立的状态和方法", "examples": [{"code": "// 同一组件中多次调用自定义Hook\nfunction useInput(defaultValue = '') {\n  const [value, setValue] = useState(defaultValue);\n  const onChange = e => setValue(e.target.value);\n  return { value, onChange };\n}\nfunction Form() {\n  const name = useInput();\n  const email = useInput();\n  return (\n    <form>\n      <input placeholder='姓名' {...name} />\n      <input placeholder='邮箱' {...email} />\n      <div>姓名: {name.value}, 邮箱: {email.value}</div>\n    </form>\n  );\n}", "explanation": "每次调用 useInput 都会生成独立的 value 状态，实现状态隔离。"}]}}, {"name": "Common Custom Hook Examples", "trans": ["常见自定义Hook示例"], "usage": {"syntax": "// 见下方示例", "description": "常见的自定义Hook包括：数据获取、表单处理、事件监听、节流防抖、媒体查询、权限检测等。", "parameters": [], "returnValue": "返回与具体功能相关的状态和方法", "examples": [{"code": "// 数据获取Hook\nimport { useState, useEffect } from 'react';\nfunction useFetch(url) {\n  const [data, setData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    setLoading(true);\n    fetch(url)\n      .then(res => res.json())\n      .then(setData)\n      .catch(setError)\n      .finally(() => setLoading(false));\n  }, [url]);\n  return { data, loading, error };\n}\n// 在组件中直接调用 useFetch(url) 获取数据。", "explanation": "这是一个常见的数据获取自定义Hook，封装了异步请求和状态管理。"}, {"code": "// 节流Hook\nimport { useRef } from 'react';\nfunction useThrottle(fn, delay) {\n  const lastCall = useRef(0);\n  return (...args) => {\n    const now = Date.now();\n    if (now - lastCall.current > delay) {\n      lastCall.current = now;\n      fn(...args);\n    }\n  };\n}", "explanation": "这是一个节流函数的自定义Hook，可以限制高频事件的触发频率。"}]}}, {"name": "Testing Custom Hooks", "trans": ["测试自定义Hook"], "usage": {"syntax": "// 使用 @testing-library/react-hooks\nimport { renderHook, act } from '@testing-library/react-hooks';\nconst { result } = renderHook(() => useCounter(0));\nact(() => { result.current.increment(); });", "description": "自定义Hook可以用专门的测试库进行单元测试，如@testing-library/react-hooks。测试时可以模拟调用Hook、触发方法、断言状态变化。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import { renderHook, act } from '@testing-library/react-hooks';\nimport useCounter from './useCounter';\n\ntest('should increment counter', () => {\n  const { result } = renderHook(() => useCounter(0));\n  act(() => { result.current.increment(); });\n  expect(result.current.count).toBe(1);\n});", "explanation": "这个例子展示了如何用 renderHook 和 act 测试自定义Hook的行为。"}]}}, {"name": "Publishing and Sharing", "trans": ["发布和共享"], "usage": {"syntax": "// 发布到npm\n// 1. 创建npm包\n// 2. 编写README和类型声明\n// 3. npm publish", "description": "优秀的自定义Hook可以打包发布到npm，供团队或社区复用。建议为Hook编写详细文档、类型声明和测试用例。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 发布流程简要\n// 1. npm init\n// 2. 编写源码和package.json\n// 3. npm publish\n// 4. 在项目中通过npm install使用", "explanation": "这个例子简要说明了自定义Hook的发布流程。"}]}}, {"name": "作业：实现一个useLocalStorage自定义Hook", "trans": ["作业"], "usage": {"syntax": "// 需求：实现一个useLocalStorage(key, initialValue)\n// 1. 能读写localStorage，支持任意类型。\n// 2. 组件状态和localStorage同步。\n// 3. 提供set和remove方法。\n// 4. 代码结构清晰，注释完整。", "description": "请实现一个useLocalStorage自定义Hook，要求如上。提交时请附上完整代码和注释。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 示例代码略，请学生自行实现\n// 提示：可以用 useState、useEffect 结合 localStorage 完成。", "explanation": "作业要求学生综合运用自定义Hook，巩固对状态、持久化和副作用的理解。"}, {"code": "import { useState, useEffect } from 'react';\nfunction useLocalStorage(key, initialValue) {\n  const [value, setValue] = useState(() => {\n    const stored = window.localStorage.getItem(key);\n    return stored ? JSON.parse(stored) : initialValue;\n  });\n  useEffect(() => {\n    window.localStorage.setItem(key, JSON.stringify(value));\n  }, [key, value]);\n  const remove = () => {\n    window.localStorage.removeItem(key);\n    setValue(initialValue);\n  };\n  return [value, setValue, remove];\n}\n// 用法示例\nfunction Demo() {\n  const [name, setName, removeName] = useLocalStorage('name', '');\n  return (\n    <div>\n      <input value={name} onChange={e => setName(e.target.value)} />\n      <button onClick={removeName}>清除</button>\n      <div>当前值: {name}</div>\n    </div>\n  );\n}", "explanation": "这是一个完整的 useLocalStorage 自定义Hook实现，支持本地持久化和状态同步。"}]}}]}