{"name": "Async and Scheduled Tasks", "trans": ["异步任务与定时任务"], "methods": [{"name": "@Async Asynchronous Method", "trans": ["@Async异步方法"], "usage": {"syntax": "@Async\npublic void asyncMethod() { ... }", "description": "@Async注解可将方法异步执行，提升系统吞吐量。需在主类加@EnableAsync注解。", "parameters": [{"name": "@Async", "description": "标记方法为异步执行"}, {"name": "@EnableAsync", "description": "在配置类或主类上启用异步支持"}], "returnValue": "void或Future/CompletableFuture，表示异步结果", "examples": [{"code": "// 1. 启用异步\n@SpringBootApplication\n@EnableAsync\npublic class Application { }\n\n// 2. 异步方法\n@Service\npublic class AsyncService {\n    @Async\n    public void sendEmail(String to) {\n        // 发送邮件逻辑\n        System.out.println(\"发送邮件给: \" + to);\n    }\n}", "explanation": "演示了如何启用@Async和定义异步方法。"}]}}, {"name": "ThreadPool Configuration", "trans": ["线程池配置"], "usage": {"syntax": "@Bean\npublic Executor taskExecutor() { ... }", "description": "自定义线程池可提升异步任务的性能和可控性。通过实现AsyncConfigurer或定义Executor Bean实现。", "parameters": [{"name": "corePoolSize", "description": "核心线程数"}, {"name": "maxPoolSize", "description": "最大线程数"}, {"name": "queueCapacity", "description": "队列容量"}, {"name": "threadNamePrefix", "description": "线程名称前缀"}], "returnValue": "Executor线程池实例", "examples": [{"code": "@Configuration\npublic class AsyncConfig {\n    @Bean\n    public Executor taskExecutor() {\n        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();\n        executor.setCorePoolSize(5);\n        executor.setMaxPoolSize(10);\n        executor.setQueueCapacity(100);\n        executor.setThreadNamePrefix(\"async-task-\");\n        executor.initialize();\n        return executor;\n    }\n}", "explanation": "演示了如何自定义线程池并用于@Async异步任务。"}]}}, {"name": "@Scheduled Scheduled Task", "trans": ["@Scheduled定时任务"], "usage": {"syntax": "@Scheduled(cron = \"表达式\")\npublic void scheduledTask() { ... }", "description": "@Scheduled注解可实现定时任务，支持cron表达式、fixedRate、fixedDelay等多种调度方式。需在主类加@EnableScheduling注解。", "parameters": [{"name": "@Scheduled", "description": "标记方法为定时任务"}, {"name": "cron", "description": "cron表达式，指定执行时间"}, {"name": "fixedRate", "description": "固定速率（毫秒）"}, {"name": "fixedDelay", "description": "固定延迟（毫秒）"}, {"name": "@EnableScheduling", "description": "在配置类或主类上启用定时任务支持"}], "returnValue": "void，无返回值", "examples": [{"code": "// 1. 启用定时任务\n@SpringBootApplication\n@EnableScheduling\npublic class Application { }\n\n// 2. 定时任务方法\n@Component\npublic class ScheduledTasks {\n    @Scheduled(cron = \"0 0 1 * * ?\")\n    public void clearCache() {\n        System.out.println(\"每天凌晨1点清理缓存\");\n    }\n}", "explanation": "演示了如何启用@Scheduled和定义定时任务。"}]}}, {"name": "Distributed Scheduled Task (xxl-job)", "trans": ["分布式定时任务（xxl-job）"], "usage": {"syntax": "引入xxl-job-core依赖，@XxlJob注解，配置调度中心地址", "description": "xxl-job是流行的分布式定时任务平台，支持任务分片、失败重试、动态管理等。Spring Boot可通过starter快速集成。", "parameters": [{"name": "xxl-job-core", "description": "xxl-job核心依赖"}, {"name": "@XxlJob", "description": "标记分布式任务方法"}, {"name": "调度中心地址", "description": "xxl-job-admin服务地址"}], "returnValue": "分布式调度任务执行结果", "examples": [{"code": "// 1. 任务Handler\n@Component\npublic class SampleJob {\n    @XxlJob(\"demoJobHandler\")\n    public void demoJob() {\n        System.out.println(\"执行分布式定时任务\");\n    }\n}\n\n// 2. application.properties\nxxl.job.admin.addresses=http://localhost:8080/xxl-job-admin\nxxl.job.executor.appname=demo-executor", "explanation": "演示了xxl-job分布式定时任务的基本用法。"}]}}, {"name": "Async and Scheduled Assignment", "trans": ["异步与定时任务练习"], "usage": {"syntax": "# 异步与定时任务练习", "description": "完成以下练习，巩固Spring Boot异步与定时任务相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 使用@Async实现一个异步方法，并自定义线程池。\n2. 使用@Scheduled实现一个定时任务，打印当前时间。\n3. 集成xxl-job，实现一个分布式定时任务。", "explanation": "通过这些练习掌握Spring Boot异步与定时任务的核心用法。"}]}}]}