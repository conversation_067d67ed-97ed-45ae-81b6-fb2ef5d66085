{"name": "React Testing Library", "trans": ["React Testing Library"], "methods": [{"name": "Render Component", "trans": ["渲染组件"], "usage": {"syntax": "import { render } from '@testing-library/react';\nrender(<MyComponent />);", "description": "使用render函数将React组件渲染到测试环境，返回查询和操作工具。", "parameters": [{"name": "component", "description": "待渲染的React组件。"}], "returnValue": "返回包含查询方法的对象。", "examples": [{"code": "import { render } from '@testing-library/react';\nfunction Hello() { return <div>Hi</div>; }\ntest('渲染组件', () => {\n  const { getByText } = render(<Hello />);\n  expect(getByText('Hi')).toBeInTheDocument();\n});", "explanation": "渲染组件并通过getByText断言内容。"}]}}, {"name": "Query Elements", "trans": ["查询元素"], "usage": {"syntax": "const { getByText, getByRole, queryByTestId } = render(<MyComponent />);\ngetByText('内容');\ngetByRole('button');\nqueryByTestId('id');", "description": "通过getBy、queryBy等方法查找DOM元素，支持按文本、角色、testid等多种方式。", "parameters": [{"name": "query method", "description": "如getByText、getByRole、queryByTestId等。"}, {"name": "selector", "description": "用于查找的文本、角色或testid。"}], "returnValue": "返回匹配的DOM节点或null。", "examples": [{"code": "import { render } from '@testing-library/react';\nfunction Btn() { return <button data-testid=\"ok\">确定</button>; }\ntest('查询元素', () => {\n  const { getByText, getByRole, getByTestId } = render(<Btn />);\n  expect(getByText('确定')).toBeInTheDocument();\n  expect(getByRole('button')).toBeInTheDocument();\n  expect(getByTestId('ok')).toBeInTheDocument();\n});", "explanation": "通过多种方式查询并断言元素存在。"}]}}, {"name": "Fire Events", "trans": ["触发事件"], "usage": {"syntax": "import userEvent from '@testing-library/user-event';\nuserEvent.click(element);\nuserEvent.type(element, '内容');", "description": "使用userEvent模拟用户操作，如点击、输入、悬停等，驱动组件行为。", "parameters": [{"name": "element", "description": "要操作的DOM节点。"}, {"name": "event", "description": "如click、type等事件类型。"}], "returnValue": "无返回值，事件触发后可断言组件行为。", "examples": [{"code": "import { render, screen } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nfunction Btn() { const [clicked, setClicked] = useState(false); return <button onClick={() => setClicked(true)}>{clicked ? '已点击' : '点我'}</button>; }\ntest('点击事件', () => {\n  render(<Btn />);\n  userEvent.click(screen.getByRole('button'));\n  expect(screen.getByText('已点击')).toBeInTheDocument();\n});", "explanation": "模拟点击事件并断言状态变化。"}]}}, {"name": "Assert Results", "trans": ["断言结果"], "usage": {"syntax": "expect(element).toBeInTheDocument();\nexpect(element).toHaveTextContent('内容');", "description": "通过expect和jest-dom扩展断言DOM节点的存在、内容、属性、样式等。", "parameters": [{"name": "element", "description": "被断言的DOM节点。"}, {"name": "matcher", "description": "如toBeInTheDocument、toHaveTextContent等断言方法。"}], "returnValue": "无返回值，断言通过即测试通过。", "examples": [{"code": "import { render } from '@testing-library/react';\nfunction Hello() { return <div>Hi</div>; }\ntest('断言内容', () => {\n  const { getByText } = render(<Hello />);\n  expect(getByText('Hi')).toBeInTheDocument();\n  expect(getByText('Hi')).toHaveTextContent('Hi');\n});", "explanation": "断言元素存在和内容正确。"}]}}, {"name": "Async Testing", "trans": ["异步测试"], "usage": {"syntax": "await findByText('内容');\nawait waitFor(() => expect(...));", "description": "异步测试用于等待异步渲染或数据加载，常用findBy和waitFor等方法。", "parameters": [{"name": "findBy method", "description": "如findByText、findByRole等。"}, {"name": "waitFor callback", "description": "等待条件成立的回调函数。"}], "returnValue": "返回Promise，异步断言通过即测试通过。", "examples": [{"code": "import { render, screen, waitFor } from '@testing-library/react';\nfunction AsyncHello() { const [msg, setMsg] = useState(''); useEffect(() => { setTimeout(() => setMsg('Hi'), 100); }, []); return <div>{msg}</div>; }\ntest('异步渲染', async () => {\n  render(<AsyncHello />);\n  expect(await screen.findByText('Hi')).toBeInTheDocument();\n  await waitFor(() => expect(screen.getByText('Hi')).toHaveTextContent('Hi'));\n});", "explanation": "等待异步内容渲染并断言。"}]}}, {"name": "Best Practices", "trans": ["最佳实践"], "usage": {"syntax": "// 1. 只测试用户可见行为\n// 2. 避免测试实现细节\n// 3. 使用语义化查询\n// 4. 保持测试独立和可维护", "description": "推荐关注用户行为、语义化查询、避免实现细节、保持测试独立和可维护，提升测试质量。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 示例：\n// 1. 使用getByRole代替getByTestId\n// 2. 只断言用户可见内容和交互", "explanation": "最佳实践示例，提升测试可读性和健壮性。"}]}}, {"name": "作业：Testing Library实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 使用render、查询、事件、断言、异步测试\n// 2. 遵循最佳实践编写测试", "description": "通过实践Testing Library的渲染、查询、事件、断言和异步测试，掌握高质量测试方法。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 编写渲染、查询、事件、断言、异步测试", "explanation": "作业提示，学生需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nimport { render, screen } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nfunction Btn() { const [clicked, setClicked] = useState(false); return <button onClick={() => setClicked(true)}>{clicked ? '已点击' : '点我'}</button>; }\ntest('渲染和交互', () => {\n  render(<Btn />);\n  expect(screen.getByText('点我')).toBeInTheDocument();\n  userEvent.click(screen.getByRole('button'));\n  expect(screen.getByText('已点击')).toBeInTheDocument();\n});", "explanation": "Testing Library测试的正确实现示例，覆盖渲染、查询、事件和断言。"}]}}]}