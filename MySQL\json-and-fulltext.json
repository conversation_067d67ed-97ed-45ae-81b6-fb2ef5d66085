{"name": "JSON and Fulltext Index", "trans": ["JSON与全文索引"], "methods": [{"name": "JSON Data Type", "trans": ["JSON数据类型"], "usage": {"syntax": "CREATE TABLE 表名 (id INT PRIMARY KEY, data JSON);", "description": "MySQL支持原生JSON数据类型，可用于存储结构化的JSON文档，支持高效的存储和查询。", "parameters": [{"name": "data", "description": "JSON类型字段"}], "returnValue": "无返回值", "examples": [{"code": "-- 创建包含JSON字段的表\nCREATE TABLE products (id INT PRIMARY KEY, info JSON);\n\n-- 插入JSON数据\nINSERT INTO products VALUES (1, '{\"brand\":\"Apple\",\"model\":\"iPhone 15\"}');", "explanation": "本例展示了如何创建JSON字段并插入JSON格式数据。"}]}}, {"name": "JSON Query", "trans": ["JSON查询"], "usage": {"syntax": "SELECT info->'$.brand' AS brand FROM products WHERE info->'$.model' = 'iPhone 15';", "description": "通过JSON路径表达式查询JSON字段中的数据，支持提取、筛选和条件判断。", "parameters": [{"name": "info->'$.brand'", "description": "提取JSON字段中的brand属性"}, {"name": "info->'$.model'", "description": "提取JSON字段中的model属性"}], "returnValue": "返回JSON字段中指定路径的值", "examples": [{"code": "-- 查询所有品牌为Apple的产品\nSELECT id, info->'$.brand' AS brand FROM products WHERE info->'$.brand' = 'Apple';", "explanation": "本例展示了如何通过JSON路径表达式查询和筛选JSON字段内容。"}]}}, {"name": "Fulltext Index and Search", "trans": ["全文索引与搜索"], "usage": {"syntax": "CREATE TABLE articles (id INT PRIMARY KEY, content TEXT, FULLTEXT(content));\n\n-- 全文搜索\nSELECT * FROM articles WHERE MATCH(content) AGAINST('数据库');", "description": "MySQL支持全文索引（FULLTEXT），可用于高效的文本搜索，适用于大文本字段的模糊查找。", "parameters": [{"name": "content", "description": "需要全文索引的文本字段"}], "returnValue": "返回匹配全文搜索条件的记录", "examples": [{"code": "-- 创建带全文索引的表\nCREATE TABLE articles (id INT PRIMARY KEY, content TEXT, FULLTEXT(content));\n\n-- 全文搜索包含'数据库'的文章\nSELECT * FROM articles WHERE MATCH(content) AGAINST('数据库');", "explanation": "本例展示了如何创建全文索引并进行全文搜索。"}]}}, {"name": "JSON and Fulltext Assignment", "trans": ["JSON与全文索引练习"], "usage": {"syntax": "# JSON与全文索引练习", "description": "完成以下练习，巩固MySQL JSON数据类型和全文索引的实际应用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 创建一个包含JSON字段的表，并插入多条JSON数据。\n2. 查询所有brand为Apple的记录。\n3. 创建带全文索引的articles表，插入几条包含'数据库'关键字的文章。\n4. 使用全文搜索查找包含'数据库'的文章。", "explanation": "通过这些练习，你将掌握MySQL JSON数据类型的存储、查询和全文索引的实际应用。"}]}}]}