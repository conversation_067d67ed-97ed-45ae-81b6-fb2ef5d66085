{"topics": [{"id": "intro", "name": "Docker简介", "file": "Docker/intro.json", "description": "Docker基本介绍、容器与虚拟机对比、核心概念（镜像、容器、仓库）、应用场景及Docker生态系统，适合初学者了解Docker技术基础。"}, {"id": "install-and-config", "name": "安装与环境配置", "file": "Docker/install-and-config.json", "description": "Docker在Windows/Mac/Linux下的安装方法、Docker Desktop使用、国内镜像加速配置、基本命令行工具及升级与卸载方法，适合初次接触Docker的用户。"}, {"id": "architecture", "name": "Docker架构原理", "file": "Docker/architecture.json", "description": "Docker架构组成、Docker守护进程、客户端与服务端通信、镜像分层原理及联合文件系统，适合深入理解Docker内部工作机制的用户。"}, {"id": "image-manage", "name": "镜像获取与管理", "file": "docker-image-manage.json", "description": "介绍如何拉取、查看、删除、打标签和搜索镜像，掌握镜像的基本管理方法。"}, {"id": "image-build", "name": "镜像构建", "file": "Docker/docker-image-build.json", "description": "Dockerfile基础语法、镜像构建命令、多阶段构建、镜像优化技巧与体积分析，帮助学员掌握自定义镜像的构建与优化。"}, {"id": "image-registry", "name": "镜像仓库", "file": "Docker/docker-image-registry.json", "description": "Docker Hub使用、私有仓库搭建、镜像推送、拉取策略与安全扫描，帮助学员掌握镜像仓库的实际操作与安全管理。"}, {"id": "container-ops", "name": "容器操作", "file": "Docker/docker-container-ops.json", "description": "容器的创建与启动、查看、停止与删除、日志查看、进入与交互等核心操作，帮助学员掌握容器生命周期管理。"}, {"id": "container-resource", "name": "容器资源限制", "file": "Docker/docker-container-resource.json", "description": "CPU/内存限制、重启策略、健康检查、自动重启、容器命名与管理等，帮助学员掌握容器资源与生命周期的精细控制。"}, {"id": "container-network", "name": "容器网络", "file": "Docker/docker-container-network.json", "description": "网络模式、自定义网络、容器间通信、端口映射与暴露、网络隔离与安全，帮助学员掌握容器网络的配置与安全。"}, {"id": "volume", "name": "数据卷（Volume）", "file": "Docker/docker-volume.json", "description": "Volume概念与作用、创建与挂载、匿名卷与具名卷、生命周期、备份与恢复，帮助学员掌握容器数据持久化与管理。"}, {"id": "bind-mount", "name": "绑定挂载", "file": "Docker/docker-bind-mount.json", "description": "本地目录挂载、挂载权限管理、配置文件注入、挂载与容器持久化，帮助学员掌握主机与容器的数据实时同步与安全。"}, {"id": "compose-basic", "name": "Compose基础", "file": "Docker/docker-compose-basic.json", "description": "Compose安装、yml语法、服务编排与依赖、多容器应用管理、常用命令，帮助学员掌握多容器编排的基础。"}, {"id": "compose-advanced", "name": "Compose进阶", "file": "Docker/docker-compose-advanced.json", "description": "环境变量与配置、多环境配置文件、服务扩缩容、Compose与Swarm集成、调试与日志，帮助学员掌握Compose高级用法。"}, {"id": "dockerfile-instructions", "name": "Dockerfile指令详解", "file": "Docker/dockerfile-instructions.json", "description": "FROM、RUN、CMD、ENTRYPOINT、COPY、ADD、WORKDIR、ENV、EXPOSE、VOLUME、USER、HEALTHCHECK、多阶段构建等指令详解，帮助学员精通Dockerfile编写。"}, {"id": "dockerfile-best-practice", "name": "Dockerfile最佳实践", "file": "Docker/dockerfile-best-practice.json", "description": "镜像层优化、缓存利用、安全性建议、构建速度提升、常见错误与调试，帮助学员写出高效、安全、易维护的Dockerfile。"}, {"id": "ci-integration", "name": "与CI工具集成", "file": "Docker/docker-ci-integration.json", "description": "Jenkins、GitLab CI/CD、GitHub Actions与Docker集成，自动化构建与推送、镜像版本管理，帮助学员掌握CI/CD与容器结合。"}, {"id": "auto-deploy", "name": "自动化部署", "file": "Docker/docker-auto-deploy.json", "description": "容器自动化部署流程、蓝绿部署与滚动更新、回滚策略、部署脚本编写、自动化测试与验证，帮助学员掌握生产环境下的自动化部署与运维。"}, {"id": "troubleshooting", "name": "常见问题排查", "file": "Docker/docker-troubleshooting.json", "description": "容器无法启动、镜像拉取失败、网络不通、数据卷权限问题、日志与监控，帮助学员掌握Docker常见问题的排查与解决方法。"}, {"id": "security", "name": "安全与合规", "file": "Docker/docker-security.json", "description": "镜像安全扫描、最小权限原则、镜像签名与验证、容器运行用户管理、安全加固建议，帮助学员掌握Docker安全与合规的核心措施。"}, {"id": "performance", "name": "性能优化", "file": "Docker/docker-performance.json", "description": "镜像体积优化、容器资源分配、网络与存储优化、构建与启动速度优化、监控与调优工具，帮助学员掌握Docker性能优化的实用方法。"}, {"id": "production-best-practice", "name": "生产环境最佳实践", "file": "Docker/docker-production-best-practice.json", "description": "镜像版本管理规范、配置与密钥管理、灰度发布与回滚、日志采集与分析、容器编排与K8s集成，帮助学员掌握生产环境下的最佳实践。"}, {"id": "practical-cases", "name": "实战案例", "file": "Docker/docker-practical-cases.json", "description": "构建并运行Node.js应用、多容器Web+DB项目、Compose管理微服务、前后端分离项目容器化、集成CI/CD自动化部署，帮助学员掌握Docker在真实项目中的应用。"}, {"id": "exercises-projects", "name": "练习与项目", "file": "Docker/docker-exercises-projects.json", "description": "编写自己的Dockerfile、搭建私有镜像仓库、多环境Compose编排、优化镜像体积和构建速度、部署与回滚脚本、生产环境安全加固，帮助学员通过实践掌握Docker核心技能。"}]}