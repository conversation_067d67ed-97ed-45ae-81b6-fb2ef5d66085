{"name": "<PERSON><PERSON><PERSON>", "trans": ["数组基础"], "methods": [{"name": "Array Declaration & Initialization", "trans": ["数组声明与初始化"], "usage": {"syntax": "const arr1 = [1, 2, 3];\nconst arr2 = new Array(3);\nconst arr3 = Array.of(1, 2, 3);", "description": "数组可用字面量[]、new Array()、Array.of()等方式声明和初始化。", "parameters": [{"name": "[]", "description": "数组字面量。"}, {"name": "new Array()", "description": "构造函数创建数组。"}, {"name": "Array.of()", "description": "ES6创建数组的新方法。"}], "returnValue": "返回新数组。", "examples": [{"code": "const a = [1,2,3];\nconst b = new Array(2);\nconst c = Array.of(4,5);\nconsole.log(a, b, c);", "explanation": "三种常见数组声明与初始化方式。"}]}}, {"name": "<PERSON><PERSON><PERSON> Traversal", "trans": ["数组遍历"], "usage": {"syntax": "for (let i = 0; i < arr.length; i++) { }\narr.forEach(fn);\narr.map(fn);\narr.filter(fn);\narr.reduce(fn, init);", "description": "for循环、forEach、map、filter、reduce等方法可遍历数组并处理元素。", "parameters": [{"name": "for", "description": "传统循环遍历。"}, {"name": "for<PERSON>ach", "description": "对每个元素执行回调。"}, {"name": "map", "description": "返回新数组。"}, {"name": "filter", "description": "筛选新数组。"}, {"name": "reduce", "description": "累加/归并。"}], "returnValue": "返回遍历结果或新数组。", "examples": [{"code": "const arr = [1,2,3];\nfor(let i=0;i<arr.length;i++){console.log(arr[i]);}\narr.forEach(x=>console.log(x));\nconst doubled = arr.map(x=>x*2);\nconst even = arr.filter(x=>x%2===0);\nconst sum = arr.reduce((a,b)=>a+b,0);\nconsole.log(doubled, even, sum);", "explanation": "演示多种数组遍历和处理方法。"}]}}, {"name": "Common Array Methods", "trans": ["数组常用方法"], "usage": {"syntax": "arr.push(x);\narr.pop();\narr.shift();\narr.unshift(x);\narr.splice(i, n, ...items);\narr.slice(start, end);\narr.concat(arr2);\narr.join(',');\narr.sort();\narr.reverse();", "description": "数组常用方法包括增删改查、排序、拼接、转换等。", "parameters": [{"name": "push/pop", "description": "尾部添加/删除元素。"}, {"name": "shift/unshift", "description": "头部添加/删除元素。"}, {"name": "splice", "description": "任意位置增删改。"}, {"name": "slice", "description": "截取子数组。"}, {"name": "concat", "description": "合并数组。"}, {"name": "join", "description": "连接为字符串。"}, {"name": "sort/reverse", "description": "排序/反转。"}], "returnValue": "返回操作结果或新数组。", "examples": [{"code": "let arr = [1,2,3];\narr.push(4);\narr.pop();\narr.unshift(0);\narr.shift();\narr.splice(1,1,9);\nconsole.log(arr);\nconsole.log(arr.slice(0,2));\nconsole.log(arr.concat([5,6]));\nconsole.log(arr.join('-'));\nconsole.log([3,1,2].sort());\nconsole.log([1,2,3].reverse());", "explanation": "演示常用数组方法的用法。"}]}}, {"name": "Multidimensional & Sparse Arrays", "trans": ["多维数组与稀疏数组"], "usage": {"syntax": "const matrix = [[1,2],[3,4]];\nconst sparse = [1,,3];", "description": "多维数组是数组的数组，稀疏数组有未定义元素。访问稀疏元素返回undefined。", "parameters": [{"name": "多维数组", "description": "嵌套数组。"}, {"name": "稀疏数组", "description": "有空位的数组。"}], "returnValue": "返回多维数组或稀疏数组。", "examples": [{"code": "const m = [[1,2],[3,4]];\nconsole.log(m[1][0]); // 3\nconst s = [1,,3];\nconsole.log(s[1]); // undefined\nconsole.log(s.length); // 3", "explanation": "多维数组和稀疏数组的声明与访问。"}]}}, {"name": "作业：数组基础实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 声明和初始化数组\n// 2. 遍历和处理数组\n// 3. 使用常用方法操作数组\n// 4. 多维和稀疏数组的应用", "description": "通过实践数组声明、遍历、方法、多维与稀疏数组，掌握JS数组基础。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. 声明/初始化/遍历\n// 2. 常用方法/多维/稀疏数组", "explanation": "作业提示，需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nconst arr = [1,2,3]; arr.push(4); arr.splice(1,1,9);\narr.forEach(x=>console.log(x));\nconst m = [[1,2],[3,4]];\nconst s = [1,,3];\nconsole.log(arr,m[1][1],s[1]);", "explanation": "涵盖数组声明、遍历、方法、多维与稀疏数组的正确实现。"}]}}]}