{"name": "Aggregation and Grouping", "trans": ["聚合与分组"], "methods": [{"name": "Aggregate Functions", "trans": ["聚合函数（COUNT、SUM、AVG、MAX、MIN）"], "usage": {"syntax": "SELECT 聚合函数(列名) FROM 表名;", "description": "聚合函数对一组值执行计算并返回单个值。常用的聚合函数有COUNT（计数）、SUM（求和）、AVG（平均值）、MAX（最大值）、MIN（最小值）。", "parameters": [{"name": "列名", "description": "要进行聚合计算的字段名"}], "returnValue": "聚合计算的结果", "examples": [{"code": "-- 计算学生总人数\nSELECT COUNT(*) FROM students;\n\n-- 计算学生平均年龄\nSELECT AVG(age) FROM students;\n\n-- 查询最高分和最低分\nSELECT MAX(score), MIN(score) FROM exams;\n\n-- 计算总成绩\nSELECT SUM(score) FROM exams;", "explanation": "使用不同的聚合函数计算学生数量、平均年龄、最高分、最低分和总分。"}]}}, {"name": "GROUP BY Clause", "trans": ["GROUP BY分组"], "usage": {"syntax": "SELECT 列名, 聚合函数(列名) FROM 表名 GROUP BY 列名;", "description": "GROUP BY子句将查询结果按一个或多个列的值分组，然后对每个组应用聚合函数。", "parameters": [{"name": "列名", "description": "用于分组的字段名"}, {"name": "聚合函数", "description": "应用于每个分组的聚合计算"}], "returnValue": "每个分组的聚合结果", "examples": [{"code": "-- 按班级统计学生人数\nSELECT class_id, COUNT(*) AS student_count\nFROM students\nGROUP BY class_id;\n\n-- 按性别统计平均年龄\nSELECT gender, AVG(age) AS avg_age\nFROM students\nGROUP BY gender;", "explanation": "第一个查询按班级分组，统计每个班级的学生人数；第二个查询按性别分组，计算每个性别的平均年龄。"}]}}, {"name": "HAVING Clause", "trans": ["HAVING条件"], "usage": {"syntax": "SELECT 列名, 聚合函数(列名) FROM 表名 GROUP BY 列名 HAVING 条件;", "description": "HAVING子句用于过滤分组后的结果，类似于WHERE，但HAVING用于分组后的筛选，而WHERE用于分组前的筛选。", "parameters": [{"name": "列名", "description": "用于分组的字段名"}, {"name": "聚合函数", "description": "应用于每个分组的聚合计算"}, {"name": "条件", "description": "用于过滤分组的条件表达式"}], "returnValue": "满足条件的分组聚合结果", "examples": [{"code": "-- 查询人数大于2的班级\nSELECT class_id, COUNT(*) AS student_count\nFROM students\nGROUP BY class_id\nHAVING COUNT(*) > 2;\n\n-- 查询平均分大于80的科目\nSELECT subject, AVG(score) AS avg_score\nFROM exams\nGROUP BY subject\nHAVING AVG(score) > 80;", "explanation": "第一个查询找出学生人数大于2的班级；第二个查询找出平均分大于80的科目。"}]}}, {"name": "Combining WHERE, GROUP BY, and HAVING", "trans": ["组合使用WHERE、GROUP BY和HAVING"], "usage": {"syntax": "SELECT 列名, 聚合函数(列名) FROM 表名 WHERE 条件1 GROUP BY 列名 HAVING 条件2;", "description": "在同一查询中组合使用WHERE、GROUP BY和HAVING，WHERE在分组前筛选行，HAVING在分组后筛选组。", "parameters": [{"name": "列名", "description": "要查询和分组的字段名"}, {"name": "条件1", "description": "分组前筛选行的条件(WHERE)"}, {"name": "条件2", "description": "分组后筛选组的条件(HAVING)"}], "returnValue": "满足所有条件的分组聚合结果", "examples": [{"code": "-- 查询年龄大于18岁的学生中，人数大于2的班级\nSELECT class_id, COUNT(*) AS student_count\nFROM students\nWHERE age > 18\nGROUP BY class_id\nHAVING COUNT(*) > 2;\n\n-- 查询2023年考试中，平均分大于80的科目\nSELECT subject, AVG(score) AS avg_score\nFROM exams\nWHERE YEAR(exam_date) = 2023\nGROUP BY subject\nHAVING AVG(score) > 80;", "explanation": "第一个查询先筛选出年龄大于18岁的学生，然后按班级分组，最后找出人数大于2的班级；第二个查询先筛选出2023年的考试记录，然后按科目分组，最后找出平均分大于80的科目。"}]}}, {"name": "Aggregation and Grouping Assignment", "trans": ["聚合与分组练习"], "usage": {"syntax": "# 聚合与分组练习", "description": "完成以下练习，巩固聚合函数和分组查询的使用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n假设有以下两个表：\n\n-- 学生表\nCREATE TABLE students (\n  id INT PRIMARY KEY,\n  name VARCHAR(20),\n  gender CHAR(1),\n  class_id INT,\n  age INT\n);\n\n-- 成绩表\nCREATE TABLE scores (\n  id INT PRIMARY KEY,\n  student_id INT,\n  subject VARCHAR(20),\n  score DECIMAL(5,2)\n);\n\n-- 插入数据\nINSERT INTO students VALUES \n(1, '张三', 'M', 1, 18),\n(2, '李四', 'M', 1, 19),\n(3, '王五', 'F', 2, 18),\n(4, '赵六', 'F', 2, 20),\n(5, '钱七', 'M', 3, 19);\n\nINSERT INTO scores VALUES\n(1, 1, '数学', 85.5),\n(2, 1, '语文', 76.0),\n(3, 2, '数学', 90.0),\n(4, 2, '语文', 88.5),\n(5, 3, '数学', 78.0),\n(6, 3, '语文', 92.0),\n(7, 4, '数学', 82.5),\n(8, 4, '语文', 87.5),\n(9, 5, '数学', 95.0),\n(10, 5, '语文', 80.0);\n\n请完成以下查询：\n1. 计算所有学生的平均年龄。\n2. 按班级统计学生人数。\n3. 按科目统计平均分。\n4. 查找平均分大于85分的科目。\n5. 按性别分组，统计每组的人数和平均年龄。\n6. 查找每个班级中年龄最大的学生。", "explanation": "通过实际操作掌握聚合函数、GROUP BY分组和HAVING条件的使用。"}]}}]}