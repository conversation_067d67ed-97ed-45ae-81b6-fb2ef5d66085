{"name": "Debugging and Common Errors", "trans": ["调试与常见错误"], "methods": [{"name": "Common Compilation Errors", "trans": ["常见编译错误"], "usage": {"syntax": "// 语法错误示例\n// 缺少分号\nprint('Hello World') // 错误：缺少分号\nprint('Hello World'); // 正确\n\n// 类型错误\nint number = 'text'; // 错误：类型不匹配\nint number = 42; // 正确\n\n// 未声明变量\nprint(undeclaredVar); // 错误：变量未声明\nvar declaredVar = 'value';\nprint(declaredVar); // 正确", "description": "编译错误是在代码编译阶段发现的错误，这些错误会阻止程序运行。常见的编译错误包括：1) 语法错误：如缺少分号、括号不匹配、关键字拼写错误等；2) 类型错误：如将错误类型的值赋给变量、函数参数类型不匹配等；3) 作用域错误：如使用未声明的变量、在错误的作用域中访问变量等；4) 导入错误：如导入不存在的库、循环导入等。Dart的静态分析器会在编译时检查这些错误，并提供详细的错误信息和建议。理解这些错误信息对于快速定位和修复问题非常重要。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 常见编译错误示例和修复方法\n\nvoid main() {\n  // 1. 语法错误：缺少分号\n  // print('Hello') // 错误：Expected to find ';'\n  print('Hello'); // 正确\n  \n  // 2. 语法错误：括号不匹配\n  // if (true { // 错误：Expected to find ')'\n  if (true) { // 正确\n    print('条件为真');\n  }\n  \n  // 3. 类型错误：类型不匹配\n  // int number = 'text'; // 错误：A value of type 'String' can't be assigned to a variable of type 'int'\n  int number = 42; // 正确\n  String text = 'text'; // 正确\n  \n  // 4. 类型错误：函数参数类型不匹配\n  // calculateSum('5', '3'); // 错误：参数类型不匹配\n  calculateSum(5, 3); // 正确\n  \n  // 5. 作用域错误：使用未声明的变量\n  // print(undeclaredVariable); // 错误：Undefined name 'undeclaredVariable'\n  String declaredVariable = 'value';\n  print(declaredVariable); // 正确\n  \n  // 6. 空安全错误：可能为null的值\n  String? nullableString;\n  // print(nullableString.length); // 错误：The property 'length' can't be unconditionally accessed\n  print(nullableString?.length ?? 0); // 正确：使用空安全操作符\n  \n  // 7. 常量错误：修改final变量\n  final int finalValue = 10;\n  // finalValue = 20; // 错误：Can't assign to the final variable 'finalValue'\n  \n  // 8. 类型转换错误\n  String numberString = '42';\n  // int converted = numberString; // 错误：类型不匹配\n  int converted = int.parse(numberString); // 正确：显式转换\n  \n  // 9. 集合类型错误\n  List<int> numbers = [1, 2, 3];\n  // numbers.add('4'); // 错误：The argument type 'String' can't be assigned to the parameter type 'int'\n  numbers.add(4); // 正确\n  \n  // 10. 函数返回类型错误\n  // String result = getNumber(); // 错误：返回类型不匹配\n  int result = getNumber(); // 正确\n}\n\n// 正确的函数定义\nint calculateSum(int a, int b) {\n  return a + b;\n}\n\nint getNumber() {\n  return 42;\n}\n\n// 常见的类定义错误\nclass Person {\n  String name;\n  int age;\n  \n  // 构造函数错误示例\n  // Person(this.name); // 错误：age字段未初始化\n  \n  // 正确的构造函数\n  Person(this.name, this.age);\n  \n  // 方法定义错误\n  // void greet() {\n  //   print('Hello, $undefinedVariable'); // 错误：未定义的变量\n  // }\n  \n  // 正确的方法\n  void greet() {\n    print('Hello, $name');\n  }\n}", "explanation": "这个示例展示了Dart中最常见的编译错误类型及其修复方法。包括语法错误（如缺少分号、括号不匹配）、类型错误（如类型不匹配、参数类型错误）、作用域错误（如使用未声明变量）、空安全错误、常量修改错误等。每个错误都提供了错误示例和正确的修复方法。"}]}}, {"name": "Runtime Errors and Exceptions", "trans": ["运行时错误与异常"], "usage": {"syntax": "// 捕获运行时异常\ntry {\n  // 可能抛出异常的代码\n  int result = int.parse('invalid');\n} catch (e) {\n  print('捕获到异常: $e');\n}\n\n// 特定异常类型捕获\ntry {\n  // 代码\n} on FormatException catch (e) {\n  print('格式异常: $e');\n} on RangeError catch (e) {\n  print('范围错误: $e');\n} catch (e) {\n  print('其他异常: $e');\n}", "description": "运行时错误是程序在执行过程中发生的错误，这些错误在编译时无法检测到。常见的运行时错误包括：1) 空指针异常：访问null对象的属性或方法；2) 类型转换异常：如字符串转数字失败；3) 索引越界异常：访问列表或字符串的无效索引；4) 除零异常：整数除以零；5) 文件操作异常：文件不存在或权限不足；6) 网络异常：网络连接失败或超时。Dart提供了完善的异常处理机制，使用try-catch-finally语句可以优雅地处理这些异常。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import 'dart:io';\nimport 'dart:math';\n\nvoid main() {\n  print('=== 运行时错误处理示例 ===\\n');\n  \n  // 1. 空指针异常处理\n  demonstrateNullPointerError();\n  \n  // 2. 类型转换异常\n  demonstrateFormatException();\n  \n  // 3. 索引越界异常\n  demonstrateRangeError();\n  \n  // 4. 除零异常\n  demonstrateDivisionError();\n  \n  // 5. 文件操作异常\n  demonstrateFileError();\n  \n  // 6. 自定义异常\n  demonstrateCustomException();\n}\n\nvoid demonstrateNullPointerError() {\n  print('1. 空指针异常处理:');\n  \n  String? nullableString;\n  \n  try {\n    // 错误：尝试访问null对象的属性\n    // print(nullableString.length); // 这会抛出异常\n    \n    // 正确的处理方式\n    if (nullableString != null) {\n      print('字符串长度: ${nullableString.length}');\n    } else {\n      print('字符串为null，无法获取长度');\n    }\n    \n    // 使用空安全操作符\n    print('安全获取长度: ${nullableString?.length ?? 0}');\n    \n  } catch (e) {\n    print('捕获到空指针异常: $e');\n  }\n  \n  print('');\n}\n\nvoid demonstrateFormatException() {\n  print('2. 类型转换异常:');\n  \n  List<String> inputs = ['42', '3.14', 'abc', '', '  123  '];\n  \n  for (String input in inputs) {\n    try {\n      int number = int.parse(input);\n      print('\"$input\" 转换为整数: $number');\n    } on FormatException catch (e) {\n      print('\"$input\" 转换失败: ${e.message}');\n      \n      // 尝试其他转换方式\n      int? tryParse = int.tryParse(input.trim());\n      if (tryParse != null) {\n        print('  使用tryParse成功: $tryParse');\n      } else {\n        print('  tryParse也失败了');\n      }\n    } catch (e) {\n      print('\"$input\" 其他异常: $e');\n    }\n  }\n  \n  print('');\n}\n\nvoid demonstrateRangeError() {\n  print('3. 索引越界异常:');\n  \n  List<String> fruits = ['苹果', '香蕉', '橙子'];\n  List<int> indices = [0, 1, 2, 3, -1, 10];\n  \n  for (int index in indices) {\n    try {\n      String fruit = fruits[index];\n      print('索引 $index: $fruit');\n    } on RangeError catch (e) {\n      print('索引 $index 越界: ${e.message}');\n      print('  有效索引范围: 0 到 ${fruits.length - 1}');\n    } catch (e) {\n      print('索引 $index 其他错误: $e');\n    }\n  }\n  \n  // 安全的索引访问\n  print('\\n安全访问示例:');\n  for (int index in indices) {\n    if (index >= 0 && index < fruits.length) {\n      print('安全访问索引 $index: ${fruits[index]}');\n    } else {\n      print('索引 $index 超出范围，跳过');\n    }\n  }\n  \n  print('');\n}\n\nvoid demonstrateDivisionError() {\n  print('4. 除零异常:');\n  \n  List<List<int>> operations = [\n    [10, 2],\n    [15, 3],\n    [8, 0],\n    [0, 5],\n    [7, 0]\n  ];\n  \n  for (List<int> op in operations) {\n    int a = op[0];\n    int b = op[1];\n    \n    try {\n      // 浮点除法\n      double result = a / b;\n      print('$a / $b = $result');\n      \n      // 整数除法\n      if (b != 0) {\n        int intResult = a ~/ b;\n        print('$a ~/ $b = $intResult (整除)');\n      }\n    } on IntegerDivisionByZeroException catch (e) {\n      print('$a ~/ $b: 整数除零错误 - $e');\n    } catch (e) {\n      print('$a / $b: 其他错误 - $e');\n    }\n  }\n  \n  print('');\n}\n\nvoid demonstrateFileError() {\n  print('5. 文件操作异常:');\n  \n  List<String> filePaths = [\n    'existing_file.txt',\n    'nonexistent_file.txt',\n    '/root/protected_file.txt'\n  ];\n  \n  for (String path in filePaths) {\n    try {\n      File file = File(path);\n      \n      // 检查文件是否存在\n      if (file.existsSync()) {\n        String content = file.readAsStringSync();\n        print('成功读取 $path: ${content.length} 字符');\n      } else {\n        print('文件不存在: $path');\n      }\n    } on FileSystemException catch (e) {\n      print('文件系统异常 $path: ${e.message}');\n    } on PathAccessException catch (e) {\n      print('路径访问异常 $path: ${e.message}');\n    } catch (e) {\n      print('文件操作异常 $path: $e');\n    }\n  }\n  \n  print('');\n}\n\nvoid demonstrateCustomException() {\n  print('6. 自定义异常:');\n  \n  try {\n    validateAge(-5);\n  } on InvalidAgeException catch (e) {\n    print('年龄验证失败: ${e.message}');\n    print('错误代码: ${e.errorCode}');\n  }\n  \n  try {\n    validateAge(25);\n    print('年龄验证通过');\n  } catch (e) {\n    print('意外错误: $e');\n  }\n  \n  print('');\n}\n\n// 自定义异常类\nclass InvalidAgeException implements Exception {\n  final String message;\n  final int errorCode;\n  \n  const InvalidAgeException(this.message, this.errorCode);\n  \n  @override\n  String toString() {\n    return 'InvalidAgeException: $message (Code: $errorCode)';\n  }\n}\n\nvoid validateAge(int age) {\n  if (age < 0) {\n    throw InvalidAgeException('年龄不能为负数', 1001);\n  }\n  if (age > 150) {\n    throw InvalidAgeException('年龄不能超过150岁', 1002);\n  }\n}", "explanation": "这个示例展示了Dart中常见的运行时错误及其处理方法。包括空指针异常、类型转换异常、索引越界异常、除零异常、文件操作异常和自定义异常。每种异常都提供了错误示例、正确的处理方式和预防措施。"}]}}]}