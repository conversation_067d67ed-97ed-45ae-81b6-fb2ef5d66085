{"name": "Debugging and Common Errors", "trans": ["调试与常见错误"], "methods": [{"name": "Common Compilation Errors", "trans": ["常见编译错误"], "usage": {"syntax": "// 语法错误示例\n// 缺少分号\nprint('Hello World') // 错误：缺少分号\nprint('Hello World'); // 正确\n\n// 类型错误\nint number = 'text'; // 错误：类型不匹配\nint number = 42; // 正确\n\n// 未声明变量\nprint(undeclaredVar); // 错误：变量未声明\nvar declaredVar = 'value';\nprint(declaredVar); // 正确", "description": "编译错误是在代码编译阶段发现的错误，这些错误会阻止程序运行。常见的编译错误包括：1) 语法错误：如缺少分号、括号不匹配、关键字拼写错误等；2) 类型错误：如将错误类型的值赋给变量、函数参数类型不匹配等；3) 作用域错误：如使用未声明的变量、在错误的作用域中访问变量等；4) 导入错误：如导入不存在的库、循环导入等。Dart的静态分析器会在编译时检查这些错误，并提供详细的错误信息和建议。理解这些错误信息对于快速定位和修复问题非常重要。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 常见编译错误示例和修复方法\n\nvoid main() {\n  // 1. 语法错误：缺少分号\n  // print('Hello') // 错误：Expected to find ';'\n  print('Hello'); // 正确\n  \n  // 2. 语法错误：括号不匹配\n  // if (true { // 错误：Expected to find ')'\n  if (true) { // 正确\n    print('条件为真');\n  }\n  \n  // 3. 类型错误：类型不匹配\n  // int number = 'text'; // 错误：A value of type 'String' can't be assigned to a variable of type 'int'\n  int number = 42; // 正确\n  String text = 'text'; // 正确\n  \n  // 4. 类型错误：函数参数类型不匹配\n  // calculateSum('5', '3'); // 错误：参数类型不匹配\n  calculateSum(5, 3); // 正确\n  \n  // 5. 作用域错误：使用未声明的变量\n  // print(undeclaredVariable); // 错误：Undefined name 'undeclaredVariable'\n  String declaredVariable = 'value';\n  print(declaredVariable); // 正确\n  \n  // 6. 空安全错误：可能为null的值\n  String? nullableString;\n  // print(nullableString.length); // 错误：The property 'length' can't be unconditionally accessed\n  print(nullableString?.length ?? 0); // 正确：使用空安全操作符\n  \n  // 7. 常量错误：修改final变量\n  final int finalValue = 10;\n  // finalValue = 20; // 错误：Can't assign to the final variable 'finalValue'\n  \n  // 8. 类型转换错误\n  String numberString = '42';\n  // int converted = numberString; // 错误：类型不匹配\n  int converted = int.parse(numberString); // 正确：显式转换\n  \n  // 9. 集合类型错误\n  List<int> numbers = [1, 2, 3];\n  // numbers.add('4'); // 错误：The argument type 'String' can't be assigned to the parameter type 'int'\n  numbers.add(4); // 正确\n  \n  // 10. 函数返回类型错误\n  // String result = getNumber(); // 错误：返回类型不匹配\n  int result = getNumber(); // 正确\n}\n\n// 正确的函数定义\nint calculateSum(int a, int b) {\n  return a + b;\n}\n\nint getNumber() {\n  return 42;\n}\n\n// 常见的类定义错误\nclass Person {\n  String name;\n  int age;\n  \n  // 构造函数错误示例\n  // Person(this.name); // 错误：age字段未初始化\n  \n  // 正确的构造函数\n  Person(this.name, this.age);\n  \n  // 方法定义错误\n  // void greet() {\n  //   print('Hello, $undefinedVariable'); // 错误：未定义的变量\n  // }\n  \n  // 正确的方法\n  void greet() {\n    print('Hello, $name');\n  }\n}", "explanation": "这个示例展示了Dart中最常见的编译错误类型及其修复方法。包括语法错误（如缺少分号、括号不匹配）、类型错误（如类型不匹配、参数类型错误）、作用域错误（如使用未声明变量）、空安全错误、常量修改错误等。每个错误都提供了错误示例和正确的修复方法。"}]}}, {"name": "Runtime Errors and Exceptions", "trans": ["运行时错误与异常"], "usage": {"syntax": "// 捕获运行时异常\ntry {\n  // 可能抛出异常的代码\n  int result = int.parse('invalid');\n} catch (e) {\n  print('捕获到异常: $e');\n}\n\n// 特定异常类型捕获\ntry {\n  // 代码\n} on FormatException catch (e) {\n  print('格式异常: $e');\n} on RangeError catch (e) {\n  print('范围错误: $e');\n} catch (e) {\n  print('其他异常: $e');\n}", "description": "运行时错误是程序在执行过程中发生的错误，这些错误在编译时无法检测到。常见的运行时错误包括：1) 空指针异常：访问null对象的属性或方法；2) 类型转换异常：如字符串转数字失败；3) 索引越界异常：访问列表或字符串的无效索引；4) 除零异常：整数除以零；5) 文件操作异常：文件不存在或权限不足；6) 网络异常：网络连接失败或超时。Dart提供了完善的异常处理机制，使用try-catch-finally语句可以优雅地处理这些异常。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import 'dart:io';\nimport 'dart:math';\n\nvoid main() {\n  print('=== 运行时错误处理示例 ===\\n');\n  \n  // 1. 空指针异常处理\n  demonstrateNullPointerError();\n  \n  // 2. 类型转换异常\n  demonstrateFormatException();\n  \n  // 3. 索引越界异常\n  demonstrateRangeError();\n  \n  // 4. 除零异常\n  demonstrateDivisionError();\n  \n  // 5. 文件操作异常\n  demonstrateFileError();\n  \n  // 6. 自定义异常\n  demonstrateCustomException();\n}\n\nvoid demonstrateNullPointerError() {\n  print('1. 空指针异常处理:');\n  \n  String? nullableString;\n  \n  try {\n    // 错误：尝试访问null对象的属性\n    // print(nullableString.length); // 这会抛出异常\n    \n    // 正确的处理方式\n    if (nullableString != null) {\n      print('字符串长度: ${nullableString.length}');\n    } else {\n      print('字符串为null，无法获取长度');\n    }\n    \n    // 使用空安全操作符\n    print('安全获取长度: ${nullableString?.length ?? 0}');\n    \n  } catch (e) {\n    print('捕获到空指针异常: $e');\n  }\n  \n  print('');\n}\n\nvoid demonstrateFormatException() {\n  print('2. 类型转换异常:');\n  \n  List<String> inputs = ['42', '3.14', 'abc', '', '  123  '];\n  \n  for (String input in inputs) {\n    try {\n      int number = int.parse(input);\n      print('\"$input\" 转换为整数: $number');\n    } on FormatException catch (e) {\n      print('\"$input\" 转换失败: ${e.message}');\n      \n      // 尝试其他转换方式\n      int? tryParse = int.tryParse(input.trim());\n      if (tryParse != null) {\n        print('  使用tryParse成功: $tryParse');\n      } else {\n        print('  tryParse也失败了');\n      }\n    } catch (e) {\n      print('\"$input\" 其他异常: $e');\n    }\n  }\n  \n  print('');\n}\n\nvoid demonstrateRangeError() {\n  print('3. 索引越界异常:');\n  \n  List<String> fruits = ['苹果', '香蕉', '橙子'];\n  List<int> indices = [0, 1, 2, 3, -1, 10];\n  \n  for (int index in indices) {\n    try {\n      String fruit = fruits[index];\n      print('索引 $index: $fruit');\n    } on RangeError catch (e) {\n      print('索引 $index 越界: ${e.message}');\n      print('  有效索引范围: 0 到 ${fruits.length - 1}');\n    } catch (e) {\n      print('索引 $index 其他错误: $e');\n    }\n  }\n  \n  // 安全的索引访问\n  print('\\n安全访问示例:');\n  for (int index in indices) {\n    if (index >= 0 && index < fruits.length) {\n      print('安全访问索引 $index: ${fruits[index]}');\n    } else {\n      print('索引 $index 超出范围，跳过');\n    }\n  }\n  \n  print('');\n}\n\nvoid demonstrateDivisionError() {\n  print('4. 除零异常:');\n  \n  List<List<int>> operations = [\n    [10, 2],\n    [15, 3],\n    [8, 0],\n    [0, 5],\n    [7, 0]\n  ];\n  \n  for (List<int> op in operations) {\n    int a = op[0];\n    int b = op[1];\n    \n    try {\n      // 浮点除法\n      double result = a / b;\n      print('$a / $b = $result');\n      \n      // 整数除法\n      if (b != 0) {\n        int intResult = a ~/ b;\n        print('$a ~/ $b = $intResult (整除)');\n      }\n    } on IntegerDivisionByZeroException catch (e) {\n      print('$a ~/ $b: 整数除零错误 - $e');\n    } catch (e) {\n      print('$a / $b: 其他错误 - $e');\n    }\n  }\n  \n  print('');\n}\n\nvoid demonstrateFileError() {\n  print('5. 文件操作异常:');\n  \n  List<String> filePaths = [\n    'existing_file.txt',\n    'nonexistent_file.txt',\n    '/root/protected_file.txt'\n  ];\n  \n  for (String path in filePaths) {\n    try {\n      File file = File(path);\n      \n      // 检查文件是否存在\n      if (file.existsSync()) {\n        String content = file.readAsStringSync();\n        print('成功读取 $path: ${content.length} 字符');\n      } else {\n        print('文件不存在: $path');\n      }\n    } on FileSystemException catch (e) {\n      print('文件系统异常 $path: ${e.message}');\n    } on PathAccessException catch (e) {\n      print('路径访问异常 $path: ${e.message}');\n    } catch (e) {\n      print('文件操作异常 $path: $e');\n    }\n  }\n  \n  print('');\n}\n\nvoid demonstrateCustomException() {\n  print('6. 自定义异常:');\n  \n  try {\n    validateAge(-5);\n  } on InvalidAgeException catch (e) {\n    print('年龄验证失败: ${e.message}');\n    print('错误代码: ${e.errorCode}');\n  }\n  \n  try {\n    validateAge(25);\n    print('年龄验证通过');\n  } catch (e) {\n    print('意外错误: $e');\n  }\n  \n  print('');\n}\n\n// 自定义异常类\nclass InvalidAgeException implements Exception {\n  final String message;\n  final int errorCode;\n  \n  const InvalidAgeException(this.message, this.errorCode);\n  \n  @override\n  String toString() {\n    return 'InvalidAgeException: $message (Code: $errorCode)';\n  }\n}\n\nvoid validateAge(int age) {\n  if (age < 0) {\n    throw InvalidAgeException('年龄不能为负数', 1001);\n  }\n  if (age > 150) {\n    throw InvalidAgeException('年龄不能超过150岁', 1002);\n  }\n}", "explanation": "这个示例展示了Dart中常见的运行时错误及其处理方法。包括空指针异常、类型转换异常、索引越界异常、除零异常、文件操作异常和自定义异常。每种异常都提供了错误示例、正确的处理方式和预防措施。"}]}}, {"name": "Debugging Tools and Techniques", "trans": ["调试工具与技巧"], "usage": {"syntax": "// 使用print调试\nprint('调试信息: $variable');\n\n// 使用assert断言\nassert(condition, '错误信息');\n\n// 使用debugger()暂停执行\nimport 'dart:developer';\ndebugger();\n\n// 使用log输出\nimport 'dart:developer';\nlog('调试信息', name: 'MyApp');\n\n// 条件调试\nif (kDebugMode) {\n  print('仅在调试模式下输出');\n}", "description": "Dart提供了多种调试工具和技巧来帮助开发者定位和解决问题。常用的调试方法包括：1) print调试：最简单的调试方式，在关键位置输出变量值和程序状态；2) assert断言：在开发阶段验证程序假设，生产环境会被忽略；3) debugger()函数：在代码中设置断点，暂停程序执行；4) log函数：比print更专业的日志输出，支持分类和过滤；5) IDE调试器：使用断点、单步执行、变量监视等功能；6) 条件编译：使用kDebugMode等常量控制调试代码的执行。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "import 'dart:developer';\nimport 'dart:io';\n\nvoid main() {\n  print('=== Dart调试技巧演示 ===\\n');\n  \n  // 1. 基本print调试\n  demonstratePrintDebugging();\n  \n  // 2. assert断言调试\n  demonstrateAssertDebugging();\n  \n  // 3. log函数调试\n  demonstrateLogDebugging();\n  \n  // 4. 调试复杂数据结构\n  demonstrateComplexDataDebugging();\n  \n  // 5. 性能调试\n  demonstratePerformanceDebugging();\n}\n\nvoid demonstratePrintDebugging() {\n  print('1. Print调试技巧:');\n  \n  List<int> numbers = [1, 2, 3, 4, 5];\n  \n  // 基本变量调试\n  print('原始列表: $numbers');\n  \n  // 循环调试\n  for (int i = 0; i < numbers.length; i++) {\n    int doubled = numbers[i] * 2;\n    print('索引 $i: ${numbers[i]} -> $doubled');\n  }\n  \n  // 函数调用调试\n  int sum = calculateSum(numbers);\n  print('计算结果: $sum');\n  \n  // 条件调试\n  bool isEven = sum % 2 == 0;\n  print('和是否为偶数: $isEven');\n  \n  print('');\n}\n\nint calculateSum(List<int> numbers) {\n  print('[DEBUG] 开始计算和，列表长度: ${numbers.length}');\n  \n  int sum = 0;\n  for (int number in numbers) {\n    sum += number;\n    print('[DEBUG] 当前数字: $number, 累计和: $sum');\n  }\n  \n  print('[DEBUG] 计算完成，最终和: $sum');\n  return sum;\n}\n\nvoid demonstrateAssertDebugging() {\n  print('2. Assert断言调试:');\n  \n  try {\n    // 正常情况\n    int age = 25;\n    assert(age >= 0, '年龄不能为负数');\n    assert(age <= 150, '年龄不能超过150');\n    print('年龄验证通过: $age');\n    \n    // 异常情况（在调试模式下会抛出异常）\n    int invalidAge = -5;\n    print('尝试验证无效年龄: $invalidAge');\n    // assert(invalidAge >= 0, '年龄不能为负数'); // 这会在调试模式下失败\n    \n    // 使用assert验证函数参数\n    double result = divide(10, 2);\n    print('除法结果: $result');\n    \n  } catch (e) {\n    print('Assert异常: $e');\n  }\n  \n  print('');\n}\n\ndouble divide(double a, double b) {\n  assert(b != 0, '除数不能为零');\n  return a / b;\n}\n\nvoid demonstrateLogDebugging() {\n  print('3. Log函数调试:');\n  \n  // 基本日志\n  log('应用启动', name: 'MyApp');\n  \n  // 带级别的日志\n  log('这是一条信息日志', name: 'MyApp', level: 800);\n  log('这是一条警告日志', name: 'MyApp', level: 900);\n  log('这是一条错误日志', name: 'MyApp', level: 1000);\n  \n  // 带时间戳的日志\n  log('操作开始', name: 'Operation', time: DateTime.now());\n  \n  // 模拟一些操作\n  for (int i = 1; i <= 3; i++) {\n    log('处理步骤 $i', name: 'Process');\n    // 模拟处理时间\n    sleep(Duration(milliseconds: 100));\n  }\n  \n  log('操作完成', name: 'Operation', time: DateTime.now());\n  \n  print('');\n}\n\nvoid demonstrateComplexDataDebugging() {\n  print('4. 复杂数据结构调试:');\n  \n  // 调试Map\n  Map<String, dynamic> user = {\n    'name': 'Alice',\n    'age': 30,\n    'hobbies': ['reading', 'swimming', 'coding'],\n    'address': {\n      'city': 'Beijing',\n      'country': 'China'\n    }\n  };\n  \n  print('用户信息:');\n  debugMap(user, '  ');\n  \n  // 调试List of Objects\n  List<Map<String, dynamic>> students = [\n    {'name': 'Bob', 'score': 85},\n    {'name': 'Charlie', 'score': 92},\n    {'name': 'David', 'score': 78}\n  ];\n  \n  print('\\n学生列表:');\n  for (int i = 0; i < students.length; i++) {\n    print('  学生 ${i + 1}:');\n    debugMap(students[i], '    ');\n  }\n  \n  print('');\n}\n\nvoid debugMap(Map<String, dynamic> map, String indent) {\n  map.forEach((key, value) {\n    if (value is Map) {\n      print('$indent$key:');\n      debugMap(value as Map<String, dynamic>, '$indent  ');\n    } else if (value is List) {\n      print('$indent$key: $value');\n    } else {\n      print('$indent$key: $value');\n    }\n  });\n}\n\nvoid demonstratePerformanceDebugging() {\n  print('5. 性能调试:');\n  \n  // 测量执行时间\n  Stopwatch stopwatch = Stopwatch();\n  \n  stopwatch.start();\n  \n  // 模拟一些计算密集型操作\n  List<int> numbers = List.generate(100000, (index) => index);\n  int sum = 0;\n  \n  for (int number in numbers) {\n    sum += number;\n  }\n  \n  stopwatch.stop();\n  \n  print('计算 ${numbers.length} 个数字的和');\n  print('结果: $sum');\n  print('耗时: ${stopwatch.elapsedMilliseconds} 毫秒');\n  \n  // 内存使用调试\n  print('\\n内存使用情况:');\n  print('列表占用内存估算: ${numbers.length * 8} 字节'); // int通常占8字节\n  \n  // 比较不同算法的性能\n  print('\\n算法性能比较:');\n  compareAlgorithms();\n  \n  print('');\n}\n\nvoid compareAlgorithms() {\n  List<int> data = List.generate(10000, (index) => index);\n  \n  // 算法1：普通循环\n  Stopwatch sw1 = Stopwatch()..start();\n  int sum1 = 0;\n  for (int i = 0; i < data.length; i++) {\n    sum1 += data[i];\n  }\n  sw1.stop();\n  \n  // 算法2：for-in循环\n  Stopwatch sw2 = Stopwatch()..start();\n  int sum2 = 0;\n  for (int value in data) {\n    sum2 += value;\n  }\n  sw2.stop();\n  \n  // 算法3：reduce方法\n  Stopwatch sw3 = Stopwatch()..start();\n  int sum3 = data.reduce((a, b) => a + b);\n  sw3.stop();\n  \n  print('普通循环: ${sw1.elapsedMicroseconds} 微秒, 结果: $sum1');\n  print('for-in循环: ${sw2.elapsedMicroseconds} 微秒, 结果: $sum2');\n  print('reduce方法: ${sw3.elapsedMicroseconds} 微秒, 结果: $sum3');\n}", "explanation": "这个示例展示了Dart中各种调试技巧的使用。包括基本的print调试、assert断言调试、log函数的专业日志输出、复杂数据结构的调试方法，以及性能调试技术。这些技巧可以帮助开发者快速定位问题、验证程序逻辑和优化性能。"}]}}, {"name": "Assignment", "trans": ["实践作业"], "usage": {"syntax": "// 调试与错误处理实践", "description": "完成以下任务，练习Dart的调试技巧和错误处理：1) 创建一个包含多种编译错误的程序，然后逐一修复这些错误；2) 编写一个程序，故意包含运行时错误，使用try-catch处理这些异常；3) 实现一个计算器程序，使用assert验证输入参数，使用log记录操作过程；4) 创建一个文件处理程序，包含完整的异常处理机制；5) 挑战：实现一个调试工具类，提供格式化输出、性能测量、内存监控等功能。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 参考实现：调试工具类\n\nimport 'dart:developer';\nimport 'dart:io';\n\nclass DebugUtils {\n  static bool _debugMode = true;\n  static final Map<String, Stopwatch> _timers = {};\n  \n  // 设置调试模式\n  static void setDebugMode(bool enabled) {\n    _debugMode = enabled;\n  }\n  \n  // 格式化调试输出\n  static void debug(String message, {String? tag, int level = 0}) {\n    if (!_debugMode) return;\n    \n    String timestamp = DateTime.now().toString().substring(11, 23);\n    String prefix = tag != null ? '[$tag]' : '[DEBUG]';\n    String indent = '  ' * level;\n    \n    print('$timestamp $prefix $indent$message');\n  }\n  \n  // 错误输出\n  static void error(String message, {String? tag, Object? exception}) {\n    String timestamp = DateTime.now().toString().substring(11, 23);\n    String prefix = tag != null ? '[$tag]' : '[ERROR]';\n    \n    stderr.writeln('$timestamp $prefix $message');\n    if (exception != null) {\n      stderr.writeln('  异常详情: $exception');\n    }\n  }\n  \n  // 警告输出\n  static void warn(String message, {String? tag}) {\n    if (!_debugMode) return;\n    \n    String timestamp = DateTime.now().toString().substring(11, 23);\n    String prefix = tag != null ? '[$tag]' : '[WARN]';\n    \n    print('$timestamp $prefix $message');\n  }\n  \n  // 开始计时\n  static void startTimer(String name) {\n    _timers[name] = Stopwatch()..start();\n    debug('开始计时: $name', tag: 'TIMER');\n  }\n  \n  // 结束计时\n  static void endTimer(String name) {\n    Stopwatch? timer = _timers[name];\n    if (timer != null) {\n      timer.stop();\n      debug('计时结束: $name - ${timer.elapsedMilliseconds}ms', tag: 'TIMER');\n      _timers.remove(name);\n    } else {\n      warn('计时器不存在: $name', tag: 'TIMER');\n    }\n  }\n  \n  // 打印对象详情\n  static void printObject(Object? obj, {String? name, int maxDepth = 3}) {\n    if (!_debugMode) return;\n    \n    String objName = name ?? 'Object';\n    debug('=== $objName 详情 ===', tag: 'OBJECT');\n    _printObjectRecursive(obj, 0, maxDepth);\n    debug('=== $objName 结束 ===', tag: 'OBJECT');\n  }\n  \n  static void _printObjectRecursive(Object? obj, int depth, int maxDepth) {\n    if (depth > maxDepth) {\n      debug('... (超过最大深度)', level: depth);\n      return;\n    }\n    \n    if (obj == null) {\n      debug('null', level: depth);\n    } else if (obj is Map) {\n      debug('Map (${obj.length} 项):', level: depth);\n      obj.forEach((key, value) {\n        debug('$key:', level: depth + 1);\n        _printObjectRecursive(value, depth + 2, maxDepth);\n      });\n    } else if (obj is List) {\n      debug('List (${obj.length} 项):', level: depth);\n      for (int i = 0; i < obj.length; i++) {\n        debug('[$i]:', level: depth + 1);\n        _printObjectRecursive(obj[i], depth + 2, maxDepth);\n      }\n    } else {\n      debug('${obj.runtimeType}: $obj', level: depth);\n    }\n  }\n  \n  // 内存使用估算\n  static void printMemoryUsage(String operation) {\n    if (!_debugMode) return;\n    \n    // 这是一个简化的内存使用估算\n    debug('内存使用 - $operation', tag: 'MEMORY');\n    debug('注意：Dart没有直接的内存查询API，这里仅作演示', tag: 'MEMORY');\n  }\n  \n  // 断言包装\n  static void assertCondition(bool condition, String message, {String? tag}) {\n    try {\n      assert(condition, message);\n      debug('断言通过: $message', tag: tag ?? 'ASSERT');\n    } catch (e) {\n      error('断言失败: $message', tag: tag ?? 'ASSERT', exception: e);\n      rethrow;\n    }\n  }\n  \n  // 异常包装执行\n  static T? safeExecute<T>(T Function() operation, {String? operationName}) {\n    String name = operationName ?? 'Operation';\n    \n    try {\n      debug('开始执行: $name', tag: 'SAFE');\n      T result = operation();\n      debug('执行成功: $name', tag: 'SAFE');\n      return result;\n    } catch (e, stackTrace) {\n      error('执行失败: $name', tag: 'SAFE', exception: e);\n      debug('堆栈跟踪:\\n$stackTrace', tag: 'SAFE');\n      return null;\n    }\n  }\n}\n\n// 使用示例\nvoid main() {\n  print('=== 调试工具类演示 ===\\n');\n  \n  // 设置调试模式\n  DebugUtils.setDebugMode(true);\n  \n  // 基本调试输出\n  DebugUtils.debug('程序开始执行');\n  DebugUtils.warn('这是一个警告信息');\n  \n  // 计时功能\n  DebugUtils.startTimer('数据处理');\n  \n  // 模拟一些操作\n  List<int> data = List.generate(1000, (i) => i);\n  int sum = data.reduce((a, b) => a + b);\n  \n  DebugUtils.endTimer('数据处理');\n  DebugUtils.debug('计算结果: $sum');\n  \n  // 对象调试\n  Map<String, dynamic> testData = {\n    'name': 'Test',\n    'values': [1, 2, 3],\n    'nested': {\n      'key1': 'value1',\n      'key2': [4, 5, 6]\n    }\n  };\n  \n  DebugUtils.printObject(testData, name: 'TestData');\n  \n  // 断言测试\n  DebugUtils.safeExecute(() {\n    DebugUtils.assertCondition(sum > 0, '和应该大于0');\n    DebugUtils.assertCondition(data.isNotEmpty, '数据不应为空');\n    return sum;\n  }, operationName: '数据验证');\n  \n  // 异常处理测试\n  DebugUtils.safeExecute(() {\n    int result = 10 ~/ 0; // 故意制造异常\n    return result;\n  }, operationName: '除零测试');\n  \n  DebugUtils.debug('程序执行完成');\n}", "explanation": "这个参考实现展示了一个完整的调试工具类，包含了格式化输出、计时功能、对象详情打印、内存监控、断言包装和安全执行等功能。这个工具类可以在实际项目中使用，帮助开发者更好地调试和监控程序运行状态。"}]}}]}