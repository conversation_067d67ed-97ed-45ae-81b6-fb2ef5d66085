{"name": "Security & Compliance", "trans": ["安全与合规"], "methods": [{"name": "Image Security Scan", "trans": ["镜像安全扫描"], "usage": {"syntax": "docker scan <镜像名>\ntrivy image <镜像名>", "description": "通过docker scan或Trivy等工具对镜像进行漏洞扫描，及时发现安全隐患。", "parameters": [{"name": "docker scan", "description": "官方镜像安全扫描命令。"}, {"name": "trivy image", "description": "第三方Trivy工具扫描镜像漏洞。"}], "returnValue": "无返回值，输出镜像安全报告。", "examples": [{"code": "docker scan nginx\ntrivy image nginx:latest", "explanation": "分别用官方和Trivy工具扫描nginx镜像安全。"}]}}, {"name": "Principle of Least Privilege", "trans": ["最小权限原则"], "usage": {"syntax": "docker run --user <UID> ...\nUSER <UID> # Dockerfile中", "description": "容器应以非root用户运行，限制权限，降低安全风险。可通过--user参数或Dockerfile的USER指令实现。", "parameters": [{"name": "--user", "description": "指定容器运行用户。"}, {"name": "USER", "description": "Dockerfile中指定运行用户。"}], "returnValue": "无返回值，提升容器安全性。", "examples": [{"code": "docker run --user 1000 nginx\n# Dockerfile中\nUSER 1000", "explanation": "以普通用户运行容器，避免root权限。"}]}}, {"name": "Image Signature & Verification", "trans": ["镜像签名与验证"], "usage": {"syntax": "docker trust sign <镜像名>:<标签>\ndocker trust inspect --pretty <镜像名>", "description": "通过Docker Content Trust对镜像进行签名和验证，防止镜像被篡改。", "parameters": [{"name": "docker trust sign", "description": "为镜像签名。"}, {"name": "docker trust inspect", "description": "验证镜像签名。"}], "returnValue": "无返回值，保障镜像来源可信。", "examples": [{"code": "docker trust sign myapp:1.0\ndocker trust inspect --pretty myapp:1.0", "explanation": "签名并验证myapp:1.0镜像的可信性。"}]}}, {"name": "Container User Management", "trans": ["容器运行用户管理"], "usage": {"syntax": "docker exec -u <UID> <容器> ...\nUSER <UID> # Dockerfile中", "description": "通过指定容器运行用户，避免高权限操作，提升安全性。可在运行时或Dockerfile中设置。", "parameters": [{"name": "-u/--user", "description": "指定exec命令的用户。"}, {"name": "USER", "description": "Dockerfile中指定默认用户。"}], "returnValue": "无返回值，规范容器用户权限。", "examples": [{"code": "docker exec -u 1000 myapp bash\n# Dockerfile中\nUSER 1000", "explanation": "以普通用户进入容器，避免高权限操作。"}]}}, {"name": "Security Hardening Advice", "trans": ["安全加固建议"], "usage": {"syntax": "# 常见加固措施\n1. 只开放必要端口\n2. 定期更新镜像\n3. 使用只读文件系统\n4. 限制资源使用\n5. 启用AppArmor/SELinux等安全模块", "description": "通过端口限制、镜像更新、只读文件系统、资源限制和安全模块等多种手段提升容器安全。", "parameters": [{"name": "端口限制", "description": "只映射和开放业务所需端口。"}, {"name": "镜像更新", "description": "定期拉取和重建最新镜像。"}, {"name": "只读文件系统", "description": "docker run --read-only参数。"}, {"name": "资源限制", "description": "--memory、--cpus等参数。"}, {"name": "安全模块", "description": "AppArmor、SELinux等。"}], "returnValue": "无返回值，提升整体安全防护能力。", "examples": [{"code": "docker run --read-only --memory=512m -p 80:80 nginx", "explanation": "只读文件系统+资源限制+端口限制的安全运行示例。"}]}}, {"name": "Assignment: 安全与合规实践", "trans": ["作业：安全与合规实践"], "usage": {"syntax": "请完成以下任务：\n1. 对镜像进行安全扫描并输出报告\n2. 以非root用户运行容器\n3. 为镜像签名并验证可信性\n4. 编写安全加固的容器运行命令", "description": "通过实际操作掌握镜像安全扫描、最小权限、签名验证和安全加固。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 1. 镜像安全扫描\ndocker scan nginx\n# 2. 非root用户运行\ndocker run --user 1000 nginx\n# 3. 镜像签名与验证\ndocker trust sign myapp:1.0\ndocker trust inspect --pretty myapp:1.0\n# 4. 安全加固运行\ndocker run --read-only --memory=512m -p 80:80 nginx", "explanation": "依次完成安全扫描、最小权限、签名验证和加固实践。"}]}}]}