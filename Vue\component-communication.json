{"name": "Component Communication Patterns", "trans": ["组件通信模式"], "methods": [{"name": "Props and Emit", "trans": ["props与emit"], "usage": {"syntax": "// 父传子\n<MyComp :msg=\"message\" />\n// 子组件props接收\nprops: ['msg']\n// 子传父\nthis.$emit('update', value)\n// 父组件监听\n<MyComp @update=\"onUpdate\" />", "description": "props用于父组件向子组件传递数据，emit用于子组件向父组件发送事件，是最常用的通信方式。", "parameters": [{"name": "props", "description": "父传子数据"}, {"name": "$emit", "description": "子传父事件"}], "returnValue": "props为子组件提供数据，emit触发父组件事件，无返回值", "examples": [{"code": "// 父组件\n<MyComp :msg=\"message\" @update=\"onUpdate\" />\n// 子组件\nprops: ['msg']\nthis.$emit('update', 123)", "explanation": "父组件通过props传递数据，子组件通过emit通知父组件。"}]}}, {"name": "Provide/Inject", "trans": ["provide/inject"], "usage": {"syntax": "provide('key', value)\nconst val = inject('key')", "description": "provide/inject用于祖先组件向任意后代组件传递数据，适合跨层级通信。", "parameters": [{"name": "provide", "description": "祖先组件提供数据"}, {"name": "inject", "description": "后代组件注入数据"}], "returnValue": "后代组件可访问祖先组件数据", "examples": [{"code": "// 父组件\nprovide('color', 'red')\n// 孙组件\nconst color = inject('color')", "explanation": "父组件通过provide提供数据，孙组件通过inject获取。"}]}}, {"name": "EventBus", "trans": ["eventBus"], "usage": {"syntax": "// 创建eventBus\nconst bus = mitt()\n// 发送事件\nbus.emit('event', data)\n// 监听事件\nbus.on('event', handler)", "description": "eventBus是一种全局事件通信方案，适合兄弟组件、跨层级组件通信。Vue3推荐使用mitt等库实现。", "parameters": [{"name": "emit", "description": "发送事件"}, {"name": "on", "description": "监听事件"}], "returnValue": "事件总线实现全局通信，无返回值", "examples": [{"code": "import mitt from 'mitt'\nconst bus = mitt()\nbus.on('custom', data => console.log(data))\nbus.emit('custom', 123)", "explanation": "通过mitt实现全局事件总线通信。"}]}}, {"name": "Vuex/Pinia", "trans": ["Vuex/Pinia"], "usage": {"syntax": "// Vuex\nimport { useStore } from 'vuex'\nconst store = useStore()\nstore.state.xxx\nstore.commit('mutation')\n// Pinia\nimport { useCounterStore } from '@/stores/counter'\nconst counter = useCounterStore()\ncounter.count++", "description": "Vuex和Pinia是官方推荐的全局状态管理方案，适合大型应用复杂通信。Pinia为Vue3官方新一代状态库。", "parameters": [{"name": "store", "description": "全局状态对象"}], "returnValue": "全局共享的响应式状态", "examples": [{"code": "// Pinia用法\nimport { useCounterStore } from '@/stores/counter'\nconst counter = useCounterStore()\ncounter.count++", "explanation": "通过Pinia实现全局状态共享和通信。"}]}}, {"name": "$attrs and $listeners", "trans": ["$attrs与$listeners"], "usage": {"syntax": "// 子组件\n<template>\n  <input v-bind=\"$attrs\" />\n</template>\n// 父组件\n<MyInput placeholder=\"请输入\" @focus=\"onFocus\" />", "description": "$attrs包含父组件传递但未被props接收的属性，$listeners包含所有事件监听器，适合多级组件透传。Vue3中$listeners已合并到$attrs。", "parameters": [{"name": "$attrs", "description": "未被props接收的属性和事件"}], "returnValue": "实现属性和事件的透传", "examples": [{"code": "// 子组件\n<template>\n  <input v-bind=\"$attrs\" />\n</template>\n// 父组件\n<MyInput placeholder=\"请输入\" @focus=\"onFocus\" />", "explanation": "通过$attrs实现属性和事件的多级透传。"}]}}, {"name": "v-model Two-way Binding", "trans": ["v-model双向绑定"], "usage": {"syntax": "// 父组件\n<MyInput v-model=\"value\" />\n// 子组件\nprops: ['modelValue']\nthis.$emit('update:modelValue', newValue)", "description": "v-model实现父子组件间的双向数据绑定，子组件通过modelValue和update:modelValue事件与父组件同步。", "parameters": [{"name": "modelValue", "description": "父组件传递的值"}, {"name": "update:modelValue", "description": "子组件更新事件"}], "returnValue": "父子组件数据实时同步", "examples": [{"code": "// 父组件\n<MyInput v-model=\"value\" />\n// 子组件\nprops: ['modelValue']\nthis.$emit('update:modelValue', newValue)", "explanation": "通过v-model实现父子组件双向绑定。"}]}}, {"name": "Component Communication Assignment", "trans": ["组件通信练习"], "usage": {"syntax": "# 组件通信练习", "description": "完成以下练习，巩固组件通信相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用props和emit实现父子通信。\n2. 用provide/inject实现跨层级通信。\n3. 用mitt实现eventBus通信。\n4. 用Pinia实现全局状态共享。\n5. 用$attrs透传属性和事件。\n6. 用v-model实现双向绑定。", "explanation": "通过这些练习掌握各种组件通信模式。"}]}}]}