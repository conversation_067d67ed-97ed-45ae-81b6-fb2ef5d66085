{"name": "Template Syntax", "trans": ["模板语法"], "methods": [{"name": "Interpolation", "trans": ["插值表达式"], "usage": {"syntax": "{{ 表达式 }}", "description": "在模板中使用双大括号插入变量或表达式的结果，常用于展示数据。表达式只能是单个表达式，不能包含语句。", "parameters": [{"name": "表达式", "description": "可访问的数据属性或计算表达式"}], "returnValue": "表达式的计算结果，渲染为字符串", "examples": [{"code": "<template>\n  <div>你好，{{ username }}！</div>\n  <div>2 + 3 = {{ 2 + 3 }}</div>\n</template>\n<script>\nexport default {\n  data() {\n    return { username: '小明' }\n  }\n}\n</script>", "explanation": "第一个插值渲染变量username，第二个插值直接渲染表达式2+3的结果。"}]}}, {"name": "Directives", "trans": ["指令（v-bind、v-if、v-for等）"], "usage": {"syntax": "<div v-bind:属性=\"表达式\"></div>\n<div v-if=\"条件\"></div>\n<div v-for=\"item in list\"></div>", "description": "指令是带有v-前缀的特殊属性，用于在模板中实现动态绑定、条件渲染、列表渲染等功能。常用指令有v-bind、v-if、v-for等。", "parameters": [{"name": "v-bind", "description": "动态绑定属性"}, {"name": "v-if", "description": "条件渲染"}, {"name": "v-for", "description": "列表渲染"}], "returnValue": "根据指令类型动态渲染DOM结构或属性", "examples": [{"code": "<template>\n  <img v-bind:src=\"imgUrl\" />\n  <div v-if=\"isShow\">显示内容</div>\n  <li v-for=\"item in items\" :key=\"item.id\">{{ item.name }}</li>\n</template>\n<script>\nexport default {\n  data() {\n    return {\n      imgUrl: 'logo.png',\n      isShow: true,\n      items: [{id:1, name:'A'},{id:2, name:'B'}]\n    }\n  }\n}\n</script>", "explanation": "v-bind动态绑定图片src，v-if控制内容显示，v-for遍历数组渲染列表。"}]}}, {"name": "Event Binding", "trans": ["事件绑定（v-on）"], "usage": {"syntax": "<button v-on:click=\"方法名\">点击</button>\n<button @click=\"方法名\">点击</button>", "description": "通过v-on或@语法为元素绑定事件监听器，常用于响应用户交互。", "parameters": [{"name": "事件名", "description": "如click、input等"}, {"name": "方法名", "description": "组件中定义的事件处理函数"}], "returnValue": "无返回值，事件触发时执行方法", "examples": [{"code": "<template>\n  <button @click=\"sayHello\">点击</button>\n</template>\n<script>\nexport default {\n  methods: {\n    sayHello() {\n      alert('你好！')\n    }\n  }\n}\n</script>", "explanation": "点击按钮时触发sayHello方法，弹出提示。"}]}}, {"name": "Attribute Binding", "trans": ["属性绑定"], "usage": {"syntax": "<img v-bind:src=\"imgUrl\" />\n<img :src=\"imgUrl\" />", "description": "通过v-bind或:语法将数据动态绑定到HTML属性，实现属性值的动态变化。", "parameters": [{"name": "属性名", "description": "要绑定的HTML属性，如src、href等"}, {"name": "表达式", "description": "用于计算属性值的数据或表达式"}], "returnValue": "属性值根据表达式动态变化", "examples": [{"code": "<template>\n  <img :src=\"logoUrl\" alt=\"logo\" />\n</template>\n<script>\nexport default {\n  data() {\n    return { logoUrl: 'logo.png' }\n  }\n}\n</script>", "explanation": "img标签的src属性动态绑定到logoUrl变量。"}]}}, {"name": "Conditional Rendering", "trans": ["条件渲染"], "usage": {"syntax": "<div v-if=\"isShow\">显示内容</div>\n<div v-else>隐藏内容</div>\n<div v-show=\"isVisible\">可见内容</div>", "description": "通过v-if、v-else、v-show等指令，根据条件动态控制元素的显示与隐藏。v-if会销毁和重建DOM，v-show仅切换display样式。", "parameters": [{"name": "条件表达式", "description": "用于判断显示与否的布尔表达式"}], "returnValue": "根据条件渲染或隐藏DOM元素", "examples": [{"code": "<template>\n  <div v-if=\"isLogin\">欢迎回来</div>\n  <div v-else>请登录</div>\n  <div v-show=\"isVisible\">这段内容可见</div>\n</template>\n<script>\nexport default {\n  data() {\n    return { isLogin: false, isVisible: true }\n  }\n}\n</script>", "explanation": "v-if/v-else根据isLogin切换内容，v-show根据isVisible控制可见性。"}]}}, {"name": "List Rendering", "trans": ["列表渲染"], "usage": {"syntax": "<li v-for=\"item in items\" :key=\"item.id\">{{ item.name }}</li>", "description": "通过v-for指令遍历数组或对象，动态渲染列表项。建议为每项提供唯一key以优化渲染性能。", "parameters": [{"name": "item in items", "description": "遍历的数组或对象"}, {"name": ":key", "description": "每项的唯一标识"}], "returnValue": "渲染出对应的列表DOM结构", "examples": [{"code": "<template>\n  <ul>\n    <li v-for=\"user in users\" :key=\"user.id\">{{ user.name }}</li>\n  </ul>\n</template>\n<script>\nexport default {\n  data() {\n    return { users: [{id:1, name:'A'},{id:2, name:'B'}] }\n  }\n}\n</script>", "explanation": "v-for遍历users数组，渲染每个用户的姓名。"}]}}, {"name": "Filters (Vue 2.x)", "trans": ["过滤器（2.x）"], "usage": {"syntax": "{{ message | capitalize }}", "description": "过滤器用于对插值表达式的输出结果进行格式化处理。Vue3已移除过滤器，推荐用方法或计算属性替代。", "parameters": [{"name": "表达式", "description": "需要格式化的数据"}, {"name": "过滤器名", "description": "定义的过滤器函数名"}], "returnValue": "格式化后的字符串", "examples": [{"code": "<template>\n  <div>{{ name | capitalize }}</div>\n</template>\n<script>\nexport default {\n  data() { return { name: 'vue' } },\n  filters: {\n    capitalize(val) {\n      if (!val) return ''\n      return val.charAt(0).toUpperCase() + val.slice(1)\n    }\n  }\n}\n</script>", "explanation": "将name首字母大写后输出，仅适用于Vue2。"}]}}, {"name": "Template Reference (ref)", "trans": ["模板引用ref"], "usage": {"syntax": "<input ref=\"inputRef\" />", "description": "通过ref为模板元素或子组件注册引用，可在js中直接访问DOM或组件实例。", "parameters": [{"name": "ref名", "description": "引用的名称"}], "returnValue": "返回对应的DOM元素或组件实例", "examples": [{"code": "<template>\n  <input ref=\"myInput\" />\n  <button @click=\"focusInput\">聚焦</button>\n</template>\n<script>\nexport default {\n  methods: {\n    focusInput() {\n      this.$refs.myInput.focus()\n    }\n  }\n}\n</script>", "explanation": "通过ref获取input元素并在点击按钮时聚焦。"}]}}, {"name": "Template Syntax Assignment", "trans": ["模板语法练习"], "usage": {"syntax": "# 模板语法练习", "description": "完成以下练习，巩固模板语法的使用。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 使用插值表达式显示一个变量。\n2. 用v-bind动态绑定图片src。\n3. 用v-if和v-show分别控制元素显示。\n4. 用v-for渲染一个用户列表。\n5. 用ref获取input并实现按钮点击聚焦。", "explanation": "通过这些练习掌握模板语法的核心用法。"}]}}]}