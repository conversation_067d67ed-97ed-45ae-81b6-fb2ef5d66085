{"name": "Network Security", "trans": ["网络安全"], "methods": [{"name": "Ports and Firewall", "trans": ["端口与防火墙"], "usage": {"syntax": "通过配置端口和防火墙规则，限制Redis访问范围。", "description": "建议只开放必要端口（默认6379），并通过防火墙限制外部访问，防止未授权连接。", "parameters": [{"name": "port", "description": "Redis服务监听端口。"}, {"name": "防火墙规则", "description": "允许或拒绝特定IP访问。"}], "returnValue": "无返回值", "examples": [{"code": "# redis.conf配置端口\nport 6379\n# 防火墙规则示例（Linux）\niptables -A INPUT -p tcp --dport 6379 -s ***********/24 -j ACCEPT\niptables -A INPUT -p tcp --dport 6379 -j DROP\n", "explanation": "通过端口和防火墙规则限制Redis访问范围。"}]}}, {"name": "Bind to Local/Internal Network", "trans": ["只绑定本地/内网"], "usage": {"syntax": "通过bind参数只监听本地或内网IP，防止外部访问。", "description": "建议将Redis服务只绑定到127.0.0.1或内网IP，避免暴露在公网，提升安全性。", "parameters": [{"name": "bind", "description": "指定监听的IP地址。"}], "returnValue": "无返回值", "examples": [{"code": "# redis.conf配置只绑定本地\nbind 127.0.0.1\n# 或绑定内网IP\nbind *************\n", "explanation": "通过bind参数只监听本地或内网，防止外部访问。"}]}}, {"name": "Prevent Unauthorized Access", "trans": ["防止未授权访问"], "usage": {"syntax": "通过多重措施防止未授权访问，包括密码、ACL、加密和网络隔离。", "description": "建议同时配置密码、ACL、TLS加密和网络隔离，最大限度防止未授权访问和数据泄露。", "parameters": [{"name": "requirepass/ACL", "description": "设置访问密码和权限。"}, {"name": "TLS", "description": "开启加密传输。"}, {"name": "bind", "description": "只绑定本地或内网IP。"}], "returnValue": "无返回值", "examples": [{"code": "# redis.conf安全配置\nrequirepass strongPass\nbind 127.0.0.1\ntls-cert-file /path/to/cert.pem\n", "explanation": "通过多重措施防止未授权访问。"}]}}, {"name": "网络安全作业", "trans": ["作业：网络安全"], "usage": {"syntax": "请完成以下任务：\n1. 配置端口和防火墙规则。\n2. 只绑定本地或内网IP。\n3. 配置密码和TLS加密。", "description": "通过实际操作理解网络安全的配置和防护措施。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 作业示例\n# 1. 配置port和iptables规则\n# 2. 配置bind参数\n# 3. 配置requirepass和TLS\n", "explanation": "通过动手实验掌握网络安全的关键配置。"}]}}, {"name": "网络安全正确实现示例", "trans": ["正确实现卡片"], "usage": {"syntax": "网络安全的标准配置与防护建议。", "description": "展示如何标准配置端口、防火墙、绑定IP和加密，确保Redis安全运行。", "parameters": [{"name": "port", "description": "设置监听端口。"}, {"name": "bind", "description": "只绑定本地或内网。"}, {"name": "iptables", "description": "配置防火墙规则。"}, {"name": "TLS", "description": "开启加密。"}], "returnValue": "无返回值", "examples": [{"code": "# redis.conf标准安全配置\nport 6379\nbind 127.0.0.1\niptables -A INPUT -p tcp --dport 6379 -s ***********/24 -j ACCEPT\niptables -A INPUT -p tcp --dport 6379 -j DROP\ntls-cert-file /path/to/cert.pem\n", "explanation": "标准网络安全配置，保证Redis安全可靠。"}]}}]}