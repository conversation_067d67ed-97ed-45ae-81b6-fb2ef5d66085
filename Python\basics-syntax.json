{"name": "Basic Syntax", "trans": ["基础语法"], "methods": [{"name": "Python Interpreter and Execution", "trans": ["Python解释器与运行方式"], "usage": {"syntax": "python script.py 或 python3 script.py", "description": "Python代码可通过命令行直接运行.py文件，也可在交互式解释器中逐行执行。常见运行方式有：\n- 命令行运行脚本\n- 交互式解释器\n- 集成开发环境(IDE)\n- Jupyter Notebook", "parameters": [{"name": "script.py", "description": "要运行的Python脚本文件名"}], "returnValue": "无返回值", "examples": [{"code": "# 运行Python脚本\npython hello.py\n\n# 进入交互式解释器\npython\n>>> print('Hello, World!')\nHello, World!", "explanation": "展示了如何在命令行和交互式环境下运行Python代码。"}]}}, {"name": "Comments and Indentation", "trans": ["注释与缩进"], "usage": {"syntax": "# 单行注释\n'''多行注释'''\n缩进：4个空格", "description": "Python使用#进行单行注释，三引号('''或\"\"\")可用于多行注释。缩进是语法的一部分，通常为4个空格，不能混用Tab和空格。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 这是单行注释\n'''\n这是多行注释\n可以用于文档说明\n'''\nif True:\n    print('缩进为4个空格')  # 正确\n# print('缩进错误') # 错误示例：缩进不一致会报错", "explanation": "演示了单行、多行注释和正确缩进的用法。"}]}}, {"name": "Identifiers and Keywords", "trans": ["标识符与关键字"], "usage": {"syntax": "变量名 = 值\n关键字：if, else, for, while, def, class 等", "description": "标识符用于命名变量、函数、类等，必须以字母或下划线开头，区分大小写。关键字是Python保留字，不能用作标识符。", "parameters": [{"name": "标识符", "description": "自定义的名称，如变量、函数名"}, {"name": "关键字", "description": "Python内置保留字"}], "returnValue": "无返回值", "examples": [{"code": "name = 'Alice'  # 合法标识符\n_foo = 123      # 下划线开头合法\n# 1var = 5      # 非法，不能以数字开头\n# if = 10      # 非法，if是关键字", "explanation": "展示了标识符命名规则和关键字的用法限制。"}]}}, {"name": "Code Blocks and Statements", "trans": ["代码块与语句"], "usage": {"syntax": "if 条件:\n    代码块\n单行语句; 多条语句用分号分隔", "description": "Python通过缩进表示代码块，不使用大括号。每行通常为一条语句，多个语句可用分号分隔。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "if True:\n    print('这是一个代码块')\nprint('单行语句'); print('多条语句')", "explanation": "演示了代码块的缩进和多条语句的分隔方式。"}]}}, {"name": "Input and Output", "trans": ["输入与输出"], "usage": {"syntax": "input('提示信息')\nprint(值, ...)\nprint(值, end='\n', sep=' ')", "description": "input()用于接收用户输入，返回字符串。print()用于输出内容，可指定分隔符和结尾。", "parameters": [{"name": "prompt", "description": "输入时的提示信息"}, {"name": "value", "description": "要输出的内容"}, {"name": "end", "description": "输出结尾字符，默认换行"}, {"name": "sep", "description": "多个值之间的分隔符，默认空格"}], "returnValue": "input返回字符串，print无返回值", "examples": [{"code": "name = input('请输入姓名：')\nprint('你好,', name)\nprint('A', 'B', 'C', sep='-', end='!')  # 输出：A-B-C!", "explanation": "演示了input和print的常用用法。"}]}}, {"name": "Interactive Programming", "trans": ["交互式编程"], "usage": {"syntax": "python\n>>> 表示交互式提示符", "description": "Python交互式解释器允许逐行输入和执行代码，适合调试和实验。可直接在命令行输入python进入。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": ">>> 2 + 3\n5\n>>> print('Hello')\nHello", "explanation": "展示了在交互式环境下直接输入和执行代码的效果。"}]}}, {"name": "Basic Syntax Assignment", "trans": ["基础语法练习"], "usage": {"syntax": "# 基础语法练习", "description": "完成以下练习，巩固基础语法知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 编写一个Python脚本，输出你的名字和年龄。\n2. 让用户输入一个数字，输出它的平方。\n3. 尝试在交互式解释器中执行简单的加法和字符串拼接。", "explanation": "练习要求动手实践输入输出、变量、交互式编程等基础语法。"}]}}]}