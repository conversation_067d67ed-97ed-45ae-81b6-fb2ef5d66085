{"name": "Dockerfile Best Practices", "trans": ["Dockerfile最佳实践"], "methods": [{"name": "Image Layer Optimization", "trans": ["镜像层优化"], "usage": {"syntax": "# 合并RUN指令\nRUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*", "description": "通过合并RUN指令、减少无用文件、合理排序指令等方式减少镜像层数和体积。", "parameters": [{"name": "RUN", "description": "合并多个命令为一条，减少镜像层。"}], "returnValue": "无返回值，镜像体积更小，构建更高效。", "examples": [{"code": "RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*", "explanation": "合并安装和清理命令，减少镜像层和体积。"}]}}, {"name": "Cache Utilization", "trans": ["缓存利用"], "usage": {"syntax": "# 先COPY package.json再COPY源码\nCOPY package.json ./\nRUN npm install\nCOPY . .", "description": "合理利用构建缓存，优先COPY依赖文件，减少无效重建，提高构建速度。", "parameters": [{"name": "COPY", "description": "先复制依赖文件，后复制源码。"}], "returnValue": "无返回值，提升构建速度。", "examples": [{"code": "COPY package.json ./\nRUN npm install\nCOPY . .", "explanation": "先安装依赖，源码变动不影响依赖缓存。"}]}}, {"name": "Security Recommendations", "trans": ["安全性建议"], "usage": {"syntax": "# 使用非root用户\nUSER appuser\n# 最小化基础镜像\nFROM alpine", "description": "建议使用最小权限用户、精简基础镜像、及时更新依赖，避免敏感信息写入镜像。", "parameters": [{"name": "USER", "description": "指定非root用户。"}, {"name": "FROM", "description": "选择alpine等精简镜像。"}], "returnValue": "无返回值，提升镜像安全性。", "examples": [{"code": "FROM alpine\nUSER appuser", "explanation": "使用alpine基础镜像并指定普通用户运行。"}]}}, {"name": "Build Speed Improvement", "trans": ["构建速度提升"], "usage": {"syntax": "# 多阶段构建+缓存\nFROM node:16 AS build\nWORKDIR /app\nCOPY package.json ./\nRUN npm install\nCOPY . .\nRUN npm run build\nFROM nginx:alpine\nCOPY --from=build /app/dist /usr/share/nginx/html", "description": "通过多阶段构建、缓存依赖、合并指令等方式提升构建速度。", "parameters": [{"name": "多阶段构建", "description": "分离构建和运行环境，提升效率。"}], "returnValue": "无返回值，构建更快。", "examples": [{"code": "FROM node:16 AS build\nWORKDIR /app\nCOPY package.json ./\nRUN npm install\nCOPY . .\nRUN npm run build\nFROM nginx:alpine\nCOPY --from=build /app/dist /usr/share/nginx/html", "explanation": "先构建后复制产物，充分利用缓存和多阶段。"}]}}, {"name": "Common Errors & Debugging", "trans": ["常见错误与调试"], "usage": {"syntax": "# 查看构建日志\ndocker build .\n# 进入中间容器调试\ndocker run -it <镜像ID> /bin/sh", "description": "通过查看构建日志、进入中间容器、分步调试等方式排查和修复Dockerfile错误。", "parameters": [{"name": "docker build", "description": "构建时输出详细日志。"}, {"name": "docker run", "description": "进入镜像调试。"}], "returnValue": "无返回值，便于定位和修复问题。", "examples": [{"code": "docker build .\ndocker run -it <镜像ID> /bin/sh", "explanation": "查看日志和进入容器排查问题。"}]}}, {"name": "Assignment: Dockerfile最佳实践实践", "trans": ["作业：Dockerfile最佳实践实践"], "usage": {"syntax": "请完成以下任务：\n1. 合理合并RUN指令优化镜像层\n2. 利用缓存加速依赖安装\n3. 使用非root用户和alpine基础镜像\n4. 多阶段构建提升速度\n5. 构建失败时调试并修复错误", "description": "通过实际操作掌握Dockerfile最佳实践，优化镜像体积、安全性和构建效率。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 1. 合并RUN指令\nRUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*\n# 2. 利用缓存\nCOPY package.json ./\nRUN npm install\nCOPY . .\n# 3. 非root用户和alpine\nFROM alpine\nUSER appuser\n# 4. 多阶段构建\nFROM node:16 AS build\n...\n# 5. 构建调试\ndocker build .\ndocker run -it <镜像ID> /bin/sh", "explanation": "依次完成镜像优化、安全、调试等最佳实践。"}]}}]}