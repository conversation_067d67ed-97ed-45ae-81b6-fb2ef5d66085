{"name": "Docker Architecture", "trans": ["Docker架构原理"], "methods": [{"name": "Architecture Components", "trans": ["架构组成"], "usage": {"syntax": "Docker = Client + Daemon + Images + Containers + Registry", "description": "Docker主要由客户端（Client）、守护进程（Daemon）、镜像（Images）、容器（Containers）和仓库（Registry）组成。客户端与守护进程通过REST API通信，守护进程负责容器和镜像的管理。", "parameters": [{"name": "Client", "description": "用户操作Docker的命令行或API接口。"}, {"name": "Daemon", "description": "后台运行的服务进程，负责实际的容器和镜像管理。"}, {"name": "Images", "description": "容器运行所需的只读模板。"}, {"name": "Containers", "description": "镜像的运行实例，包含应用和环境。"}, {"name": "Registry", "description": "镜像集中存储和分发的仓库。"}], "returnValue": "无返回值", "examples": [{"code": "# 查看Docker架构主要组件\n# 1. 客户端\ndocker version\n# 2. 守护进程\nsystemctl status docker\n# 3. 镜像\ndocker images\n# 4. 容器\ndocker ps\n# 5. 仓库\n# 通过docker pull/push与仓库交互", "explanation": "通过命令行查看和理解Docker的各个核心组件。"}]}}, {"name": "<PERSON>er <PERSON>", "trans": ["Docker守护进程"], "usage": {"syntax": "dockerd [OPTIONS]", "description": "Docker守护进程（dockerd）是后台服务，负责接收客户端请求、管理容器生命周期、镜像操作和网络存储等。", "parameters": [{"name": "OPTIONS", "description": "启动参数，如--host、--storage-driver等。"}], "returnValue": "无返回值", "examples": [{"code": "# 启动Docker守护进程\nsudo systemctl start docker\n# 查看守护进程状态\nsudo systemctl status docker\n# 查看守护进程日志\nsudo journalctl -u docker", "explanation": "通过systemctl和journalctl管理和排查Docker守护进程。"}]}}, {"name": "Client-Server Communication", "trans": ["客户端与服务端通信"], "usage": {"syntax": "docker <command> [OPTIONS]", "description": "Docker客户端通过命令行或API与守护进程通信，采用REST API协议，支持本地Unix Socket或远程TCP。", "parameters": [{"name": "command", "description": "具体的Docker操作命令。"}, {"name": "OPTIONS", "description": "命令参数。"}], "returnValue": "根据命令不同返回不同结果，如容器ID、镜像列表等。", "examples": [{"code": "# 本地通信（默认）\ndocker ps\n# 远程通信\ndocker -H tcp://*************:2375 ps", "explanation": "演示Docker客户端通过本地和远程方式与守护进程通信。"}]}}, {"name": "Image Layer Principle", "trans": ["镜像分层原理"], "usage": {"syntax": "镜像 = 多个只读层（Layer）叠加", "description": "Docker镜像由多层只读文件系统组成，每一层代表一次文件变更。分层结构便于镜像复用、加速构建和节省存储。", "parameters": [{"name": "Layer", "description": "镜像的只读层，每层可被多个镜像共享。"}], "returnValue": "无返回值", "examples": [{"code": "# 查看镜像分层结构\ndocker history nginx:latest", "explanation": "通过docker history命令查看镜像的分层构成。"}]}}, {"name": "Union File System", "trans": ["联合文件系统"], "usage": {"syntax": "UnionFS（如Overlay2、AUFS）", "description": "联合文件系统（UnionFS）将多个只读层和一个可写层组合为单一文件系统，容器运行时所有更改写入最上层可写层。", "parameters": [{"name": "只读层", "description": "镜像的基础层，内容不可更改。"}, {"name": "可写层", "description": "容器运行时新增的最上层，所有更改写入此层。"}], "returnValue": "无返回值", "examples": [{"code": "# 查看存储驱动\ndocker info | grep 'Storage Driver'\n# 查看容器文件系统变化\ndocker diff <容器ID>", "explanation": "演示联合文件系统的存储驱动和容器文件变更。"}]}}, {"name": "Assignment: <PERSON><PERSON>架构原理实践", "trans": ["作业：Docker架构原理实践"], "usage": {"syntax": "请完成以下任务：\n1. 画出Docker架构主要组件示意图\n2. 启动并查看Docker守护进程状态\n3. 使用本地和远程方式与Docker通信\n4. 查看镜像分层结构和容器文件变更", "description": "通过实践任务加深对Docker架构、守护进程、通信机制、镜像分层和联合文件系统的理解。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "# 1. 架构图可手绘或用工具绘制\n# 2. systemctl status docker\n# 3. docker -H tcp://<IP>:2375 ps\n# 4. docker history <镜像名>\n   docker diff <容器ID>", "explanation": "依次完成架构原理相关的实践任务。"}]}}]}