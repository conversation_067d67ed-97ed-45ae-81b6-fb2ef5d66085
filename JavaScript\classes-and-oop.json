{"name": "Classes and ES6 OOP", "trans": ["类与ES6面向对象"], "methods": [{"name": "Class Declaration", "trans": ["class声明"], "usage": {"syntax": "class Person {\n  constructor(name) {\n    this.name = name;\n  }\n}\nconst p = new Person('Tom');", "description": "class用于声明类，constructor为构造函数，new关键字创建实例。", "parameters": [{"name": "constructor", "description": "构造函数，初始化实例。"}, {"name": "name", "description": "实例属性。"}], "returnValue": "返回类的实例对象。", "examples": [{"code": "class Animal {\n  constructor(type) {\n    this.type = type;\n  }\n}\nconst dog = new Animal('dog');\nconsole.log(dog.type); // 'dog'", "explanation": "用class声明类并创建实例。"}]}}, {"name": "Constructor & super", "trans": ["构造函数与super"], "usage": {"syntax": "class Parent {\n  constructor(x) { this.x = x; }\n}\nclass Child extends Parent {\n  constructor(x, y) {\n    super(x);\n    this.y = y;\n  }\n}", "description": "子类构造函数需调用super()，用于继承父类属性和方法。", "parameters": [{"name": "super", "description": "调用父类构造函数。"}, {"name": "x, y", "description": "构造函数参数。"}], "returnValue": "返回子类实例。", "examples": [{"code": "class A { constructor(a) { this.a = a; } }\nclass B extends A { constructor(a, b) { super(a); this.b = b; } }\nconst b = new B(1, 2);\nconsole.log(b.a, b.b); // 1 2", "explanation": "子类构造函数通过super继承父类属性。"}]}}, {"name": "Static Properties & Methods", "trans": ["静态属性与方法"], "usage": {"syntax": "class Tool {\n  static version = '1.0';\n  static info() { return '工具类'; }\n}\nconsole.log(Tool.version);\nconsole.log(Tool.info());", "description": "static声明静态属性和方法，只能通过类名访问，实例无法访问。", "parameters": [{"name": "static", "description": "声明静态属性或方法。"}], "returnValue": "返回静态属性值或方法结果。", "examples": [{"code": "class MathUtil {\n  static PI = 3.14;\n  static add(a, b) { return a + b; }\n}\nconsole.log(MathUtil.PI);\nconsole.log(MathUtil.add(1,2));", "explanation": "演示静态属性和方法的用法。"}]}}, {"name": "Inheritance & Polymorphism", "trans": ["继承与多态"], "usage": {"syntax": "class Animal { speak() { return '叫'; } }\nclass Dog extends Animal { speak() { return '汪'; } }\nconst a = new Animal();\nconst d = new Dog();\na.speak();\nd.speak();", "description": "extends实现类继承，子类可重写父类方法实现多态。", "parameters": [{"name": "extends", "description": "继承父类。"}, {"name": "speak", "description": "重写父类方法。"}], "returnValue": "返回方法执行结果。", "examples": [{"code": "class Shape { area() { return 0; } }\nclass Circle extends Shape { area() { return Math.PI * 1 * 1; } }\nconst s = new Shape();\nconst c = new Circle();\nconsole.log(s.area()); // 0\nconsole.log(c.area()); // 3.141592653589793", "explanation": "子类重写父类方法实现多态。"}]}}, {"name": "Getter & Setter", "trans": ["getter/setter"], "usage": {"syntax": "class User {\n  constructor(name) { this._name = name; }\n  get name() { return this._name; }\n  set name(val) { this._name = val; }\n}\nconst u = new User('<PERSON>');\nu.name = '<PERSON>';\nconsole.log(u.name);", "description": "get/set定义属性的读取和设置方法，常用于数据保护和计算属性。", "parameters": [{"name": "get", "description": "读取属性时调用。"}, {"name": "set", "description": "设置属性时调用。"}], "returnValue": "返回属性值或设置结果。", "examples": [{"code": "class Counter {\n  constructor() { this._v = 0; }\n  get value() { return this._v; }\n  set value(val) { this._v = val; }\n}\nconst c = new Counter();\nc.value = 5;\nconsole.log(c.value); // 5", "explanation": "getter/setter的基本用法。"}]}}, {"name": "Instance & Prototype Methods", "trans": ["实例与原型方法"], "usage": {"syntax": "class Demo {\n  instanceMethod() { return '实例方法'; }\n  static staticMethod() { return '静态方法'; }\n}\nconst d = new Demo();\nd.instanceMethod();\nDemo.staticMethod();", "description": "实例方法只能通过实例调用，静态方法只能通过类调用。", "parameters": [{"name": "实例方法", "description": "属于实例的普通方法。"}, {"name": "静态方法", "description": "属于类的静态方法。"}], "returnValue": "返回方法执行结果。", "examples": [{"code": "class Tool {\n  run() { return 'run'; }\n  static info() { return 'info'; }\n}\nconst t = new Tool();\nconsole.log(t.run());\nconsole.log(Tool.info());", "explanation": "实例方法与静态方法的区别。"}]}}, {"name": "作业：类与ES6面向对象实践", "trans": ["作业"], "usage": {"syntax": "// 需求：\n// 1. 用class声明类和构造函数\n// 2. 静态属性/方法和继承\n// 3. getter/setter和多态\n// 4. 区分实例与静态方法", "description": "通过实践class、继承、多态、getter/setter等，掌握ES6面向对象基础。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "// 学生实现，以下为提示\n// 1. class/构造函数/super\n// 2. 静态属性/方法/继承\n// 3. getter/setter/多态\n// 4. 实例与静态方法", "explanation": "作业提示，需结合本节内容完成实现。"}, {"code": "// 正确实现示例\nclass Base { get v() { return 1; } }\nclass Sub extends Base { get v() { return 2; } }\nclass Tool { static info() { return 'info'; } run() { return 'run'; } }\nconst s = new Sub();\nconsole.log(s.v, Tool.info(), new Tool().run());", "explanation": "涵盖class、继承、多态、getter/setter、实例与静态方法的正确实现。"}]}}]}