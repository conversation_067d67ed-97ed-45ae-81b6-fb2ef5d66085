{"name": "Event Handling", "trans": ["事件处理"], "methods": [{"name": "Event Binding and Modifiers", "trans": ["事件绑定与修饰符"], "usage": {"syntax": "<button @click=\"onClick\">点击</button>\n<input @keyup.enter=\"onEnter\" />\n<button @click.stop=\"onStop\">阻止冒泡</button>", "description": "通过@或v-on为元素绑定事件，修饰符如.stop、.prevent、.capture、.once、.self等可控制事件行为。", "parameters": [{"name": "@/v-on", "description": "事件绑定语法"}, {"name": "修饰符", "description": "如.stop、.prevent等"}], "returnValue": "无返回值，事件触发时执行方法", "examples": [{"code": "<button @click=\"onClick\">点击</button>\n<input @keyup.enter=\"onEnter\" />\n<button @click.stop=\"onStop\">阻止冒泡</button>", "explanation": "分别演示了基本事件绑定、键盘修饰符和.stop修饰符。"}]}}, {"name": "Event Object", "trans": ["事件对象"], "usage": {"syntax": "<button @click=\"handle($event)\">传递事件对象</button>", "description": "事件处理函数可通过$event参数获取原生事件对象，访问事件属性和方法。", "parameters": [{"name": "$event", "description": "事件对象，包含事件相关信息"}], "returnValue": "事件对象本身，可访问如target、type等属性", "examples": [{"code": "<button @click=\"handle($event)\">传递事件对象</button>\n<script>\nexport default {\n  methods: {\n    handle(e) {\n      alert(e.type)\n    }\n  }\n}\n</script>", "explanation": "通过$event获取事件对象并访问其属性。"}]}}, {"name": "Custom Events", "trans": ["自定义事件"], "usage": {"syntax": "// 子组件\nthis.$emit('事件名', 参数)\n// 父组件\n<MyChild @事件名=\"方法\" />", "description": "子组件通过$emit派发自定义事件，父组件通过@事件名监听，实现父子通信。", "parameters": [{"name": "$emit", "description": "触发自定义事件的方法"}, {"name": "@事件名", "description": "父组件监听事件的写法"}], "returnValue": "无返回值，事件触发时父组件方法被调用", "examples": [{"code": "// 子组件\nthis.$emit('change', value)\n// 父组件\n<MyChild @change=\"onChange\" />", "explanation": "子组件通过$emit派发change事件，父组件通过@change监听。"}]}}, {"name": "Event Bubbling and Capturing", "trans": ["事件冒泡与捕获"], "usage": {"syntax": "<div @click.capture=\"onParent\">\n  <button @click=\"onChild\">子</button>\n</div>", "description": "事件冒泡是事件从子到父传播，.capture修饰符可让事件在捕获阶段触发。", "parameters": [{"name": ".capture", "description": "捕获阶段触发事件"}], "returnValue": "事件在捕获或冒泡阶段被处理", "examples": [{"code": "<div @click.capture=\"onParent\">\n  <button @click=\"onChild\">子</button>\n</div>\n<script>\nexport default {\n  methods: {\n    onParent() { alert('父') },\n    onChild() { alert('子') }\n  }\n}\n</script>", "explanation": "onParent在捕获阶段触发，onChild在冒泡阶段触发。"}]}}, {"name": "Event Unbinding", "trans": ["事件解绑"], "usage": {"syntax": "// v-once只绑定一次\n<button @click.once=\"onClick\">只触发一次</button>", "description": "通过.once修饰符让事件只触发一次，或在js中移除事件监听实现解绑。", "parameters": [{"name": ".once", "description": "事件只触发一次"}], "returnValue": "事件只会触发一次或被移除", "examples": [{"code": "<button @click.once=\"onClick\">只触发一次</button>", "explanation": "点击按钮后事件解绑，只触发一次。"}]}}, {"name": "Event Delegation", "trans": ["事件委托"], "usage": {"syntax": "<ul @click=\"onListClick\">\n  <li v-for=\"item in items\" :key=\"item.id\">{{ item.name }}</li>\n</ul>", "description": "事件委托通过在父元素上绑定事件，利用事件冒泡统一处理多个子元素的事件，提升性能。", "parameters": [{"name": "父元素事件绑定", "description": "在父级统一监听事件"}], "returnValue": "通过事件对象区分目标子元素，实现高效事件处理", "examples": [{"code": "<ul @click=\"onListClick\">\n  <li v-for=\"item in items\" :key=\"item.id\">{{ item.name }}</li>\n</ul>\n<script>\nexport default {\n  data() { return { items: [{id:1, name:'A'},{id:2, name:'B'}] } },\n  methods: {\n    onListClick(e) {\n      if (e.target.tagName === 'LI') {\n        alert(e.target.innerText)\n      }\n    }\n  }\n}\n</script>", "explanation": "在ul上绑定事件，利用冒泡统一处理所有li的点击。"}]}}, {"name": "Event Handling Assignment", "trans": ["事件处理练习"], "usage": {"syntax": "# 事件处理练习", "description": "完成以下练习，巩固事件处理相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用@绑定点击事件。\n2. 用.stop和.prevent修饰符控制事件。\n3. 获取事件对象并输出。\n4. 用$emit实现自定义事件。\n5. 用.once实现事件解绑。\n6. 用事件委托优化列表点击。", "explanation": "通过这些练习掌握事件绑定、修饰符、事件对象、自定义事件、解绑和委托。"}]}}]}