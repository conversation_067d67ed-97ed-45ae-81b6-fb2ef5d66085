{"name": "Transaction", "trans": ["事务机制"], "methods": [{"name": "MULTI/EXEC/DISCARD/WATCH Commands", "trans": ["MULTI/EXEC/DISCARD/WATCH命令"], "usage": {"syntax": "MULTI\n命令1\n命令2\n...\nEXEC\nDISCARD\nWATCH key [key ...]", "description": "MULTI开启事务，后续命令入队，EXEC提交事务，DISCARD放弃事务。WATCH监控键，支持乐观锁。", "parameters": [{"name": "key", "description": "被监控的键名（WATCH用）"}], "returnValue": "事务执行结果或被放弃", "examples": [{"code": "$ redis-cli\n> MULTI\n> set a 1\n> incr a\n> EXEC\n# WATCH示例\n> WATCH balance\n> MULTI\n> DECR balance\n> EXEC", "explanation": "演示了事务的基本用法和WATCH乐观锁。"}]}}, {"name": "Transaction Features and Notes", "trans": ["事务特性与注意事项"], "usage": {"syntax": "事务命令入队，EXEC统一执行，单条命令失败不回滚，WATCH实现乐观锁。", "description": "Redis事务不保证原子性，命令在EXEC时批量执行，单条命令失败不影响其他命令。WATCH可实现CAS乐观锁。", "parameters": [], "returnValue": "事务执行的批量结果", "examples": [{"code": "# 事务不回滚示例\nMULTI\nSET a 1\nINCR a # 正常\nINCR b # b不存在也不会回滚a\nEXEC", "explanation": "演示了事务中部分命令失败不会回滚。"}]}}, {"name": "Practice: 事务机制练习", "trans": ["事务机制练习"], "usage": {"syntax": "# Redis事务机制练习", "description": "完成以下练习，掌握Redis事务的基本用法和乐观锁机制。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 用MULTI/EXEC实现批量写入。\n2. 用WATCH实现余额扣减的乐观锁。\n3. 验证事务中部分命令失败不会回滚。", "explanation": "通过这些练习掌握事务和乐观锁的用法。"}, {"code": "# 正确实现示例\n$ redis-cli\n> MULTI\n> set x 10\n> incr x\n> EXEC\n> WATCH money\n> MULTI\n> decr money\n> EXEC", "explanation": "展示了练习的标准实现过程。"}]}}]}