{"name": "<PERSON><PERSON> and <PERSON><PERSON>", "trans": ["Redis与缓存"], "methods": [{"name": "Redis Integration and Configuration", "trans": ["Redis集成与配置"], "usage": {"syntax": "pom.xml添加spring-boot-starter-data-redis依赖，配置redis参数", "description": "Spring Boot通过spring-boot-starter-data-redis集成Redis，需配置主机、端口、密码等参数。", "parameters": [{"name": "spring-boot-starter-data-redis", "description": "Redis核心依赖"}, {"name": "spring.redis.*", "description": "Redis连接参数"}], "returnValue": "自动配置的RedisTemplate和StringRedisTemplate实例", "examples": [{"code": "<!-- pom.xml -->\n<dependency>\n  <groupId>org.springframework.boot</groupId>\n  <artifactId>spring-boot-starter-data-redis</artifactId>\n</dependency>\n\n# application.properties\nspring.redis.host=localhost\nspring.redis.port=6379\nspring.redis.password=123456", "explanation": "引入依赖并配置参数即可使用Redis。"}]}}, {"name": "String/Hash/List Operations", "trans": ["String/Hash/List等操作"], "usage": {"syntax": "使用RedisTemplate或StringRedisTemplate操作各种数据结构", "description": "Spring Boot提供RedisTemplate和StringRedisTemplate，支持String、Hash、List、Set、ZSet等多种数据结构操作。", "parameters": [{"name": "RedisTemplate", "description": "通用Redis操作对象"}, {"name": "StringRedisTemplate", "description": "专门处理字符串的操作对象"}], "returnValue": "Redis中对应类型的数据操作结果", "examples": [{"code": "@Autowired\nprivate StringRedisTemplate stringRedisTemplate;\n// String操作\nstringRedisTemplate.opsForValue().set(\"key\", \"value\");\nString value = stringRedisTemplate.opsForValue().get(\"key\");\n// Hash操作\nstringRedisTemplate.opsForHash().put(\"user\", \"name\", \"张三\");\n// List操作\nstringRedisTemplate.opsForList().leftPush(\"list\", \"item1\");", "explanation": "通过不同API可操作Redis多种数据结构。"}]}}, {"name": "Cache Annotations (@Cacheable etc.)", "trans": ["缓存注解（@Cacheable等）"], "usage": {"syntax": "@Cacheable/@CachePut/@CacheEvict注解实现方法级缓存", "description": "Spring Boot集成Spring Cache，支持@Cacheable、@CachePut、@CacheEvict等注解实现方法结果缓存、更新和清除。", "parameters": [{"name": "@Cacheable", "description": "方法结果缓存"}, {"name": "@CachePut", "description": "方法执行后更新缓存"}, {"name": "@CacheEvict", "description": "清除缓存"}], "returnValue": "自动管理的缓存数据", "examples": [{"code": "@Cacheable(value = \"user\", key = \"#id\")\npublic User getUser(Long id) {\n    // 查询数据库\n}\n@CacheEvict(value = \"user\", key = \"#id\")\npublic void deleteUser(Long id) {\n    // 删除用户\n}", "explanation": "@Cacheable等注解可自动管理方法级缓存。"}]}}, {"name": "Distributed Lock", "trans": ["分布式锁"], "usage": {"syntax": "使用Redis setnx或Redisson实现分布式锁", "description": "可通过Redis的setnx命令或Redisson库实现分布式锁，保障分布式环境下的互斥访问。", "parameters": [{"name": "setnx", "description": "原子性加锁命令"}, {"name": "Redisson", "description": "常用分布式锁实现库"}], "returnValue": "加锁与解锁的操作结果", "examples": [{"code": "// setnx实现分布式锁\nBoolean locked = stringRedisTemplate.opsForValue().setIfAbsent(\"lock\", \"1\");\n// Redisson实现\nRLock lock = redissonClient.getLock(\"lock\");\nlock.lock();\ntry {\n    // 业务逻辑\n} finally {\n    lock.unlock();\n}", "explanation": "setnx适合简单场景，Redisson支持可重入锁、自动续期等高级特性。"}]}}, {"name": "Cache Penetration and Avalanche Protection", "trans": ["缓存穿透与雪崩防护"], "usage": {"syntax": "缓存空值、设置过期时间、加锁等方式防护", "description": "可通过缓存空值、合理设置过期时间、加锁等方式防止缓存穿透和雪崩，保障系统稳定性。", "parameters": [{"name": "缓存空值", "description": "防止缓存穿透"}, {"name": "过期时间", "description": "防止雪崩"}, {"name": "分布式锁", "description": "防止并发击穿"}], "returnValue": "更健壮的缓存系统", "examples": [{"code": "// 缓存空值\nif (dbResult == null) {\n    stringRedisTemplate.opsForValue().set(\"key\", \"\", 5, TimeUnit.MINUTES);\n}\n// 设置过期时间\nstringRedisTemplate.opsForValue().set(\"key\", \"value\", 10, TimeUnit.MINUTES);\n// 加锁防止击穿\nBoolean locked = stringRedisTemplate.opsForValue().setIfAbsent(\"lock\", \"1\");", "explanation": "多种手段结合可有效防止缓存穿透和雪崩。"}]}}, {"name": "<PERSON><PERSON> and <PERSON>ache Assignment", "trans": ["Redis与缓存练习"], "usage": {"syntax": "# Redis与缓存练习", "description": "完成以下练习，巩固Spring Boot Redis与缓存相关知识。", "parameters": [], "returnValue": "无返回值", "examples": [{"code": "练习：\n1. 配置Redis依赖和参数。\n2. 实现String、Hash、List等操作。\n3. 用@Cacheable实现方法缓存。\n4. 用setnx或Redisson实现分布式锁。\n5. 防护缓存穿透和雪崩。", "explanation": "通过这些练习掌握Spring Boot Redis与缓存的核心用法。"}]}}]}